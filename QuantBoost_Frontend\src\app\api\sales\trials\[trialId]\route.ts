import { NextRequest } from 'next/server';
import { getSupabaseAdmin } from '@/lib/supabaseServer';

export async function GET(_req: NextRequest, ctx: { params: Promise<{ trialId: string }> }) {
  const supa = getSupabaseAdmin();
  const { trialId } = await ctx.params;

  const { data: trial, error } = await supa
    .from('enterprise_trials')
    .select('*, enterprise_customers:customer_id ( company_name, contact_email )')
    .eq('id', trialId)
    .single();
  if (error) return Response.json({ error: error.message }, { status: 404 });

  const { data: invites, error: invErr } = await supa
    .from('trial_invites')
    .select('id, trial_id, email, accepted_at, user_id, created_at')
    .eq('trial_id', trialId)
    .order('created_at', { ascending: false });
  if (invErr) return Response.json({ error: invErr.message }, { status: 500 });

  return Response.json({ trial, invites });
}
