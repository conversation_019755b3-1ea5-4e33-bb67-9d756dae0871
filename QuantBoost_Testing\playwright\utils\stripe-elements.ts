import { Page, Frame, APIResponse } from '@playwright/test';

/**
 * Resilient Stripe Elements discovery & interaction utilities.
 */
const DEBUG = (process.env.DEBUG_STRIPE || '').toLowerCase() === 'true';
function d(msg: string, meta?: any) { if (DEBUG) console.log(`[stripe-helper] ${msg}`, meta ?? ''); }

async function findFrameWithSelector(page: Page, selector: string, timeout = 10000): Promise<Frame | null> {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    for (const frame of page.frames()) {
      try {
  const el = await frame.$(selector);
  if (el) { d(`Found '${selector}' in frame`, frame.url()); return frame; }
      } catch { /* ignore */ }
    }
    await page.waitForTimeout(150);
  }
  return null;
}

export async function fillLinkEmail(page: Page, email: string): Promise<boolean> {
  const direct = await page.$('input[type=email], input[autocomplete="email"], [data-testid=email-input]');
  if (direct) { await direct.fill(email); return true; }
  const frame = await findFrameWithSelector(page, 'input[type=email], input[autocomplete=email]');
  if (!frame) return false;
  const emailEl = await frame.$('input[type=email], input[autocomplete=email]');
  if (!emailEl) return false; await emailEl.fill(email); return true;
}

// Wait until frontend sets readiness flag (or PaymentElement container present) before interacting.
export async function waitForPaymentElementReady(page: Page, timeout = 15000): Promise<boolean> {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    const readyFlag = await page.evaluate(() => (window as any).__QB_PAYMENT_ELEMENT_READY === true).catch(()=>false);
    if (readyFlag) return true;
    const container = await page.$('[data-testid=qb-payment-element], .PaymentElement');
    if (container) return true;
    await page.waitForTimeout(150);
  }
  return false;
}

// Dump stripe frame diagnostics to console for debugging structure.
export async function dumpStripeFrames(page: Page, label: string) {
  const frames = page.frames().filter(f => /stripe/i.test(f.url()));
  const out: any[] = [];
  for (const f of frames) {
    try {
      const snapshot = await f.evaluate(() => {
        const inputs = Array.from(document.querySelectorAll('input')).map((el: any) => ({
          name: el.name,
          placeholder: el.placeholder,
          aria: el.getAttribute('aria-label'),
          type: el.type,
          valueLength: (el.value || '').length
        }));
        return { html: document.body.innerText.slice(0, 400), inputs };
      }).catch(() => ({ html: 'eval-failed', inputs: [] }));
      out.push({ url: f.url(), ...snapshot });
    } catch { out.push({ url: f.url(), error: 'unreadable' }); }
  }
  d(`Frame dump ${label}`, out);
  // Always log a JSON block for CI artifact parsing
  console.log(JSON.stringify({ stripeFrameDump: { label, frames: out } }));
}

export async function fillCardDetails(page: Page, opts: { number: string; exp?: string; cvc?: string; postal?: string }): Promise<boolean> {
  const { number, exp = '12/30', cvc = '123', postal } = opts;

  // Helper to normalize expiry
  const normExp = exp.includes('/') ? exp : exp.length === 4 ? `${exp.slice(0,2)}/${exp.slice(2)}` : exp;

  // 1. Attempt classic multi-iframe Card Element model first (separate iframes)
  try {
    const cardFrame = await findFrameWithSelector(page, 'input[name="cardnumber"], input[placeholder*="Card number" i], input[aria-label*="Card number" i]');
    if (cardFrame) {
      d('Filling classic multi-field card element');
      const numberEl = await cardFrame.$('input[name="cardnumber"], input[placeholder*="Card number" i], input[aria-label*="Card number" i]');
      if (!numberEl) return false;
      await numberEl.fill(number);

      const expFrame = (await findFrameWithSelector(page, 'input[name="exp-date"], input[placeholder*="MM / YY" i], input[placeholder*="MM/YY" i], input[aria-label*="Expiration" i]')) || cardFrame;
      const expEl = await expFrame.$('input[name="exp-date"], input[placeholder*="MM / YY" i], input[placeholder*="MM/YY" i], input[aria-label*="Expiration" i]');
      if (expEl) await expEl.fill(normExp);

      const cvcFrame = (await findFrameWithSelector(page, 'input[name="cvc"], input[placeholder*="CVC" i], input[placeholder*="CVV" i], input[aria-label*="CVC" i]')) || cardFrame;
      const cvcEl = await cvcFrame.$('input[name="cvc"], input[placeholder*="CVC" i], input[placeholder*="CVV" i], input[aria-label*="CVC" i]');
      if (cvcEl) await cvcEl.fill(cvc);

      if (postal) {
        const postalFrame = (await findFrameWithSelector(page, 'input[name="postal"], input[autocomplete="postal-code"], input[placeholder*="ZIP" i], input[aria-label*="ZIP" i]')) || cardFrame;
        const postalEl = await postalFrame.$('input[name="postal"], input[autocomplete="postal-code"], input[placeholder*="ZIP" i], input[aria-label*="ZIP" i]');
        if (postalEl) await postalEl.fill(postal);
      }

      return true;
    }
  } catch (e) { d('Classic card fill attempt error', e); }

  // 2. Unified Payment Element approach: target stable field types per Stripe docs.
  // Ensure readiness before continuing
  const ready = await waitForPaymentElementReady(page, 15000);
  if (!ready) {
    d('Payment Element readiness wait timed out');
    await dumpStripeFrames(page, 'timeout-before-fill');
    return false;
  }

  const stripeFrames = page.frames().filter(f => /stripe/i.test(f.url()));
  d('Unified Payment Element frames detected', stripeFrames.map(f => f.url()));
  if (!stripeFrames.length) {
    await dumpStripeFrames(page, 'no-frames');
    return false;
  }

  // Early hybrid fallback: try to type ONLY the card number into the first viable frame before advanced classification.
  let earlyCardTyped = false;
  for (const frame of stripeFrames) {
    if (earlyCardTyped) break;
    try {
      const candidate = await frame.$('input[aria-label*="Card" i], input[placeholder*="Card" i], input[name="cardnumber"], input[type=tel]');
      if (candidate) {
        await candidate.click({ force: true });
        await candidate.press('Control+A').catch(()=>{});
        await candidate.type(number.replace(/\s+/g, ''), { delay: 12 });
        earlyCardTyped = true;
        d('Early hybrid card number typed in frame', frame.url());
      }
    } catch { /* ignore */ }
  }

  interface RoleMap { card?: Frame; expiry?: Frame; cvc?: Frame; postal?: Frame; }
  const roles: RoleMap = {};

  // Attempt to classify each Stripe frame by scanning placeholders / aria-labels / input names
  for (const frame of stripeFrames) {
    try {
      const inputs = await frame.$$('[name],[placeholder],[aria-label]');
      for (const handle of inputs) {
        try {
          const name = (await handle.getAttribute('name')) || '';
          const ph = ((await handle.getAttribute('placeholder')) || '').toLowerCase();
          const aria = ((await handle.getAttribute('aria-label')) || '').toLowerCase();
          const combo = `${name} ${ph} ${aria}`;
          if (!roles.card && /(card number|cardnumber)/i.test(combo)) roles.card = frame;
          else if (!roles.expiry && /(mm\s*\/\s*yy|exp|expiration)/i.test(combo)) roles.expiry = frame;
          else if (!roles.cvc && /(cvc|cvv|security code)/i.test(combo)) roles.cvc = frame;
          else if (!roles.postal && /(zip|postal)/i.test(combo)) roles.postal = frame;
        } catch { /* ignore single input errors */ }
      }
    } catch { /* ignore frame */ }
  }

  d('Role classification', {
    card: !!roles.card,
    expiry: !!roles.expiry,
    cvc: !!roles.cvc,
    postal: !!roles.postal
  });

  async function fillInFrame(frame: Frame | undefined, patterns: string[], value: string, friendly: string): Promise<boolean> {
    if (!frame) { d(`No frame for ${friendly}`); return false; }
    for (const sel of patterns) {
      try {
        const el = await frame.$(sel);
        if (el) {
          await el.click({ force: true });
          // Some Stripe fields reject .fill() so use clear via triple click then type
          await el.press('Control+A').catch(()=>{});
          await el.type(value, { delay: 15 });
          d(`Filled ${friendly} with selector`, sel);
          return true;
        }
      } catch { /* try next */ }
    }
    d(`Failed to find selector for ${friendly}`);
    return false;
  }

  // Patterns for each role
  const cardPatterns = ['input[name="cardnumber"]', 'input[aria-label*="Card" i]', 'input[placeholder*="Card" i]', 'input[type=tel]'];
  const expPatterns = ['input[name="exp-date"]', 'input[placeholder*="MM" i]', 'input[aria-label*="Exp" i]'];
  const cvcPatterns = ['input[name="cvc"]', 'input[placeholder*="CVC" i]', 'input[placeholder*="CVV" i]', 'input[aria-label*="CVC" i]'];
  const postalPatterns = ['input[name="postal"]', 'input[autocomplete="postal-code"]', 'input[placeholder*="ZIP" i]', 'input[aria-label*="ZIP" i]'];

  const cardOk = earlyCardTyped || await fillInFrame(roles.card, cardPatterns, number.replace(/\s+/g, ''), 'card');
  // Allow layout to auto-advance
  if (cardOk) await page.waitForTimeout(120);
  const expOk = await fillInFrame(roles.expiry, expPatterns, normExp.replace('/', ''), 'expiry');
  if (expOk) await page.waitForTimeout(80);
  const cvcOk = await fillInFrame(roles.cvc, cvcPatterns, cvc, 'cvc');
  let postalOk = true;
  if (postal) postalOk = await fillInFrame(roles.postal, postalPatterns, postal, 'postal');

  // Fallback: if any critical field missing, attempt generic sequential typing into first stripe frame
  if ((!cardOk || !expOk || !cvcOk) && !earlyCardTyped) {
    d('Entering fallback sequential typing mode');
    const first = stripeFrames[0];
    if (first) {
      try {
        const anyInput = await first.$('input');
        if (anyInput) {
          await anyInput.click({ force: true });
          await anyInput.type(number.replace(/\s+/g, ''), { delay: 15 });
          await page.keyboard.type(' ' + normExp.replace('/', ''), { delay: 15 });
          await page.keyboard.type(' ' + cvc, { delay: 15 });
          if (postal) await page.keyboard.type(' ' + postal, { delay: 15 });
        }
      } catch (e) { d('Fallback typing failed', e); }
    }
  }

  // Validation: search for last4 in any frame content
  let seenLast4 = false;
  for (const frame of stripeFrames) {
    try {
      const bodyTxt = await frame.evaluate(() => document.body.innerText.slice(0, 300));
      if (/4242/.test(bodyTxt)) { seenLast4 = true; break; }
    } catch { /* ignore */ }
  }
  if (!seenLast4) d('Did not detect last4=4242 after fill attempt');
  if (!seenLast4 && process.env.STRICT_CARD_ENTRY === '1') return false;

  return (cardOk || seenLast4) && (expOk || seenLast4) && (cvcOk || seenLast4) && postalOk;
}

export async function waitForPaymentReady(page: Page, timeout = 15000): Promise<boolean> {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    const stripeFrames = page.frames().filter(f => /stripe/i.test(f.url()));
    const stripeFramePresent = stripeFrames.length > 0;
    const paymentContainer = await page.$('[data-testid=qb-payment-element], .PaymentElement, [data-testid=PaymentElement]');
    // Detect classic cardnumber iframe even if container not matched
    let cardIframeFound = false;
    for (const f of stripeFrames) {
      try {
        if (await f.$('input[name="cardnumber"], input[placeholder*="Card number" i]')) { cardIframeFound = true; break; }
      } catch { /* ignore */ }
    }
    const submitVisible = await page.$('[data-testid=qb-complete-button], button[data-testid=submit], button[type=submit], button:has-text("Complete Purchase"), button:has-text("Complete"), button:has-text("Pay"), button:has-text("Subscribe")');
    if (stripeFramePresent && submitVisible && (paymentContainer || cardIframeFound)) return true;
    await page.waitForTimeout(250);
  }
  d('waitForPaymentReady timed out');
  return false;
}

/**
 * Wait for the app to finish creating / finalizing the Payment Intent (stage after email entry).
 * Heuristics:
 *  1. Network response containing create-payment-intent (or similar) 2xx
 *  2. Console log containing 'payment intent ready' | 'finalClientSecret'
 *  3. Email input becomes disabled / readonly
 *  4. PaymentElement container appears
 */
export async function waitForIntentReady(page: Page, timeout = 15000): Promise<boolean> {
  const start = Date.now();
  const consoleEvents: string[] = [];
  function onConsole(msg: any) { const txt = msg.text(); consoleEvents.push(txt); }
  page.on('console', onConsole);
  try {
    let networkHit = false;
    // Fire and forget network wait (best effort)
    const networkPromise = page.waitForResponse(resp => /payment-intent|create-payment-intent|finalize-intent/i.test(resp.url()) && resp.status() < 400, { timeout }).then(() => { networkHit = true; }).catch(()=>{});
    while (Date.now() - start < timeout) {
      // Condition 4 early quick check
      const paymentContainer = await page.$('.PaymentElement, [data-testid=PaymentElement]');
      if (paymentContainer) { d('Intent ready: PaymentElement container present'); return true; }
      // Condition 3 email disabled
      const emailInput = await page.$('input[type=email], input[autocomplete="email"], [data-testid=email-input]');
      if (emailInput) {
        const disabled = await emailInput.isDisabled().catch(()=>false);
        const ro = await emailInput.getAttribute('readonly');
        if (disabled || ro !== null) { d('Intent ready: email input locked'); return true; }
      }
      // Condition 2 console marker
      if (consoleEvents.some(c => /payment intent ready|finalClientSecret/i.test(c))) { d('Intent ready: console marker'); return true; }
      // Condition 1 network
      if (networkHit) { d('Intent ready: network create-payment-intent observed (awaiting container)'); }
      await page.waitForTimeout(200);
    }
    d('waitForIntentReady timed out', { consoleEvents });
    return false;
  } finally {
    page.off('console', onConsole);
  }
}

/** Collects diagnostic information about Stripe frames & Payment Element for troubleshooting. */
export async function collectStripeDiagnostics(page: Page) {
  const frames = page.frames().filter(f => /stripe/i.test(f.url()));
  const details: any[] = [];
  for (const f of frames) {
    try {
      const html = await f.evaluate(() => document.body.innerHTML.slice(0, 500)).catch(()=> 'eval-failed');
      const inputs = await f.$$eval('input', els => els.map(e => ({ name: (e as HTMLInputElement).name, placeholder: (e as HTMLInputElement).placeholder, aria: (e as HTMLElement).getAttribute('aria-label') })).slice(0,10)).catch(()=>[]);
      details.push({ url: f.url(), inputs, snippet: html });
    } catch {
      details.push({ url: f.url(), error: 'unreadable' });
    }
  }
  const paymentContainerExists = !!await page.$('[data-testid=qb-payment-element], .PaymentElement, [data-testid=PaymentElement]');
  d('Stripe diagnostics', { frames: details.length, paymentContainerExists });
  return { frameCount: frames.length, frames: details, paymentContainerExists };
}

/** Tick required consent checkboxes (terms & privacy) */
export async function acceptRequiredConsents(page: Page) {
  const selectors = ['[data-testid=qb-terms]', '[data-testid=qb-privacy]'];
  for (const sel of selectors) {
    const el = await page.$(sel);
    if (el) {
      try {
        const checked = await el.isChecked();
        if (!checked) await el.check({ force: true });
      } catch { /* ignore */ }
    }
  }
}

export async function submitPayment(page: Page): Promise<boolean> {
  const selectors = [
    'button[data-testid=submit]',
    'button[type=submit]',
    'button:has-text("Pay")',
    'button:has-text("Subscribe")',
    'button:has-text("Complete")',
    'button:has-text("Confirm")'
  ];
  for (const sel of selectors) {
    const el = await page.$(sel);
    if (el) {
      try {
        await el.waitForElementState('enabled', { timeout: 5000 }).catch(()=>{});
        await el.click();
        d('Clicked submit', sel);
        return true;
      } catch (e) { d('Submit click failed', { sel, e }); }
    }
  }
  d('No submit button found');
  return false;
}

export async function handleThreeDSIfPresent(page: Page, timeout = 15000): Promise<boolean> {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    const challengeFrame = page.frames().find(f => /stripe|3ds|authentication/i.test(f.url()));
    if (challengeFrame) {
      const complete = await challengeFrame.$('text=/Complete authentication/i, text=/Submit/i, text=/Authorize/i');
      if (complete) { await complete.click(); return true; }
    }
    await page.waitForTimeout(300);
  }
  return false;
}

export async function waitForReceiptOrSuccess(page: Page, timeout = 15000): Promise<boolean> {
  try { await page.waitForSelector('text=/thank you|success|receipt/i', { timeout }); return true; } catch { return false; }
}
