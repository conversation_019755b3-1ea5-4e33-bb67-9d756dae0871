# Excel Trace V2 - Testing Guide

## Quick Verification Tests

### 1. Basic Functionality Test
```csharp
// Test the core tracing functionality
var tracerEngine = new TracerEngine();
var rootNode = tracerEngine.BuildPrecedentTree(selectedCell, 3);

// Verify:
// - Root node is created successfully
// - Children are populated correctly
// - No duplicate nodes exist
// - Formula positions are tracked
```

### 2. UI Component Test
```csharp
// Test the WPF UI components
var traceView = new TraceView();
traceView.SetTraceData(rootNode);

// Verify:
// - TreeView displays correctly
// - Formula highlighting works
// - Column resizing functions
// - Context menu appears
// - Status text updates
```

### 3. Performance Test
```csharp
// Test with complex formulas
var stopwatch = Stopwatch.StartNew();
var result = tracerEngine.BuildPrecedentTree(complexCell, 5);
stopwatch.Stop();

// Verify:
// - Completes within timeout (30 seconds)
// - Memory usage is reasonable
// - No COM object leaks
// - Proper error handling
```

## Manual Testing Scenarios

### Formula Highlighting
1. Open Excel with a complex formula like `=SUMIF(A:A,">100",B:B)+VLOOKUP(D1,F:G,2,0)`
2. Run Excel Trace on the cell
3. Click different precedents in the tree
4. **Expected**: Corresponding parts of the formula are highlighted in yellow

### Column Resizing
1. Open the trace dialog
2. Drag the column splitter between "Precedents" and "Value"
3. **Expected**: Columns resize smoothly, content adjusts properly

### Keyboard Navigation
1. Use Tab to focus the TreeView
2. Use Up/Down arrows to navigate
3. Use Right/Left arrows to expand/collapse
4. Press Enter to navigate to a cell
5. **Expected**: All keyboard interactions work smoothly

### Context Menu
1. Right-click on any node in the tree
2. Test each menu item:
   - Navigate to Cell
   - Copy Cell Address
   - Copy Cell Value
   - Expand All
   - Collapse All
3. **Expected**: All actions work correctly

### Error Handling
1. Test with cells containing errors (#REF!, #N/A, etc.)
2. Test with protected workbooks
3. Test with very deep precedent chains
4. **Expected**: Graceful error handling, no crashes

## Performance Benchmarks

### Target Performance
- **Trace Time**: < 5 seconds for typical formulas
- **Memory Usage**: < 100MB for large traces
- **UI Responsiveness**: No freezing during trace operations
- **Timeout Protection**: Automatic termination after 30 seconds

### Test Cases
1. **Simple Formula**: `=A1+B1` (should complete in < 1 second)
2. **Medium Complexity**: `=SUMIF(A:A,">100",B:B)` (should complete in < 3 seconds)
3. **High Complexity**: Nested VLOOKUP with multiple sheets (should complete in < 10 seconds)
4. **Extreme Case**: Deep precedent chain (should timeout gracefully at 30 seconds)

## Regression Testing

### Before Each Release
1. Test all major formula types (SUM, VLOOKUP, IF, etc.)
2. Test cross-sheet references
3. Test external workbook references
4. Test with hidden rows/columns
5. Test with filtered data
6. Test with large datasets (1000+ rows)

### Browser Compatibility
- Test in different Excel versions (2016, 2019, 365)
- Test on different Windows versions
- Test with different screen resolutions
- Test with high-DPI displays

## Known Limitations

### Current Constraints
1. **Maximum Depth**: 10 levels (configurable)
2. **Maximum Nodes**: 500 per level
3. **Timeout**: 30 seconds maximum
4. **Memory**: Designed for workbooks up to 100MB

### Future Enhancements
1. **Async Processing**: Background trace operations
2. **Incremental Loading**: Lazy-load deep precedents
3. **Export Features**: Save trace results to file
4. **Advanced Filtering**: Filter by node type or error status

## Troubleshooting

### Common Issues
1. **Slow Performance**: Check trace depth settings, reduce if necessary
2. **Memory Issues**: Close other applications, restart Excel
3. **COM Errors**: Ensure workbook is not protected or corrupted
4. **UI Issues**: Check Windows version compatibility

### Debug Information
- All operations are logged to the error handling service
- Performance metrics are captured automatically
- Memory usage can be monitored via Task Manager
- COM object disposal is tracked internally

## Success Criteria

### Must Pass
✅ All formula highlighting works correctly
✅ Column resizing functions smoothly  
✅ Keyboard navigation is fully functional
✅ Context menu items work as expected
✅ No memory leaks or COM object issues
✅ Performance meets target benchmarks
✅ Error handling prevents crashes
✅ UI is responsive and professional

### Quality Gates
- **Code Coverage**: > 80% for core components
- **Performance**: Meets all benchmark targets
- **Memory**: No leaks detected in 1-hour stress test
- **Compatibility**: Works across all supported Excel versions
- **Accessibility**: Full keyboard navigation support
- **Documentation**: All public APIs documented

This testing guide ensures the refactored Excel Trace feature meets production quality standards.
