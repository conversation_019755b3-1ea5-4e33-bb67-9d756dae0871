using System;
using System.Collections.Generic;
using System.Windows.Forms;
using QuantBoost_Powerpoint_Addin.ExcelLink;
using Excel = Microsoft.Office.Interop.Excel;

namespace QuantBoost_Powerpoint_Addin.UI
{
    /// <summary>
    /// Dialog for selecting a chart from an Excel workbook.
    /// </summary>
    public class SelectChartDialog : Form
    {
        public string SelectedFilePath { get; private set; }
        public string SelectedWorksheetName { get; private set; }
        public string SelectedChartName { get; private set; }

        private TreeView _treeViewCharts;
        private Button _okButton;
        private Button _cancelButton;

        public SelectChartDialog(string excelFilePath)
        {
            SelectedFilePath = excelFilePath;
            InitializeComponent();
            LoadChartsFromExcel();
        }

        private void InitializeComponent()
        {
            Text = "Select Excel Chart";
            Width = 400;
            Height = 500;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            StartPosition = FormStartPosition.CenterParent;

            _treeViewCharts = new TreeView
            {
                Dock = DockStyle.Fill,
                Height = Height - 80, // Adjust height
                FullRowSelect = true,
                ShowLines = true
            };
            _treeViewCharts.NodeMouseDoubleClick += TreeViewCharts_NodeMouseDoubleClick;

            _okButton = new Button
            {
                Text = "OK",
                DialogResult = DialogResult.OK,
                Enabled = false, // Enabled only when a chart node is selected
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                Top = _treeViewCharts.Bottom + 10,
                Left = Width - 190
            };
            _okButton.Click += OkButton_Click;

            _cancelButton = new Button
            {
                Text = "Cancel",
                DialogResult = DialogResult.Cancel,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                Top = _treeViewCharts.Bottom + 10,
                Left = _okButton.Left + _okButton.Width + 10
            };

            _treeViewCharts.AfterSelect += (s, e) => { _okButton.Enabled = (e.Node != null && e.Node.Tag is string); }; // Enable OK only for chart nodes

            Controls.Add(_treeViewCharts);
            Controls.Add(_okButton);
            Controls.Add(_cancelButton);
            AcceptButton = _okButton;
            CancelButton = _cancelButton;
        }

        private void LoadChartsFromExcel()
        {
            _treeViewCharts.Nodes.Clear();
            try
            {
                using (var excelWrapper = new ExcelComWrapper())
                {
                    var workbook = excelWrapper.OpenWorkbook(SelectedFilePath);
                    if (workbook == null) throw new Exception("Failed to open workbook.");

                    foreach (Excel.Worksheet sheet in workbook.Sheets)
                    {
                        var sheetNode = new TreeNode(sheet.Name);
                        _treeViewCharts.Nodes.Add(sheetNode);
                        Excel.ChartObjects chartObjects = null;
                        try
                        {
                            chartObjects = (Excel.ChartObjects)sheet.ChartObjects();
                            if (chartObjects.Count > 0)
                            {
                                foreach (Excel.ChartObject chartObj in chartObjects)
                                {
                                    var chartNode = new TreeNode(chartObj.Name);
                                    chartNode.Tag = chartObj.Name; // Store chart name in Tag for retrieval
                                    sheetNode.Nodes.Add(chartNode);
                                    System.Runtime.InteropServices.Marshal.ReleaseComObject(chartObj);
                                }
                            }
                        }
                        finally
                        {
                            if (chartObjects != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(chartObjects);
                            System.Runtime.InteropServices.Marshal.ReleaseComObject(sheet);
                        }
                    }
                }
                _treeViewCharts.ExpandAll();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading charts from Excel file:\n{ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.Cancel; // Close dialog on error
            }
        }

        private void OkButton_Click(object sender, EventArgs e)
        {
            if (_treeViewCharts.SelectedNode != null && _treeViewCharts.SelectedNode.Tag is string chartName)
            {
                SelectedWorksheetName = _treeViewCharts.SelectedNode.Parent.Text;
                SelectedChartName = chartName;
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("Please select a chart node.", "Selection Required", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                DialogResult = DialogResult.None; // Keep dialog open
            }
        }

        private void TreeViewCharts_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            // Double-clicking a chart node is equivalent to selecting and clicking OK
            if (e.Node != null && e.Node.Tag is string)
            {
                _treeViewCharts.SelectedNode = e.Node;
                OkButton_Click(sender, e);
            }
        }
    }
}
