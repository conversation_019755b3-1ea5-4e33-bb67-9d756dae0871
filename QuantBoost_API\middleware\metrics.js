const telemetry = require('../utils/telemetry');
const logger = require('../utils/logger');

const metricsMiddleware = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    
    // Only track metrics for errors and slow requests to reduce volume
    const shouldTrack = res.statusCode >= 400 || duration > 1000;
    
    if (shouldTrack) {
      // Track metrics only for significant events
      telemetry.trackMetric('http_request_duration_ms', duration, {
        method: req.method,
        path: req.path,
        statusCode: res.statusCode
      });
      
      telemetry.trackEvent('http_request_significant', {
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        duration,
        requestId: req.requestId,
        reason: res.statusCode >= 400 ? 'error' : 'slow'
      });
    }

    // Always track errors separately
    if (res.statusCode >= 400) {
      telemetry.trackEvent('http_error', {
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        duration,
        requestId: req.requestId
      });
    }
  });
  
  next();
};

module.exports = metricsMiddleware;