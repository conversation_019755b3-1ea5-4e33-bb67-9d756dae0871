const path = require('path');

module.exports = async () => {
  console.log('Executing globalSetup.js...');

  // Only load .env.test if we're running locally (not in CI)
  if (!process.env.CI && !process.env.GITHUB_ACTIONS) {
    require('dotenv').config({ path: path.resolve(__dirname, '../.env.test'), override: true });
    console.log('Local development: Loaded .env.test file');
  } else {
    console.log('CI environment: Using environment variables from CI/CD');
  }

  console.log('Environment check:');
  console.log('- STRIPE_SECRET_KEY:', process.env.STRIPE_SECRET_KEY ? 'Loaded' : 'Not Loaded');
  console.log('- SUPABASE_URL:', process.env.SUPABASE_URL ? 'Loaded' : 'Not Loaded');
  console.log('- MASTER_API_KEY:', process.env.MASTER_API_KEY ? 'Loaded' : 'Not Loaded');
  console.log('- NODE_ENV:', process.env.NODE_ENV);
};
