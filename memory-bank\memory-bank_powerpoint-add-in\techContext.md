---
title: Technical Context for PowerPoint Add-in
purpose: Details the technology stack, dependencies, and environment for the QuantBoost PowerPoint Add-in.
projects: ["powerpoint-add-in"]
source_analysis: "Codebase analysis of QuantBoost_PPTX (powerpoint-add-in), including files from Batch 1 (Analysis, ExcelLink Core), Batch 2 (UI), and Batch 3 (Utilities, Properties, ThisAddIn.cs)." # Updated source_analysis
status: bootstrapped-incomplete
last_updated: 2025-05-13T16:00:00Z # Updated timestamp
tags: ["powerpoint-add-in", "technical", "dependencies", "environment", "ui", "utilities", "core"] # Added tags
---

## Technology Stack and Dependencies for QuantBoost PowerPoint Add-in

### Core Technologies
*   **Primary Language:** C# 7.3
*   **Framework:** .NET Framework 4.8.1
*   **Project Type:** VSTO (Visual Studio Tools for Office) PowerPoint Add-in

### UI Technologies
*   **Windows Forms:** Used for creating custom Task Panes (`AnalyzePane.cs`, `LinkManagerPane.cs`) and Dialogs (`EditLinkDialog.cs`, `LicenseDialog.cs`, `SelectChartDialog.cs`).
    *   Common Controls: `DataGridView`, `ToolStrip`, `Button`, `Label`, `TextBox`, `ComboBox`, `ProgressBar`, `TreeView`.
*   **Ribbon (Office Fluent UI):** Custom ribbon defined in `QuantBoostRibbon.xml` and controlled by `QuantBoostRibbon.cs`.

### Key Libraries and Dependencies
*   **DocumentFormat.OpenXml:** Version 2.18.0. Used for direct manipulation of OpenXML PowerPoint files (e.g., by `PptxFileParser.cs`).
*   **Newtonsoft.Json:** Version 13.0.3. Used for JSON processing (e.g., potentially for configuration, metadata, or API communication).
*   **Office Interop Assemblies:**
    *   `Microsoft.Office.Interop.PowerPoint`
    *   `Microsoft.Office.Interop.Excel`
    *   `Microsoft.Office.Core`
    (Used for interacting with PowerPoint and Excel applications via COM, e.g., in `ExcelLinkService.cs`, `ExcelComWrapper.cs`, `PowerPointComWrapper.cs`, and UI components like `EditLinkDialog.cs` for validation).
*   **`QuantBoost.Licensing.dll`:** Project reference. Handles licensing for the add-in. Details of this library are external to this specific project's codebase but it's consumed in `ThisAddIn.cs` and UI components like `LicenseDialog.cs`.
*   **`System.Threading.Tasks`:** Used for asynchronous operations, particularly in `AsyncHelper.cs` and `ThisAddIn.cs` for managing background tasks and license validation.

### Build and Environment
*   **Build Tool:** MSBuild (via `.csproj` and `.sln` files).
*   **Target Environment:** Desktop environment with Microsoft PowerPoint and .NET Framework 4.8.1 installed.
*   **Development Environment:** Visual Studio with VSTO development tools.
*   **Assembly Versioning:** Managed in `Properties/AssemblyInfo.cs`. Current version is `*******`.

### External API Calls
*   `ThisAddIn.cs` initializes `QuantBoostLicensingManager` with an `API_BASE_URL` of `http://localhost:3000`, presumably for license validation and management.
*   Likely interacts with a licensing server via the `QuantBoost.Licensing.dll`.
*   `LicenseDialog.cs` attempts to open a web browser to `https://www.quantboost.ai/portal`.

### Database Interactions
*   No direct database interactions are apparent from the analyzed codebase for this project. Data persistence related to Excel links is handled via custom XML parts within PowerPoint files.
