import { describe, it, expect, vi, beforeEach } from 'vitest';
import { billingClient } from '../billingClient';
import * as authFetchMod from '../authFetch';

// Mock authJson to simulate various responses
const mockAuthJson = vi.fn();
vi.spyOn(authFetchMod, 'authJson').mockImplementation(((...args: any[]) => mockAuthJson(...args)) as any);

beforeEach(() => {
  mockAuthJson.mockReset();
});

describe('billingClient error handling', () => {
  it('returns body on success', async () => {
    mockAuthJson.mockResolvedValue({ ok: true, body: { client_secret: 'cs_123' } });
    const res = await billingClient.createSetupIntent('sub_1');
    expect(res.ok).toBe(true);
    expect(res.body.client_secret).toBe('cs_123');
  });

  it('propagates error state', async () => {
    mockAuthJson.mockResolvedValue({ ok: false, status: 500, body: { error: 'boom' } });
    const res: any = await billingClient.getUpcomingInvoice('sub_1');
    expect(res.ok).toBe(false);
    expect(res.body.error).toBe('boom');
  });
});
