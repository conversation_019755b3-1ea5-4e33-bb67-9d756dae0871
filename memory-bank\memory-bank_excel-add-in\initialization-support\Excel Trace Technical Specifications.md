# QuantBoost Excel Trace Add-in  
## Technical Specification (v3.0, Enhanced SaaS Edition)

_Last updated: March 30, 2025_

---

## 1. Purpose

Deliver a modern Excel add-in that allows rapid tracing of cell dependents, integrates seamlessly with the QuantBoost unified SaaS licensing backend, and provides a responsive, informative user experience with robust error handling and support for licensing states like expired, trial, grace, and upgrades.

---

## 2. Platform & Technology

- **Host:** Microsoft Excel (Microsoft 365 Desktop, Windows 10/11)  
- **Framework:** .NET Framework 4.8+, VSTO Add-in  
- **Languages:** C# (.NET, async/await)  
- **Dependencies:**  
  - QuantBoost Licensing SDK v1.x  
  - Microsoft Office PIA  
  - WiX Toolset deployment  
  - Optional: Newtonsoft.Json / System.Text.Json  
- **SaaS API Endpoint:** https://www.quantboost.ai/api/v1/

---

## 3. Architectural Overview

```mermaid
flowchart TD
    Ribbon["Ribbon & Shortcut (Ctrl+Shift+[)"]
    Controller["TraceController"]
    Tracer["DependencyTracer (Async)"]
    Pane["Task Pane UI (WPF/WinForms)"]
    SDK["Licensing SDK"]
    API["Quant Boost Licensing API"]

    Ribbon --> Controller
    Controller --> Tracer
    Tracer --> ExcelCOM["Excel COM API"]

    Controller --> Pane
    SDK --> API
    Controller --> SDK
    SDK --> Cache["Local Cached Status + Grace Logic"]
    Controller -->|Listen for events| Pane
```

- Plugins **invoke SDK** for all licensing acts — no raw API calls elsewhere.  
- License SDK **handles machine ID, caching, grace period, retries, errors**.  
- Licensing **status events** drive plugin UI, feature gating & toasts.  
- Async **trace execution** prevents UI freezing.  
- SaaS API status controls **feature access, upgrade prompt, expired handling.**

---

## 4. Core Features Specification

### 4.1 Licensing & Activation

- Plugin initializes **QuantBoostLicensingManager** on load with ProductId `"QuantBoost-suite"`  
- Call `ValidateUsingStoredKeyAsync()` **async on startup**.  
- Subscribe to `LicenseStatusChanged` event for live updates.  
- Support **License states:**  
  - **Active / TrialActive:** Full features  
  - **GracePeriod:** Features work, warning + countdown  
  - **Expired / Limit Reached / Invalid:** Trace features disabled, upgrade prompts shown  
- UI prompts for license key via SDK dialog, upgrade links  
- Leverages SDK **local caching, gracing, device ID hash**  
- Licensing does **not** block plugin load, API calls happen in background

---

### 4.2 Dependency Tracing Core

- Scans dependents of `ActiveCell` by:  
  - Calling `.Dependents` on cell `Range`  
  - Handling multi-area, multi-sheet cases  
  - Catching COM exceptions (broken links, circular references) gracefully  
- Creates list of:  
  - Sheet & address  
  - Formula preview snippet (optional)  
  - Cell type / data sample (optional future)  
- Runs **async** via `Task.Run()` or similar, supports cancellation  
- Exposes results to UI for display, selection, navigation
- Works in unsaved or protected workbooks; error reporting adjusted accordingly

---

### 4.3 Ribbon, Hotkey & Feature Gating

- Default shortcut: **`Ctrl+Shift+[`** registers only if permitted by license, with SDK status gating  
- Ribbon button: **Excel Trace**  
  - Disabled/hides in expired/license-required states  
  - Context menu or right-click extender (future)  
- Upgrade/Renew action links shown dynamically as per SDK status  
- Hotkey registration **unregisters on license lost or expired**

---

### 4.4 UI (Task Pane)

- Modeless pane or dockable window  
- Async loading spinner, cancel button  
- Displays sorted list of:  
  - Sheet, address, formula preview  
- License state banner:  
  - Trial days left or grace countdown  
  - Expired / upgrade CTA  
- Toast notifications (non-modal) on licensing events, errors  
- Export dependents list to CSV (optional, premium feature gating possible)  
- Copy to clipboard, optional search/filter (roadmap)  

---

### 4.5 Subscription & Feature Management

- SDK fully controls **license caching, grace fallback, activation prompts**  
- Ribbon/pane adaptive to license state:  
  - **TrialActive / Active**: full enable  
  - **GracePeriod**: warn gracefully, enable all  
  - **Limit Reached / Expired / Invalid**: disable tracing, show CTA  
  - **No License**: show activation prompt on use  
- Upgrade & manage-subscription links deep-link to Quant Boost portal  
- Licensing transitions handled with **events + UI updates in realtime**

---

## 5. Data Types

### Licensing (from SDK)

```csharp
public enum LicenseStatusClient
{
    Unknown, Active, TrialActive, Expired, TrialExpired,
    InvalidKey, ProductMismatch, ActivationLimitReached,
    SubscriptionInactive, GracePeriod, ActivationRequired
}
```

### Dependency Info

```csharp
public class DependentInfo
{
    public string SheetName { get; set; }
    public string CellAddress { get; set; }  // A1 notation
    public string FormulaPreview { get; set; }    // optional
    public string FullAddress => $"'{SheetName}'!{CellAddress}";
}
```

---

## 6. Security

- All traffic via **HTTPS TLS** to API  
- MachineId via SDK: stable **SHA-256 hashed + salted** identifiers  
- Local keys stored **obfuscated & separately per product**  
- No sheet or formula content sent to server, only license info  
- No PII ever stored or transmitted beyond license key  
- MSI & DLL **code signed**  

---

## 7. Error Handling & Offline Support

- On API/network error SDK returns cached+grace status  
- Plugin disables features only when grace expired  
- Errors surfaced via **non-blocking toasts + pane banners**, never modal blocks  
- Logs non-PII errors/events locally (opt-in telemetry optional)  
- Never blocks Excel startup or UI regardless of license server state

---

## 8. Performance

- SDK calls fully async / background  
- Trace runs on background task, keeps UI snappy  
- Hotkey registration rapid & resilient  
- Memory footprint minimal, no worker threads unless needed  
- All COM objects released promptly  
- Configurable timeouts and retry windows (SDK internal)

---

## 9. Testing Matrix

- Licensing states (Active, Trial, Expired, Grace, Limit Reached, Invalid Key/no key)  
- Multi-workbook/multisheet dependents  
- Large sheet w/ many dependents  
- Hotkey conflicts / registration failure  
- Protected + read-only workbooks  
- Network down / offline scenarios  
- Activation, upgrade, renew flows  
- Performance traces under load  
- Uninstall/reinstall flows, registry cleanup

---

## 10. Deployment

- MSI signed installer via WiX, includes SDK DLL  
- Updatable via manifest file hosted on quantboost.ai  
- No admin rights *required* for basic user install  
- Future: update channel / auto-update support  
- Telemetry opt-in toggle defaults to off

---

## 11. Future Extensions

- User hotkey customization in options pane  
- Additional formula detail visualization  
- Multi-level dependent analysis  
- Cross-workbook tracing  
- Admin seat/activation management  
- Telemetry dashboards  
- Multi-language UI packs

---

## Summary

This enhanced specification delivers a **robust, user-friendly, enterprise-ready Excel tracing add-in**, leveraging a shared Licensing SDK to enable SaaS monetization, deep feature gating, grace period resilience, upgrade prompts, and smooth error handling.

---

**End of Quant Boost Excel Trace Add-in Technical Specification v3.0**