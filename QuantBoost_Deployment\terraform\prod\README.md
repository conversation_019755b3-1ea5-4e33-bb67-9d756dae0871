# QuantBoost Deployment (Production)

This folder contains IaC for production deployment of QuantBoost infrastructure. It consolidates Terraform under a clear top-level path in the monorepo and prepares us for secure artifact distribution and code signing.

Highlights
- Azure Trusted Signing (managed code signing) resources
- Azure Container Apps for the API
- Azure Key Vault for secrets with Container Apps secret references
- Azure Front Door Standard as CDN in front of Azure Storage (installer hosting)

Repository placement
- Keep WiX installer project in `QuantBoost.WixInstaller/`. Do not move WiX into this folder.
- This folder is for deployment assets: Terraform, tfvars, scripts, and runbooks.

State management
- Use a remote backend (Azure Storage) for Terraform state in production. Example backend config is not included here—configure via `-backend-config` or `backend` block as fits your org policy.

Quick start (prod)
1) Ensure AZ CLI login and correct subscription.
2) API stack is optional. By default `deploy_api = false` so your existing API RG is not touched.
3) Manage secrets manually in Key Vault via Azure Portal (Terraform won’t create secrets).
4) Initialize, plan, and apply.

Variables
- See `variables.tf` for full list.

prod.auto.tfvars.example

environment = "prod"
api_location = "West US 3"
distribution_location = "East US"
custom_domain_host_name = "download.quantboost.ai"
deploy_api = false

## Secrets
# Secrets are managed manually in Key Vault when/if API is deployed via Terraform.
# If you later set deploy_api = true, configure data sources for KV secrets in Terraform or inject via CI.

Notes and next steps
- Front Door -> Storage: consider Private Link origin and disabling public blob access. WAF policy is recommended.
- Container Registry: consider disabling ACR admin and granting `AcrPull` to Container App Managed Identity; adjust the Container App registry block accordingly.
- Trusted Signing: Complete organization identity validation in the Azure Portal before using in CI.