QuantBoost PowerPoint Add-in Development

**Project:** QuantBoost_Powerpoint_Addin (VSTO PowerPoint Add-in)
**Goal:** Guide AI code generation/suggestions to adhere strictly to project constraints and best practices for production readiness.

---

## 1. Core Technical Constraints (MANDATORY)

*   **Target Framework:** **MUST** target **.NET Framework 4.8.1**. Do **NOT** use APIs or features exclusive to later .NET versions (.NET Core, .NET 5+).
*   **Language Version:** **MUST** use **C# 7.3**. Do **NOT** use C# 8.0+ features (e.g., `record`s, init-only setters, default interface methods, simplified `using` declarations, switch expressions, `await using`).
*   **OpenXML:** **MUST** use **DocumentFormat.OpenXml SDK v2.18.0**.
    *   **Strictly PROHIBITED:** Using any APIs from **OpenXML SDK v3.x** or `System.IO.Packaging` directly for OOXML manipulation.
    *   All OpenXML code must be compatible with v2.18.0 patterns (e.g., using `StringValue`, handling attributes correctly, understanding part relationships).
*   **UI Framework:** WinForms (Ribbon, Custom Task Panes, Standard Dialogs, Custom Toasts).

---

## 2. Key Architectural Components & Patterns

*   **`ThisAddIn.cs`:** Core add-in class. Handles `_Startup`/`_Shutdown`, licensing init (`QuantBoostLicensingManager`), Ribbon creation (`CreateRibbonExtensibilityObject`), global state (like `CancellationTokenSource`), background task tracking.
*   **`QuantBoostRibbon.cs` + `QuantBoostRibbon.xml`:** Implements `IRibbonExtensibility`.
    *   **CRITICAL:** The `QuantBoostRibbon` constructor **MUST BE MINIMAL**. **DO NOT** access `Globals.ThisAddIn` or perform complex initialization here due to VSTO load timing.
    *   Use the `OnLoad` callback to cache the `Office.IRibbonUI` object.
    *   Do NOT use the loadImage callback in XML if only using imageMso attributes for Ribbon controls; Office handles built-in icons automatically. The IRibbonUI object does NOT provide a reliable way to load icons for use in separate WinForms controls (like Task Panes).
    *   Use `getEnabled`/`getVisible` callbacks (defined in XML, implemented in C#) for dynamic UI state based on license or context. Trigger updates via the cached `IRibbonUI.Invalidate()`.
*   **`Utilities` Namespace:** Contains essential helpers:
    *   **`AsyncHelper.cs`:** MUST call `AsyncHelper.Initialize()` in `ThisAddIn_Startup`. Use `RunOnUIThread` for marshalling calls to the UI thread. Use `RunSafeAsync` or `FireAndNotify`/`FireAndLog` extensions for background tasks/`async void` handlers.
    *   If multiple AsyncHelper classes exist (e.g., in different project namespaces), ensure calls use the fully qualified name (e.g., QuantBoost_Powerpoint_Addin.Utilities.AsyncHelper) to avoid compilation errors (CS0104).
    *   **`ErrorHandlingService.cs`:** Centralized logging (`LogException`) and user notification (`HandleException`). **AVOID** `HandleException` (which shows MessageBox) during critical startup (constructors, `ThisAddIn_Startup`). Use `LogException` during startup.
    *   **`ToastNotifier.cs`:** Use for non-modal user notifications. Ensures execution on UI thread via `AsyncHelper`. Provides `CreateProgressReporter`.
    *   **COM Wrappers:** Assume existence of wrappers (like `PowerPointComWrapper`, `ExcelComWrapper`) that likely implement `IDisposable` for managing Office COM object lifetimes. Use `using` statements with wrappers where possible.
    *   Wrappers should implement IDisposable for their own cleanup. Consider adding public static ReleaseComObject(object obj) helper methods to wrappers for convenient and safe release of COM objects returned by wrapper methods but managed by the caller.
*   **`PptxFileParser.cs`:** Handles all OpenXML interactions using **v2.18.0** APIs. Focus on robustness and detailed error reporting via its result object (`PptxFileAnalysisResult.AnalysisErrors`).
*   **Lazy Loading:** Services or components that depend on `Globals.ThisAddIn` (like `ExcelLinkService` initialized in the Ribbon) should use lazy loading (e.g., via a private property getter) to ensure `ThisAddIn` is ready when they are first accessed.
*   **Task Panes (`AnalyzePane`, `LinkManagerPane`):** Standard `UserControl` derived panes added via `Globals.ThisAddIn.CustomTaskPanes.Add()`. Constructors should be lightweight. Data loading/refresh should occur when the pane becomes visible or via user action.
*   Load custom icons for controls within Task Panes (e.g., ToolStripButton images in LinkManagerPane) from project resources (Properties.Resources), not via Ribbon callbacks or GetImageMso helpers.

---

## 3. Coding Standards & Best Practices

*   **Error Handling:** Use `try-catch` extensively. Catch specific exceptions where appropriate. Use `ErrorHandlingService` for logging/reporting.
*   **Asynchronous Programming:** Use `async`/`await` for I/O bound or long-running operations (OpenXML parsing, API calls, Excel automation). Use `ConfigureAwait(false)` in library-like code or background tasks unless UI context *must* be restored immediately after the `await`. Use `AsyncHelper`'s methods for safe execution and UI marshalling. **Avoid `async void`** except for top-level event handlers; use helper extensions like `FireAndNotify` instead.
*   **COM Interop:**
    *   Minimize direct COM interaction where wrappers exist.
    *   If direct interaction is needed, **release COM objects explicitly and deterministically**. Use `Marshal.ReleaseComObject` or `Marshal.FinalReleaseComObject` in `finally` blocks or via `using` statements with `IDisposable` wrappers. Be mindful of the "two-dot" rule (e.g., don't chain `excelApp.ActiveWorkbook.ActiveSheet.Select()` without releasing intermediate objects).
    *   Avoid holding references to COM objects longer than necessary.
*   **Threading:** All UI updates **MUST** occur on the UI thread. Use `AsyncHelper.RunOnUIThread` to marshal calls from background threads. Be mindful of potential deadlocks if using `SynchronizationContext.Send` (prefer `Post` as used in `AsyncHelper`).
*   **Null Checks:** Perform defensive null checks, especially for Office objects, `Globals.ThisAddIn`, and results from service calls. Use null-conditional (`?.`) and null-coalescing (`??`) operators where appropriate (C# 7.x compatible).
*   **Naming & Comments:** Use clear, descriptive names. Comment complex logic, assumptions, or workarounds.
*   **Production Readiness:** Code should be robust against common issues (file not found, network errors, unexpected Office states, corrupted documents, permission errors).
*   **Regions:** use #region "description" and #endregion to break up code blocks into managable chunks for easy auditability.
*   **Comments:** use descriptive comments and break code blocks down into laymens terms that are easy to understand 

---

## 4. Forbidden Practices

*   **DO NOT** use any C# 8.0+ language features.
*   **DO NOT** use any .NET Core / .NET 5+ specific APIs.
*   **DO NOT** use OpenXML SDK v3.x APIs or `System.IO.Packaging` directly.
*   **DO NOT** access `Globals.ThisAddIn` inside the `QuantBoostRibbon` constructor.
*   **DO NOT** perform long-running or blocking operations on the UI thread.
*   **DO NOT** leak COM objects; ensure they are released.
*   **DO NOT** ignore exceptions; handle or log them appropriately using `ErrorHandlingService`.
*   **DO NOT** update UI elements directly from background threads.
