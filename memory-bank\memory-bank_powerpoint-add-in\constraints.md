---
title: Technical Constraints for PowerPoint Add-in
purpose: Lists implicit and explicit technical constraints for the QuantBoost PowerPoint Add-in.
projects: ["powerpoint-add-in"]
source_analysis: "Codebase analysis of QuantBoost_PPTX (powerpoint-add-in)."
status: bootstrapped-incomplete
last_updated: 2025-05-13T14:15:00Z
tags: ["powerpoint-add-in", "constraints", "dependencies", "technical_debt"]
---

## Technical Constraints and Assumptions

### Dependency Constraints
*   **.NET Framework Version:** Targets .NET Framework 4.8.1. This limits the use of newer .NET features and libraries.
*   **Microsoft Office Version:** Requires a compatible version of Microsoft PowerPoint (and Excel for linking features) to be installed on the user's machine. Specific version compatibility (e.g., Office 2016, Microsoft 365) should be documented.
*   **`DocumentFormat.OpenXml`:** Locked to version 2.18.0. Upgrading might require code changes if APIs have been altered.
*   **`Newtonsoft.Json`:** Locked to version 13.0.3.
*   **`QuantBoost.Licensing.dll`:** The add-in is dependent on this specific assembly for licensing. Its availability and compatibility are crucial.

### Language and Development Constraints
*   **C# Language Version:** C# 7.3 global constraint is enforced.
*   **VSTO Development Model:** Bound by the VSTO architecture, including its threading model (primarily single-threaded UI) and event handling.

### Data Format and Storage Assumptions
*   **ExcelLinkMetadata:** Relies on a specific custom XML structure embedded within PowerPoint files for storing Excel link information. Changes to this structure would require migration or break compatibility.
*   **OpenXML Structure:** `PptxFileParser.cs` likely makes assumptions about the structure of PowerPoint files it analyzes.

### Performance and Resource Considerations
*   **COM Interop:** Interactions with Excel via COM can be performance-sensitive and resource-intensive if not managed carefully (e.g., object release, avoiding unnecessary calls).
*   **Large Presentations/Excel Files:** Performance might degrade when handling very large PowerPoint presentations or linking to extensive Excel workbooks.

### Deployment and Environment
*   **Windows-Specific:** VSTO add-ins are typically Windows-only.
*   **Permissions:** May require specific permissions for installation or for COM interop to function correctly.
