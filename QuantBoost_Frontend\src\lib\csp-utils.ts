/**
 * Client-side utility to get the CSP nonce for dynamic script insertion
 * This is useful when you need to add scripts dynamically on the client side
 */
export function getCSPNonce(): string {
  if (typeof window !== 'undefined') {
    return (window as any).__CSP_NONCE__ || '';
  }
  return '';
}

/**
 * Helper function to create a script element with the proper nonce
 * @param src - The source URL of the script
 * @param attributes - Additional attributes to set on the script element
 * @returns The created script element
 */
export function createScriptWithNonce(
  src: string, 
  attributes: { [key: string]: string } = {}
): HTMLScriptElement {
  const script = document.createElement('script');
  script.src = src;
  script.nonce = getCSPNonce();
  
  // Apply additional attributes
  Object.entries(attributes).forEach(([key, value]) => {
    script.setAttribute(key, value);
  });
  
  return script;
}

/**
 * Helper function to execute inline JavaScript with the proper nonce
 * @param code - The JavaScript code to execute
 * @param attributes - Additional attributes to set on the script element
 */
export function executeInlineScriptWithNonce(
  code: string,
  attributes: { [key: string]: string } = {}
): void {
  const script = document.createElement('script');
  script.nonce = getCSPNonce();
  script.textContent = code;
  
  // Apply additional attributes
  Object.entries(attributes).forEach(([key, value]) => {
    script.setAttribute(key, value);
  });
  
  document.head.appendChild(script);
  document.head.removeChild(script); // Clean up after execution
}
