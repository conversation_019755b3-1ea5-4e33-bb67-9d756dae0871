import { z } from 'zod';

export const createTrialSchema = z.object({
  company: z.string().min(2).max(200),
  contactEmail: z.string().email().max(254),
  domain: z.string().trim().max(200).optional().or(z.literal('')),
  seatLimit: z.number().int().min(1).max(500).optional(),
});

export type CreateTrialInput = z.infer<typeof createTrialSchema>;

export const bulkInviteSchema = z.object({
  emails: z.array(z.string().email()).optional(),
  csv: z.string().optional(),
}).refine((d) => (d.emails && d.emails.length > 0) || (d.csv && d.csv.trim().length > 0), {
  message: 'Provide emails[] or csv',
});

export type BulkInviteInput = z.infer<typeof bulkInviteSchema>;

export const inviteAcceptSchema = z.object({
  token: z.string().min(10),
});

export const convertSchema = z.object({
  priceId: z.string().min(5),
  quantity: z.number().int().min(1).max(500).optional(),
  successUrl: z.string().url().optional(),
  cancelUrl: z.string().url().optional(),
});

export type ConvertInput = z.infer<typeof convertSchema>;

export function extractEmailsFromCsv(csv: string): string[] {
  return csv
    .split(/[\n,;\t]/g)
    .map((s) => s.trim())
    .filter((s) => s.length > 0);
}
