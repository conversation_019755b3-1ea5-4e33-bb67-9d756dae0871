﻿<Project ToolsVersion="17.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <!--
    This section defines project-level properties.

    AssemblyName
      Name of the output assembly.
    Configuration
      Specifies a default value for debug.
    OutputType
      Must be "Library" for VSTO.
    Platform
      Specifies what CPU the output of this project can run on.
    NoStandardLibraries
      Set to "false" for VSTO.
    RootNamespace
      In C#, this specifies the namespace given to new files. In VB, all objects are
      wrapped in this namespace at runtime.
  -->
  <PropertyGroup>
    <ProjectTypeGuids>{BAA0C2D2-18E2-41B9-852F-F413020CAA33};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{DF2FFE8E-0C17-4D53-8A68-92DDA729D54D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <NoStandardLibraries>false</NoStandardLibraries>
    <RootNamespace>QuantBoost_Powerpoint_Addin</RootNamespace>
    <AssemblyName>QuantBoost_Powerpoint_Addin</AssemblyName>
    <LoadBehavior>3</LoadBehavior>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <DefineConstants>VSTO40;UseOfficeInterop</DefineConstants>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <BootstrapperComponentsLocation>HomeSite</BootstrapperComponentsLocation>
    <ResolveComReferenceSilent>true</ResolveComReferenceSilent>
  </PropertyGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.VSTORuntime.4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft Visual Studio 2010 Tools for Office Runtime %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <PropertyGroup>
    <!--
      OfficeApplication
        Add-in host application
    -->
    <OfficeApplication>PowerPoint</OfficeApplication>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <LangVersion>8.0</LangVersion>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Debug" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);DEBUG;TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Release" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <!--
    This section specifies references for the project.
  -->
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="DocumentFormat.OpenXml, Version=2.18.0.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>packages\DocumentFormat.OpenXml.2.18.0\lib\net46\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Office.Interop.Excel.15.0.4795.1001\lib\net20\Microsoft.Office.Interop.Excel.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json.Bson, Version=1.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.Bson.1.0.3\lib\net45\Newtonsoft.Json.Bson.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="WindowsBase" />
    <!-- WPF References for ExcelLink WPF UI -->
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System.Xaml" />
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.v4.0.Framework, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.Tools.Applications.Runtime, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Common, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.Common.v4.0.Utilities, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <Choose>
    <When Condition="$([System.String]::Copy(&quot;;$(DefineConstants);&quot;).ToLower().Contains(';useofficeinterop;')) or $([System.String]::Copy(&quot;,$(DefineConstants),&quot;).ToLower().Contains(',useofficeinterop,'))">
      <ItemGroup>
        <Reference Include="Office, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
          <Private>False</Private>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </Reference>
        <Reference Include="Microsoft.Office.Interop.PowerPoint, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
          <Private>False</Private>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </Reference>
      </ItemGroup>
    </When>
    <Otherwise>
      <ItemGroup>
        <COMReference Include="Microsoft.Office.Core">
          <Guid>{2DF8D04C-5BFA-101B-BDE5-00AA0044DE52}</Guid>
          <VersionMajor>2</VersionMajor>
          <VersionMinor>7</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
        <COMReference Include="Microsoft.Office.Interop.PowerPoint">
          <Guid>{91493440-5A91-11CF-8700-00AA0060263B}</Guid>
          <VersionMajor>2</VersionMajor>
          <VersionMinor>11</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
      </ItemGroup>
    </Otherwise>
  </Choose>
  <ItemGroup>
    <Reference Include="stdole, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <!--
    This section defines the user source files that are part of the project.
     
    A "Compile" element specifies a source file to compile.
    An "EmbeddedResource" element specifies an .resx file for embedded resources.
    A "None" element specifies a file that is not to be passed to the compiler (for instance, 
    a text file or XML file).
    The "AppDesigner" element specifies the directory where the application properties files
    can be found.
  -->
  <ItemGroup>
    <Compile Include="ExcelLink\ClipboardManager.cs" />
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="ThisAddIn.cs">
      <SubType>Code</SubType>
    </Compile>
    <None Include="QuantBoost_Powerpoint_Addin_TemporaryKey.pfx" />
    <None Include="ThisAddIn.Designer.xml">
      <DependentUpon>ThisAddIn.cs</DependentUpon>
    </None>
    <Compile Include="ThisAddIn.Designer.cs">
      <DependentUpon>ThisAddIn.Designer.xml</DependentUpon>
    </Compile>
    <AppDesigner Include="Properties\" />
    <!-- Include all UI source files -->
    <Compile Include="UI\AnalyzePane.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <!-- Legacy LinkManagerPane.cs - REMOVED (replaced by WPF implementation) -->
    <Compile Include="UI\QuantBoostRibbon.cs" />
    <Compile Include="UI\SelectChartDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <!-- Include all Utilities source files -->
    <Compile Include="Utilities\CustomExceptions.cs" />
    <Compile Include="Utilities\FormattingUtils.cs" />
    <!-- Add other Utilities/*.cs files as needed -->
    <!-- Include all ExcelLink source files -->
    <Compile Include="ExcelLink\ChartLink.cs" />
    <Compile Include="ExcelLink\ExcelComWrapper.cs" />
    <Compile Include="ExcelLink\ExcelLinkMetadata.cs" />
    <Compile Include="ExcelLink\ExcelLinkService.cs" />
    <Compile Include="ExcelLink\PowerPointComWrapper.cs" />
    <!-- Include new WPF ExcelLink UI components -->
    <Compile Include="Features\ExcelLink\Views\LinkManagerView.xaml.cs">
      <DependentUpon>LinkManagerView.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="Features\ExcelLink\Views\LinkManagerView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Compile Include="Features\ExcelLink\ViewModels\LinkManagerViewModel.cs" />
    <Compile Include="Features\ExcelLink\Models\LinkDisplayModel.cs" />
    <Compile Include="Features\ExcelLink\UI\WpfHostControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Features\ExcelLink\Converters\LinkTypeToIconConverter.cs" />
    <Compile Include="Features\ExcelLink\Converters\ErrorStateToVisibilityConverter.cs" />
    <!-- Include all SizeAnalyzer source files -->
    <Compile Include="Features\SizeAnalyzer\UI\WpfHostControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Features\SizeAnalyzer\ViewModels\AnalysisPaneViewModel.cs" />
    <Compile Include="Features\SizeAnalyzer\Models\SlideAnalysisDisplayItem.cs" />
    <Compile Include="Features\SizeAnalyzer\Mvvm\RelayCommand.cs" />
    <Compile Include="Features\SizeAnalyzer\Views\AnalysisPaneView.xaml.cs">
      <DependentUpon>AnalysisPaneView.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="Features\SizeAnalyzer\Views\AnalysisPaneView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <!-- Include all Analysis source files -->
    <Compile Include="Analysis\AnalysisExtensions.cs" />
    <Compile Include="Analysis\AnalysisService.cs" />
    <Compile Include="Analysis\AnalysisUsageDemo.cs" />
    <Compile Include="Analysis\OpenXmlStub.cs" />
    <Compile Include="Analysis\PptxFileParser.cs" />
    <!-- Include QuantBoost.Licensing source files -->
  </ItemGroup>
  <Import Project="..\QuantBoost_Shared\QuantBoost_Shared.projitems" Label="Shared" />
  <ItemGroup>
    <ProjectReference Include="..\QuantBoost_Licensing\QuantBoost_Licensing.csproj">
      <Project>{ec2d374b-a69b-4214-a1fc-7cef1ad5931c}</Project>
      <Name>QuantBoost_Licensing</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="UI\QuantBoostRibbon.xml" />
    <!-- Custom Icon Resources -->
    <EmbeddedResource Include="Resources\FileSizeAnalyzer_16x16.png" />
    <EmbeddedResource Include="Resources\FileSizeAnalyzer_24x24.png" />
    <EmbeddedResource Include="Resources\FileSizeAnalyzer_32x32.png" />
    <EmbeddedResource Include="Resources\FileSizeAnalyzer_40x40.png" />
    <EmbeddedResource Include="Resources\FileSizeAnalyzer_48x48.png" />
    <EmbeddedResource Include="Resources\FileSizeAnalyzer_64x64.png" />
    <EmbeddedResource Include="Resources\FileSizeAnalyzer_80x80.png" />
    <EmbeddedResource Include="Resources\FileSizeAnalyzer_96x96.png" />
    <EmbeddedResource Include="Resources\ExcelLink_16x16.PNG" />
    <EmbeddedResource Include="Resources\ExcelLink_24x24.PNG" />
    <EmbeddedResource Include="Resources\ExcelLink_32x32.PNG" />
    <EmbeddedResource Include="Resources\ExcelLink_40x40.PNG" />
    <EmbeddedResource Include="Resources\ExcelLink_48x48.PNG" />
    <EmbeddedResource Include="Resources\ExcelLink_64x64.PNG" />
    <EmbeddedResource Include="Resources\ExcelLink_80x80.PNG" />
    <EmbeddedResource Include="Resources\ExcelLink_96x96.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_Refresh_16x16.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_Refresh_24x24.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_Refresh_32x32.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_Refresh_40x40.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_Refresh_48x48.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_Refresh_64x64.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_Refresh_80x80.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_Refresh_96x96.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_RefreshAll_16x16.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_RefreshAll_24x24.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_RefreshAll_32x32.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_RefreshAll_40x40.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_RefreshAll_48x48.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_RefreshAll_64x64.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_RefreshAll_80x80.PNG" />
    <EmbeddedResource Include="Resources\Excel_Link_RefreshAll_96x96.PNG" />
    <EmbeddedResource Include="Resources\link_manager_16x16.PNG" />
    <EmbeddedResource Include="Resources\link_manager_24x24.PNG" />
    <EmbeddedResource Include="Resources\link_manager_32x32.PNG" />
    <EmbeddedResource Include="Resources\link_manager_40x40.PNG" />
    <EmbeddedResource Include="Resources\link_manager_48x48.PNG" />
    <EmbeddedResource Include="Resources\link_manager_64x64.PNG" />
    <EmbeddedResource Include="Resources\link_manager_80x80.PNG" />
    <EmbeddedResource Include="Resources\link_manager_96x96.PNG" />
    <EmbeddedResource Include="Resources\login_16x16.PNG" />
    <EmbeddedResource Include="Resources\login_24x24.PNG" />
    <EmbeddedResource Include="Resources\login_32x32.PNG" />
    <EmbeddedResource Include="Resources\login_40x40.PNG" />
    <EmbeddedResource Include="Resources\login_48x48.PNG" />
    <EmbeddedResource Include="Resources\login_64x64.PNG" />
    <EmbeddedResource Include="Resources\login_80x80.PNG" />
    <EmbeddedResource Include="Resources\login_96x96.PNG" />
    <EmbeddedResource Include="Resources\logout_16x16.PNG" />
    <EmbeddedResource Include="Resources\logout_32x32.PNG" />
    <EmbeddedResource Include="Resources\logout_40x40.PNG" />
    <EmbeddedResource Include="Resources\logout_48x48.PNG" />
    <EmbeddedResource Include="Resources\logout_64x64.PNG" />
    <EmbeddedResource Include="Resources\logout_80x80.PNG" />
    <EmbeddedResource Include="Resources\logout_96x96.PNG" />
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>QuantBoost_Powerpoint_Addin_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>FFFF3D78D779872F2FCD11D8175AB7F90C284810</ManifestCertificateThumbprint>
  </PropertyGroup>
  <!-- Include the build rules for a C# project. -->
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- Include additional build rules for an Office application add-in. -->
  <Import Project="$(VSToolsPath)\OfficeTools\Microsoft.VisualStudio.Tools.Office.targets" Condition="'$(VSToolsPath)' != ''" />
  <!-- This section defines VSTO properties that describe the host-changeable project properties. -->
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{BAA0C2D2-18E2-41B9-852F-F413020CAA33}">
        <ProjectProperties HostName="PowerPoint" HostPackage="{29A7B9D7-A7F1-4328-8EF0-6B2D1A56B2C1}" OfficeVersion="15.0" VstxVersion="4.0" ApplicationType="PowerPoint" Language="cs" TemplatesPath="" DebugInfoExeName="#Software\Microsoft\Office\16.0\PowerPoint\InstallRoot\Path#powerpnt.exe" AddItemTemplatesGuid="{51063C3A-E220-4D12-8922-BDA915ACD783}" />
        <Host Name="PowerPoint" GeneratedCodeNamespace="QuantBoost_Powerpoint_Addin" IconIndex="0">
          <HostItem Name="ThisAddIn" Code="ThisAddIn.cs" CanonicalName="AddIn" CanActivate="false" IconIndex="1" Blueprint="ThisAddIn.Designer.xml" GeneratedCode="ThisAddIn.Designer.cs" />
        </Host>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>