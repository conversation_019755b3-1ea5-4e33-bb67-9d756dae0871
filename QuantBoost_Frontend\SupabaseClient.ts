import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Create Supabase client safely for both build-time and runtime
function createSupabaseClient(): SupabaseClient {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // During build time, environment variables might not be available
  if (!supabaseUrl || !supabaseAnonKey) {
    // Use placeholder values for build time - this won't be used at runtime
    return createClient('https://placeholder.supabase.co', 'placeholder-key');
  }

  return createClient(supabaseUrl, supabaseAnonKey);
}

export const supabase = createSupabaseClient();