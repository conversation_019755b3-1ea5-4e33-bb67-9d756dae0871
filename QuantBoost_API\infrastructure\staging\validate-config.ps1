# Validate Terraform Configuration for App Service Migration
# Quick validation script to check the updated infrastructure

param(
    [Parameter(Mandatory=$false)]
    [switch]$Detailed = $false
)

$ErrorActionPreference = "Stop"

Write-Host "🔍 Validating QuantBoost App Service Terraform Configuration" -ForegroundColor Green
Write-Host "=============================================================" -ForegroundColor Green

# Change to the staging infrastructure directory
$stagingDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $stagingDir

Write-Host "📁 Working directory: $stagingDir" -ForegroundColor Cyan

# Check if Terraform is installed
try {
    $tfVersion = terraform version -json | ConvertFrom-Json
    Write-Host "✅ Terraform version: $($tfVersion.terraform_version)" -ForegroundColor Green
} catch {
    Write-Error "❌ Terraform is not installed or not in PATH"
    exit 1
}

# Initialize Terraform (if needed)
if (-not (Test-Path ".terraform")) {
    Write-Host "🔧 Initializing Terraform..." -ForegroundColor Yellow
    terraform init
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ Terraform initialization failed"
        exit 1
    }
}

# Validate configuration
Write-Host "✅ Validating Terraform configuration..." -ForegroundColor Yellow
terraform validate

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Terraform validation failed"
    exit 1
}

Write-Host "✅ Terraform configuration is valid!" -ForegroundColor Green

# Format check
Write-Host "🎨 Checking Terraform formatting..." -ForegroundColor Yellow
$formatResult = terraform fmt -check -diff

if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️  Terraform files need formatting. Run 'terraform fmt' to fix." -ForegroundColor Yellow
} else {
    Write-Host "✅ Terraform formatting is correct!" -ForegroundColor Green
}

# Plan (dry run)
Write-Host "📋 Running Terraform plan (dry run)..." -ForegroundColor Yellow
terraform plan -out=validation-plan

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Terraform plan failed"
    exit 1
}

Write-Host "✅ Terraform plan completed successfully!" -ForegroundColor Green

# Show what will be created/modified
if ($Detailed) {
    Write-Host ""
    Write-Host "📋 Detailed Plan Output:" -ForegroundColor Cyan
    terraform show validation-plan
}

# Clean up plan file
Remove-Item validation-plan -ErrorAction SilentlyContinue

# Check for required variables
Write-Host ""
Write-Host "🔍 Checking required variables..." -ForegroundColor Yellow

$requiredVars = @(
    "supabase_url",
    "supabase_anon_key", 
    "supabase_service_role_key",
    "stripe_publishable_key",
    "stripe_secret_key",
    "stripe_webhook_signing_secret",
    "jwt_secret"
)

$tfvarsFile = "terraform.tfvars"
if (Test-Path $tfvarsFile) {
    $tfvarsContent = Get-Content $tfvarsFile -Raw
    
    foreach ($var in $requiredVars) {
        if ($tfvarsContent -match "$var\s*=") {
            Write-Host "✅ $var is configured" -ForegroundColor Green
        } else {
            Write-Host "❌ $var is missing from terraform.tfvars" -ForegroundColor Red
        }
    }
} else {
    Write-Host "⚠️  terraform.tfvars file not found" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 Key Infrastructure Changes:" -ForegroundColor Cyan
Write-Host "   ✅ App Service Plan (B1 Basic) - NEW" -ForegroundColor Green
Write-Host "   ✅ App Service (Node.js 18) - NEW" -ForegroundColor Green
Write-Host "   ✅ Key Vault access for App Service - NEW" -ForegroundColor Green
Write-Host "   ✅ Environment variables configured - UPDATED" -ForegroundColor Green
Write-Host "   ⚠️  Static Web Apps - TO BE REMOVED AFTER TESTING" -ForegroundColor Yellow

Write-Host ""
Write-Host "💰 Cost Impact:" -ForegroundColor Cyan
Write-Host "   App Service B1: ~$13.14/month" -ForegroundColor White
Write-Host "   Application Insights: ~$2.30/month" -ForegroundColor White
Write-Host "   Total Additional: ~$4.14/month vs Static Web Apps" -ForegroundColor White

Write-Host ""
Write-Host "🚀 Next Steps:" -ForegroundColor Green
Write-Host "1. Run: terraform apply" -ForegroundColor White
Write-Host "2. Update GitHub Actions workflow" -ForegroundColor White
Write-Host "3. Deploy Next.js app to App Service" -ForegroundColor White
Write-Host "4. Test payment processing" -ForegroundColor White
Write-Host "5. Remove Static Web Apps after confirmation" -ForegroundColor White

Write-Host ""
Write-Host "✅ Configuration validation completed successfully!" -ForegroundColor Green
