using System.Security.Cryptography;
using System.Text;
using System.IO;
using System; // For Environment.SpecialFolder
using QuantBoost_Shared.Utilities; // For ErrorHandlingService
using System.Diagnostics; // For Debug logging
using System.Security.Cryptography; // For ProtectedData and DataProtectionScope

namespace QuantBoost_Shared.Security // Correct namespace now matches folder structure
{
    /// <summary>
    /// Provides secure storage for refresh tokens using Windows DPAPI (Data Protection API).
    /// Tokens are encrypted and stored in the user's local application data directory.
    /// </summary>
    public static class TokenStorage
    {
        // Optional: A salt or "entropy" can be added for extra security,
        // but must be the same for encryption and decryption.
        // If used, this salt itself needs to be stored or derived consistently.
        // For simplicity, we'll omit it here, but consider it for higher security.
        // private static readonly byte[] s_entropy = Encoding.UTF8.GetBytes("YourUniqueEntropyString");

        private const string TOKEN_FILENAME = "qb_refresh_token.dat";
        private const string APP_FOLDER_NAME = "QuantBoost";

        /// <summary>
        /// Gets the full path to the token storage file.
        /// Creates the directory if it doesn't exist.
        /// </summary>
        /// <returns>Full path to the token file</returns>
        private static string GetTokenFilePath()
        {
            try
            {
                // Store in a user-specific, application-specific folder
                string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                string addinFolder = Path.Combine(appDataPath, APP_FOLDER_NAME);
                
                // Ensure the folder exists
                if (!Directory.Exists(addinFolder))
                {
                    Directory.CreateDirectory(addinFolder);
                    Debug.WriteLine($"[TokenStorage] Created directory: {addinFolder}");
                }
                
                string tokenPath = Path.Combine(addinFolder, TOKEN_FILENAME);
                Debug.WriteLine($"[TokenStorage] Token file path: {tokenPath}");
                return tokenPath;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Failed to determine token file path");
                throw; // Re-throw as this is a critical error
            }
        }

        /// <summary>
        /// Securely stores a refresh token using Windows DPAPI encryption.
        /// </summary>
        /// <param name="refreshToken">The refresh token to store</param>
        /// <returns>True if storage succeeded, false otherwise</returns>
        public static bool StoreRefreshToken(string refreshToken)
        {
            if (string.IsNullOrEmpty(refreshToken))
            {
                Debug.WriteLine("[TokenStorage] StoreRefreshToken: Null or empty token provided");
                return false;
            }

            try
            {
                Debug.WriteLine($"[TokenStorage] Storing refresh token (length: {refreshToken.Length})");
                
                byte[] tokenBytes = Encoding.UTF8.GetBytes(refreshToken);
                byte[] encryptedTokenBytes = ProtectedData.Protect(
                    tokenBytes,
                    null, // Optional entropy
                    DataProtectionScope.CurrentUser); // Encrypt for current user

                string tokenFilePath = GetTokenFilePath();
                File.WriteAllBytes(tokenFilePath, encryptedTokenBytes);
                
                Debug.WriteLine($"[TokenStorage] Successfully stored encrypted token to: {tokenFilePath}");
                ErrorHandlingService.LogException(null, "Refresh token stored successfully"); // Info log
                return true;
            }
            catch (UnauthorizedAccessException uaEx)
            {
                ErrorHandlingService.LogException(uaEx, "Access denied when storing refresh token - check folder permissions");
                return false;
            }
            catch (CryptographicException cryptoEx)
            {
                ErrorHandlingService.LogException(cryptoEx, "Cryptographic error when encrypting refresh token");
                return false;
            }
            catch (IOException ioEx)
            {
                ErrorHandlingService.LogException(ioEx, "IO error when writing refresh token file");
                return false;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Unexpected error when storing refresh token securely");
                return false;
            }
        }

        /// <summary>
        /// Retrieves and decrypts a stored refresh token.
        /// </summary>
        /// <returns>The refresh token if successfully retrieved, null otherwise</returns>
        public static string RetrieveRefreshToken()
        {
            try
            {
                string tokenFilePath = GetTokenFilePath();
                
                if (!File.Exists(tokenFilePath))
                {
                    Debug.WriteLine($"[TokenStorage] No token file found at: {tokenFilePath}");
                    return null;
                }

                Debug.WriteLine($"[TokenStorage] Retrieving token from: {tokenFilePath}");
                
                byte[] encryptedTokenBytes = File.ReadAllBytes(tokenFilePath);
                
                if (encryptedTokenBytes.Length == 0)
                {
                    Debug.WriteLine("[TokenStorage] Token file is empty");
                    ErrorHandlingService.LogException(null, "Token file exists but is empty - removing corrupted file");
                    ClearRefreshToken();
                    return null;
                }

                byte[] tokenBytes = ProtectedData.Unprotect(
                    encryptedTokenBytes,
                    null, // Optional entropy
                    DataProtectionScope.CurrentUser); // Decrypt for current user

                string token = Encoding.UTF8.GetString(tokenBytes);
                Debug.WriteLine($"[TokenStorage] Successfully retrieved token (length: {token?.Length ?? 0})");
                
                // Basic validation
                if (string.IsNullOrWhiteSpace(token))
                {
                    Debug.WriteLine("[TokenStorage] Retrieved token is null or whitespace");
                    ErrorHandlingService.LogException(null, "Retrieved token is null or empty - removing corrupted file");
                    ClearRefreshToken();
                    return null;
                }

                ErrorHandlingService.LogException(null, "Refresh token retrieved successfully"); // Info log
                return token;
            }
            catch (FileNotFoundException)
            {
                Debug.WriteLine("[TokenStorage] Token file not found");
                return null;
            }
            catch (UnauthorizedAccessException uaEx)
            {
                ErrorHandlingService.LogException(uaEx, "Access denied when reading refresh token - check folder permissions");
                return null;
            }
            catch (CryptographicException cryptoEx)
            {
                // This can happen if the token is corrupted, or if decryption fails (e.g., user profile issue)
                ErrorHandlingService.LogException(cryptoEx, "Failed to decrypt refresh token. Token might be corrupt or inaccessible - clearing file");
                ClearRefreshToken(); // Corrupted token, clear it
                return null;
            }
            catch (IOException ioEx)
            {
                ErrorHandlingService.LogException(ioEx, "IO error when reading refresh token file");
                return null;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Unexpected error when retrieving refresh token");
                return null;
            }
        }

        /// <summary>
        /// Removes the stored refresh token file.
        /// </summary>
        /// <returns>True if clearing succeeded or file didn't exist, false if an error occurred</returns>
        public static bool ClearRefreshToken()
        {
            try
            {
                string tokenFilePath = GetTokenFilePath();
                
                if (File.Exists(tokenFilePath))
                {
                    Debug.WriteLine($"[TokenStorage] Clearing token file: {tokenFilePath}");
                    File.Delete(tokenFilePath);
                    ErrorHandlingService.LogException(null, "Refresh token cleared successfully"); // Info log
                }
                else
                {
                    Debug.WriteLine($"[TokenStorage] Token file doesn't exist, nothing to clear: {tokenFilePath}");
                }
                
                return true;
            }
            catch (UnauthorizedAccessException uaEx)
            {
                ErrorHandlingService.LogException(uaEx, "Access denied when clearing refresh token");
                return false;
            }
            catch (IOException ioEx)
            {
                ErrorHandlingService.LogException(ioEx, "IO error when deleting refresh token file");
                return false;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Unexpected error when clearing refresh token");
                return false;
            }
        }

        /// <summary>
        /// Checks if a refresh token is currently stored.
        /// </summary>
        /// <returns>True if a token file exists, false otherwise</returns>
        public static bool HasStoredToken()
        {
            try
            {
                string tokenFilePath = GetTokenFilePath();
                bool exists = File.Exists(tokenFilePath);
                Debug.WriteLine($"[TokenStorage] Token file exists: {exists}");
                return exists;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error checking if refresh token exists");
                return false;
            }
        }

        /// <summary>
        /// Gets diagnostic information about the token storage.
        /// Useful for debugging authentication issues.
        /// </summary>
        /// <returns>Diagnostic information as a string</returns>
        public static string GetDiagnosticInfo()
        {
            try
            {
                var sb = new StringBuilder();
                sb.AppendLine("=== TokenStorage Diagnostic Information ===");
                
                string tokenFilePath = GetTokenFilePath();
                sb.AppendLine($"Token file path: {tokenFilePath}");
                
                bool fileExists = File.Exists(tokenFilePath);
                sb.AppendLine($"Token file exists: {fileExists}");
                
                if (fileExists)
                {
                    var fileInfo = new FileInfo(tokenFilePath);
                    sb.AppendLine($"File size: {fileInfo.Length} bytes");
                    sb.AppendLine($"Created: {fileInfo.CreationTime}");
                    sb.AppendLine($"Modified: {fileInfo.LastWriteTime}");
                    sb.AppendLine($"File attributes: {fileInfo.Attributes}");
                }
                
                sb.AppendLine($"Current user: {Environment.UserName}");
                sb.AppendLine($"Local app data: {Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData)}");
                
                return sb.ToString();
            }
            catch (Exception ex)
            {
                return $"Error generating diagnostic info: {ex.Message}";
            }
        }

        /// <summary>
        /// Validates that the DPAPI system is working correctly.
        /// </summary>
        /// <returns>True if DPAPI is functional, false otherwise</returns>
        public static bool ValidateDPAPI()
        {
            try
            {
                Debug.WriteLine("[TokenStorage] Testing DPAPI functionality");
                
                // Test encryption/decryption with a test string
                string testData = "QuantBoost-DPAPI-Test-" + DateTime.UtcNow.Ticks;
                byte[] testBytes = Encoding.UTF8.GetBytes(testData);
                
                // Encrypt
                byte[] encryptedBytes = ProtectedData.Protect(
                    testBytes, 
                    null, 
                    DataProtectionScope.CurrentUser);
                
                // Decrypt
                byte[] decryptedBytes = ProtectedData.Unprotect(
                    encryptedBytes, 
                    null, 
                    DataProtectionScope.CurrentUser);
                
                string decryptedData = Encoding.UTF8.GetString(decryptedBytes);
                
                bool isValid = testData == decryptedData;
                Debug.WriteLine($"[TokenStorage] DPAPI validation result: {(isValid ? "PASSED" : "FAILED")}");
                
                if (isValid)
                {
                    ErrorHandlingService.LogException(null, "DPAPI validation successful");
                }
                else
                {
                    ErrorHandlingService.LogException(null, "DPAPI validation failed - encryption/decryption mismatch");
                }
                
                return isValid;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "DPAPI validation failed with exception");
                return false;
            }
        }
    }
}