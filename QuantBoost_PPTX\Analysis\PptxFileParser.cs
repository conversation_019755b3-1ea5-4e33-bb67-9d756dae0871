using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging; // For OpenXmlPackageException, SlidePart, ChartPart, ImagePart, MediaDataPart, CustomXmlPart, EmbeddedObjectPart, DataPartReferenceRelationship etc.
using DocumentFormat.OpenXml.Presentation; // For PresentationDocument, SlideId, Shape, Picture, GraphicFrame, OleObject etc.
using DocumentFormat.OpenXml.Drawing; // For BlipFill, Blip (a: namespace)
using DocumentFormat.OpenXml.Drawing.Charts; // For ChartReference, ExternalData (c: namespace)
using System; // For Math, Exception, ArgumentOutOfRangeException, StringComparison, Guid etc.
using System.Collections.Generic;
using System.IO; // For System.IO.Path, FileMode, FileAccess, FileNotFoundException, FileFormatException
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq; // For XDocument parsing of CustomXml

namespace QuantBoost_Powerpoint_Addin.Analysis
{
    // --- Definition of PptxFileAnalysisResult and its inner classes ---
    public class PptxFileAnalysisResult
    {
        public List<SlideInfo> Slides { get; set; } = new List<SlideInfo>();
        public List<ImageAsset> Images { get; set; } = new List<ImageAsset>();
        public List<ChartAsset> Charts { get; set; } = new List<ChartAsset>();
        public List<MediaAsset> Media { get; set; } = new List<MediaAsset>();
        public List<ExcelLinkTable> ExcelLinks { get; set; } = new List<ExcelLinkTable>();
        public List<EmbeddedObjectAsset> EmbeddedObjects { get; set; } = new List<EmbeddedObjectAsset>();
        public List<string> AnalysisErrors { get; set; } = new List<string>();

        public class SlideInfo
        {
            public int Index { get; set; } // 1-based
            public string Title { get; set; }
        }

        public abstract class AssetBase
        {
            public int SlideIndex { get; set; } // 1-based
            public string Description { get; set; } // User-friendly description or name
            public string ErrorState { get; set; } // Errors specific to parsing this asset
        }

        public class ImageAsset : AssetBase
        {
            public string ImageType { get; set; } // e.g., "image/png"
            public long SizeBytes { get; set; }
            public string FileName { get; set; } // Filename from the package if available
        }

        public class ChartAsset : AssetBase
        {
            public string ChartType { get; set; } // e.g., "Embedded Chart", "Excel Linked Chart (QuantBoost)"
            public string ChartId { get; set; } // r:id from ChartReference or from QuantBoostLink
            public bool IsExcelLinked { get; set; }
            public string SourceFileHash { get; set; } // From QuantBoostLink
            public string LastRefresh { get; set; } // From QuantBoostLink
            public bool FallbackUsed { get; set; } // From QuantBoostLink
            public string Notes { get; set; } // From QuantBoostLink
        }

        public class MediaAsset : AssetBase
        {
            public string MediaType { get; set; } // e.g., "video/mp4"
            public long SizeBytes { get; set; }
            public string FileName { get; set; }
        }

        public class ExcelLinkTable : AssetBase // Represents QuantBoostLink metadata not directly tied to a chart/OLE
        {
            public string LinkId { get; set; } // Often the ChartId it's associated with
            public string SourceFileHash { get; set; }
            public string ChartId { get; set; } // Specific Chart r:id if applicable
            public string Notes { get; set; }
            public string LastRefresh { get; set; }
            public bool FallbackUsed { get; set; }
        }

        public class EmbeddedObjectAsset : AssetBase
        {
            public string ObjectType { get; set; } // e.g., "application/vnd.ms-excel", "Embedded Excel Workbook"
            public long SizeBytes { get; set; }
            public string FileName { get; set; } // Best guess for filename
        }
    }

    public static class PptxFileParser
    {
        private const string PresentationMLDrawingNS = "http://schemas.openxmlformats.org/drawingml/2006/main";
        private const string PresentationMLChartNS = "http://schemas.openxmlformats.org/drawingml/2006/chart";
        private const string PresentationMLOleNS = "http://schemas.openxmlformats.org/presentationml/2006/ole";

        public static async Task<PptxFileAnalysisResult> AnalyzeAsync(string pptxPath)
        {
            return await Task.Run(() => Analyze(pptxPath)).ConfigureAwait(false);
        }

        public static PptxFileAnalysisResult Analyze(string pptxPath)
        {
            var result = new PptxFileAnalysisResult();
            if (string.IsNullOrEmpty(pptxPath) || !System.IO.File.Exists(pptxPath))
            {
                result.AnalysisErrors.Add($"File not found or path is invalid: {pptxPath ?? "null"}");
                return result;
            }

            try
            {
                using (PresentationDocument doc = PresentationDocument.Open(pptxPath, false))
                {
                    var presentationPart = doc.PresentationPart;
                    if (presentationPart?.Presentation?.SlideIdList == null)
                    {
                        result.AnalysisErrors.Add("Presentation part or SlideIdList is missing or invalid.");
                        return result;
                    }

                    var slideIds = presentationPart.Presentation.SlideIdList.Elements<SlideId>().ToList();
                    for (int i = 0; i < slideIds.Count; i++)
                    {
                        var slideIdElement = slideIds[i];
                        int currentSlideNumber = i + 1; // Accurate 1-based slide number

                        SlidePart slidePart = null;
                        string slideTitle = $"Slide {currentSlideNumber}";
                        var processedImageRelIdsOnSlide = new HashSet<string>();

                        try
                        {
                            var part = presentationPart.GetPartById(slideIdElement.RelationshipId);
                            if (part is SlidePart tempSlidePart && tempSlidePart.Slide != null)
                            {
                                slidePart = tempSlidePart;
                                slideTitle = ExtractSlideTitle(slidePart, currentSlideNumber) ?? slideTitle;
                            }
                            else
                            {
                                result.AnalysisErrors.Add($"Slide {currentSlideNumber}: Referenced part (ID: {slideIdElement.RelationshipId}) is not a valid SlidePart or Slide content is null.");
                                result.Slides.Add(new PptxFileAnalysisResult.SlideInfo { Index = currentSlideNumber, Title = $"{slideTitle} (Error - Inaccessible)" });
                                continue; // Skip to next slideId
                            }
                        }
                        catch (Exception ex)
                        {
                            result.AnalysisErrors.Add($"Slide {currentSlideNumber}: Error accessing slide part (ID: {slideIdElement.RelationshipId}): {ex.Message}");
                            result.Slides.Add(new PptxFileAnalysisResult.SlideInfo { Index = currentSlideNumber, Title = $"{slideTitle} (Error - Accessing Part)" });
                            continue; // Skip to next slideId
                        }

                        result.Slides.Add(new PptxFileAnalysisResult.SlideInfo { Index = currentSlideNumber, Title = slideTitle });

                        // Process assets in a specific order to help with consolidation (e.g., OLE previews)
                        ProcessChartsOnSlide(slidePart, currentSlideNumber, result, processedImageRelIdsOnSlide);
                        ProcessOleObjectsOnSlide(slidePart, currentSlideNumber, result, processedImageRelIdsOnSlide);
                        ProcessImagesOnSlide(slidePart, currentSlideNumber, result, processedImageRelIdsOnSlide); // Processes remaining images
                        ProcessMediaOnSlide(slidePart, currentSlideNumber, result); // Implements Option 2
                        ProcessQuantBoostLinksOnSlide(slidePart, currentSlideNumber, result); // For standalone links
                    }
                }
            }
            catch (OpenXmlPackageException oxpe)
            {
                result.AnalysisErrors.Add($"OpenXML package error analyzing '{System.IO.Path.GetFileName(pptxPath)}': {oxpe.Message}. The file might be corrupted or not a valid OOXML format.");
                System.Diagnostics.Debug.WriteLine($"PptxFileParser OpenXmlPackageException: {oxpe}");
            }
            catch (Exception ex)
            {
                result.AnalysisErrors.Add($"Fatal error analyzing presentation '{System.IO.Path.GetFileName(pptxPath)}': {ex.Message}. Analysis may be incomplete.");
                System.Diagnostics.Debug.WriteLine($"PptxFileParser Unhandled Exception: {ex}");
            }
            return result;
        }

        private static string ExtractSlideTitle(SlidePart slidePart, int slideNumber)
        {
            try
            {
                var titlePlaceholderTypes = new[] { PlaceholderValues.Title, PlaceholderValues.CenteredTitle };
                var titleShape = slidePart.Slide
                    .Descendants<DocumentFormat.OpenXml.Presentation.Shape>()
                    .FirstOrDefault(s =>
                    {
                        var placeholder = s.Descendants<PlaceholderShape>().FirstOrDefault();
                        if (placeholder?.Type?.HasValue == true && titlePlaceholderTypes.Contains(placeholder.Type.Value)) return true;
                        return s.NonVisualShapeProperties?.NonVisualDrawingProperties?.Name?.Value?.ToLowerInvariant().Contains("title") == true;
                    });

                if (titleShape?.TextBody != null)
                {
                    string titleText = string.Join(" ", titleShape.TextBody.Descendants<DocumentFormat.OpenXml.Drawing.Text>().Select(t => t.InnerText)).Trim();
                    return string.IsNullOrWhiteSpace(titleText) ? null : titleText;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Slide {slideNumber}: Error extracting title: {ex.Message}");
            }
            return null;
        }

        private static void ProcessChartsOnSlide(SlidePart slidePart, int slideNumber, PptxFileAnalysisResult result, HashSet<string> processedImageRelIds)
        {
            try
            {
                foreach (var graphicFrame in slidePart.Slide.Descendants<DocumentFormat.OpenXml.Presentation.GraphicFrame>())
                {
                    var graphicData = graphicFrame.Graphic?.GraphicData;
                    if (graphicData?.Uri?.Value != PresentationMLChartNS) continue;

                    string chartRelId = graphicData.Descendants<ChartReference>().FirstOrDefault()?.Id?.Value;
                    if (string.IsNullOrEmpty(chartRelId)) continue;

                    var chartAsset = new PptxFileAnalysisResult.ChartAsset
                    {
                        SlideIndex = slideNumber, ChartId = chartRelId,
                        Description = graphicFrame.NonVisualGraphicFrameProperties?.NonVisualDrawingProperties?.Description?.Value ??
                                      graphicFrame.NonVisualGraphicFrameProperties?.NonVisualDrawingProperties?.Name?.Value ?? $"Chart (ID: {chartRelId})",
                        ChartType = "Embedded Chart"
                    };

                    try
                    {
                        var chartPartById = slidePart.GetPartById(chartRelId);
                        if (chartPartById is ChartPart chartPart) // ChartPart is in DocumentFormat.OpenXml.Packaging
                        {
                            if (chartPart.ChartSpace?.Descendants<ExternalData>().Any() == true) chartAsset.ChartType = "Chart with External Data";
                        }
                        else chartAsset.ErrorState = $"ChartPart (ID: {chartRelId}) not found or invalid type.";
                    }
                    catch (Exception ex) { chartAsset.ErrorState = $"Error accessing ChartPart (ID: {chartRelId}): {ex.Message}"; }

                    var qbLink = FindQuantBoostLinkForAsset(slidePart, chartAsset.ChartId, chartAsset.Description);
                    if (qbLink != null)
                    {
                        chartAsset.IsExcelLinked = true; chartAsset.ChartType = "Excel Linked Chart (QuantBoost)";
                        chartAsset.SourceFileHash = qbLink.SourceFileHash; chartAsset.LastRefresh = qbLink.LastRefresh;
                        chartAsset.FallbackUsed = qbLink.FallbackUsed; chartAsset.Notes = qbLink.Notes;
                        if (!string.IsNullOrEmpty(qbLink.Description)) chartAsset.Description = qbLink.Description;
                    }
                    result.Charts.Add(chartAsset);
                }
            }
            catch (Exception ex) { result.AnalysisErrors.Add($"Slide {slideNumber}: Error processing charts: {ex.Message}"); }
        }

        private static void ProcessOleObjectsOnSlide(SlidePart slidePart, int slideNumber, PptxFileAnalysisResult result, HashSet<string> processedImageRelIds)
        {
            try
            {
                foreach (var graphicFrame in slidePart.Slide.Descendants<DocumentFormat.OpenXml.Presentation.GraphicFrame>())
                {
                    var graphicData = graphicFrame.Graphic?.GraphicData;
                    if (graphicData?.Uri?.Value != PresentationMLOleNS) continue;

                    var oleObjectElement = graphicData.Descendants<OleObject>().FirstOrDefault();
                    if (oleObjectElement == null) continue;

                    string oleRelId = oleObjectElement.Id?.Value;

                    var fallbackPicBlip = graphicFrame.Descendants<DocumentFormat.OpenXml.Presentation.Picture>()
                                                      .FirstOrDefault()?
                                                      .BlipFill?.Blip;
                    if (fallbackPicBlip?.Embed?.HasValue == true)
                    {
                        processedImageRelIds.Add(fallbackPicBlip.Embed.Value);
                    }

                    var oleAsset = new PptxFileAnalysisResult.EmbeddedObjectAsset
                    {
                        SlideIndex = slideNumber,
                        Description = graphicFrame.NonVisualGraphicFrameProperties?.NonVisualDrawingProperties?.Description?.Value ??
                                      graphicFrame.NonVisualGraphicFrameProperties?.NonVisualDrawingProperties?.Name?.Value ?? "Embedded OLE Object",
                        ObjectType = "OLE Object"
                    };

                    if (!string.IsNullOrEmpty(oleRelId))
                    {
                        try
                        {
                            var partById = slidePart.GetPartById(oleRelId);
                            if (partById is EmbeddedObjectPart embeddedPart) // EmbeddedObjectPart is in DocumentFormat.OpenXml.Packaging
                            {
                                oleAsset.ObjectType = embeddedPart.ContentType ?? "application/octet-stream";
                                oleAsset.FileName = GetOleObjectFileName(oleObjectElement, embeddedPart, slidePart, oleRelId);
                                using (var stream = embeddedPart.GetStream(FileMode.Open, FileAccess.Read)) { oleAsset.SizeBytes = stream.Length; }

                                if (oleAsset.ObjectType.IndexOf("excel", StringComparison.OrdinalIgnoreCase) >= 0 ||
                                    oleAsset.ObjectType.IndexOf("spreadsheet", StringComparison.OrdinalIgnoreCase) >= 0 ||
                                    oleAsset.FileName?.ToLowerInvariant().EndsWith(".xlsx") == true ||
                                    oleAsset.FileName?.ToLowerInvariant().EndsWith(".xlsb") == true ||
                                    oleAsset.FileName?.ToLowerInvariant().EndsWith(".xlsm") == true)
                                {
                                    oleAsset.ObjectType = "Embedded Excel Workbook";
                                    var qbLink = FindQuantBoostLinkForAsset(slidePart, oleRelId, oleAsset.Description, oleAsset.FileName);
                                    if (qbLink != null)
                                    {
                                        oleAsset.ObjectType = "Linked Excel Object (QuantBoost)";
                                        if (!string.IsNullOrEmpty(qbLink.Description)) oleAsset.Description = qbLink.Description;
                                        oleAsset.Description += $" (QB Link: {qbLink.SourceFileHash?.Substring(0, Math.Min(8, qbLink.SourceFileHash?.Length ?? 0))})";
                                    }
                                }
                            }
                            else oleAsset.ErrorState = $"EmbeddedObjectPart (ID: {oleRelId}) not found or invalid type.";
                        }
                        catch (Exception ex) { oleAsset.ErrorState = $"Error accessing EmbeddedObjectPart (ID: {oleRelId}): {ex.Message}"; }
                    }
                    else oleAsset.ErrorState = "OLE Object r:id is missing.";
                    result.EmbeddedObjects.Add(oleAsset);
                }
            }
            catch (Exception ex) { result.AnalysisErrors.Add($"Slide {slideNumber}: Error processing OLE objects: {ex.Message}"); }
        }

        private static string GetOleObjectFileName(OleObject oleObjectElement, EmbeddedObjectPart embeddedPart, SlidePart slidePart, string oleRelId)
        {
            string progId = oleObjectElement.ProgId?.Value;
            var extRel = slidePart.ExternalRelationships.FirstOrDefault(r => r.Id == oleRelId);
            if (extRel?.Uri != null)
            {
                try { return System.IO.Path.GetFileName(extRel.Uri.OriginalString); } catch { /* ignore path errors */ }
            }
            if (!string.IsNullOrEmpty(progId))
            {
                if (progId.StartsWith("Excel.Sheet", StringComparison.OrdinalIgnoreCase)) return "EmbeddedWorkbook.xlsx";
                if (progId.StartsWith("Word.Document", StringComparison.OrdinalIgnoreCase)) return "EmbeddedDocument.docx";
                if (progId.StartsWith("PowerPoint.Show", StringComparison.OrdinalIgnoreCase)) return "EmbeddedPresentation.pptx";
                if (progId.Equals("Package", StringComparison.OrdinalIgnoreCase)) return "EmbeddedPackageObject";
            }
            return System.IO.Path.GetFileName(embeddedPart.Uri.ToString()) ?? $"oleObject_{oleRelId ?? Guid.NewGuid().ToString().Substring(0, 8)}";
        }

        private static void ProcessImagesOnSlide(SlidePart slidePart, int slideNumber, PptxFileAnalysisResult result, HashSet<string> consumedImageRelIds)
        {
            try
            {
                foreach (var picElement in slidePart.Slide.Descendants<DocumentFormat.OpenXml.Presentation.Picture>())
                {
                    var blip = picElement.BlipFill?.Blip; // Blip is in DocumentFormat.OpenXml.Drawing
                    string imageRelId = blip?.Embed?.Value;

                    if (string.IsNullOrEmpty(imageRelId) || consumedImageRelIds.Contains(imageRelId)) continue;

                    var imageAsset = new PptxFileAnalysisResult.ImageAsset
                    {
                        SlideIndex = slideNumber,
                        Description = picElement.NonVisualPictureProperties?.NonVisualDrawingProperties?.Description?.Value ??
                                      picElement.NonVisualPictureProperties?.NonVisualDrawingProperties?.Name?.Value ?? "Image",
                    };
                    try
                    {
                        var partById = slidePart.GetPartById(imageRelId);
                        if (partById is ImagePart imagePart) // ImagePart is in DocumentFormat.OpenXml.Packaging
                        {
                            imageAsset.ImageType = imagePart.ContentType;
                            imageAsset.FileName = System.IO.Path.GetFileName(imagePart.Uri.OriginalString);
                            using (var stream = imagePart.GetStream(FileMode.Open, FileAccess.Read)) { imageAsset.SizeBytes = stream.Length; }
                        }
                        else imageAsset.ErrorState = $"ImagePart (ID: {imageRelId}) not found or invalid type.";
                    }
                    catch (Exception ex) { imageAsset.ErrorState = $"Error accessing ImagePart (ID: {imageRelId}): {ex.Message}"; }
                    result.Images.Add(imageAsset);
                }
            }
            catch (Exception ex) { result.AnalysisErrors.Add($"Slide {slideNumber}: Error processing images: {ex.Message}"); }
        }

        // OPTION 2 IMPLEMENTED HERE for ProcessMediaOnSlide
        private static void ProcessMediaOnSlide(SlidePart slidePart, int slideNumber, PptxFileAnalysisResult result)
        {
            try
            {
                // DataPartReferenceRelationship is in DocumentFormat.OpenXml.Packaging
                foreach (DataPartReferenceRelationship relationship in slidePart.DataPartReferenceRelationships)
                {
                    // MediaDataPart is in DocumentFormat.OpenXml.Packaging
                    if (relationship.DataPart is MediaDataPart mediaDataPart)
                    {
                        var mediaAsset = new PptxFileAnalysisResult.MediaAsset { SlideIndex = slideNumber };
                        try
                        {
                            mediaAsset.MediaType = mediaDataPart.ContentType;
                            mediaAsset.FileName = System.IO.Path.GetFileName(mediaDataPart.Uri.OriginalString);
                            mediaAsset.Description = $"Media ({mediaAsset.MediaType}) - {mediaAsset.FileName}";
                            using (var stream = mediaDataPart.GetStream(FileMode.Open, FileAccess.Read))
                            {
                                mediaAsset.SizeBytes = stream.Length;
                            }
                        }
                        catch (Exception ex)
                        {
                            mediaAsset.ErrorState = $"Error accessing MediaDataPart content: {ex.Message}";
                        }
                        result.Media.Add(mediaAsset);
                    }
                }
            }
            catch (Exception ex)
            {
                result.AnalysisErrors.Add($"Slide {slideNumber}: Error processing media relationships: {ex.Message}");
            }
        }

        private static PptxFileAnalysisResult.ExcelLinkTable FindQuantBoostLinkForAsset(SlidePart slidePart, string assetRelId, string assetDescription, string assetFileName = null)
        {
            try
            {
                foreach (var idPartPair in slidePart.Parts.Where(p => p.OpenXmlPart is CustomXmlPart)) // CustomXmlPart is in Packaging
                {
                    var customXmlPart = (CustomXmlPart)idPartPair.OpenXmlPart;
                    try
                    {
                        using (var stream = customXmlPart.GetStream(FileMode.Open, FileAccess.Read))
                        {
                            var xml = XDocument.Load(stream);
                            var linkNode = xml.Descendants("QuantBoostLink")
                                .FirstOrDefault(ln =>
                                {
                                    string chartId = ln.Element("Chart")?.Attribute("Id")?.Value;
                                    string linkedFileName = ln.Element("LinkedFile")?.Attribute("name")?.Value;
                                    if (!string.IsNullOrEmpty(chartId) && chartId.Equals(assetRelId, StringComparison.OrdinalIgnoreCase)) return true;
                                    if (!string.IsNullOrEmpty(linkedFileName) && !string.IsNullOrEmpty(assetFileName) && (assetFileName.IndexOf(linkedFileName, StringComparison.OrdinalIgnoreCase) >= 0 || linkedFileName.IndexOf(assetFileName, StringComparison.OrdinalIgnoreCase) >= 0)) return true;
                                    if (!string.IsNullOrEmpty(assetDescription)) { if (!string.IsNullOrEmpty(linkedFileName) && assetDescription.IndexOf(linkedFileName, StringComparison.OrdinalIgnoreCase) >= 0) return true; if (!string.IsNullOrEmpty(chartId) && assetDescription.IndexOf(chartId, StringComparison.OrdinalIgnoreCase) >= 0) return true; }
                                    return false;
                                });
                            if (linkNode != null) return new PptxFileAnalysisResult.ExcelLinkTable { LinkId = linkNode.Element("Chart")?.Attribute("Id")?.Value ?? assetRelId, SourceFileHash = linkNode.Element("LinkedFile")?.Attribute("hash")?.Value, ChartId = linkNode.Element("Chart")?.Attribute("Id")?.Value, Notes = linkNode.Element("Notes")?.Value, LastRefresh = linkNode.Element("LastRefresh")?.Value, FallbackUsed = (bool?)linkNode.Element("FallbackUsed") ?? false, Description = $"QuantBoost Link: {linkNode.Element("LinkedFile")?.Attribute("name")?.Value ?? "Unknown File"}" };
                        }
                    }
                    catch { /* Ignore errors reading individual custom XML parts */ }
                }
            }
            catch { /* Ignore errors iterating slide parts */ }
            return null;
        }

        private static void ProcessQuantBoostLinksOnSlide(SlidePart slidePart, int slideNumber, PptxFileAnalysisResult result)
        {
            try
            {
                foreach (var idPartPair in slidePart.Parts.Where(p => p.OpenXmlPart is CustomXmlPart))
                {
                    var customXmlPart = (CustomXmlPart)idPartPair.OpenXmlPart;
                    try
                    {
                        using (var stream = customXmlPart.GetStream(FileMode.Open, FileAccess.Read))
                        {
                            var xml = XDocument.Load(stream);
                            foreach (var linkNode in xml.Descendants("QuantBoostLink"))
                            {
                                string chartId = linkNode.Element("Chart")?.Attribute("Id")?.Value; string sourceFileHash = linkNode.Element("LinkedFile")?.Attribute("hash")?.Value;
                                bool chartCaptured = result.Charts.Any(c => c.SlideIndex == slideNumber && c.IsExcelLinked && c.SourceFileHash == sourceFileHash && (c.ChartId == chartId || string.IsNullOrEmpty(chartId)));
                                bool oleCaptured = result.EmbeddedObjects.Any(o => o.SlideIndex == slideNumber && (o.Description.IndexOf("QuantBoost Link", StringComparison.OrdinalIgnoreCase) >= 0) && (string.IsNullOrEmpty(sourceFileHash) || o.Description.IndexOf(sourceFileHash.Substring(0, Math.Min(8, sourceFileHash.Length)), StringComparison.OrdinalIgnoreCase) >= 0));
                                if (!chartCaptured && !oleCaptured && !result.ExcelLinks.Any(l => l.SlideIndex == slideNumber && l.SourceFileHash == sourceFileHash && (l.ChartId == chartId || string.IsNullOrEmpty(chartId))))
                                { result.ExcelLinks.Add(new PptxFileAnalysisResult.ExcelLinkTable { SlideIndex = slideNumber, LinkId = chartId, SourceFileHash = sourceFileHash, ChartId = chartId, Notes = linkNode.Element("Notes")?.Value, LastRefresh = linkNode.Element("LastRefresh")?.Value, FallbackUsed = (bool?)linkNode.Element("FallbackUsed") ?? false, Description = $"Standalone QB Link: {linkNode.Element("LinkedFile")?.Attribute("name")?.Value ?? "Unknown"}" }); }
                            }
                        }
                    }
                    catch (Exception ex) { result.AnalysisErrors.Add($"Slide {slideNumber}: Error parsing CustomXML for QB links: {ex.Message}"); }
                }
            }
            catch (Exception ex) { result.AnalysisErrors.Add($"Slide {slideNumber}: Error processing standalone QB links: {ex.Message}"); }
        }
    }
}
