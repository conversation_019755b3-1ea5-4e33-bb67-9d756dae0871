import { createBrowserClient } from '@supabase/ssr';

interface AuthFetchOptions extends RequestInit { retryOn401?: boolean; }

let browserClient: ReturnType<typeof createBrowserClient> | null = null;
function getClient() {
  if (!browserClient) {
    const url = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
    const anon = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'public-anon-key';
    browserClient = createBrowserClient(url, anon);
  }
  return browserClient;
}

export async function authFetch(input: string, init: AuthFetchOptions = {}) {
  const supabase = getClient();
  const { data: { session } } = await supabase.auth.getSession();
  const token = session?.access_token;
  const headers: Record<string, string> = { 'Content-Type': 'application/json', ...(init.headers as Record<string, string> || {}) };
  if (token) headers['Authorization'] = `Bearer ${token}`;
  const exec = async () => fetch(input, { ...init, headers });
  let res = await exec();
  if (res.status === 401 && init.retryOn401 !== false) {
    await supabase.auth.refreshSession();
    const { data: { session: s2 } } = await supabase.auth.getSession();
    if (s2?.access_token) { headers['Authorization'] = `Bearer ${s2.access_token}`; res = await exec(); }
  }
  return res;
}

export async function authJson<T=any>(input: string, init: AuthFetchOptions = {}) {
  const res = await authFetch(input, init);
  let body: any = null; try { body = await res.json(); } catch {}
  return { ok: res.ok, status: res.status, body: body as T, response: res };
}
