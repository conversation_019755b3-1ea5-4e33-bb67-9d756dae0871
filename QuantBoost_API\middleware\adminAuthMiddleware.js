// middleware/adminAuthMiddleware.js
const { sendError } = require('../utils/responseHelpers');

/**
 * Middleware to authorize if a user has an 'admin' role.
 * This should be placed *after* the authenticateJWT middleware.
 */
async function authorizeAdmin(req, res, next) {
    // 1. Check if a user object is attached to the request (from previous middleware)
    if (!req.user) {
        console.error('[authorizeAdmin] No user object found on request. Ensure authenticateJWT runs first.');
        return sendError(res, 'Authentication required before authorization', 401);
    }

    // 2. Check for the user's roles in their app_metadata.
    // Supabase stores custom roles in `app_metadata`.
    const roles = req.user.app_metadata?.roles;
    if (roles && Array.isArray(roles) && roles.includes('admin')) {
        // 3. User has the 'admin' role, proceed to the next handler.
        console.log(`[authorizeAdmin] SUCCESS: User ${req.user.id} is authorized as admin.`);
        return next();
    }

    // 4. If the user is not an admin, deny access.
    console.warn(`[authorizeAdmin] FORBIDDEN: User ${req.user.id} attempted to access an admin route without 'admin' role.`);
    return sendError(res, 'Forbidden: You do not have administrative privileges', 403);
}

module.exports = { authorizeAdmin };
