import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';

export async function POST(req: NextRequest) {
  // Create Supabase client inside the function to avoid build-time issues
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  );
  try {
    const { email } = await req.json();

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Check if user already exists
    const { data: existingUser, error: existingUserError } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      return NextResponse.json({ error: 'An account with this email already exists.' }, { status: 409 });
    }
    if (existingUserError && existingUserError.code !== 'PGRST116') { // Ignore 'No rows found' error
        throw existingUserError;
    }


    // 1. Create a new user in auth.users
    const password = randomUUID(); // Generate a secure random password
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
    });

    if (authError) {
      console.error('Supabase auth error:', authError);
      return NextResponse.json({ error: authError.message || 'Failed to create user.' }, { status: 500 });
    }
    if (!authData.user) {
        return NextResponse.json({ error: 'User could not be created.' }, { status: 500 });
    }

    // The trigger on auth.users should have created a profile. Now, create a trial license.
    const trialStartDate = new Date();
    const trialExpiryDate = new Date(trialStartDate);
    trialExpiryDate.setDate(trialExpiryDate.getDate() + 14);

    const { error: licenseError } = await supabase.from('licenses').insert({
      user_id: authData.user.id,
      product_id: 'prod_S6Fn893jGxRhKk', // Individual License Product ID for trial
      status: 'trial_active',
      email: email,
      trial_start_date: trialStartDate.toISOString(),
      trial_expiry_date: trialExpiryDate.toISOString(),
      license_tier: 'Trial',
      max_activations: 1,
    });

    if (licenseError) {
        console.error('Supabase license creation error:', licenseError);
        // Potentially roll back user creation or mark as needing attention
        return NextResponse.json({ error: 'Failed to create trial license.' }, { status: 500 });
    }

    // In a real app, you'd send a welcome email with a password reset link
    // since we generated a random password.
    console.log(`Successfully created trial for ${email}. User should reset their password.`);

    return NextResponse.json({ success: true, message: 'Trial started! Please check your email to set up your account.' });

  } catch (error) {
    console.error('Error creating trial:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}