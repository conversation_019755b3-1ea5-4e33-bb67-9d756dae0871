import { test, expect } from '../../fixtures/stripe.fixture';

const ANNUAL_PRICE_ID = 'price_1RC3HTE6FvhUKV1bE9D6zf6e';

const requireEnv = () => { if (!process.env.BASE_URL) test.skip(true, 'BASE_URL not set'); };

test.describe('Team Plan Checkout', () => {
  test('team purchase with 5 seats (skeleton)', async ({ page }) => {
    requireEnv();
    await page.goto(`/checkout/${ANNUAL_PRICE_ID}?quantity=5`);
    // TODO: assert team UI elements, total price reflects quantity * unitPrice.
    // Blocking details until selectors confirmed.
  });
});
