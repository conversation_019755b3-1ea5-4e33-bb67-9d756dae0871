---
title: Technical Context for QuantBoost_Frontend
purpose: To document the technologies, libraries, and architecture of the QuantBoost_Frontend project.
projects: ["QuantBoost_Frontend"]
source_analysis: Codebase analysis of QuantBoost_Frontend
status: bootstrapped-incomplete
last_updated: 2025-05-13T00:00:00.000Z
tags: ["frontend", "technical", "nextjs", "supabase", "tailwindcss"]
---

## QuantBoost_Frontend Technical Context

### Primary Technologies

*   **Programming Language:** TypeScript
*   **Framework:** Next.js (App Router)
*   **UI Styling:** Tailwind CSS
*   **UI Components:** Shadcn/ui (indicated by `components.json` and `src/components/ui`)

### Key Libraries & Dependencies

*   **Supabase:**
    *   `@supabase/supabase-js`: For client-side interactions with Supabase (authentication, database).
    *   `@supabase/auth-helpers-nextjs`: For Next.js specific Supabase authentication.
    *   Used for user authentication (`SupabaseProvider.tsx`, `LogoutButton.tsx`, `SupabaseClient.ts`, `SupabaseServerClient.ts`) and potentially database interactions (though not explicitly shown in the reviewed files beyond auth).
*   **React:** Core of Next.js.
*   **clsx**, **tailwind-merge**: Utility libraries for conditional and merged Tailwind CSS classes (`src/lib/utils.ts`).
*   **@radix-ui/react-slot**: Used by Shadcn/ui components (e.g., `Button.tsx`).
*   **class-variance-authority**: Used by Shadcn/ui components for styling variants (e.g., `Button.tsx`).
*   **next/font**: For font optimization (`layout.tsx` uses Geist).
*   **ESLint**: For code linting (`eslint.config.mjs`).
*   **PostCSS**: For Tailwind CSS processing (`postcss.config.js`).

### Database Interactions

*   **Supabase:** Used as the backend and database.
    *   `SupabaseClient.ts`: General client for Supabase.
    *   `SupabaseServerClient.ts`: Server-side client, specifically configured with a service role key, implying administrative database operations.
    *   Authentication is handled via Supabase Auth.
    *   The `pricing/page.tsx` interacts with a custom API endpoint (`/api/checkout/create-session`) which likely involves database operations (e.g., storing user and subscription data) via the `SupabaseServerClient` or direct Supabase calls in the API route.

### External API Calls

*   **Stripe:**
    *   The `pricing/page.tsx` initiates a checkout session by calling a backend API endpoint (`/api/checkout/create-session`). This endpoint, in turn, would interact with the Stripe API to create a checkout session and redirect the user to Stripe's hosted checkout page.
    *   Links to Stripe's billing portal (`https://billing.stripe.com/p/login/...`) are present.
*   **YouTube:**
    *   Feature pages (`slide-size-analyzer/page.tsx`, `keyboard-shortcuts/page.tsx`, `excel-trace/page.tsx`, `clean-excel/page.tsx`) embed YouTube videos for demos.
* 

### Build Tools & Environment

*   **Next.js CLI:** Used for development, building, and serving the application (inferred from `package.json` scripts, though not directly read yet).
*   **TypeScript Compiler (tsc):** Used for type checking (`tsconfig.json`).
*   **Node.js & npm/yarn:** Required for running the Next.js application and managing dependencies.
*   **Environment Variables:**
    *   `NEXT_PUBLIC_SUPABASE_URL`
    *   `NEXT_PUBLIC_SUPABASE_ANON_KEY`
    *   `SUPABASE_SERVICE_ROLE_KEY`
    *   These are crucial for Supabase integration.

### Apparent Environment Requirements & Setup Steps

1.  **Node.js and a package manager (npm or yarn) installed.**
2.  **Supabase Project:**
    *   A Supabase project must be set up.
    *   Supabase URL and Anon Key need to be configured as environment variables (`NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`).
    *   Supabase Service Role Key needs to be configured for server-side operations (`SUPABASE_SERVICE_ROLE_KEY`).
    *   Authentication (and potentially database tables for users, subscriptions) needs to be configured in Supabase.
3.  **Stripe Account:**
    *   A Stripe account is necessary for payment processing.
    *   Stripe Price IDs for products need to be configured (as seen in `pricing/page.tsx`).
    *   Stripe API keys (secret key for backend, publishable key for frontend if using Stripe.js directly, though not seen here) would be required.
    *   A webhook endpoint for Stripe events (e.g., successful payment) would likely be needed on the backend.
4.  **Installation:** Run `npm install` or `yarn install` to download dependencies.
5.  **Development Server:** Run `npm run dev` or `yarn dev` to start the Next.js development server.
6.  **Environment Variables Setup:** Create a `.env.local` file (or similar, depending on Next.js conventions) to store the Supabase and Stripe related environment variables.

### Code Structure & Conventions

*   **Next.js App Router:** The application uses the Next.js App Router (`src/app/` directory structure).
*   **TypeScript:** Code is written in TypeScript (`.ts`, `.tsx` files).
*   **Shadcn/ui:** UI components are organized under `src/components/ui/` and are likely added via the Shadcn CLI.
*   **Path Aliases:** `@/*` is configured to point to `./src/*` (`tsconfig.json`).
*   **Client Components:** `"use client";` directive is used for components requiring client-side interactivity.
*   **Styling:** Tailwind CSS is used extensively for styling. Utility functions `cn` (from `clsx` and `tailwind-merge`) are used for conditional and merging class names.
