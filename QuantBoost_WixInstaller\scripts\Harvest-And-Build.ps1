param(
  [ValidateSet('Debug','Release')]
  [string]$Configuration = 'Release',
  [string]$HeatPath
)

<#
  Harvest-And-Build.ps1
  - Detects WiX Toolset heat.exe
  - Generates ForceWin64.xslt to mark harvested Components as Win64
  - Runs heat.exe to harvest Excel and PowerPoint Release outputs
  - Updates QuantBoost.WixInstaller.wixproj to include harvested .wxs files if missing

  Usage (Windows PowerShell 5.1):
    Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
    ./Harvest-And-Build.ps1 -Configuration Release
#>

Set-StrictMode -Version Latest
$ErrorActionPreference = 'Stop'

function Write-Info($msg) { Write-Host "[INFO] $msg" -ForegroundColor Cyan }
function Write-Warn($msg) { Write-Host "[WARN] $msg" -ForegroundColor Yellow }
function Write-Err($msg)  { Write-Host "[ERROR] $msg" -ForegroundColor Red }
function Write-Success($msg) { Write-Host "[OK] $msg" -ForegroundColor Green }

function Find-HeatExe {
  # 1) Env var WIX (often set by WiX installers)
  if ($env:WIX) {
    $candidate = Join-Path $env:WIX 'bin\heat.exe'
    if (Test-Path $candidate) { return (Resolve-Path $candidate).Path }
  }

  # 2) Registry-based discovery for common WiX v3 versions
  $regPaths = @(
    'HKLM:\SOFTWARE\WOW6432Node\WiX Toolset v3.14',
    'HKLM:\SOFTWARE\WOW6432Node\WiX Toolset v3.11',
    'HKLM:\SOFTWARE\WiX Toolset v3.14',
    'HKLM:\SOFTWARE\WiX Toolset v3.11'
  )
  foreach ($rp in $regPaths) {
    try {
      $installDir = (Get-ItemProperty -Path $rp -ErrorAction Stop).InstallDir
      if ($installDir) {
        $candidate = Join-Path $installDir 'bin\heat.exe'
        if (Test-Path $candidate) { return (Resolve-Path $candidate).Path }
      }
    } catch { }
  }

  # 3) Well-known default installs
  $wellKnown = @(
    'C:\Program Files (x86)\WiX Toolset v3.14\bin\heat.exe',
    'C:\Program Files (x86)\WiX Toolset v3.11\bin\heat.exe'
  )
  foreach ($p in $wellKnown) {
    if (Test-Path $p) { return (Resolve-Path $p).Path }
  }

  throw 'heat.exe not found. Please install WiX Toolset v3.14.1 (or v3.11+) and ensure heat.exe is available.'
}

function Ensure-ForceWin64Xslt($xsltPath) {
  $content = @'
<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:wix="http://schemas.microsoft.com/wix/2006/wi">
  <xsl:output method="xml" indent="yes"/>
  <xsl:strip-space elements="*"/>
  <xsl:template match="@*|node()">
    <xsl:copy>
      <xsl:apply-templates select="@*|node()"/>
    </xsl:copy>
  </xsl:template>
  <xsl:template match="wix:Component">
    <xsl:copy>
      <xsl:apply-templates select="@*"/>
      <xsl:attribute name="Win64">yes</xsl:attribute>
      <xsl:apply-templates select="node()"/>
    </xsl:copy>
  </xsl:template>
</xsl:stylesheet>
'@
  $dir = Split-Path -Parent $xsltPath
  if (-not (Test-Path $dir)) { New-Item -ItemType Directory -Path $dir | Out-Null }
  $content | Out-File -FilePath $xsltPath -Encoding UTF8 -Force
}

function Add-Win64ToComponents($wxsPath) {
  if (-not (Test-Path $wxsPath)) { return }
  [xml]$doc = Get-Content -Path $wxsPath -Raw -Encoding UTF8
  $nsmgr = New-Object System.Xml.XmlNamespaceManager($doc.NameTable)
  $nsmgr.AddNamespace('wix', 'http://schemas.microsoft.com/wix/2006/wi')
  $nodes = $doc.SelectNodes('//wix:Component', $nsmgr)
  if ($nodes) {
    foreach ($n in $nodes) { $n.SetAttribute('Win64','yes') }
    $doc.Save($wxsPath)
    Write-Info "Post-processed Win64 on $($nodes.Count) components in $(Split-Path -Leaf $wxsPath)"
  }
}

function Update-WixProjInclude($projPath, [string[]]$wxsFiles) {
  if (-not (Test-Path $projPath)) { throw "WiX project not found: $projPath" }
  [xml]$proj = Get-Content -Path $projPath -Encoding UTF8
  $ns = $proj.DocumentElement.NamespaceURI
  $nsmgr = New-Object System.Xml.XmlNamespaceManager($proj.NameTable)
  $nsmgr.AddNamespace('msb', $ns)

  # Find or create an ItemGroup that holds Compile items
  $itemGroups = $proj.SelectNodes('//msb:Project/msb:ItemGroup', $nsmgr)
  $targetGroup = $null
  foreach ($ig in $itemGroups) {
    if ($ig.SelectSingleNode('msb:Compile', $nsmgr)) { $targetGroup = $ig; break }
  }
  if (-not $targetGroup) {
    $targetGroup = $proj.CreateElement('ItemGroup', $ns)
    $proj.DocumentElement.AppendChild($targetGroup) | Out-Null
  }

  foreach ($wxs in $wxsFiles) {
    $fileName = [System.IO.Path]::GetFileName($wxs)
    $existing = $targetGroup.SelectSingleNode("msb:Compile[@Include='$fileName']", $nsmgr)
    if (-not $existing) {
      $compile = $proj.CreateElement('Compile', $ns)
      $compile.SetAttribute('Include', $fileName)
      $targetGroup.AppendChild($compile) | Out-Null
      Write-Info "Added Compile Include='$fileName' to project"
    } else {
      Write-Info "Compile Include='$fileName' already present"
    }
  }

  $proj.Save($projPath)
}

# --- Main Script Execution ---
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host " QuantBoost WiX Harvest Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Resolve paths
$scriptDir   = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectDir  = Split-Path -Parent $scriptDir   # ..\QuantBoost_WixInstaller
$repoRoot    = Split-Path -Parent $projectDir  # ..\

$excelSource = Join-Path $repoRoot "QuantBoost_Excel\bin\$Configuration"
$pptxSource  = Join-Path $repoRoot "QuantBoost_PPTX\bin\$Configuration"
$wixProjPath = Join-Path $projectDir 'QuantBoost.WixInstaller.wixproj'

$excelHarvestOut = Join-Path $projectDir 'Excel.Harvested.wxs'
$pptxHarvestOut  = Join-Path $projectDir 'PowerPoint.Harvested.wxs'

$xsltPath = Join-Path $scriptDir 'ForceWin64.xslt'

Write-Info "Configuration: $Configuration"
Write-Info "WiX project: $wixProjPath"
Write-Info "Excel bin: $excelSource"
Write-Info "PowerPoint bin: $pptxSource"

# Validate source directories
if (-not (Test-Path $excelSource)) { 
    Write-Err "Excel output folder not found: $excelSource"
    Write-Err "Please build QuantBoost_Excel project in $Configuration mode first"
    exit 1
}
if (-not (Test-Path $pptxSource)) { 
    Write-Err "PowerPoint output folder not found: $pptxSource"
    Write-Err "Please build QuantBoost_PPTX project in $Configuration mode first"
    exit 1
}

# Find heat.exe
$heat = $null
if ($HeatPath) {
  if (-not (Test-Path $HeatPath)) { 
    Write-Err "Provided -HeatPath not found: $HeatPath"
    exit 1
  }
  $heat = (Resolve-Path $HeatPath).Path
} else {
  $heat = Find-HeatExe
}
Write-Success "Using heat.exe: $heat"

# Ensure XSLT is ready
Ensure-ForceWin64Xslt -xsltPath $xsltPath
Write-Info "ForceWin64 XSLT ready: $xsltPath"

# Run heat for Excel
Write-Host ""
Write-Info "Harvesting Excel files..."
$heatArgs = @('dir', $excelSource, '-gg', '-sfrag', '-srd', '-cg', 'ExcelFiles', 
              '-dr', 'EXCELDIR', '-var', 'var.ExcelSourceDir', 
              '-t', $xsltPath, '-out', $excelHarvestOut)
              
& $heat $heatArgs
if ($LASTEXITCODE -ne 0) {
  Write-Warn 'Transform failed; retrying harvest without XSLT and will post-process Win64.'
  if (Test-Path $excelHarvestOut) { Remove-Item $excelHarvestOut -Force -ErrorAction SilentlyContinue }
  
  $heatArgsNoXslt = @('dir', $excelSource, '-gg', '-sfrag', '-srd', '-cg', 'ExcelFiles', 
                      '-dr', 'EXCELDIR', '-var', 'var.ExcelSourceDir', '-out', $excelHarvestOut)
  & $heat $heatArgsNoXslt
  
  if ($LASTEXITCODE -ne 0) { 
    Write-Err 'Heat harvest failed for Excel without transform.'
    exit 1
  }
  Add-Win64ToComponents -wxsPath $excelHarvestOut
}
Write-Success "Excel harvesting complete"

# Run heat for PowerPoint
Write-Host ""
Write-Info 'Harvesting PowerPoint files...'
$heatArgs = @('dir', $pptxSource, '-gg', '-sfrag', '-srd', '-cg', 'PowerPointFiles', 
              '-dr', 'POWERPOINTDIR', '-var', 'var.PowerPointSourceDir', 
              '-t', $xsltPath, '-out', $pptxHarvestOut)
              
& $heat $heatArgs
if ($LASTEXITCODE -ne 0) {
  Write-Warn 'Transform failed; retrying harvest without XSLT and will post-process Win64.'
  if (Test-Path $pptxHarvestOut) { Remove-Item $pptxHarvestOut -Force -ErrorAction SilentlyContinue }
  
  $heatArgsNoXslt = @('dir', $pptxSource, '-gg', '-sfrag', '-srd', '-cg', 'PowerPointFiles', 
                      '-dr', 'POWERPOINTDIR', '-var', 'var.PowerPointSourceDir', '-out', $pptxHarvestOut)
  & $heat $heatArgsNoXslt
  
  if ($LASTEXITCODE -ne 0) { 
    Write-Err 'Heat harvest failed for PowerPoint without transform.'
    exit 1
  }
  Add-Win64ToComponents -wxsPath $pptxHarvestOut
}
Write-Success "PowerPoint harvesting complete"

# Verify output files
if (-not (Test-Path $excelHarvestOut)) { 
    Write-Err 'Excel.Harvested.wxs was not created.'
    exit 1
}
if (-not (Test-Path $pptxHarvestOut)) { 
    Write-Err 'PowerPoint.Harvested.wxs was not created.'
    exit 1
}

# Update project file
Write-Host ""
Write-Info 'Updating .wixproj to include harvested files (if needed)...'
Update-WixProjInclude -projPath $wixProjPath -wxsFiles @($excelHarvestOut, $pptxHarvestOut)

# Success message
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host " SUCCESS! Harvest Complete" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Success "Harvested files created:"
Write-Host "  - $excelHarvestOut" -ForegroundColor Green
Write-Host "  - $pptxHarvestOut" -ForegroundColor Green
Write-Host ""
Write-Info "Next steps:"
Write-Host "  1. Build the MSI project (QuantBoost_WixInstaller) in Visual Studio" -ForegroundColor Yellow
Write-Host "  2. Then build the EXE bootstrapper (QuantBoost_Bootstrapper)" -ForegroundColor Yellow
Write-Host ""