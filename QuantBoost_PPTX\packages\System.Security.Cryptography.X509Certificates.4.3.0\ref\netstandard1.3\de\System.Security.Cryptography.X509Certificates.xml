﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.X509Certificates</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle">
      <summary>Stellt ein sicheres Handle bereit, das eine X.509-Kette darstellt.Weitere Informationen finden Sie unter <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />.</summary>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeX509ChainHandle.IsInvalid"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.OpenFlags">
      <summary>Gibt an, wie der X.509-Zertifikatsspeicher geöffnet werden kann.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.IncludeArchived">
      <summary>Öffnen Sie den X.509-Zertif<PERSON>tsspei<PERSON>, und schließen Sie archivierte Zertifikate ein.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.MaxAllowed">
      <summary>Öffnen Sie den X.509-Zertifikatsspeicher für den höchstmöglichen Zugriff.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.OpenExistingOnly">
      <summary>Öffnet nur vorhandene Speicher. Wenn kein Speicher vorhanden ist, wird durch die <see cref="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)" />-Methode kein neuen Speicher erstellt.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadOnly">
      <summary>Öffnen Sie den X.509-Zertifikatsspeicher nur zum Lesen.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadWrite">
      <summary>Öffnen Sie den X.509-Zertifikatsspeicher sowohl zum Lesen als auch zum Schreiben.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.PublicKey">
      <summary>Stellt die öffentlichen Schlüsselinformationen eines Zertifikats dar.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.#ctor(System.Security.Cryptography.Oid,System.Security.Cryptography.AsnEncodedData,System.Security.Cryptography.AsnEncodedData)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" />-Klasse mithilfe eines Objektbezeichner-Objekts des öffentlichen Schlüssels, einer ASN.1-codierten Darstellung der Parameter des öffentlichen Schlüssels und einer ASN.1-codierten Darstellung des Werts des öffentlichen Schlüssels. </summary>
      <param name="oid">Ein OID, der den öffentlichen Schlüssel darstellt.</param>
      <param name="parameters">Eine ASN.1-codierte Darstellung der Parameter des öffentlichen Schlüssels.</param>
      <param name="keyValue">Eine ASN.1-codierte Darstellung des Werts des öffentlichen Schlüssels.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedKeyValue">
      <summary>Ruft die ASN.1-codierte Darstellung des Werts des öffentlichen Schlüssels ab.</summary>
      <returns>Die ASN.1-codierte Darstellung des Werts des öffentlichen Schlüssels.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedParameters">
      <summary>Ruft die ASN.1-codierte Darstellung der Parameter des öffentlichen Schlüssels ab.</summary>
      <returns>Die ASN.1-codierte Darstellung der Parameter des öffentlichen Schlüssels.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Key">
      <summary>Ruft ein <see cref="T:System.Security.Cryptography.RSACryptoServiceProvider" />-Objekt oder <see cref="T:System.Security.Cryptography.DSACryptoServiceProvider" />-Objekt ab, das den öffentlichen Schlüssel darstellt.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" />-Objekt, das den öffentlichen Schlüssel darstellt.</returns>
      <exception cref="T:System.NotSupportedException">Der Schlüsselalgorithmus wird nicht unterstützt.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Oid">
      <summary>Ruft ein OID-Objekt (Object Identifier, Objektbezeichner) des öffentlichen Schlüssels ab.</summary>
      <returns>Ein OID-Objekt des öffentlichen Schlüssels.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreLocation">
      <summary>Gibt den Speicherort des X.509-Zertifikatsspeichers an.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.CurrentUser">
      <summary>Der vom aktuellen Benutzer verwendete X.509-Zertifikatsspeicher.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.LocalMachine">
      <summary>Der dem lokalen Computer zugewiesene X.509-Zertifikatsspeicher.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreName">
      <summary>Gibt den Namen des X.509-Zertifikatsspeichers an, der geöffnet werden soll.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AddressBook">
      <summary>Der X.509-Zertifikatsspeicher für andere Benutzer.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AuthRoot">
      <summary>Der X.509-Zertifikatsspeicher für Zertifizierungsstellen von Drittanbietern.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.CertificateAuthority">
      <summary>Der X.509-Zertifikatsspeicher für Zwischenzertifizierungsstellen. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Disallowed">
      <summary>Der X.509-Zertifikatsspeicher für widerrufene Zertifikate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.My">
      <summary>Der X.509-Zertifikatsspeicher für persönliche Zertifikate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Root">
      <summary>Der X.509-Zertifikatsspeicher für vertrauenswürdige Stammzertifizierungsstellen.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPeople">
      <summary>Der X.509-Zertifikatsspeicher für direkt vertrauenswürdige Personen und Ressourcen.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPublisher">
      <summary>Der X.509-Zertifikatsspeicher für direkt vertrauenswürdige Herausgeber.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName">
      <summary>Stellt den Distinguished Name eines X509-Zertifikats dar.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Byte[])">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />-Klasse unter Verwendung des angegebenen Bytearrays.</summary>
      <param name="encodedDistinguishedName">Ein Bytearray, das Informationen zum Distinguished Name enthält.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />-Klasse unter Verwendung des angegebenen <see cref="T:System.Security.Cryptography.AsnEncodedData" />-Objekts.</summary>
      <param name="encodedDistinguishedName">Ein <see cref="T:System.Security.Cryptography.AsnEncodedData" />-Objekt, das den Distinguished Name darstellt.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.X509Certificates.X500DistinguishedName)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />-Klasse unter Verwendung des angegebenen <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />-Objekts.</summary>
      <param name="distinguishedName">Ein <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />-Objekt.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />-Klasse unter Verwendung von Informationen aus der angegebenen Zeichenfolge.</summary>
      <param name="distinguishedName">Eine Zeichenfolge, die den Distinguished Name darstellt.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String,System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />-Klasse unter Verwendung der angegebenen Zeichenfolge und des <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags" />-Flags.</summary>
      <param name="distinguishedName">Eine Zeichenfolge, die den Distinguished Name darstellt.</param>
      <param name="flag">Eine bitweise Kombination von Enumerationswerten, die die Merkmale des Distinguished Name angeben.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Decode(System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>Decodiert einen Distinguished Name mit dem vom <paramref name="flag" />-Parameter angegebenen Merkmal.</summary>
      <returns>Der decodierte Distinguished Name.</returns>
      <param name="flag">Eine bitweise Kombination von Enumerationswerten, die die Merkmale des Distinguished Name angeben.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikat besitzt einen ungültigen Namen.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Format(System.Boolean)">
      <summary>Gibt eine formatierte Version für einen X500-Distinguished Name zurück, der gedruckt oder in einem Textfenster oder einer Konsole ausgegeben werden kann.</summary>
      <returns>Eine formatierte Zeichenfolge, die den X500-Distinguished Name darstellt.</returns>
      <param name="multiLine">true, wenn die Rückgabezeichenfolge Wagenrückläufe enthalten soll, andernfalls false.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Name">
      <summary>Ruft den durch Kommas getrennten Distinguished Name aus einem X500-Zertifikat ab.</summary>
      <returns>Der durch Kommas getrennte Distinguished Name des X509-Zertifikats.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags">
      <summary>Gibt die Eigenschaften des X.500-Distinguished Name an.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUsePlusSign">
      <summary>Der Distinguished Name verwendet kein Pluszeichen.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUseQuotes">
      <summary>Der Distinguished Name verwendet keine Anführungszeichen.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.ForceUTF8Encoding">
      <summary>Erzwingt den Distinguished Name, um den spezifischen X.500-Schlüssel als UTF-8-Zeichenfolgen anstelle von druckbaren Unicode-Zeichenfolgen zu codieren.Weitere Informationen und die Liste der betroffenen X.500-Schlüssel finden Sie unter X500NameFlags-Enumeration.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.None">
      <summary>Der Distinguished Name verfügt über keine besonderen Eigenschaften.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.Reversed">
      <summary>Der Distinguished Name wird umgekehrt.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseCommas">
      <summary>Der Distinguished Name verwendet Kommas.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseNewLines">
      <summary>Der Distinguished Name verwendet das Zeichen für eine neue Zeile.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseSemicolons">
      <summary>Der Distinguished Name verwendet Semikolons.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseT61Encoding">
      <summary>Der Distinguished Name verwendet T61-Codierung.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseUTF8Encoding">
      <summary>Der Distinguished Name verwendet die UTF8-Codierung anstelle der Unicode-Zeichencodierung.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension">
      <summary>Definiert die für ein Zertifikat festgelegten Einschränkungen.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Boolean,System.Boolean,System.Int32,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" />-Klasse.Parameter geben einen Wert an, der anzeigt, ob es sich bei einem Zertifikat um ein Zertifikat einer Zertifizierungsstelle handelt. Sie geben außerdem einen Wert an, der anzeigt, ob für ein Zertifikat eine Beschränkung der Anzahl zulässiger Pfadebenen besteht, sowie die Anzahl der in einem Zertifizierungspfad zulässigen Ebenen und einen Wert, der angibt, ob die Erweiterung wichtig ist.</summary>
      <param name="certificateAuthority">true, wenn es sich bei dem Zertifikat um ein Zertifikat einer Zertifizierungsstelle handelt, andernfalls false.</param>
      <param name="hasPathLengthConstraint">true, wenn für das Zertifikat eine Beschränkung der Anzahl der zulässigen Pfadebenen besteht, andernfalls false.</param>
      <param name="pathLengthConstraint">Ruft die Anzahl der in einem Zertifikatspfad zulässigen Ebenen ab.</param>
      <param name="critical">true, wenn die Erweiterung wichtig ist, andernfalls false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" />-Klasse mithilfe eines <see cref="T:System.Security.Cryptography.AsnEncodedData" />-Objekts und eines Werts, der angibt, ob die Erweiterung wichtig ist. </summary>
      <param name="encodedBasicConstraints">Die codierten Daten, aus denen die Erweiterung erstellt werden soll.</param>
      <param name="critical">true, wenn die Erweiterung wichtig ist, andernfalls false.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CertificateAuthority">
      <summary>Ruft einen Wert ab, der angibt, ob es sich bei einem Zertifikat um ein Zertifikat einer Zertifizierungsstelle handelt.</summary>
      <returns>true, wenn es sich bei dem Zertifikat um ein Zertifikat einer Zertifizierungsstelle handelt, andernfalls false.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" />-Klasse unter Verwendung eines <see cref="T:System.Security.Cryptography.AsnEncodedData" />-Objekts.</summary>
      <param name="asnEncodedData">Die codierten Daten, aus denen die Erweiterung erstellt werden soll.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.HasPathLengthConstraint">
      <summary>Ruft einen Wert ab, der angibt, ob für ein Zertifikat eine Beschränkung der Anzahl zulässiger Pfadebenen vorhanden ist.</summary>
      <returns>true, wenn für das Zertifikat eine Beschränkung der Anzahl zulässiger Pfadebenen vorhanden ist, andernfalls false.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Die Erweiterung kann nicht decodiert werden. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.PathLengthConstraint">
      <summary>Ruft die Anzahl der in einem Zertifikatspfad zulässigen Ebenen ab.</summary>
      <returns>Eine ganze Zahl, die die Anzahl der in einem Zertifikatspfad zulässigen Ebenen angibt.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Die Erweiterung kann nicht decodiert werden. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate">
      <summary>Stellt Methoden bereit, die Sie beim Verwenden von X.509-Zertifikaten (v.3) unterstützen.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor">
      <summary>Initialisiert eine neue Instanz der<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Klasse. </summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[])">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Klasse, die aus einer Folge von Bytes zum Darstellen eines X.509v3-Zertifikats definiert ist.</summary>
      <param name="data">Ein Bytearray mit Daten aus einem X.509-Zertifikat.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiel:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="rawData" />-Parameter ist null.- oder - Die Länge des <paramref name="rawData" />-Parameters ist 0 (null).</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Klasse mit einem Bytearray und einem Kennwort.</summary>
      <param name="rawData">Ein Bytearray mit Daten aus einem X.509-Zertifikat.</param>
      <param name="password">Das für den Zugriff auf die X.509-Zertifikatsdaten erforderliche Kennwort.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiel:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="rawData" />-Parameter ist null.- oder - Die Länge des <paramref name="rawData" />-Parameters ist 0 (null).</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Klasse mit einem Bytearray, einem Kennwort und einem Schlüsselspeicherflag.</summary>
      <param name="rawData">Ein Bytearray mit Daten aus einem X.509-Zertifikat. </param>
      <param name="password">Das für den Zugriff auf die X.509-Zertifikatsdaten erforderliche Kennwort. </param>
      <param name="keyStorageFlags">Eine bitweise Kombination der Enumerationswerte, die steuern wo und wie das Zertifikat importiert wird. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiel:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="rawData" />-Parameter ist null.- oder - Die Länge des <paramref name="rawData" />-Parameters ist 0 (null).</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.IntPtr)">
      <summary>[SICHERHEITSRELEVANT] Initialisiert mithilfe eines Handles für eine nicht verwaltete <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Struktur eine neue Instanz der PCCERT_CONTEXT-Klasse.</summary>
      <param name="handle">Ein Handle für eine nicht verwaltete PCCERT_CONTEXT-Struktur.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Klasse mit dem Namen einer mit PKCS7 signierten Datei. </summary>
      <param name="fileName">Der Name einer mit PKCS7 signierten Datei.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiel:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="fileName" />-Parameter ist null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Klasse mit dem Namen einer mit PKCS7 signierten Datei und einem Kennwort für den Zugriff auf das Zertifikat.</summary>
      <param name="fileName">Der Name einer mit PKCS7 signierten Datei. </param>
      <param name="password">Das für den Zugriff auf die X.509-Zertifikatsdaten erforderliche Kennwort. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiel:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="fileName" />-Parameter ist null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Klasse mit dem Namen einer mit PKCS7 signierten Datei, einem Kennwort für den Zugriff auf das Zertifikat und einem Schlüsselspeicherflag. </summary>
      <param name="fileName">Der Name einer mit PKCS7 signierten Datei. </param>
      <param name="password">Das für den Zugriff auf die X.509-Zertifikatsdaten erforderliche Kennwort. </param>
      <param name="keyStorageFlags">Eine bitweise Kombination der Enumerationswerte, die steuern wo und wie das Zertifikat importiert wird. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiel:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="fileName" />-Parameter ist null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose">
      <summary>Gibt alle vom aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekt verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose(System.Boolean)">
      <summary>Gibt alle von dieser <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> verwendeten nicht verwalteten Ressourcen und optional auch die verwalteten Ressourcen frei. </summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Object)">
      <summary>Überprüft zwei <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekte auf Gleichheit.</summary>
      <returns>true, wenn das aktuelle <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekt und das im <paramref name="other" />-Parameter angegebene Objekt gleich sind, andernfalls false.</returns>
      <param name="obj">Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekt, das mit dem aktuellen Objekt verglichen werden soll. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Überprüft zwei <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekte auf Gleichheit.</summary>
      <returns>true, wenn das aktuelle <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekt und das im <paramref name="other" />-Parameter angegebene Objekt gleich sind, andernfalls false.</returns>
      <param name="other">Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekt, das mit dem aktuellen Objekt verglichen werden soll.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>Exportiert das aktuelle <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekt in einem durch einen der <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />-Werte beschriebenen Format in ein Bytearray. </summary>
      <returns>Ein Array von Bytes, das das aktuelle <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekt darstellt.</returns>
      <param name="contentType">Einer der <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />-Werte, die beschreiben, wie die Ausgabedaten formatiert werden. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Es wurde ein anderer Wert als <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />, <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" /> oder <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> an den <paramref name="contentType" />-Parameter übergeben.- oder - Das Zertifikat konnte nicht exportiert werden.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>Exportiert das aktuelle <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekt in einem durch einen der <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />-Werte beschriebenen Format mithilfe des angegebenen Kennworts in ein Bytearray.</summary>
      <returns>Ein Array von Bytes, das das aktuelle <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekt darstellt.</returns>
      <param name="contentType">Einer der <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />-Werte, die beschreiben, wie die Ausgabedaten formatiert werden.</param>
      <param name="password">Das für den Zugriff auf die X.509-Zertifikatsdaten erforderliche Kennwort.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Es wurde ein anderer Wert als <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />, <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" /> oder <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> an den <paramref name="contentType" />-Parameter übergeben.- oder - Das Zertifikat konnte nicht exportiert werden.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.KeyContainerPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="Open, Export" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetCertHash">
      <summary>Gibt den Hashwert für das X.509-Zertifikat (v.3) als Bytearray zurück.</summary>
      <returns>Der Hashwert des X.509-Zertifikats.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetFormat">
      <summary>Gibt den Namen des Formats dieses X.509-Zertifikats (v.3) zurück.</summary>
      <returns>Das Format dieses X.509-Zertifikats.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetHashCode">
      <summary>Gibt den Hashcode für das X.509-Zertifikat (v.3) als ganze Zahl zurück.</summary>
      <returns>Der Hashcode für das X.509-Zertifikat als ganze Zahl.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithm">
      <summary>Gibt die Schlüsselalgorithmusinformationen für dieses X.509v3-Zertifikat als Zeichenfolge zurück.</summary>
      <returns>Die Schlüsselalgorithmusinformationen für dieses X.509-Zertifikat als Zeichenfolge.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Der Zertifikatskontext ist ungültig.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParameters">
      <summary>Gibt die Schlüsselalgorithmusparameter für das X.509v3-Zertifikat als Bytearray zurück.</summary>
      <returns>Die Schlüsselalgorithmusparameter für das X.509-Zertifikat als Bytearray.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Der Zertifikatskontext ist ungültig.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParametersString">
      <summary>Gibt die Schlüsselalgorithmusparameter für das X.509v3-Zertifikat als hexadezimale Zeichenfolge zurück.</summary>
      <returns>Die Schlüsselalgorithmusparameter für das X.509-Zertifikat als hexadezimale Zeichenfolge.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Der Zertifikatskontext ist ungültig.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetPublicKey">
      <summary>Gibt den öffentlichen Schlüssel für das X.509v3-Zertifikat als Bytearray zurück.</summary>
      <returns>Der öffentliche Schlüssel für das X.509-Zertifikat als Bytearray.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Der Zertifikatskontext ist ungültig.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumber">
      <summary>Gibt die Seriennummer des X.509v3-Zertifikats als Bytearray zurück.</summary>
      <returns>Die Seriennummer des X.509-Zertifikats als Bytearray.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Der Zertifikatskontext ist ungültig.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Handle">
      <summary>[SICHERHEITSRELEVANT] Ruft ein Handle für einen von einer nicht verwalteten PCCERT_CONTEXT-Struktur beschriebenen Microsoft Cryptographic API-Zertifikatskontext ab. </summary>
      <returns>Eine <see cref="T:System.IntPtr" />-Struktur, die eine nicht verwaltete PCCERT_CONTEXT-Struktur darstellt.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Issuer">
      <summary>Ruft den Namen der Zertifizierungsstelle ab, die das X.509-Zertifikat (v.3) ausgestellt hat.</summary>
      <returns>Der Name der Zertifizierungsstelle, die das X.509-Zertifikat (v.3) ausgestellt hat.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikatshandle ist ungültig.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Subject">
      <summary>Ruft den Distinguished Name für den Antragsteller aus dem Zertifikat ab.</summary>
      <returns>Der Distinguished Name für den Antragsteller aus dem Zertifikat.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikatshandle ist ungültig.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString">
      <summary>Gibt eine Zeichenfolgendarstellung des aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekts zurück.</summary>
      <returns>Eine Zeichenfolgendarstellung des aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekts.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString(System.Boolean)">
      <summary>Gibt eine Zeichenfolgendarstellung des aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekts zurück, optional mit Zusatzinformationen.</summary>
      <returns>Eine Zeichenfolgendarstellung des aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekts.</returns>
      <param name="fVerbose">true, wenn die ausführliche Form der Zeichenfolgendarstellung gewünscht wird, andernfalls false. </param>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2">
      <summary>Stellt ein X.509-Zertifikat dar.  </summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[])">
      <summary>Initialisiert mithilfe der Informationen aus einem Bytearray eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Klasse.</summary>
      <param name="rawData">Ein Bytearray mit Daten aus einem X.509-Zertifikat. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiele:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String)">
      <summary>Initialisiert mithilfe eines Bytearrays und eines Kennworts eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Klasse.</summary>
      <param name="rawData">Ein Bytearray mit Daten aus einem X.509-Zertifikat. </param>
      <param name="password">Das für den Zugriff auf die X.509-Zertifikatsdaten erforderliche Kennwort. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiele:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initialisiert mithilfe eines Bytearrays, eines Kennworts und eines Schlüsselspeicherflags eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Klasse.</summary>
      <param name="rawData">Ein Bytearray mit Daten aus einem X.509-Zertifikat. </param>
      <param name="password">Das für den Zugriff auf die X.509-Zertifikatsdaten erforderliche Kennwort. </param>
      <param name="keyStorageFlags">Eine bitweise Kombination der Enumerationswerte, die steuern wo und wie das Zertifikat importiert wird. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiele:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.IntPtr)">
      <summary>Initialisiert mithilfe eines nicht verwalteten Handles eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Klasse.</summary>
      <param name="handle">Ein Zeiger auf einen Zertifikatskontext in nicht verwaltetem Code.Die C-Struktur wird als PCCERT_CONTEXT bezeichnet.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiele:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String)">
      <summary>Initialisiert mithilfe eines Zertifikatsdateinamens eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Klasse.</summary>
      <param name="fileName">Der Name einer Zertifikatsdatei. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiele:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String)">
      <summary>Initialisiert mithilfe des Zertifikatsdateinamens und eines für den Zugriff auf das Zertifikat verwendeten Kennworts eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Klasse.</summary>
      <param name="fileName">Der Name einer Zertifikatsdatei. </param>
      <param name="password">Das für den Zugriff auf die X.509-Zertifikatsdaten erforderliche Kennwort. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiele:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initialisiert mithilfe eines Zertifikatsdateinamens, eines für den Zugriff auf das Zertifikat verwendeten Kennworts und eines Schlüsselspeicherflags eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Klasse.</summary>
      <param name="fileName">Der Name einer Zertifikatsdatei. </param>
      <param name="password">Das für den Zugriff auf die X.509-Zertifikatsdaten erforderliche Kennwort. </param>
      <param name="keyStorageFlags">Eine bitweise Kombination der Enumerationswerte, die steuern wo und wie das Zertifikat importiert wird. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Zertifikatfehler.Beispiele:Die Zertifikatsdatei ist nicht vorhanden.Das Zertifikat ist ungültig.Das Kennwort des Zertifikats ist falsch.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Archived">
      <summary>Ruft einen Wert ab, der angibt, dass ein X.509-Zertifikat archiviert wird, oder legt diesen fest.</summary>
      <returns>true, wenn das Zertifikat archiviert wird, false, wenn das Zertifikat nicht archiviert wird.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikat kann nicht gelesen werden. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Extensions">
      <summary>Ruft eine Auflistung von <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekten ab.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Objekt.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikat kann nicht gelesen werden. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.FriendlyName">
      <summary>Ruft den einem Zertifikat zugeordneten Alias ab oder legt diesen fest.</summary>
      <returns>Der angezeigte Name des Zertifikats.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikat kann nicht gelesen werden. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.Byte[])">
      <summary>Gibt den Typ des in einem Bytearray enthaltenen Zertifikats an.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />-Objekt.</returns>
      <param name="rawData">Ein Bytearray mit Daten aus einem X.509-Zertifikat. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> hat die Länge 0 (null) oder ist null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.String)">
      <summary>Gibt den Typ des in einer Datei enthaltenen Zertifikats an.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />-Objekt.</returns>
      <param name="fileName">Der Name einer Zertifikatsdatei. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> ist null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetNameInfo(System.Security.Cryptography.X509Certificates.X509NameType,System.Boolean)">
      <summary>Ruft den die Namen des Zertifikatsantragstellers und des Zertifikatausstellers ab.</summary>
      <returns>Der Name des Zertifikats.</returns>
      <param name="nameType">Der <see cref="T:System.Security.Cryptography.X509Certificates.X509NameType" /> -Wert für den Antragsteller. </param>
      <param name="forIssuer">true, um den Namen des Ausstellers einzufügen, andernfalls false. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey">
      <summary>Ruft einen Wert ab, der angibt, ob ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekt einen privaten Schlüssel enthält. </summary>
      <returns>true, wenn das <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekt einen privaten Schlüssel enthält, andernfalls false. </returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Der Zertifikatskontext ist ungültig.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.IssuerName">
      <summary>Ruft den Distinguished Name des Zertifikatausstellers ab.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />-Objekt, das den Namen des Zertifikatausstellers enthält.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Der Zertifikatskontext ist ungültig.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotAfter">
      <summary>Ruft das Datum in Ortszeit ab, ab dem ein Zertifikat nicht mehr gültig ist.</summary>
      <returns>Ein <see cref="T:System.DateTime" />-Objekt, das das Ablaufdatum des Zertifikats darstellt.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikat kann nicht gelesen werden. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotBefore">
      <summary>Ruft das Datum in Ortszeit ab, ab dem ein Zertifikat gültig wird.</summary>
      <returns>Ein <see cref="T:System.DateTime" />-Objekt, das das Datum darstellt, an dem das Zertifikat gültig wird.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikat kann nicht gelesen werden. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PrivateKey">
      <summary>Ruft das <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" />-Objekt ab, das den einem Zertifikat zugeordneten privaten Schlüssel darstellt oder legt dieses fest.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" />-Objekt, das einen kryptografischen Dienstanbieter für RSA oder DSA darstellt.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Der Schlüsselwert ist kein RSA- oder DSA-Schlüssel, oder der Schlüssel kann nicht gelesen werden. </exception>
      <exception cref="T:System.ArgumentNullException">Der Wert, der für diese Eigenschaft festgelegt wird, ist null.</exception>
      <exception cref="T:System.NotSupportedException">Der Schlüsselalgorithmus für diesen privaten Schlüssel wird nicht unterstützt.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">Die X.509-Schlüssel stimmen nicht überein.</exception>
      <exception cref="T:System.ArgumentException">Der Schlüssel des Kryptografiedienstanbieters ist null.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey">
      <summary>Ruft ein <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" />-Objekt ab, das einem Zertifikat zugeordnet ist.</summary>
      <returns>Ein <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" />-Objekt.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Der Schlüsselwert ist kein RSA- oder DSA-Schlüssel, oder der Schlüssel kann nicht gelesen werden. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.RawData">
      <summary>Ruft die Rohdaten eines Zertifikats ab.</summary>
      <returns>Die Rohdaten des Zertifikats als Bytearray.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SerialNumber">
      <summary>Ruft die Seriennummer eines Zertifikats ab.</summary>
      <returns>Die Seriennummer des Zertifikats.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SignatureAlgorithm">
      <summary>Ruft den zum Erstellen der Signatur eines Zertifikats verwendeten Algorithmus ab.</summary>
      <returns>Gibt den Objektbezeichner (<see cref="T:System.Security.Cryptography.Oid" />) des Signaturalgorithmus zurück.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikat kann nicht gelesen werden. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SubjectName">
      <summary>Ruft den Distinguished Name für den Antragsteller aus einem Zertifikat ab.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" />-Objekt, das den Namen des Zertifikatsantragstellers darstellt.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Der Zertifikatskontext ist ungültig.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Thumbprint">
      <summary>Ruft den Fingerabdruck eines Zertifikats ab.</summary>
      <returns>Der Fingerabdruck des Zertifikats.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString">
      <summary>Zeigt ein X.509-Zertifikat in Textformat an.</summary>
      <returns>Die Zertifikatsinformationen.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString(System.Boolean)">
      <summary>Zeigt ein X.509-Zertifikat in Textformat an.</summary>
      <returns>Die Zertifikatsinformationen.</returns>
      <param name="verbose">true, um den öffentlichen Schlüssel, den privaten Schlüssel, Erweiterungen usw. anzuzeigen, false, um Informationen anzuzeigen, die denen der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Klasse ähneln, z. B. Fingerabdruck, Seriennummer, Name von Antragsteller und Aussteller usw. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Version">
      <summary>Ruft die X.509-Formatversion eines Zertifikats ab.</summary>
      <returns>Das Format des Zertifikats.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikat kann nicht gelesen werden. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection">
      <summary>Stellt eine Auflistung von <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekten dar.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Klasse ohne jegliche <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Informationen.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Klasse unter Verwendung eines <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekts.</summary>
      <param name="certificate">Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekt, aus dem die Auflistung gestartet wird.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>Initialisiert mithilfe eines Arrays von <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekten eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Klasse.</summary>
      <param name="certificates">Ein Array von <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekten. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Initialisiert mithilfe der angegeben Zertifikatsauflistung eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Klasse.</summary>
      <param name="certificates">Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Fügt am Ende der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> ein Objekt hinzu.</summary>
      <returns>Der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Index, an dem <paramref name="certificate" /> hinzugefügt wurde.</returns>
      <param name="certificate">Ein als <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekt dargestelltes X.509-Zertifikat. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> ist null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>Fügt einem <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt mehrere <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekte in einem Array hinzu.</summary>
      <param name="certificates">Ein Array von <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekten. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> ist null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Fügt einem anderen <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt mehrere <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekte eines <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekts hinzu.</summary>
      <param name="certificates">Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> ist null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Bestimmt, ob das <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt ein bestimmtes Zertifikat enthält.</summary>
      <returns>true, wenn <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> das angegebene <paramref name="certificate" /> enthält, andernfalls false.</returns>
      <param name="certificate">Das <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekt, das in der Auflistung gesucht werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> ist null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>Exportiert X.509-Zertifikatsinformationen in ein Bytearray.</summary>
      <returns>X.509-Zertifikatsinformationen in einem Bytearray.</returns>
      <param name="contentType">Ein unterstütztes <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />-Objekt. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>Exportiert X.509-Zertifikatsinformationen mithilfe eines Kennworts in ein Bytearray.</summary>
      <returns>X.509-Zertifikatsinformationen in einem Bytearray.</returns>
      <param name="contentType">Ein unterstütztes <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" />-Objekt. </param>
      <param name="password">Eine zum Schutz des Bytearrays verwendete Zeichenfolge. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikat kann nicht gelesen werden, sein Inhalt ist ungültig oder – im Fall eines Zertifikats, für das ein Kennwort erforderlich ist – sein Privatschlüssel konnte nicht exportiert werden, da das bereitgestellte Kennwort falsch war. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)">
      <summary>Sucht mithilfe der durch die <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" />-Enumeration und das <paramref name="findValue" />-Objekt angegebenen Suchkriterien ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</returns>
      <param name="findType">Einer der <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" />-Werte. </param>
      <param name="findValue">Die Suchkriterien als Objekt. </param>
      <param name="validOnly">true, damit nur gültige Zertifikate von der Suche zurückgegeben werden, andernfalls false. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="findType" /> ist ungültig. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt durchlaufen kann.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator" />-Objekt, das ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt durchlaufen kann.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[])">
      <summary>Importiert ein Zertifikat in Form eines Bytearrays in ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</summary>
      <param name="rawData">Ein Bytearray mit Daten aus einem X.509-Zertifikat. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Importiert ein Zertifikat in Form eines Bytearrays, das für den Zugriff auf das Zertifikat ein Kennwort erfordert, in ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</summary>
      <param name="rawData">Ein Bytearray mit Daten aus einem <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekt. </param>
      <param name="password">Das für den Zugriff auf die Zertifikatsinformationen erforderliche Kennwort. </param>
      <param name="keyStorageFlags">Eine bitweise Kombination der Enumerationswerte, die steuern wie und wo das Zertifikat importiert wird. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String)">
      <summary>Importiert eine Zertifikatsdatei in ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</summary>
      <param name="fileName">Der Name der Datei mit den Zertifikatsinformationen. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Importiert eine Zertifikatsdatei, die für ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt ein Kennwort erfordert.</summary>
      <param name="fileName">Der Name der Datei mit den Zertifikatsinformationen. </param>
      <param name="password">Das für den Zugriff auf die Zertifikatsinformationen erforderliche Kennwort. </param>
      <param name="keyStorageFlags">Eine bitweise Kombination der Enumerationswerte, die steuern wie und wo das Zertifikat importiert wird. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Fügt ein Objekt in ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt am angegebenen Index ein.</summary>
      <param name="index">Der nullbasierte Index, an dem der <paramref name="certificate" /> eingefügt werden soll. </param>
      <param name="certificate">Das einzufügende <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekt. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="index" /> ist größer als die <see cref="P:System.Collections.CollectionBase.Count" />-Eigenschaft. </exception>
      <exception cref="T:System.NotSupportedException">Die Auflistung ist schreibgeschützt.- oder - Die Auflistung hat eine feste Größe. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> ist null. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Item(System.Int32)">
      <summary>Ruft das Element am angegebenen Index ab oder legt dieses fest.</summary>
      <returns>Das Element am angegebenen Index.</returns>
      <param name="index">Der nullbasierte Index des Elements, das abgerufen oder festgelegt werden soll. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="index" /> ist gleich der <see cref="P:System.Collections.CollectionBase.Count" />-Eigenschaft oder größer als sie. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> ist null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Entfernt das erste Vorkommen eines Zertifikats aus dem <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</summary>
      <param name="certificate">Das aus dem <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt zu entfernende <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> ist null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>Entfernt mehrere <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekte in einem Array von einem <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</summary>
      <param name="certificates">Ein Array von <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekten. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> ist null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Entfernt mehrere <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekte in einem <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt aus einem anderen <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</summary>
      <param name="certificates">Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> ist null. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator">
      <summary>Unterstützt eine einfache Iteration durch ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Current">
      <summary>Ruft das aktuelle Element im <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt ab.</summary>
      <returns>Das aktuelle Element im <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element im <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position, d. h. vor das erste Element im <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Current">
      <summary>Eine Beschreibung dieses Members finden Sie unter <see cref="P:System.Collections.IEnumerator.Current" />.</summary>
      <returns>Das aktuelle Element im <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#MoveNext">
      <summary>Eine Beschreibung dieses Members finden Sie unter <see cref="M:System.Collections.IEnumerator.MoveNext" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Eine Beschreibung dieses Members finden Sie unter <see cref="M:System.Collections.IEnumerator.Reset" />.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection">
      <summary>Definiert eine Auflistung, in der <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekte gespeichert sind.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />-Klasse aus einem Array von <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekten.</summary>
      <param name="value">Das Array von <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekten, mit denen das neue Objekt initialisiert werden soll. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />-Klasse aus einer anderen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">Die <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />, mit der das neue Objekt initialisiert werden soll. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Add(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Fügt der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> mit dem angegebenen Wert hinzu.</summary>
      <returns>Der Index in der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />, an dem das neue <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> eingefügt wurde.</returns>
      <param name="value">Das <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, das der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> hinzugefügt werden soll. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>Kopiert die Elemente eines Arrays vom <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Typ an das Ende der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">Das Array vom <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Typ mit den Objekten, die der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> hinzugefügt werden sollen. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="value" />-Parameter ist null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>Kopiert die Elemente der angegebenen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> an das Ende der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">Die <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />, die die Objekte enthält, die der Auflistung hinzugefügt werden sollen. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="value" />-Parameter ist null. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Clear"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Ruft einen Wert ab, der angibt, ob die aktuelle <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> das angegebene <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> enthält.</summary>
      <returns>true, wenn <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> in dieser Auflistung enthalten ist, andernfalls false.</returns>
      <param name="value">Das zu suchende <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Certificate[],System.Int32)">
      <summary>Kopiert die <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Werte in der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> in eine eindimensionale <see cref="T:System.Array" />-Instanz am angegebenen Index.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> kopierten Werte ist. </param>
      <param name="index">Der Index im <paramref name="array" />, an dem mit dem Kopieren begonnen werden soll. </param>
      <exception cref="T:System.ArgumentException">Der <paramref name="array" />-Parameter ist mehrdimensional.- oder - Die Anzahl der Elemente in der <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> ist größer als der verfügbare Platz zwischen <paramref name="arrayIndex" /> und dem Ende des <paramref name="array" />. </exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="array" />-Parameter ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="arrayIndex" />-Parameter ist kleiner als die Untergrenze des <paramref name="array" />-Parameters. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Count"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> durchlaufen kann.</summary>
      <returns>Ein Enumerator der Unterelemente der <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />, mit dem die Auflistung durchlaufen werden kann.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetHashCode">
      <summary>Erstellt einen Hashwert basierend auf allen Werten, die in der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> enthalten sind.</summary>
      <returns>Ein Hashwert, der auf allen Werten basiert, die in der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> enthalten sind.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.IndexOf(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Gibt den Index des angegebenen <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> in der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> zurück.</summary>
      <returns>Der Index des durch den <paramref name="value" />-Parameter angegebenen <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> in der <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />, sofern gefunden, andernfalls -1.</returns>
      <param name="value">Das zu suchende <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />. </param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Fügt ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> am angegebenen Index in die aktuelle <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> ein.</summary>
      <param name="index">Der nullbasierte Index, an dem <paramref name="value" /> eingefügt werden soll. </param>
      <param name="value">Die einzufügende <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Item(System.Int32)">
      <summary>Ruft den Eintrag am angegebenen Index der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> ab oder legt diesen fest.</summary>
      <returns>Das <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> am angegebenen Index der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</returns>
      <param name="index">Der nullbasierte Index des Eintrags, der in der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> gesucht werden soll. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="index" />-Parameter liegt außerhalb des gültigen Bereichs von Indizes für die Auflistung. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Entfernt ein bestimmtes <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> aus der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">Das <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />, das aus der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> entfernt werden soll. </param>
      <exception cref="T:System.ArgumentException">Das durch den <paramref name="value" />-Parameter angegebene <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> wurde in der aktuellen <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> nicht gefunden. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.RemoveAt(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Add(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Contains(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IndexOf(System.Object)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Insert(System.Int32,System.Object)"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#IsReadOnly"></member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Item(System.Int32)"></member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.System#Collections#IList#Remove(System.Object)"></member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator">
      <summary>Listet die <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />-Objekte in einer <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> auf.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator" />-Klasse für das angegebene <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="mappings">Die aufzulistende <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Current">
      <summary>Ruft das aktuelle <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> in der <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> ab.</summary>
      <returns>Das aktuelle <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> in der <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der Auflistung.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Instanziieren des Enumerators geändert. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wird nach dem Instanziieren des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Current">
      <summary>Eine Beschreibung dieses Members finden Sie unter <see cref="P:System.Collections.IEnumerator.Current" />.</summary>
      <returns>Das aktuelle X.509-Zertifikatobjekt im <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />-Objekt.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#MoveNext">
      <summary>Eine Beschreibung dieses Members finden Sie unter <see cref="M:System.Collections.IEnumerator.MoveNext" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Instanziieren des Enumerators geändert. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Reset">
      <summary>Eine Beschreibung dieses Members finden Sie unter <see cref="M:System.Collections.IEnumerator.Reset" />.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Instanziieren des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Chain">
      <summary>Stellt ein Kettenerstellungsmodul für <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Zertifikate dar.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Build(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Erstellt mithilfe der in <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> angegebenen Richtlinie eine X.509-Kette.</summary>
      <returns>true, wenn das X.509-Zertifikat gültig ist, andernfalls false.</returns>
      <param name="certificate">Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="certificate" /> ist kein gültiges Zertifikat oder null. </exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="certificate" /> kann nicht gelesen werden. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainElements">
      <summary>Ruft eine Auflistung von <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" />-Objekten ab.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainPolicy">
      <summary>Ruft die beim Erstellen einer X.509-Zertifikatskette zu verwendende <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> ab oder legt diese fest.</summary>
      <returns>Das dieser X.509-Kette zugeordnete <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />-Objekt.</returns>
      <exception cref="T:System.ArgumentNullException">Der Wert, der für diese Eigenschaft festgelegt wird, ist null.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus">
      <summary>Ruft den Status aller Elemente in einem <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />-Objekt ab.</summary>
      <returns>Ein Array von <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" />-Objekten.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose">
      <summary>Gibt alle von dieser <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose(System.Boolean)">
      <summary>Gibt die von dieser <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> verwendeten nicht verwalteten Ressourcen und optional auch die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.SafeHandle">
      <summary>Ruft ein sicheres Handle für diese <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />Instanz ab. </summary>
      <returns>Gibt <see cref="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle" /> zurück.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElement">
      <summary>Stellt ein Element einer X.509-Kette dar.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Certificate">
      <summary>Ruft das X.509-Zertifikat an einem bestimmten Kettenelement ab.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.ChainElementStatus">
      <summary>Ruft den Fehlerstatus des aktuellen X.509-Zertifikats in einer Kette ab.</summary>
      <returns>Ein Array von <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" />-Objekten.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Information">
      <summary>Ruft zusätzliche Fehlerinformationen aus einer nicht verwalteten Zertifikatskettenstruktur ab.</summary>
      <returns>Eine Zeichenfolge, die den pwszExtendedErrorInfo-Member der nicht verwalteten CERT_CHAIN_ELEMENT-Struktur in der Kryptografie-API darstellt.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection">
      <summary>Stellt eine Auflistung von <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" />-Objekten dar.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509ChainElement[],System.Int32)">
      <summary>Kopiert ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />-Objekt in ein Array, wobei am angegebenen Index begonnen wird.</summary>
      <param name="array">Ein Array von <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" />-Objekten. </param>
      <param name="index">Eine ganze Zahl, die den Indexwert darstellt. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">Der angegebene <paramref name="index" /> ist kleiner als 0 (null) bzw. größer als die Länge oder gleich der Länge des Arrays. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> plus die aktuelle Anzahl ist größer als die Länge des Arrays. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Count">
      <summary>Ruft die Anzahl der Elemente in der Auflistung ab.</summary>
      <returns>Eine ganze Zahl, die die Anzahl von Elementen in der Auflistung darstellt.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.GetEnumerator">
      <summary>Ruft ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" />-Objekt ab, mit dem durch eine Auflistung von Kettenelementen navigiert werden kann.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob die Auflistung der Kettenelemente synchronisiert ist.</summary>
      <returns>Gibt immer false zurück.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Item(System.Int32)">
      <summary>Ruft das <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" />-Objekt am angegebenen Index ab.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" />-Objekt.</returns>
      <param name="index">Ein Ganzzahlwert. </param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" /> ist kleiner als 0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist größer als die Länge oder gleich der Länge der Auflistung. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />-Objekt synchronisiert werden kann.</summary>
      <returns>Ein Zeigerverweis auf das aktuelle Objekt.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />-Objekt in ein Array, wobei am angegebenen Index begonnen wird.</summary>
      <param name="array">Ein Array, in das das <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />-Objekt kopiert werden soll.</param>
      <param name="index">Der Index von <paramref name="array" />, ab dem mit dem Kopieren begonnen werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Der angegebene <paramref name="index" /> ist kleiner als 0 (null) bzw. größer als die Länge oder gleich der Länge des Arrays. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> plus die aktuelle Anzahl ist größer als die Länge des Arrays. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Ruft ein <see cref="T:System.Collections.IEnumerator" />-Objekt ab, mit dem durch eine Auflistung von Kettenelementen navigiert werden kann.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />-Objekt.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator">
      <summary>Unterstützt eine einfache Iteration durch <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Current">
      <summary>Ruft das aktuelle Element in der <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />-Auflistung ab.</summary>
      <returns>Das aktuelle Element in der <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element in der <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das aktuelle Element in der <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />-Auflistung ab.</summary>
      <returns>Das aktuelle Element in der <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy">
      <summary>Stellt die beim Erstellen einer X509-Zertifikatskette anzuwendende Kettenrichtlinie dar.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />-Klasse. </summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ApplicationPolicy">
      <summary>Ruft eine Auflistung von Objektbezeichnern (OID) ab, in der die vom Zertifikat unterstützten Anwendungsrichtlinien oder die erweiterten Schlüsselverwendungen (EKU – Enhanced Key Usage) angegeben werden.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.OidCollection" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.CertificatePolicy">
      <summary>Ruft eine Auflistung der Objektbezeichner (OID) ab, die angibt, welche Zertifikatsrichtlinien das Zertifikat unterstützt.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.OidCollection" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ExtraStore">
      <summary>Stellt eine zusätzliche Auflistung von Zertifikaten dar, die vom Verkettungsmodul beim Validieren einer Zertifikatskette durchsucht werden können.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />-Objekt.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.Reset">
      <summary>Setzt die <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />-Member auf die Standardwerte zurück.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationFlag">
      <summary>Ruft Werte für X509-Sperrflags ab oder legt diese fest.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" />-Objekt.</returns>
      <exception cref="T:System.ArgumentException">Der angegebene <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" />-Wert ist kein gültiges Flag. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationMode">
      <summary>Ruft Werte für den X509-Zertifikatssperrmodus ab oder legt diese fest.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" />-Objekt.</returns>
      <exception cref="T:System.ArgumentException">Der angegebene <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" />-Wert ist kein gültiges Flag. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.UrlRetrievalTimeout">
      <summary>Ruft die Zeitspanne ab, die während der Online-Sperrüberprüfung oder dem Herunterladen der CRL (Zertifikatsperrliste) verstrichen ist.</summary>
      <returns>Ein <see cref="T:System.TimeSpan" />-Objekt.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationFlags">
      <summary>Ruft Überprüfungsflags für das Zertifikat ab.</summary>
      <returns>Ein Wert aus der <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" />-Enumeration.</returns>
      <exception cref="T:System.ArgumentException">Der angegebene <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" />-Wert ist kein gültiges Flag.Der Standardwert ist <see cref="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationTime">
      <summary>Der in Ortszeit angegebene Zeitpunkt der Zertifikatsüberprüfung.</summary>
      <returns>Ein <see cref="T:System.DateTime" />-Objekt.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatus">
      <summary>Stellt eine einfache Struktur zum Speichern des X509-Kettenstatus und von Fehlerinformationen bereit.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.Status">
      <summary>Gibt den Status der X509-Kette an.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags" />-Wert.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.StatusInformation">
      <summary>Gibt eine Beschreibung des Werts von <see cref="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus" /> an.</summary>
      <returns>Eine lokalisierbare Zeichenfolge.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags">
      <summary>Definiert den Status einer X509-Kette.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotSignatureValid">
      <summary>Gibt an, dass die Zertifikatsvertrauensliste (CTL - Certificate Trust List) eine ungültige Signatur enthält.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotTimeValid">
      <summary>Gibt an, dass die Zertifikatsvertrauensliste (CTL - Certificate Trust List) wegen eines ungültigen Zeitwerts nicht gültig ist, z. B. wegen eines Werts, der angibt, dass die CTL abgelaufen ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotValidForUsage">
      <summary>Gibt an, dass die Zertifikatsvertrauensliste (CTL - Certificate Trust List) für diese Verwendung nicht gültig ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Cyclic">
      <summary>Gibt an, dass die X509-Kette nicht erstellt werden konnte.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasExcludedNameConstraint">
      <summary>Gibt an, dass die X509-Kette ungültig ist, da in einem Zertifikat eine Namenseinschränkung ausgeschlossen wurde.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotDefinedNameConstraint">
      <summary>Gibt an, dass das Zertifikat eine nicht definierte Namenseinschränkung enthält.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotPermittedNameConstraint">
      <summary>Gibt an, dass das Zertifikat eine unzulässige Namenskonstante enthält.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotSupportedNameConstraint">
      <summary>Gibt an, dass das Zertifikat keine unterstützte Namenseinschränkung oder eine nicht unterstützte Namenseinschränkung enthält.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidBasicConstraints">
      <summary>Gibt an, dass die X509-Kette aufgrund ungültiger Basiseinschränkungen ungültig ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidExtension">
      <summary>Gibt an, dass die X509-Kette aufgrund einer ungültigen Erweiterung ungültig ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidNameConstraints">
      <summary>Gibt an, dass die X509-Kette aufgrund ungültiger Namenseinschränkungen ungültig ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidPolicyConstraints">
      <summary>Gibt an, dass die X509-Kette aufgrund ungültiger Richtlinieneinschränkungen ungültig ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoError">
      <summary>Gibt an, dass die X509-Kette keine Fehler aufweist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoIssuanceChainPolicy">
      <summary>Gibt an, dass keine Zertifikatsrichtlinienerweiterung im Zertifikat vorhanden ist.Dieser Fehler tritt auf, wenn in einer Gruppenrichtlinie angegeben ist, dass alle Zertifikate eine Zertifikatsrichtlinie enthalten müssen.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotSignatureValid">
      <summary>Gibt an, dass die X509-Kette aufgrund einer ungültigen Zertifikatssignatur ungültig ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeNested">
      <summary>Veraltet.Gibt an, dass das Zertifikat der Zertifizierungsstelle und das ausgestellte Zertifikat nicht geschachtelte Gültigkeitsperioden enthalten.Beispielsweise kann das Zertifikat der Zertifizierungsstelle vom 01. Januar bis zum 01. Dezember gültig sein, während das ausgestellte Zertifikat vom 02. Januar bis zum 02. Dezember gültig ist, d. h., die Gültigkeitsperioden sind nicht geschachtelt.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeValid">
      <summary>Gibt an, dass die X509-Kette aufgrund eines ungültigen Zeitwerts ungültig ist, beispielsweise eines Werts, mit dem ein abgelaufenes Zertifikat angegeben wird.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotValidForUsage">
      <summary>Gibt an, dass die Schlüsselverwendung nicht gültig ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.OfflineRevocation">
      <summary>Gibt an, dass die Online-Zertifikatssperrliste, auf der die X509-Kette beruht, derzeit offline ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.PartialChain">
      <summary>Gibt an, dass die X509-Kette nicht bis zum Stammzertifikat erstellt werden konnte.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.RevocationStatusUnknown">
      <summary>Gibt an, dass nicht bestimmt werden kann, ob das Zertifikat widerrufen wurde.Möglicherweise ist die Zertifikatssperrliste offline oder nicht verfügbar.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Revoked">
      <summary>Gibt an, dass die X509-Kette aufgrund eines widerrufenen Zertifikats ungültig ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.UntrustedRoot">
      <summary>Gibt an, dass die X509-Kette aufgrund eines nicht vertrauenswürdigen Stammzertifikats ungültig ist.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ContentType">
      <summary>Gibt das Format eines X.509-Zertifikats an. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Authenticode">
      <summary>Ein Authenticode X.509-Zertifikat. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert">
      <summary>Ein einzelnes X.509-Zertifikat.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pfx">
      <summary>Ein PFX-formatiertes Zertifikat.Der Pfx-Wert ist identisch mit dem Pkcs12-Wert.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12">
      <summary>Ein PKCS #12–formatiertes Zertifikat.Der Pkcs12-Wert ist identisch mit dem Pfx-Wert.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs7">
      <summary>Ein PKCS #7–formatiertes Zertifikat.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert">
      <summary>Ein einzelnes serialisiertes X.509-Zertifikat. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedStore">
      <summary>Ein serialisierter Speicher.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Unknown">
      <summary>Ein unbekanntes X.509-Zertifikat.  </summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension">
      <summary>Definiert die Auflistung von OIDs (Object Identifier, Objektbezeichner), die die Anwendungen angibt, die den Schlüssel verwenden.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" />-Klasse mithilfe eines <see cref="T:System.Security.Cryptography.AsnEncodedData" />-Objekts und eines Werts, der angibt, ob die Erweiterung wichtig ist.</summary>
      <param name="encodedEnhancedKeyUsages">Die codierten Daten, aus denen die Erweiterung erstellt werden soll.</param>
      <param name="critical">true, wenn die Erweiterung wichtig ist, andernfalls false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.OidCollection,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" />-Klasse mithilfe einer <see cref="T:System.Security.Cryptography.OidCollection" /> und eines Werts, der angibt, ob die Erweiterung wichtig ist. </summary>
      <param name="enhancedKeyUsages">Eine <see cref="T:System.Security.Cryptography.OidCollection" />-Auflistung. </param>
      <param name="critical">true, wenn die Erweiterung wichtig ist, andernfalls false.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Die angegebene <see cref="T:System.Security.Cryptography.OidCollection" /> enthält einen oder mehrere fehlerhafte Werte.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" />-Klasse mit einem <see cref="T:System.Security.Cryptography.AsnEncodedData" />-Objekt.</summary>
      <param name="asnEncodedData">Die codierten Daten, aus denen die Erweiterung erstellt werden soll.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.EnhancedKeyUsages">
      <summary>Ruft die Auflistung von OIDs ab, die die Anwendungen angeben, die den Schlüssel verwenden.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.OidCollection" />-Objekt, das die Anwendungen angibt, die den Schlüssel verwenden.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Extension">
      <summary>Stellt eine X509-Erweiterung dar.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Klasse.</summary>
      <param name="encodedExtension">Die codierten Daten, aus denen die Erweiterung erstellt werden soll.</param>
      <param name="critical">true, wenn die Erweiterung kritisch ist, andernfalls false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.Oid,System.Byte[],System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Klasse.</summary>
      <param name="oid">Der Objektbezeichner, mit dem die Erweiterung identifiziert wird.</param>
      <param name="rawData">Die codierten Daten, aus denen die Erweiterung erstellt wird.</param>
      <param name="critical">true, wenn die Erweiterung kritisch ist, andernfalls false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oid" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="oid" /> ist eine leere Zeichenfolge ("").</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.String,System.Byte[],System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Klasse.</summary>
      <param name="oid">Eine Zeichenfolge, die den Objektbezeichner darstellt.</param>
      <param name="rawData">Die codierten Daten, aus denen die Erweiterung erstellt wird.</param>
      <param name="critical">true, wenn die Erweiterung kritisch ist, andernfalls false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Kopiert die Erweiterungseigenschaften des angegebenen <see cref="T:System.Security.Cryptography.AsnEncodedData" />-Objekts.</summary>
      <param name="asnEncodedData">Das zu kopierende <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asnEncodedData" /> besitzt keine gültige X.509-Erweiterung.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Extension.Critical">
      <summary>Ruft einen booleschen Wert ab, der angibt, ob die Erweiterung kritisch ist.</summary>
      <returns>true, wenn die Erweiterung wichtig ist, andernfalls false.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection">
      <summary>Stellt eine Auflistung von <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekten dar.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Klasse. </summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Add(System.Security.Cryptography.X509Certificates.X509Extension)">
      <summary>Fügt dem <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Objekt ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekt hinzu.</summary>
      <returns>Der Index, an dem der <paramref name="extension" />-Parameter hinzugefügt wurde.</returns>
      <param name="extension">Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekt, das dem <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Objekt hinzugefügt werden soll. </param>
      <exception cref="T:System.ArgumentNullException">Der Wert des <paramref name="extension" />-Parameters ist null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Extension[],System.Int32)">
      <summary>Kopiert eine Auflistung in ein Array, wobei am angegebenen Index begonnen wird.</summary>
      <param name="array">Ein Array von <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekten. </param>
      <param name="index">Die Position im Array, an der das Kopieren begonnen wird. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> ist eine Zeichenfolge mit der Länge 0 (null) oder enthält einen ungültigen Wert. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> gibt einen Wert an, der sich nicht im Bereich des Arrays befindet. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Count">
      <summary>Ruft die Anzahl der <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekte in einem <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Objekt ab.</summary>
      <returns>Eine ganze Zahl, die die Anzahl der <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekte im <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Objekt darstellt.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Objekt durchlaufen kann.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator" />-Objekt zum Durchlaufen des <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Objekts.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob die Threadsicherheit der Auflistung gewährleistet ist.</summary>
      <returns>true, wenn die Auflistung threadsicher ist, andernfalls false.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.Int32)">
      <summary>Ruft das <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekt am angegebenen Index ab.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekt.</returns>
      <param name="index">Der Speicherort des abzurufenden <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekts. </param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" /> ist kleiner als 0. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist größer oder gleich der Länge des Arrays. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.String)">
      <summary>Ruft das erste <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekt ab, dessen Wert oder angezeigter Name von einem Objektbezeichner (OID) angegeben wird.</summary>
      <returns>Ein <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekt.</returns>
      <param name="oid">Der Objektbezeichner (OID) der abzurufenden Erweiterung. </param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf das <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Objekt synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf das <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Objekt synchronisiert werden kann.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Auflistung in ein Array, wobei am angegebenen Index begonnen wird.</summary>
      <param name="array">Ein Array von <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" />-Objekten. </param>
      <param name="index">Die Position im Array, an der das Kopieren begonnen wird. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> ist eine Zeichenfolge mit der Länge 0 (null) oder enthält einen ungültigen Wert. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> gibt einen Wert an, der sich nicht im Bereich des Arrays befindet. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der ein <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Objekt durchlaufen kann.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />-Objekt zum Durchlaufen des <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Objekts.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator">
      <summary>Unterstützt eine einfache Iteration durch eine <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Current">
      <summary>Ruft das aktuelle Element in der <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />-Auflistung ab.</summary>
      <returns>Das aktuelle Element in der <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element in der <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft ein Objekt aus einer Auflistung ab.</summary>
      <returns>Das aktuelle Element in der <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509FindType">
      <summary>Gibt den Typ des Werts an, nach dem die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode sucht.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByApplicationPolicy">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss eine Zeichenfolge sein, die entweder den angezeigten Namen der Anwendungsrichtlinie oder den Objektbezeichner (OID oder <see cref="T:System.Security.Cryptography.Oid" />) des Zertifikats darstellt.Beispielsweise kann "Encrypting File System" oder "1.3.6.1.4.1.311.10.3.4" verwendet werden.Für eine Anwendung, die lokalisiert wird, muss der OID-Wert verwendet werden, da der Anzeigename lokalisiert wird.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByCertificatePolicy">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss eine Zeichenfolge sein, die den angezeigten Namen oder den Objektbezeichner (OID oder <see cref="T:System.Security.Cryptography.Oid" />) der Zertifikatsrichtlinie darstellt.Die optimale Methode besteht darin, den OID-Wert zu verwenden, z. B. "1.3.6.1.4.1.311.10.3.4".Für eine Anwendung, die lokalisiert wird, muss die OID verwendet werden, da der Anzeigename lokalisiert wird.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByExtension">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss eine Zeichenfolge sein, mit der die gesuchte Erweiterung beschrieben wird.Der Objektbezeichner (OID) wird meist dazu verwendet, die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode anzuweisen, nach allen Zertifikaten mit einer Erweiterung zu suchen, die dem betreffenden OID-Wert entspricht.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss eine Zeichenfolge sein, die den Distinguished Name des Ausstellers des Zertifikats darstellt.Dies ist eine bestimmtere Suche als die durch den <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" />-Enumerationswert bereitgestellte.Mit dem <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" />-Wert führt die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode einen Zeichenfolgenvergleich ohne Berücksichtigung von Groß- und Kleinschreibung für den gesamten Distinguished Name aus.Das Suchen nach Ausstellernamen ist ein weniger genaues Suchverfahren.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss eine Zeichenfolge sein, die den Namen des Ausstellers des Zertifikats darstellt.Dies ist eine weniger bestimmte Suche als die durch den <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" />-Enumerationswert bereitgestellte.Mit dem <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" />-Wert führt die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung unter Verwendung des angegebenen Werts aus.Wenn Sie beispielsweise "MyCA" an die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode übergeben, wird nach allen Zertifikaten mit einem Ausstellernamen gesucht, der diese Zeichenfolge enthält, ungeachtet anderer Werte für den Aussteller.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByKeyUsage">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss eine Zeichenfolge sein, die die Schlüsselverwendung darstellt, oder eine Ganzzahl, die eine Bitmaske mit allen angeforderten Schlüsselverwendungen enthält.Für den Zeichenfolgenwert kann nur jeweils eine Schlüsselverwendung angegeben werden, die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode kann jedoch in einer überlappenden Sequenz verwendet werden, um die Schnittmenge der angeforderten Verwendungen abzurufen.Beispielsweise kann der <paramref name="findValue" />-Parameter auf "KeyEncipherment" oder auf eine Ganzzahl (0x30 gibt "KeyEncipherment" und "DataEncipherment" an) festgelegt werden.Werte der <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" />-Enumeration können ebenfalls verwendet werden.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySerialNumber">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> Methode muss eine Zeichenfolge sein, die die Seriennummer des Zertifikats darstellt, wie vom Zertifikatsdialogfeld angezeigt, jedoch ohne die Leerzeichen, oder, wie von der <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumberString" />-Methode zurückgegeben. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss eine Zeichenfolge sein, die den Distinguished Name des Antragstellers des Zertifikats darstellt.Dies ist eine bestimmtere Suche als die durch den <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" />-Enumerationswert bereitgestellte.Mit dem <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" />-Wert führt die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode einen Zeichenfolgenvergleich ohne Berücksichtigung von Groß- und Kleinschreibung für den gesamten Distinguished Name aus.Das Suchen nach Antragstellernamen ist ein weniger genaues Suchverfahren.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectKeyIdentifier">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss eine Zeichenfolge sein, die den Zeichenfolgenbezeichner des Antragstellers in Hexadezimalschreibweise darstellt, z. B. "F3E815D45E83B8477B9284113C64EF208E897112", entsprechend der Anzeige auf der Benutzeroberfläche.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss eine Zeichenfolge sein, die den Namen des Antragstellers des Zertifikats darstellt.Dies ist eine weniger bestimmte Suche als die durch den <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" />-Enumerationswert bereitgestellte.Mit dem <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" />-Wert führt die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung unter Verwendung des angegebenen Werts aus.Wenn Sie beispielsweise "MyCert" an die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode übergeben, wird nach allen Zertifikaten mit einem Antragstellernamen gesucht, der diese Zeichenfolge enthält, ungeachtet anderer Werte für den Antragsteller.Die Suche anhand des Distinguished Name ist ein genaueres Verfahren.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTemplateName">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss eine Zeichenfolge sein, die den Vorlagennamen des Zertifikats darstellt, z. B. "ClientAuth".Ein Vorlagenname ist eine X509-Erweiterung, Version 3, in der die Zertifikatsverwendungen angegeben sind.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByThumbprint">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss eine Zeichenfolge sein, die den Fingerabdruck des Zertifikats darstellt.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss ein <see cref="T:System.DateTime" />-Wert für die Ortszeit sein.Alle Zertifikate, die bis zum Ende des Jahres gültig sind, können durch das Ausschließen der Ergebnisse eines <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Vorgangs für <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired" /> des letzten Tags des Jahres von den Ergebnissen eines <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Vorgangs für <see cref="P:System.DateTime.Now" /> gesucht werden.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss ein <see cref="T:System.DateTime" />-Wert für die Ortszeit sein.Der Wert muss nicht in der Zukunft liegen.Sie können z. B. mit <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" /> Zertifikate suchen, die im aktuellen Jahr gültig wurden, indem Sie die Schnittmenge der Ergebnisse eines <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Vorgangs für <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" /> für den letzten Tag des Jahres mit den Ergebnissen eines <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Vorgangs für <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid" /> von <see cref="P:System.DateTime.Now" /> nehmen.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid">
      <summary>Der <paramref name="findValue" />-Parameter für die <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" />-Methode muss ein <see cref="T:System.DateTime" />-Wert für die Ortszeit sein.Sie können alle gerade gültigen Zertifikate mithilfe von <see cref="P:System.DateTime.Now" /> suchen.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags">
      <summary>Definiert, wie und wo der private Schlüssel eines X.509-Zertifikats importiert wird.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.DefaultKeySet">
      <summary>Der Standardschlüsselsatz wird verwendet.  Der Benutzerschlüsselsatz stellt i. d. R. den Standard dar. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.Exportable">
      <summary>Importierte Schlüssel werden als exportierbar markiert.  </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.MachineKeySet">
      <summary>Private Schlüssel werden eher im lokalen Computerspeicher als im aktuellen Benutzerspeicher gespeichert. </summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.PersistKeySet">
      <summary>Der einer PFX-Datei zugeordnete Schlüssel bleibt beim Importieren von Zertifikaten erhalten.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserKeySet">
      <summary>Private Schlüssel werden eher im aktuellen Benutzerspeicher als im lokalen Computerspeicher gespeichert.Dies ist auch dann der Fall, wenn das Zertifikat angibt, dass die Schlüssel im lokalen Computerspeicher abgelegt werden sollen.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserProtected">
      <summary>Benachrichtigen Sie den Benutzer mithilfe eines Dialogfelds oder einer anderen Methode darüber, dass auf den Schlüssel zugegriffen wird.  Der verwendete CSP (Cryptographic Service Provider, Kryptografiedienstanbieter) definiert das genaue Verhalten.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension">
      <summary>Definiert die Verwendung eines im X.509-Zertifikat enthaltenen Schlüssels.  Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" />-Klasse mithilfe eines <see cref="T:System.Security.Cryptography.AsnEncodedData" />-Objekts und eines Werts, der angibt, ob die Erweiterung wichtig ist. </summary>
      <param name="encodedKeyUsage">Die codierten Daten, aus denen die Erweiterung erstellt werden soll.</param>
      <param name="critical">true, wenn die Erweiterung wichtig ist, andernfalls false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.X509Certificates.X509KeyUsageFlags,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" />-Klasse mithilfe des angegebenen <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" />-Werts und eines Werts, der angibt, ob die Erweiterung wichtig ist. </summary>
      <param name="keyUsages">Einer der <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" />-Werte, die die Verwendung des Schlüssels beschreiben.</param>
      <param name="critical">true, wenn die Erweiterung wichtig ist, andernfalls false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" />-Klasse unter Verwendung eines <see cref="T:System.Security.Cryptography.AsnEncodedData" />-Objekts. </summary>
      <param name="asnEncodedData">Die codierten Daten, aus denen die Erweiterung erstellt werden soll.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages">
      <summary>Ruft das Schlüsselverwendungsflag ab, das dem Zertifikat zugeordnet ist.</summary>
      <returns>Einer der <see cref="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages" />-Werte.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Die Erweiterung kann nicht decodiert werden. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags">
      <summary>Definiert die Verwendung des Zertifikatsschlüssels.Wenn dieser Wert nicht definiert ist, kann der Schlüssel beliebig verwendet werden.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.CrlSign">
      <summary>Der Schlüssel kann zum Signieren einer CRL (Certificate Revocation List, Zertifikatssperrliste) verwendet werden.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DataEncipherment">
      <summary>Der Schlüssel kann zur Datenverschlüsselung verwendet werden.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DecipherOnly">
      <summary>Der Schlüssel kann nur zur Entschlüsselung verwendet werden.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DigitalSignature">
      <summary>Der Schlüssel kann als digitale Signatur verwendet werden.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.EncipherOnly">
      <summary>Der Schlüssel kann nur zur Verschlüsselung verwendet werden.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyAgreement">
      <summary>Der Schlüssel kann verwendet werden, um die Schlüsselübereinstimmung zu bestimmen, z. B. ein mit dem Diffie-Hellman-Schlüsselübereinstimmungsalgorithmus erstellter Schlüssel.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyCertSign">
      <summary>Der Schlüssel kann zum Signieren von Zertifikaten verwendet werden.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyEncipherment">
      <summary>Der Schlüssel kann zur Schlüsselverschlüsselung verwendet werden.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.None">
      <summary>Keine Schlüsselverwendungsparameter.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.NonRepudiation">
      <summary>Der Schlüssel kann zur Authentifizierung verwendet werden.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509NameType">
      <summary>Gibt den Typ des Namens an, den das X509-Zertifikat enthält.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsFromAlternativeName">
      <summary>Der DNS-Name, der dem alternativen Namen des Antragstellers oder des Ausstellers eines X.509-Zertifikats zugeordnet ist.  Dieser Wert entspricht dem <see cref="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName" />-Wert.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName">
      <summary>Der DNS-Name, der dem alternativen Namen des Antragstellers oder des Ausstellers eines X509-Zertifikats zugeordnet ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.EmailName">
      <summary>Die E-Mail-Adresse, die einem Antragsteller bzw. Aussteller eines X509-Zertifikats zugeordnet ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.SimpleName">
      <summary>Der einfache Name eines Antragstellers oder Ausstellers eines X509-Zertifikats.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UpnName">
      <summary>Der Benutzerprinzipalname des Antragstellers oder Ausstellers eines X509-Zertifikats.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UrlName">
      <summary>Die URL-Adresse, die dem alternativen Namen des Antragstellers oder des Ausstellers eines X509-Zertifikats zugeordnet ist.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag">
      <summary>Gibt an, welche X509-Zertifikate in der Kette auf Sperrungen überprüft werden müssen.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EndCertificateOnly">
      <summary>Nur das Endzertifikat wird auf Sperrungen überprüft.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EntireChain">
      <summary>Die gesamte Zertifikatskette wird auf Sperrungen überprüft.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.ExcludeRoot">
      <summary>Die gesamte Kette, ausgenommen das Stammzertifikat, wird auf Sperrungen überprüft.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationMode">
      <summary>Gibt den Modus an, mit dessen Hilfe die Sperre von X509-Zertifikaten überprüft wird.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.NoCheck">
      <summary>Für das Zertifikat wird keine Sperrüberprüfung ausgeführt.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Offline">
      <summary>Eine Sperrüberprüfung wird mithilfe einer zwischengespeicherten Zertifikatssperrliste (CRL - Certificate Revocation List) ausgeführt.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Online">
      <summary>Eine Sperrüberprüfung wird mithilfe einer Online-Zertifikatssperrliste (CRL - Certificate Revocation List) ausgeführt.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Store">
      <summary>Stellt einen X.509-Speicher dar, der ein physikalischer Speicher ist, in dem Zertifikate erhalten bleiben und verwaltet werden.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" />-Klasse mithilfe der persönlichen Zertifikate des aktuellen Benutzerspeichers.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.Security.Cryptography.X509Certificates.StoreName,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" />-Klasse mithilfe des angegebenen <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" />-Werts und des <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" />-Werts.</summary>
      <param name="storeName">Einer der Enumerationswerte, der den Namen des X.509-Zertifikatspeichers angibt. </param>
      <param name="storeLocation">Einer der Enumerationswerte, der die Position des X.509-Zertifikatspeichers angibt. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> ist kein gültiger Speicherort, oder <paramref name="storeName" /> ist kein gültiger Name. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.String,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" />-Klasse mithilfe einer Zeichenfolge, die einen Wert aus der <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" />- und einen Wert aus der <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" />-Enumeration darstellt.</summary>
      <param name="storeName">Eine Zeichenfolge, die einen Wert aus der <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" />-Enumeration darstellt. </param>
      <param name="storeLocation">Einer der Enumerationswerte, der die Position des X.509-Zertifikatspeichers angibt. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> enthält ungültige Werte. </exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Fügt dem X.509-Zertifikatsspeicher ein Zertifikat hinzu.</summary>
      <param name="certificate">Das hinzuzufügende Zertifikat. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> ist null. </exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Das Zertifikat konnte dem Speicher nicht hinzugefügt werden.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Certificates">
      <summary>Gibt eine Auflistung von Zertifikaten in einem X.509-Zertifikatsspeicher zurück.</summary>
      <returns>Eine Auflistung mit Zertifikaten.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Dispose">
      <summary>Gibt die von dieser <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Location">
      <summary>Ruft den Speicherort des X.509-Zertifikatsspeichers ab.</summary>
      <returns>Der Speicherort des Zertifikatspeichers.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Name">
      <summary>Ruft den Namen des X.509-Zertifikatsspeichers ab.</summary>
      <returns>Der Name des Zertifikatspeichers.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)">
      <summary>Öffnet je nach <see cref="T:System.Security.Cryptography.X509Certificates.OpenFlags" />-Flageinstellungen einen X.509-Zertifikatsspeicher oder erstellt einen neuen Speicher.</summary>
      <param name="flags">Eine bitweise Kombination von Enumerationswerten, die das Verfahren zum Öffnen des X.509-Zertifikatspeicher angibt. </param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Der Speicher ist nicht lesbar. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
      <exception cref="T:System.ArgumentException">Der Speicher enthält ungültige Werte.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Entfernt ein Zertifikat aus dem X.509-Zertifikatsspeicher.</summary>
      <param name="certificate">Das zu entfernende Zertifikat.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> ist null. </exception>
      <exception cref="T:System.Security.SecurityException">Der Aufrufer verfügt nicht über die erforderliche Berechtigung. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension">
      <summary>Definiert eine Zeichenfolge, die die SKI (Subject Key Identifier, Schlüsselkennung des Antragstellers) eines Zertifikats angibt.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" />-Klasse.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Byte[],System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" />-Klasse mithilfe eines Bytearrays und eines Werts, der angibt, ob die Erweiterung wichtig ist.</summary>
      <param name="subjectKeyIdentifier">Ein Bytearray, das die Daten darstellt, mit denen die Erweiterung erstellt wird.</param>
      <param name="critical">true, wenn die Erweiterung wichtig ist, andernfalls false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" />-Klasse mithilfe codierter Daten und eines Werts, der angibt, ob die Erweiterung wichtig ist.</summary>
      <param name="encodedSubjectKeyIdentifier">Das zum Erstellen der Erweiterung zu verwendende <see cref="T:System.Security.Cryptography.AsnEncodedData" />-Objekt.</param>
      <param name="critical">true, wenn die Erweiterung wichtig ist, andernfalls false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" />-Klasse mithilfe eines öffentlichen Schlüssels und eines Werts, der angibt, ob die Erweiterung wichtig ist.</summary>
      <param name="key">Ein <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" />-Objekt, aus dem eine SKI erstellt werden soll. </param>
      <param name="critical">true, wenn die Erweiterung wichtig ist, andernfalls false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" />-Klasse mithilfe eines öffentlichen Schlüssels, eines Hashalgorithmusbezeichners und eines Werts, der angibt, ob die Erweiterung wichtig ist. </summary>
      <param name="key">Ein <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" />-Objekt, aus dem eine SKI erstellt werden soll.</param>
      <param name="algorithm">Einer der <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm" />-Werte, die den zu verwendenden Hashalgorithmus angeben.</param>
      <param name="critical">true, wenn die Erweiterung wichtig ist, andernfalls false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.String,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" />-Klasse mithilfe einer Zeichenfolge und eines Werts, der angibt, ob die Erweiterung wichtig ist.</summary>
      <param name="subjectKeyIdentifier">Eine im Hexadezimalformat codierte Zeichenfolge, die die SKI (Subject Key Identifier, Schlüsselkennung des Antragstellers) für ein Zertifikat darstellt.</param>
      <param name="critical">true, wenn die Erweiterung wichtig ist, andernfalls false.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Erstellt durch Kopieren von Informationen aus codierten Daten eine neue Instanz der <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" />-Klasse.</summary>
      <param name="asnEncodedData">Das zum Erstellen der Erweiterung zu verwendende <see cref="T:System.Security.Cryptography.AsnEncodedData" />-Objekt.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.SubjectKeyIdentifier">
      <summary>Ruft eine Zeichenfolge ab, die die SKI (Subject Key Identifier, Schlüsselkennung des Antragstellers) für ein Zertifikat darstellt.</summary>
      <returns>Eine im Hexadezimalformat codierte Zeichenfolge, die die Schlüsselkennung des Antragstellers darstellt.</returns>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Die Erweiterung kann nicht decodiert werden. </exception>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm">
      <summary>Definiert den Typ des Hashalgorithmus, der mit der <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" />-Klasse verwendet werden soll.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.CapiSha1">
      <summary>Die SKI besteht aus einem 160 Bits großen SHA-1-Hash des codierten öffentlichen Schlüssels (einschließlich Tag, Länge und Anzahl nicht verwendeter Bits).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.Sha1">
      <summary>Die SKI besteht aus dem 160 Bits großen SHA-1-Hash des Werts des öffentlichen Schlüssels (mit Ausnahme von Tag, Länge und Anzahl nicht verwendeter Bits).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.ShortSha1">
      <summary>Die SKI besteht aus einem 4-Bit-Typ-Feld mit dem Wert 0100, gefolgt von den 60 unwichtigsten Bits des SHA-1-Hash des Werts des öffentlichen Schlüssels (mit Ausnahme von Tag, Länge und Anzahl nicht verwendeter Zeichenfolgenbits).</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags">
      <summary>Gibt die Bedingungen an, unter denen die Überprüfung von Zertifikaten in der X509-Kette ausgeführt werden muss.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllFlags">
      <summary>Alle Flags werden berücksichtigt, die die Überprüfung betreffen.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllowUnknownCertificateAuthority">
      <summary>Es wird ignoriert, dass die Kette wegen einer unbekannten Zertifizierungsstelle nicht überprüft werden kann.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCertificateAuthorityRevocationUnknown">
      <summary>Beim Bestimmen der Zertifikatsüberprüfung wird ignoriert, dass die Zertifizierungsstellensperre unbekannt ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlNotTimeValid">
      <summary>Beim Bestimmen der Zertifikatsüberprüfung wird ignoriert, dass die Zertifikatsvertrauensliste ungültig ist, beispielsweise wegen des Ablaufs der Zertifikatsvertrauensliste.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlSignerRevocationUnknown">
      <summary>Beim Bestimmen der Zertifikatsüberprüfung wird ignoriert, dass die Signaturgebersperre der Zertifikatsvertrauensliste unbekannt ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreEndRevocationUnknown">
      <summary>Beim Bestimmen der Zertifikatsüberprüfung wird ignoriert, dass die Sperre des Endzertifikats (des Benutzerzertifikats) unbekannt ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidBasicConstraints">
      <summary>Beim Bestimmen der Zertifikatsüberprüfung wird ignoriert, dass die Basiseinschränkungen ungültig sind.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidName">
      <summary>Beim Bestimmen der Zertifikatsüberprüfung wird ignoriert, dass der Name des Zertifikats ungültig ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidPolicy">
      <summary>Beim Bestimmen der Zertifikatsüberprüfung wird ignoriert, dass das Zertifikat ungültige Richtlinien enthält.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeNested">
      <summary>Beim Überprüfen des Zertifikats wird ignoriert, dass das Zertifikat der Zertifizierungsstelle und das ausgestellte Zertifikat nicht geschachtelte Gültigkeitsperioden enthalten.Beispielsweise kann das Zertifikat der Zertifizierungsstelle vom 01. Januar bis zum 01. Dezember gültig sein, während das ausgestellte Zertifikat vom 02. Januar bis zum 02. Dezember gültig ist, d. h., die Gültigkeitsperioden sind nicht geschachtelt.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeValid">
      <summary>Beim Bestimmen der Zertifikatsgültigkeit wird ignoriert, dass Zertifikate in der Kette ungültig sind, weil sie abgelaufen oder noch nicht wirksam sind.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreRootRevocationUnknown">
      <summary>Beim Bestimmen der Zertifikatsüberprüfung wird ignoriert, dass die Stammsperre unbekannt ist.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreWrongUsage">
      <summary>Beim Bestimmen der Zertifikatsüberprüfung wird ignoriert, dass das Zertifikat nicht für die derzeitige Verwendung ausgestellt wurde.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag">
      <summary>Es werden keine Flags berücksichtigt, die die Überprüfung betreffen.</summary>
    </member>
  </members>
</doc>