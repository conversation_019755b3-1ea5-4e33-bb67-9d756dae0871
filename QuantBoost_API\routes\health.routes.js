const express = require('express');
const router = express.Router();
const { supabase } = require('../supabaseClient');
const logger = require('../utils/logger');

// Liveness probe - basic check if service is running
router.get('/live', (req, res) => {
  res.status(200).json({ status: 'alive', timestamp: new Date().toISOString() });
});

// Readiness probe - check dependencies
router.get('/ready', async (req, res) => {
  try {
    // Check Supabase connection
    const { error } = await supabase.from('profiles').select('count').limit(1);
    
    if (error) throw error;
    
    res.status(200).json({ 
      status: 'ready', 
      dependencies: {
        supabase: 'connected',
        database: 'accessible'
      },
      timestamp: new Date().toISOString() 
    });
  } catch (error) {
    logger.error('Health check failed', {
      error: error.message,
      stack: error.stack,
      requestId: req.requestId
    });
    
    res.status(503).json({ 
      status: 'not ready', 
      error: error.message,
      timestamp: new Date().toISOString() 
    });
  }
});

module.exports = router;