﻿// --- START OF FILE ThisAddIn.cs ---

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net; // For SecurityProtocolType
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms; // For Control class
using System.Text;
using System.Linq; // For debugging LINQ operations
using Office = Microsoft.Office.Core;
using Excel = Microsoft.Office.Interop.Excel;
using QuantBoost_Licensing; // For Licensing SDK
using QuantBoost_Excel.UI; // For Ribbon and UI components
using QuantBoost_Shared.UI; // For LicenseDialog and LicenseDetailsClient
// using QuantBoost_Excel.Analysis; // TODO: Create when needed
using QuantBoost_Shared.Utilities; // For ErrorHandlingService, AsyncHelper
using QuantBoost_Shared.Security; // Added for TokenStorage
using System.Net.Http; // Added for HttpClient
using Newtonsoft.Json; // Added for JSON serialization

namespace QuantBoost_Excel
{
    public partial class ThisAddIn
    {
        // --- Licensing ---
        internal QuantBoostLicensingManager _licensingManager; // Internal for access from Ribbon/UI if needed



        // --- Excel Trace ---
        private static QuantBoost_Excel.Features.ExcelTrace.UI.frmTrace _activeTraceForm = null;
        // This should be the LOGICAL Product ID your backend API uses for this specific Add-in.
        // It's NOT typically the Stripe Product ID (prod_...).
        internal const string PRODUCT_ID = "quantboost-suite"; // Made internal
        // API URL for local testing
        private string _apiBaseUrl = ConfigurationManager.AppSettings["ApiBaseUrl"]; // "http://localhost:3000" for dev testing
        private static readonly HttpClient httpClient = new HttpClient(); // Static instance for HttpClient

        // --- Ribbon ---
        private MainRibbon _ribbon; // Store reference to the ribbon instance

        // --- Global Cancellation & Task Management ---
        private CancellationTokenSource _globalCancellationTokenSource;
        private readonly List<Task> _backgroundTasks = new List<Task>();
        private readonly object _backgroundTaskLock = new object(); // For thread safety

        /// <summary>
        /// Provides a global cancellation token for long-running operations within the add-in.
        /// Cancelled during Add-in shutdown.
        /// </summary>
        public CancellationToken GlobalCancellationToken
        {
            get
            {
                if (_globalCancellationTokenSource == null)
                {
                    ErrorHandlingService.LogException(new InvalidOperationException("GlobalCancellationToken accessed before ThisAddIn_Startup completed."), "Addin State Error");
                    return new CancellationToken(true); // Safeguard: return cancelled token
                }
                return _globalCancellationTokenSource.Token;
            }
        }

        // Method to get API Base URL, potentially from config later
        public string GetApiBaseUrl()
        {
            // TODO: Implement logic to read from a config file or environment variable for flexibility
            return _apiBaseUrl;
        }

        //---------------------------------------------------------------------
        // VSTO Add-in Lifecycle Methods
        //---------------------------------------------------------------------
        #region VSTO Add-in Lifecycle Methods
        private void ThisAddIn_Startup(object sender, System.EventArgs e)
        {
            // Configure secure protocols for Azure communication
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;

            // Initialize Global Cancellation Source
            _globalCancellationTokenSource = new CancellationTokenSource();

            // Explicitly Install WindowsFormsSynchronizationContext
            if (System.Threading.SynchronizationContext.Current == null)
            {
                System.Diagnostics.Debug.WriteLine($"[INFO] ThisAddIn_Startup: SynchronizationContext is NULL. Explicitly installing WindowsFormsSynchronizationContext.");
                System.Threading.SynchronizationContext.SetSynchronizationContext(new System.Windows.Forms.WindowsFormsSynchronizationContext());

                // Verify it was set (optional debug)
                if (System.Threading.SynchronizationContext.Current is System.Windows.Forms.WindowsFormsSynchronizationContext)
                {
                    System.Diagnostics.Debug.WriteLine($"[INFO] ThisAddIn_Startup: WindowsFormsSynchronizationContext successfully installed.");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[WARNING] ThisAddIn_Startup: Failed to explicitly install WindowsFormsSynchronizationContext. Current is: {(System.Threading.SynchronizationContext.Current == null ? "NULL" : System.Threading.SynchronizationContext.Current.GetType().ToString())}");
                }
            }

            // --- START ASYNCHELPER DEBUG ---
            System.Diagnostics.Debug.WriteLine($"[DEBUG_SYNC] ThisAddIn_Startup: BEFORE AsyncHelper.Initialize(). Current Thread ID: {System.Threading.Thread.CurrentThread.ManagedThreadId}. Current SynchronizationContext is: {(System.Threading.SynchronizationContext.Current == null ? "NULL" : System.Threading.SynchronizationContext.Current.GetType().ToString())}");
            QuantBoost_Shared.Utilities.AsyncHelper.Initialize(); // Use fully qualified name
            System.Diagnostics.Debug.WriteLine($"[DEBUG_SYNC] ThisAddIn_Startup: AFTER AsyncHelper.Initialize(). Current SynchronizationContext is: {(System.Threading.SynchronizationContext.Current == null ? "NULL" : System.Threading.SynchronizationContext.Current.GetType().ToString())}");
            System.Diagnostics.Debug.WriteLine($"[DEBUG_SYNC] ThisAddIn_Startup: AsyncHelper.IsInitialized = {QuantBoost_Shared.Utilities.AsyncHelper.IsInitialized}"); // Assuming AsyncHelper has an IsInitialized property
            // --- END ASYNCHELPER DEBUG ---

            // Initialize Licensing Manager and attempt authentication
            try
            {
                _licensingManager = new QuantBoostLicensingManager(PRODUCT_ID, GetApiBaseUrl()); // Use GetApiBaseUrl()
                _licensingManager.LicenseStatusChanged += LicensingManager_LicenseStatusChanged;

                // Enhanced token storage diagnostics for Task 1.1.3
                ErrorHandlingService.LogException(null, "=== Token Storage Diagnostics ===");
                ErrorHandlingService.LogException(null, TokenStorage.GetDiagnosticInfo());

                // Validate DPAPI functionality
                bool dpapiValid = TokenStorage.ValidateDPAPI();
                ErrorHandlingService.LogException(null, $"DPAPI validation result: {(dpapiValid ? "SUCCESS" : "FAILED")}");

                if (!dpapiValid)
                {
                    ErrorHandlingService.LogException(null, "WARNING: DPAPI validation failed. Token storage may not work correctly.");
                }

                // Check for stored refresh token
                bool hasToken = TokenStorage.HasStoredToken();
                ErrorHandlingService.LogException(null, $"Stored token check: {(hasToken ? "TOKEN EXISTS" : "NO TOKEN")}");

                string storedRefreshToken = TokenStorage.RetrieveRefreshToken();
                if (!string.IsNullOrEmpty(storedRefreshToken))
                {
                    ErrorHandlingService.LogException(null, $"Found stored refresh token (length: {storedRefreshToken.Length}). Attempting silent login (async).");
                    // Fire and forget. If it fails, license status will be updated via LicenseStatusChanged event,
                    // and user may need to re-auth via dialog when interacting with a protected feature.
                    // Fire and forget the silent login attempt
                    Task.Run(async () =>
                    {
                        try
                        {
                            await AttemptSilentLoginWithRefreshTokenAsync(storedRefreshToken);
                        }
                        catch (Exception ex)
                        {
                            ErrorHandlingService.LogException(ex, "Silent Refresh Token Login failed");
                        }
                    });
                    // Key-based validation (ValidateLicenseInBackground) is skipped if a token was found,
                    // as token-based auth is now the primary path.
                }
                else
                {
                    if (hasToken)
                    {
                        ErrorHandlingService.LogException(null, "WARNING: Token file exists but retrieval returned null/empty. Possible corruption or decryption issue.");
                    }
                    ErrorHandlingService.LogException(null, "No valid stored refresh token found. Proceeding with stored key validation (async).");
                    ValidateLicenseInBackground(); // Initial check using stored key or prompting dialog
                }
            }
            catch (ArgumentNullException argEx) // Catch specific exceptions if helpful
            {
                ErrorHandlingService.HandleException(argEx, "Licensing Manager Initialization Failed: Product ID or API URL might be missing.");
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, "Failed to initialize Licensing Manager");
            }

            // Make ribbon accessible globally if needed
            // Globals.Ribbon is assigned in CreateRibbonExtensibilityObject via the helper method below
            // No need to assign here explicitly if using that pattern.


        }

        private void ThisAddIn_Shutdown(object sender, System.EventArgs e)
        {


            // --- Cancel and Cleanup Background Tasks ---
            try
            {
                if (_globalCancellationTokenSource != null)
                {
                    ErrorHandlingService.LogException(null, "Add-in shutdown initiated. Cancelling background tasks."); // Info log
                    _globalCancellationTokenSource.Cancel();
                    _globalCancellationTokenSource.Dispose();
                    _globalCancellationTokenSource = null;
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error during background task cancellation/cleanup on shutdown.");
            }
            lock (_backgroundTaskLock) // Clear task list
            {
                _backgroundTasks.Clear();
            }

            // --- Cleanup Licensing Manager ---
            if (_licensingManager != null)
            {
                _licensingManager.LicenseStatusChanged -= LicensingManager_LicenseStatusChanged;
                _licensingManager.Dispose();
                _licensingManager = null;
            }

            // --- Release Ribbon Reference ---
            _ribbon = null;
            Globals.Ribbon = null; // Clear global reference if used

            ErrorHandlingService.LogException(null, "Add-in shutdown complete."); // Info log
        }
        #endregion

        //---------------------------------------------------------------------
        // Ribbon Extensibility
        //---------------------------------------------------------------------
        #region - Ribbon Extensibility
        // This method is called by VSTO to get your ribbon implementation.
        // It also assigns the instance to Globals for easier access elsewhere.
        protected override Microsoft.Office.Core.IRibbonExtensibility CreateRibbonExtensibilityObject()
        {
            if (_ribbon == null)
            {
                _ribbon = new MainRibbon();
                // Assign to Globals immediately after creation
                Globals.Ribbon = _ribbon;
            }
            return _ribbon;
        }

        /// <summary>
        /// Centralized logic to launch the Excel Trace window for the currently active cell.
        /// This method is public so it can be called by Application.OnKey.
        /// </summary>
        public void LaunchTraceForActiveCell()
        {
            // TRIGGER LOG
            ErrorHandlingService.LogException(null, "[HOTKEY-DEBUG] LaunchTraceForActiveCell handler was successfully triggered.");
            try
            {
                Excel.Range activeCell = this.Application.ActiveCell;
                if (activeCell != null && activeCell.Count == 1)
                {
                    // Get current cell address for comparison
                    string currentCellAddress = $"[{((Excel.Workbook)activeCell.Worksheet.Parent).Name}]{activeCell.Worksheet.Name}!{activeCell.Address}";

                    // Check if a trace window is already open
                    if (_activeTraceForm != null && !_activeTraceForm.IsDisposed)
                    {
                        // Check if we're tracing the same cell
                        if (_activeTraceForm.CurrentCellAddress == currentCellAddress)
                        {
                            // Same cell - just activate the existing window
                            _activeTraceForm.Activate();
                            _activeTraceForm.WindowState = FormWindowState.Normal;
                            return;
                        }
                        else
                        {
                            // Different cell - update the trace data
                            var tracer = new QuantBoost_Excel.Features.ExcelTrace.Logic.TracerEngine();
                            var rootNode = tracer.BuildPrecedentTree(activeCell);
                            _activeTraceForm.UpdateTrace(rootNode, currentCellAddress);
                            _activeTraceForm.Activate();
                            _activeTraceForm.WindowState = FormWindowState.Normal;
                            return;
                        }
                    }

                    // No window open - create new one
                    var newTracer = new QuantBoost_Excel.Features.ExcelTrace.Logic.TracerEngine();
                    var newRootNode = newTracer.BuildPrecedentTree(activeCell);

                    // Create and show the form
                    _activeTraceForm = new QuantBoost_Excel.Features.ExcelTrace.UI.frmTrace();
                    _activeTraceForm.InitializeTrace(newRootNode, currentCellAddress);
                    _activeTraceForm.FormClosed += (s, e) => { _activeTraceForm = null; };
                    _activeTraceForm.Show(); // Use Show() for a non-modal window
                }
                else
                {
                    MessageBox.Show("Please select a single cell before tracing precedents.", "Selection Error", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                // HANDLER ERROR LOG
                QuantBoost_Shared.Utilities.ErrorHandlingService.HandleException(ex, "[HOTKEY-DEBUG] An error occurred INSIDE the hotkey handler.");
                MessageBox.Show($"Could not launch trace: {ex.Message}", "Trace Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        #endregion

        #region - Background Task Management
        //---------------------------------------------------------------------
        // Background Task Management
        //---------------------------------------------------------------------

        /// <summary>
        /// Registers a background task for tracking and potential cancellation via the global token.
        /// Automatically removes the task from tracking upon completion (success, fault, or cancellation).
        /// </summary>
        /// <param name="task">The task to register.</param>
        public void RegisterBackgroundTask(Task task)
        {
            if (_globalCancellationTokenSource == null || _globalCancellationTokenSource.IsCancellationRequested)
            {
                ErrorHandlingService.LogException(null, "Attempted to register background task after shutdown started or cancellation requested. Task not registered.");
                return;
            }

            if (task != null)
            {
                lock (_backgroundTaskLock)
                {
                    _backgroundTasks.Add(task);
                    // ErrorHandlingService.LogException(null, $"Background task registered. Current count: {_backgroundTasks.Count}"); // Verbose logging
                }

                task.ContinueWith(completedTask =>
                {
                    lock (_backgroundTaskLock)
                    {
                        _backgroundTasks.Remove(completedTask);
                        // ErrorHandlingService.LogException(null, $"Background task completed (Status: {completedTask.Status}). Current count: {_backgroundTasks.Count}"); // Verbose logging
                    }
                }, TaskScheduler.Default);
            }
        }
        #endregion

        //---------------------------------------------------------------------
        // Licensing Integration
        //---------------------------------------------------------------------
        #region - Licensing Integration
        /// <summary>
        /// Performs the initial license validation in the background.
        /// </summary>
        private void ValidateLicenseInBackground()
        {
            if (_licensingManager == null)
            {
                ErrorHandlingService.LogException(new InvalidOperationException("ValidateLicenseInBackground called but _licensingManager is null."), "Licensing Error");
                return;
            }
            // Use FireAndLog extension method for background tasks with logging
            Task validationTask = Task.Run(async () => // Use Task.Run
               {
                   ErrorHandlingService.LogException(null, "Performing initial license validation..."); // Info log
                   await _licensingManager.ValidateUsingStoredKeyAsync();
                   // Status update happens via the LicenseStatusChanged event handler
               });
            // Fire and forget the validation task with logging
            Task.Run(async () =>
            {
                try
                {
                    await validationTask;
                }
                catch (Exception ex)
                {
                    ErrorHandlingService.LogException(ex, "Initial Background License Validation failed");
                }
            });
        }


        /// <summary>
        /// Handles license status changes from the licensing manager.
        /// Updates the Ribbon UI accordingly (enabling/disabling features based on status).
        /// </summary>
        private void LicensingManager_LicenseStatusChanged(object sender, LicenseDetails licenseDetails)
        {
            ErrorHandlingService.LogException(null, $"License status changed: {licenseDetails?.LastKnownStatus ?? LicenseStatus.Unknown}"); // Info log

            // Ensure UI updates run on the main thread
            AsyncHelper.RunOnUIThread(() =>
            {
                // Update Ribbon UI based on the license status.
                // The ribbon's UpdateLicenseUI method should contain the logic
                // to enable/disable controls based on whether the license is considered active.
                if (Globals.Ribbon != null) // Use Globals.Ribbon for safety
                {
                    // Map the detailed SDK license info to the potentially simpler
                    // structure needed by the UI.
                    var clientDetails = MapToClientDetails(licenseDetails);

                    // The ribbon method handles enabling/disabling based on the mapped status.
                    Globals.Ribbon.UpdateLicenseUI(clientDetails);
                }
                else
                {
                    ErrorHandlingService.LogException(new InvalidOperationException("Ribbon reference in Globals is null during LicenseStatusChanged."), "UI Update Error");
                }
            });
        }

        // Added to allow LicenseDialog to inform ThisAddIn of a state change that requires UI refresh.
        // Specifically after logout from the dialog.
        public void TriggerLicenseStatusUpdateForRibbon()
        {
            if (_licensingManager != null)
            {
                 LicensingManager_LicenseStatusChanged(this, _licensingManager.CurrentLicense);
            }
        }

        // --- Helper Method to Map LicenseDetails ---
        // Maps the SDK's LicenseDetails to the UI's simplified LicenseDetailsClient
        public QuantBoost_Shared.UI.LicenseDetailsClient MapToClientDetails(LicenseDetails sdkDetails) // Made public
        {
            if (sdkDetails == null)
            {
                // Return a default state if sdkDetails is null, indicating no valid license info
                return new LicenseDetailsClient
                {
                    LastKnownStatus = QuantBoost_Shared.UI.LicenseStatusClient.Unknown,
                    IsSubscriptionEffectivelyActive = false,
                    Email = "-",
                    ExpiryDate = null, // Corrected from ExpiryUtc to ExpiryDate
                    LicenseKey = null,
                    LicenseTier = "-",
                    TrialDaysLeft = 0
                };
            }

            // Map to the simpler structure focused on overall status for UI enablement
            return new QuantBoost_Shared.UI.LicenseDetailsClient
            {
                // Core info for display/logic
                LastKnownStatus = (QuantBoost_Shared.UI.LicenseStatusClient)sdkDetails.LastKnownStatus,
                LicenseKey = sdkDetails.LicenseKey, // Or sdkDetails.Email if preferred for display
                Email = sdkDetails.Email,
                ExpiryDate = sdkDetails.ExpiryDateUtc, // Corrected from ExpiryUtc to ExpiryDate
                LicenseTier = sdkDetails.LicenseTier,
                TrialDaysLeft = sdkDetails.TrialDaysLeft,
                // Determine overall active state for UI enablement logic
                IsSubscriptionEffectivelyActive = sdkDetails.LastKnownStatus == LicenseStatus.Active
                                    || sdkDetails.LastKnownStatus == LicenseStatus.TrialActive
                                    || sdkDetails.LastKnownStatus == LicenseStatus.GracePeriod,
                // URLs for buttons
                ManageUrl = sdkDetails.ManageUrl,
                UpgradeUrl = sdkDetails.UpgradeUrl,
                // Optional fields if needed by UI text, etc.
                ActivationId = sdkDetails.ActivationId,
                ProductId = sdkDetails.ProductId,
                GracePeriodEndsUtc = null, // This would typically be calculated based on rules if needed
                                           // Features map is omitted as UI enables/disables all based on status
            };
        }

        // Maps the SDK's status enum to the UI's status enum
        private QuantBoost_Shared.UI.LicenseStatusClient MapToClientStatus(QuantBoost_Licensing.LicenseStatus sdkStatus)
        {
            switch (sdkStatus)
            {
                case LicenseStatus.Active: return QuantBoost_Shared.UI.LicenseStatusClient.Active;
                case LicenseStatus.TrialActive: return QuantBoost_Shared.UI.LicenseStatusClient.Trial;
                case LicenseStatus.GracePeriod: return QuantBoost_Shared.UI.LicenseStatusClient.GracePeriod;
                case LicenseStatus.Expired: return QuantBoost_Shared.UI.LicenseStatusClient.Expired;
                // Map error/inactive states
                case LicenseStatus.InvalidKey: return QuantBoost_Shared.UI.LicenseStatusClient.Invalid;
                case LicenseStatus.NotAssigned: return QuantBoost_Shared.UI.LicenseStatusClient.Invalid;
                case LicenseStatus.ActivationBlocked: return QuantBoost_Shared.UI.LicenseStatusClient.Invalid;
                case LicenseStatus.NetworkError: return QuantBoost_Shared.UI.LicenseStatusClient.NetworkError;
                case LicenseStatus.Unknown:
                default:
                    return QuantBoost_Shared.UI.LicenseStatusClient.Unknown;
            }
        }

        // private async Task AttemptSilentLoginWithRefreshTokenAsync(string refreshToken)
        private static readonly object _refreshLock = new object();
        private static DateTime _lastRefreshTime = DateTime.MinValue;

        private async Task<bool> AttemptSilentLoginWithRefreshTokenAsync(string refreshToken)
        {
            lock (_refreshLock)
            {
                // Prevent multiple refresh attempts within 10 seconds
                if ((DateTime.UtcNow - _lastRefreshTime).TotalSeconds < 10)
                {
                    ErrorHandlingService.LogException(null, "Skipping refresh - too soon after last attempt");
                    return false;
                }
                _lastRefreshTime = DateTime.UtcNow;
            }
            
            ErrorHandlingService.LogException(null, $"Attempting to refresh session with token: {refreshToken.Substring(0, Math.Min(refreshToken.Length, 10))}...");
            try
            {
                var requestData = new { refreshToken = refreshToken };
                string jsonRequestData = JsonConvert.SerializeObject(requestData);
                HttpContent content = new System.Net.Http.StringContent(jsonRequestData, System.Text.Encoding.UTF8, "application/json");

                HttpResponseMessage response = await httpClient.PostAsync($"{GetApiBaseUrl()}/v1/auth/refresh-token", content);

                if (response.IsSuccessStatusCode)
                {
                    string jsonResponse = await response.Content.ReadAsStringAsync();
                    ErrorHandlingService.LogException(null, $"Refresh token API response body: {jsonResponse}");
                    
                    // Parse as JObject for more reliable field access
                    var responseObj = Newtonsoft.Json.Linq.JObject.Parse(jsonResponse);
                    
                    // Check if response has success=true
                    bool success = responseObj["success"]?.ToObject<bool>() ?? false;
                    if (!success)
                    {
                        ErrorHandlingService.LogException(null, "Refresh token API returned success=false");
                        TokenStorage.ClearRefreshToken();
                        return false;
                    }
                    
                    // Extract tokens from data object
                    var dataObj = responseObj["data"] as Newtonsoft.Json.Linq.JObject;
                    if (dataObj == null)
                    {
                        ErrorHandlingService.LogException(null, "Refresh token API response missing 'data' object");
                        TokenStorage.ClearRefreshToken();
                        return false;
                    }
                    
                    string newAccessToken = dataObj["accessToken"]?.ToString();
                    string newRefreshToken = dataObj["refreshToken"]?.ToString();
                    
                    ErrorHandlingService.LogException(null, $"Parsed tokens - Access: {(!string.IsNullOrEmpty(newAccessToken) ? $"Present ({newAccessToken.Length} chars)" : "NULL/EMPTY")}, Refresh: {(!string.IsNullOrEmpty(newRefreshToken) ? $"Present ({newRefreshToken.Length} chars)" : "NULL/EMPTY")}");

                    if (string.IsNullOrEmpty(newAccessToken) || string.IsNullOrEmpty(newRefreshToken))
                    {
                        ErrorHandlingService.LogException(null, "One or both tokens are missing from API response");
                        TokenStorage.ClearRefreshToken();
                        return false;
                    }
                    
                    // Store the new refresh token
                    bool stored = TokenStorage.StoreRefreshToken(newRefreshToken);
                    ErrorHandlingService.LogException(null, $"Storing new refresh token ({newRefreshToken.Length} chars): {(stored ? "SUCCESS" : "FAILED")}");
                    
                    if (!stored)
                    {
                        ErrorHandlingService.LogException(null, "Failed to store new refresh token");
                        return false;
                    }

                    if (_licensingManager != null)
                    {
                        string deviceId = GetOrCreateDeviceId();
                        try
                        {
                            // This call will now throw AccessTokenExpiredException if the *new* token is immediately invalid
                            await _licensingManager.ValidateWithAccessTokenAsync(newAccessToken); // Corrected: removed PRODUCT_ID and deviceId
                            ErrorHandlingService.LogException(null, "Silent login: License validated successfully with new access token.");
                            return true; // Successfully refreshed and validated
                        }
                        catch (AccessTokenExpiredException tex)
                        {
                            ErrorHandlingService.LogException(tex, "Silent login: Newly refreshed access token is already invalid/expired. Clearing tokens.");
                            TokenStorage.ClearRefreshToken();
                            // Optionally, clear current license in _licensingManager
                            _licensingManager.ClearCurrentLicense(); 
                            LicensingManager_LicenseStatusChanged(this, _licensingManager.CurrentLicense); // Notify UI of change
                            return false;
                        }
                        catch (Exception valEx)
                        {
                            ErrorHandlingService.LogException(valEx, "Silent login: Error validating license with new access token. Clearing tokens.");
                            TokenStorage.ClearRefreshToken();
                            _licensingManager.ClearCurrentLicense();
                            LicensingManager_LicenseStatusChanged(this, _licensingManager.CurrentLicense);
                            return false;
                        }
                    }
                    else
                    {
                        ErrorHandlingService.LogException(new InvalidOperationException("LicensingManager not initialized when access token was refreshed."), "Silent Login Error");
                        return false;
                    }
                }
                else
                {
                    string errorBody = await response.Content.ReadAsStringAsync();
                    ErrorHandlingService.LogException(null, $"Failed to refresh token. Status: {response.StatusCode}, Body: {errorBody}. Clearing stored token.");
                    TokenStorage.ClearRefreshToken();
                    // ValidateLicenseInBackground(); // Fallback handled by caller
                     _licensingManager?.ClearCurrentLicense();
                    if (_licensingManager != null) LicensingManager_LicenseStatusChanged(this, _licensingManager.CurrentLicense);
                    return false;
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Exception during silent login with refresh token. Clearing token.");
                TokenStorage.ClearRefreshToken();
                // ValidateLicenseInBackground(); // Fallback handled by caller
                _licensingManager?.ClearCurrentLicense();
                if (_licensingManager != null) LicensingManager_LicenseStatusChanged(this, _licensingManager.CurrentLicense);
                return false;
            }
        }

        public async Task<T> ExecuteAuthenticatedApiCallAsync<T>(Func<Task<T>> apiCallDelegate, bool isLicenseValidationCall = false)
        {
            try
            {
                return await apiCallDelegate();
            }
            catch (AccessTokenExpiredException ex)
            {
                ErrorHandlingService.LogException(ex, "Access token expired. Attempting refresh.");
                string refreshToken = TokenStorage.RetrieveRefreshToken();

                if (!string.IsNullOrEmpty(refreshToken))
                {
                    bool refreshSuccess = await AttemptSilentLoginWithRefreshTokenAsync(refreshToken);
                    if (refreshSuccess)
                    {
                        ErrorHandlingService.LogException(null, "Token refresh successful. Retrying original API call.");
                        if (isLicenseValidationCall && typeof(T) == typeof(LicenseDetails))
                        {
                            return (T)(object)_licensingManager.CurrentLicense;
                        }
                        // For non-license validation calls, or if T is not LicenseDetails,
                        // retry the original delegate. The new access token is implicitly used by _licensingManager.
                        return await apiCallDelegate();
                    }
                    else
                    {
                        ErrorHandlingService.LogException(null, "Token refresh failed. Clearing token and prompting for re-authentication.");
                        TokenStorage.ClearRefreshToken();
                        _licensingManager?.ClearCurrentLicense();
                        if (_licensingManager != null) LicensingManager_LicenseStatusChanged(this, _licensingManager.CurrentLicense);
                        // ShowLicenseDialog(); // Trigger UI for re-authentication
                        throw new QuantBoost_Licensing.AuthenticationRequiredException("Refresh token is invalid or expired. Please log in again.", ex);
                    }
                }
                else
                {
                    ErrorHandlingService.LogException(null, "No refresh token found. Prompting for re-authentication.");
                     _licensingManager?.ClearCurrentLicense();
                    if (_licensingManager != null) LicensingManager_LicenseStatusChanged(this, _licensingManager.CurrentLicense);
                    // ShowLicenseDialog(); // Trigger UI for re-authentication
                    throw new AuthenticationRequiredException("No refresh token available. Please log in again.", ex);
                }
            }
            catch (AuthenticationRequiredException authEx) // Catch and rethrow to ensure it's handled upstream
            {
                ErrorHandlingService.LogException(authEx, "AuthenticationRequiredException caught in ExecuteAuthenticatedApiCallAsync. Re-throwing.");
                ShowLicenseDialog(); // Ensure dialog is shown if not already handled
                throw; // Re-throw the original exception
            }
            catch (Exception ex) // Catch other general exceptions
            {
                ErrorHandlingService.LogException(ex, "An unexpected error occurred during an authenticated API call.");
                // Decide if a generic AuthenticationRequiredException should be thrown or if the original should propagate
                // For now, let's re-throw the original to allow specific handling if needed, but log it.
                throw;
            }
        }


        // Method to get or create a unique device ID (placeholder)
        public string GetOrCreateDeviceId()
        {
            // TODO: Implement a robust way to get a unique and persistent device ID.
            // For example, using machine GUID, a value stored in registry, or a combination of hardware identifiers.
            // For now, a simple GUID will be used but it won't be persistent across add-in restarts in this form.
            // Consider storing it in user settings or registry after first generation.
            string deviceId = Properties.Settings.Default.DeviceId;
            if (string.IsNullOrEmpty(deviceId))
            {
                deviceId = Guid.NewGuid().ToString();
                Properties.Settings.Default.DeviceId = deviceId;
                Properties.Settings.Default.Save();
            }
            return deviceId;
        }

        public void ShowLicenseDialog()
        {
            AsyncHelper.RunOnUIThread(() =>
            {
                if (_licensingManager == null)
                {
                    ErrorHandlingService.LogException(new InvalidOperationException("Cannot show LicenseDialog: _licensingManager is null."), "UI Error");
                    // Consider a more user-friendly way to inform the user, specific to Office context if possible.
                    MessageBox.Show("Licensing system is not currently available. Please try restarting Excel or contact support if the issue persists.",
                                    "Licensing Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                string deviceId = GetOrCreateDeviceId();
                QuantBoost_Shared.UI.LicenseDetailsClient currentLicenseDetailsClient = MapToClientDetails(_licensingManager.CurrentLicense);

                // Pass the current license details to the dialog
                var licenseDialog = new QuantBoost_Shared.UI.LicenseDialog(currentLicenseDetailsClient, _licensingManager as IQuantBoostLicensingManager, PRODUCT_ID, deviceId);
                licenseDialog.TriggerLicenseStatusUpdate = TriggerLicenseStatusUpdateForRibbon;
                licenseDialog.GetApiBaseUrl = GetApiBaseUrl;
                licenseDialog.ShowDialog(); // This will block until the dialog is closed.
                // After the dialog closes, the license status might have changed (e.g., user logged in or out).
                // The LicenseStatusChanged event should handle UI updates automatically for login.
                // For logout initiated from the dialog, the dialog itself now calls Globals.ThisAddIn.LicensingManager_LicenseStatusChanged.
            });
        }

        /// <summary>
        /// Test method for Task 1.1.4: Tests the complete token persistence and automatic login flow.
        /// This method can be called to validate that tokens are properly stored and retrieved across Excel restarts.
        /// </summary>
        /// <returns>Diagnostic information about the test results</returns>
        public string TestTokenPersistenceFlow()
        {
            var result = new StringBuilder();
            result.AppendLine("=== Token Persistence Flow Test ===");
            result.AppendLine($"Test started at: {DateTime.Now}");

            try
            {
                // 1. Test DPAPI functionality
                result.AppendLine("\n1. Testing DPAPI functionality...");
                bool dpapiValid = TokenStorage.ValidateDPAPI();
                result.AppendLine($"   DPAPI validation: {(dpapiValid ? "PASS" : "FAIL")}");

                if (!dpapiValid)
                {
                    result.AppendLine("   ERROR: DPAPI validation failed. Token storage will not work.");
                    return result.ToString();
                }

                // 2. Test basic token storage
                result.AppendLine("\n2. Testing basic token storage...");
                string testToken = "test_refresh_token_" + DateTime.UtcNow.Ticks;
                bool storeResult = TokenStorage.StoreRefreshToken(testToken);
                result.AppendLine($"   Store test token: {(storeResult ? "PASS" : "FAIL")}");

                if (!storeResult)
                {
                    result.AppendLine("   ERROR: Failed to store test token.");
                    return result.ToString();
                }

                // 3. Test token retrieval
                result.AppendLine("\n3. Testing token retrieval...");
                string retrievedToken = TokenStorage.RetrieveRefreshToken();
                bool retrieveMatch = testToken.Equals(retrievedToken);
                result.AppendLine($"   Retrieve test token: {(retrieveMatch ? "PASS" : "FAIL")}");
                result.AppendLine($"   Original length: {testToken.Length}, Retrieved length: {retrievedToken?.Length ?? 0}");

                if (!retrieveMatch)
                {
                    result.AppendLine("   ERROR: Retrieved token doesn't match stored token.");
                    result.AppendLine($"   Expected: {testToken.Substring(0, Math.Min(20, testToken.Length))}...");
                    result.AppendLine($"   Got: {(retrievedToken?.Substring(0, Math.Min(20, retrievedToken?.Length ?? 0)) ?? "null")}...");
                }

                // 4. Test token clearing
                result.AppendLine("\n4. Testing token clearing...");
                bool clearResult = TokenStorage.ClearRefreshToken();
                result.AppendLine($"   Clear token: {(clearResult ? "PASS" : "FAIL")}");

                string clearedToken = TokenStorage.RetrieveRefreshToken();
                bool isCleared = string.IsNullOrEmpty(clearedToken);
                result.AppendLine($"   Verify cleared: {(isCleared ? "PASS" : "FAIL")}");

                // 5. Test licensing manager integration
                result.AppendLine("\n5. Testing licensing manager integration...");
                result.AppendLine($"   Licensing manager initialized: {(_licensingManager != null ? "YES" : "NO")}");

                if (_licensingManager != null)
                {
                    var currentLicense = _licensingManager.CurrentLicense;
                    result.AppendLine($"   Current license status: {currentLicense?.LastKnownStatus ?? LicenseStatus.Unknown}");
                    result.AppendLine($"   Current license email: {currentLicense?.Email ?? "None"}");
                }

                // 6. Test diagnostic information
                result.AppendLine("\n6. Token storage diagnostic information:");
                string diagnostics = TokenStorage.GetDiagnosticInfo();
                result.AppendLine(diagnostics);

                result.AppendLine("\n=== Test Summary ===");
                result.AppendLine($"DPAPI: {(dpapiValid ? "✓" : "✗")}");
                result.AppendLine($"Store: {(storeResult ? "✓" : "✗")}");
                result.AppendLine($"Retrieve: {(retrieveMatch ? "✓" : "✗")}");
                result.AppendLine($"Clear: {(clearResult && isCleared ? "✓" : "✗")}");

                bool overallSuccess = dpapiValid && storeResult && retrieveMatch && clearResult && isCleared;
                result.AppendLine($"\nOverall Result: {(overallSuccess ? "SUCCESS" : "FAILURE")}");

            }
            catch (Exception ex)
            {
                result.AppendLine($"\nERROR during test: {ex.Message}");
                result.AppendLine($"Exception type: {ex.GetType().Name}");
                result.AppendLine($"Stack trace: {ex.StackTrace}");
            }

            string finalResult = result.ToString();
            ErrorHandlingService.LogException(null, "Token Persistence Flow Test Results:\n" + finalResult);

            return finalResult;
        }
        #endregion

        //---------------------------------------------------------------------
        // VSTO Generated Code
        //---------------------------------------------------------------------

        #region VSTO generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InternalStartup()
        {
            this.Startup += new System.EventHandler(ThisAddIn_Startup);
            this.Shutdown += new System.EventHandler(ThisAddIn_Shutdown);
        }

        #endregion
    }

    // --- Helper Classes Used in ThisAddIn ---

    // Extend the VSTO-generated Globals class to add our custom Ribbon property
    internal sealed partial class Globals
    {
        // Provide global access to the Ribbon instance, assigned in CreateRibbonExtensibilityObject
        public static MainRibbon Ribbon { get; internal set; }
    }
}
// --- END OF FILE ThisAddIn.cs ---
