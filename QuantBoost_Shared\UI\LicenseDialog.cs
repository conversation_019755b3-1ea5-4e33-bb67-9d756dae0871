// Constraints: .NET Framework 4.8.1, C# 7.3

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Drawing;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;
using QuantBoost_Licensing;
using QuantBoost_Shared.Security;
using QuantBoost_Shared.Utilities;

namespace QuantBoost_Shared.UI
{
    public class LicenseDialog : Form
    {
        // --- UI Controls ---
        private Label _statusLabel;
        private Button _closeButton;
        private TextBox _emailTextBox;
        private Button _continueButton;
        private Label _otpLabel;
        private TextBox _otpTextBox;
        private Button _verifyOtpButton;
        private Label _loggedInEmailLabel;
        private Label _loggedInStatusLabel;
        private Button _logoutButton;

        // --- Services & State ---
        private MagicLinkAuthReceiver _magicLinkAuthReceiver;
        private CancellationTokenSource _cts;
        private readonly IQuantBoostLicensingManager _licensingManager;
        private readonly string _productId;
        private readonly string _deviceId;
        private bool _isAuthenticatedOnOpen = false;
        private readonly object _cleanupLock = new object(); // Lock for thread-safe cleanup

        // Delegate for triggering license status updates and API base URL retrieval
        public delegate void LicenseStatusUpdateDelegate();
        public delegate string GetApiBaseUrlDelegate();
        
        public LicenseStatusUpdateDelegate TriggerLicenseStatusUpdate { get; set; }
        public GetApiBaseUrlDelegate GetApiBaseUrl { get; set; }

        public LicenseDialog(LicenseDetailsClient license, IQuantBoostLicensingManager licensingManager, string productId, string deviceId)
        {
            Text = "QuantBoost License";
            int baseWidth = 540;
            
            // Handle high DPI scaling for width only
            using (Graphics g = CreateGraphics())
            {
                float dpiX = g.DpiX;
                if (dpiX > 96)
                {
                    float scaleFactor = dpiX / 96f;
                    baseWidth = (int)(baseWidth * Math.Min(scaleFactor, 1.5f)); // Cap scaling at 150%
                }
            }
            
            Width = baseWidth;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            StartPosition = FormStartPosition.CenterParent;
            BackColor = Color.FromArgb(250, 250, 250);

            _statusLabel = new Label
            {
                Text = "Enter your email address to authenticate or manage your QuantBoost license.",
                Left = 30,
                Top = 30,
                Width = ClientSize.Width - 60,
                // --- FIX: Reduced height for a single line and centered text ---
                Height = 40, 
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(87, 87, 87),
                TextAlign = ContentAlignment.MiddleCenter, // Center the text
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right, // Make it robust
                BackColor = Color.Transparent
            };

            _emailTextBox = new TextBox
            {
                Left = 30,
                Top = _statusLabel.Bottom + 20,
                Width = ClientSize.Width - 60,
                Height = 35,
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                ForeColor = Color.FromArgb(33, 33, 33)
            };

            _emailTextBox.Text = "<EMAIL>";
            _emailTextBox.ForeColor = Color.FromArgb(128, 128, 128);

            _emailTextBox.GotFocus += (s, e) =>
            {
                if (_emailTextBox.Text == "<EMAIL>")
                {
                    _emailTextBox.Text = "";
                    _emailTextBox.ForeColor = Color.FromArgb(33, 33, 33);
                }
            };

            _emailTextBox.LostFocus += (s, e) =>
            {
                if (string.IsNullOrWhiteSpace(_emailTextBox.Text))
                {
                    _emailTextBox.Text = "<EMAIL>";
                    _emailTextBox.ForeColor = Color.FromArgb(128, 128, 128);
                }
            };

            _continueButton = new Button
            {
                Text = "Login with Link",
                Width = 200,
                Height = 40,
                Top = _emailTextBox.Bottom + 25,
                Left = (ClientSize.Width - 200) / 2,
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            _continueButton.FlatAppearance.BorderSize = 0;
            _continueButton.Click += ContinueButton_Click;

            _otpLabel = new Label
            {
                Text = "Or enter your 6-digit code below:",
                Left = 30,
                Top = _continueButton.Bottom + 30,
                Width = ClientSize.Width - 60,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(87, 87, 87),
                TextAlign = ContentAlignment.MiddleLeft,
                BackColor = Color.Transparent
            };

            _otpTextBox = new TextBox
            {
                Left = 30,
                Top = _otpLabel.Bottom + 15,
                Width = ClientSize.Width - 60,
                Height = 35,
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                ForeColor = Color.FromArgb(33, 33, 33),
                MaxLength = 6,
                TextAlign = HorizontalAlignment.Center
            };

            _verifyOtpButton = new Button
            {
                Text = "Verify Code",
                Width = 140,
                Height = 36,
                Top = _otpTextBox.Bottom + 20,
                Left = (ClientSize.Width - 140) / 2,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(243, 243, 243),
                ForeColor = Color.FromArgb(33, 33, 33),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            _verifyOtpButton.FlatAppearance.BorderColor = Color.FromArgb(173, 173, 173);
            _verifyOtpButton.FlatAppearance.BorderSize = 1;
            _verifyOtpButton.Click += VerifyOtpButton_Click;

            _closeButton = new Button
            {
                Text = "Close",
                Width = 100,
                Height = 36,
                Left = (ClientSize.Width - 100) / 2,
                DialogResult = DialogResult.OK,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(243, 243, 243),
                ForeColor = Color.FromArgb(33, 33, 33),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            _closeButton.FlatAppearance.BorderColor = Color.FromArgb(173, 173, 173);
            _closeButton.FlatAppearance.BorderSize = 1;

            _loggedInEmailLabel = new Label
            {
                Left = 30,
                Top = _statusLabel.Bottom + 20,
                Width = ClientSize.Width - 60,
                Height = 50,
                Font = new Font("Segoe UI", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(33, 33, 33),
                BackColor = Color.Transparent,
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleLeft,
                Visible = false
            };

            _loggedInStatusLabel = new Label
            {
                Left = 30,
                Top = _loggedInEmailLabel.Bottom + 20,
                Width = ClientSize.Width - 60,
                Height = 70,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(87, 87, 87),
                BackColor = Color.Transparent,
                AutoSize = false,
                TextAlign = ContentAlignment.TopLeft,
                Visible = false
            };

            _logoutButton = new Button
            {
                Text = "Logout",
                Width = 140,
                Height = 36,
                Top = _loggedInStatusLabel.Bottom + 30,
                Left = (ClientSize.Width - 140) / 2,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Visible = false
            };
            _logoutButton.FlatAppearance.BorderSize = 0;
            _logoutButton.Click += LogoutButton_Click;

            Controls.Add(_statusLabel);
            Controls.Add(_emailTextBox);
            Controls.Add(_continueButton);
            Controls.Add(_otpLabel);
            Controls.Add(_otpTextBox);
            Controls.Add(_verifyOtpButton);
            Controls.Add(_loggedInEmailLabel);
            Controls.Add(_loggedInStatusLabel);
            Controls.Add(_logoutButton);
            Controls.Add(_closeButton);

            _licensingManager = licensingManager;
            _productId = productId;
            _deviceId = deviceId;

            string storedRefreshToken = TokenStorage.RetrieveRefreshToken();
            _isAuthenticatedOnOpen = !string.IsNullOrEmpty(storedRefreshToken) &&
                                   !string.IsNullOrEmpty(license?.Email) &&
                                   license.Email != "-";

            UpdateUIVisibility(_isAuthenticatedOnOpen, license);

            ActiveControl = _isAuthenticatedOnOpen ? (Control)_logoutButton : _emailTextBox;
        }

        private void UpdateUIVisibility(bool isAuthenticated, LicenseDetailsClient currentLicense)
        {
            if (isAuthenticated)
            {
                _statusLabel.Text = "You are currently authenticated.";
                _statusLabel.ForeColor = Color.FromArgb(87, 87, 87);
                _emailTextBox.Visible = false;
                _continueButton.Visible = false;
                _otpLabel.Visible = false;
                _otpTextBox.Visible = false;
                _verifyOtpButton.Visible = false;

                _loggedInEmailLabel.Text = $"Email: {currentLicense?.Email ?? "N/A"}";
                _loggedInEmailLabel.ForeColor = Color.FromArgb(33, 33, 33);
                _loggedInEmailLabel.Visible = true;

                string statusText = $"Status: {currentLicense?.LastKnownStatus.ToString() ?? "Unknown"}";
                if (currentLicense?.ExpiryDate.HasValue == true)
                {
                    statusText += $" (Expires: {currentLicense.ExpiryDate.Value.ToLocalTime().ToShortDateString()})";
                }
                else if (currentLicense?.LastKnownStatus == LicenseStatusClient.Trial && currentLicense?.TrialDaysLeft.HasValue == true)
                {
                    statusText += $" ({currentLicense.TrialDaysLeft} trial days left)";
                }
                _loggedInStatusLabel.Text = statusText;
                _loggedInStatusLabel.ForeColor = Color.FromArgb(87, 87, 87);
                _loggedInStatusLabel.Visible = true;

                _logoutButton.Visible = true;
                _closeButton.Top = _logoutButton.Bottom + 25;
                Height = _closeButton.Bottom + 70;
            }
            else
            {
                _statusLabel.Text = "Enter your email address to authenticate or manage your QuantBoost license.";
                _emailTextBox.Visible = true;
                _continueButton.Visible = true;
                _otpLabel.Visible = true;
                _otpTextBox.Visible = true;
                _verifyOtpButton.Visible = true;

                _loggedInEmailLabel.Visible = false;
                _loggedInStatusLabel.Visible = false;
                _logoutButton.Visible = false;

                _closeButton.Top = _verifyOtpButton.Bottom + 20;
                Height = _closeButton.Bottom + 70;
            }
        }

        private void LogoutButton_Click(object sender, EventArgs e)
        {
            _logoutButton.Enabled = false;
            _statusLabel.Text = "Logging out...";

            // The server-side logout API call is removed because the client does not persist the short-lived access token
            // required by the server's /logout endpoint. The most critical part of logout is clearing the local
            // refresh token, which effectively logs the user out of this client instance.
            Debug.WriteLine("[LicenseDialog] Performing local logout by clearing tokens.");

            TokenStorage.ClearRefreshToken();
            _licensingManager.ClearCurrentLicense();

            // Trigger license status update through delegate
            TriggerLicenseStatusUpdate?.Invoke();

            MessageBox.Show("You have been logged out.", "Logout Successful", MessageBoxButtons.OK, MessageBoxIcon.Information);

            UpdateUIVisibility(false, null); // Switch to unauthenticated view
            _emailTextBox.Text = "<EMAIL>";
            _emailTextBox.ForeColor = Color.FromArgb(128, 128, 128);
            _otpTextBox.Text = "";
            _statusLabel.Text = "Enter your email address to authenticate or manage your QuantBoost license.";
            
            // The logout button is now hidden by UpdateUIVisibility, so no need to re-enable it.
            ActiveControl = _emailTextBox;
        }

        private async void ContinueButton_Click(object sender, EventArgs e)
        {
            string email = _emailTextBox.Text.Trim();

            if (string.IsNullOrWhiteSpace(email) || !IsValidEmail(email) || email == "<EMAIL>")
            {
                MessageBox.Show("Please enter a valid email address.", "Invalid Email", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _emailTextBox.Focus();
                return;
            }

            _continueButton.Enabled = false;
            _emailTextBox.Enabled = false;
            _statusLabel.Text = "Initiating magic link process...";

            try
            {
                // Ensure we have a valid cancellation token source
                if (_cts != null)
                {
                    CleanUpAuthReceiver();
                }
                _cts = new CancellationTokenSource();
                
                if (_magicLinkAuthReceiver != null)
                {
                    CleanUpAuthReceiver();
                }
                _magicLinkAuthReceiver = new MagicLinkAuthReceiver();

                _statusLabel.Text = "Starting local listener...";
                string listenerRedirectUri = _magicLinkAuthReceiver.ListeningUrl;

                if (string.IsNullOrEmpty(listenerRedirectUri))
                {
                    MessageBox.Show("Failed to initialize local authentication listener. The redirect URI is invalid.", "Listener Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    CleanUpAuthReceiver();
                    ResetToInitialState("Listener initialization failed. Please try again.");
                    return;
                }
                Debug.WriteLine($"[LicenseDialog] MagicLinkAuthReceiver will listen on: {listenerRedirectUri}");

                Task<Dictionary<string, string>> listeningTask = _magicLinkAuthReceiver.StartListeningAsync(_cts);

                _statusLabel.Text = "Requesting magic link from server...";
                
                bool requestSent = false;
                try
                {
                    // Use the manager to make the API call. It will use its own configured HttpClient.
                    // Pass the redirect URI to support the magic link flow
                    requestSent = await _licensingManager.RequestMagicLinkAsync(email, listenerRedirectUri);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[LicenseDialog] Exception from RequestMagicLinkAsync: {ex.ToString()}");
                    MessageBox.Show($"An unexpected error occurred while requesting the magic link: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    CleanUpAuthReceiver();
                    ResetToInitialState("Magic link request failed.");
                    return;
                }

                if (requestSent)
                {
                    _statusLabel.Text = "Magic link sent! Check your email and click the link, or enter the 6-digit code below.";
                    Debug.WriteLine("[LicenseDialog] Magic link request successful. Waiting for callback.");

                    try
                    {
                        Dictionary<string, string> tokens = await listeningTask;

                        _statusLabel.Text = "Authentication successful! Processing tokens...";
                        Debug.WriteLine($"[LicenseDialog] Tokens received via listener. AccessToken: {tokens.ContainsKey("access_token")}, RefreshToken: {tokens.ContainsKey("refresh_token")}");

                        string accessToken = tokens.TryGetValue("access_token", out var at) ? at : null;
                        string refreshToken = tokens.TryGetValue("refresh_token", out var rt) ? rt : null;

                        if (string.IsNullOrEmpty(accessToken) || string.IsNullOrEmpty(refreshToken))
                        {
                            MessageBox.Show("Authentication callback received, but essential tokens are missing.", "Token Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            ResetToInitialState("Token retrieval failed post-callback.");
                            return;
                        }

                        TokenStorage.StoreRefreshToken(refreshToken);
                        LicenseStatus validationStatus = await _licensingManager.ValidateWithAccessTokenAsync(accessToken);
                        var sdkLicenseDetails = _licensingManager.CurrentLicense;
                        LicenseDetailsClient currentLicenseDetails = null;
                        if (sdkLicenseDetails != null)
                        {
                            currentLicenseDetails = new LicenseDetailsClient
                            {
                                LicenseKey = sdkLicenseDetails.LicenseKey,
                                Email = sdkLicenseDetails.Email,
                                LastKnownStatus = (LicenseStatusClient)sdkLicenseDetails.LastKnownStatus,
                                IsSubscriptionEffectivelyActive = sdkLicenseDetails.IsValid,
                                ExpiryDate = sdkLicenseDetails.ExpiryDateUtc,
                                LicenseTier = sdkLicenseDetails.LicenseTier,
                                TrialDaysLeft = sdkLicenseDetails.TrialDaysLeft,
                                StatusMessage = sdkLicenseDetails.ResultMessage,
                                ManageUrl = sdkLicenseDetails.ManageUrl,
                                UpgradeUrl = sdkLicenseDetails.UpgradeUrl,
                                ActivationId = sdkLicenseDetails.ActivationId,
                                ProductId = sdkLicenseDetails.ProductId
                            };
                        }

                        if (validationStatus == LicenseStatus.Active || validationStatus == LicenseStatus.TrialActive || validationStatus == LicenseStatus.GracePeriod)
                        {
                            await HandleSuccessfulAuthentication();
                        }
                        else
                        {
                            string errorMessage = currentLicenseDetails?.StatusMessage ?? "Failed to validate license with new token.";
                            _statusLabel.Text = $"Token validation failed: {errorMessage}";
                            MessageBox.Show(errorMessage, "Token Validation Failed", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            ResetToInitialState("Token validation failed. Please try again.");
                        }
                    }
                    catch (TaskCanceledException)
                    {
                        Debug.WriteLine("[LicenseDialog] Listening task was canceled.");
                        _statusLabel.Text = "Authentication canceled or timed out.";
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"[LicenseDialog] Error from MagicLinkAuthReceiver task: {ex.ToString()}");
                        MessageBox.Show($"An error occurred with the local authentication listener: {ex.Message}", "Listener Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        ResetToInitialState("Authentication listener error. Please try again.");
                    }
                }
                else
                {
                    MessageBox.Show("Failed to request magic link. Please check the logs or try again.", "API Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    CleanUpAuthReceiver();
                    ResetToInitialState("Failed to send magic link. Please try again.");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[LicenseDialog] Outer catch block in ContinueButton_Click: {ex.ToString()}");
                MessageBox.Show($"An unexpected error occurred during the magic link process: {ex.Message}", "Critical Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                CleanUpAuthReceiver();
                ResetToInitialState("Critical error. Please try again or contact support.");
            }
        }
        
        private async Task HandleSuccessfulAuthentication()
        {
            // 1. Provide clear visual feedback within the dialog.
            _statusLabel.Text = "Success! Your license is now active.";
            _statusLabel.ForeColor = Color.FromArgb(21, 118, 0); // A dark, professional green.

            // 2. Disable all controls to prevent user interaction during the auto-close sequence.
            _continueButton.Enabled = false;
            _verifyOtpButton.Enabled = false;
            _closeButton.Enabled = false;
            _emailTextBox.Enabled = false;
            _otpTextBox.Enabled = false;

            // 3. Update the main application's ribbon through delegate
            TriggerLicenseStatusUpdate?.Invoke();

            // 4. Pause for 1.5 seconds so the user can read the message.
            await Task.Delay(1500);

            // 5. Set the dialog result and close the form.
            DialogResult = DialogResult.OK;
            Close();
        }
        
        private class ErrorResponse { public ErrorDetail error { get; set; } }
        private class ErrorDetail { public string message { get; set; } }

        private void ResetToInitialState(string statusMessagePrefix = null)
        {
            _continueButton.Enabled = true;
            _verifyOtpButton.Enabled = true;
            _emailTextBox.Enabled = true;
            _otpTextBox.Enabled = true;
            _statusLabel.Text = string.IsNullOrEmpty(statusMessagePrefix) ?
                                "Enter your email address to authenticate or manage your QuantBoost license." :
                                $"{statusMessagePrefix} You can try again or enter an OTP if available.";

            if (_isAuthenticatedOnOpen)
            {
                UpdateUIVisibility(false, null);
            }
        }

        private void CleanUpAuthReceiver()
        {
            // This lock ensures only one thread can execute this cleanup logic at a time, preventing race conditions.
            lock (_cleanupLock)
            {
                Debug.WriteLine("[LicenseDialog] CleanUpAuthReceiver called.");
                if (_cts != null)
                {
                    if (!_cts.IsCancellationRequested)
                    {
                        _cts.Cancel();
                    }
                    _cts.Dispose();
                    _cts = null;
                }

                if (_magicLinkAuthReceiver != null)
                {
                    _magicLinkAuthReceiver.Dispose();
                    _magicLinkAuthReceiver = null;
                }
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            CleanUpAuthReceiver();
            base.OnFormClosed(e);
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private string GetStatusText(LicenseDetailsClient license)
        {
            if (license == null || license.LastKnownStatus == LicenseStatusClient.Unknown)
            {
                return "License status is unknown or not yet validated.";
            }

            string status = $"Status: {license.LastKnownStatus}";
            if (license.ExpiryDate.HasValue)
            {
                status += $" (Expires: {license.ExpiryDate.Value.ToLocalTime().ToShortDateString()})";
            }
            else if (license.LastKnownStatus == LicenseStatusClient.Trial && license.TrialDaysLeft.HasValue)
            {
                status += $" ({license.TrialDaysLeft} trial days left)";
            }
            return status;
        }

        private async void VerifyOtpButton_Click(object sender, EventArgs e)
        {
            string email = _emailTextBox.Text.Trim();
            string otp = _otpTextBox.Text.Trim();

            if (string.IsNullOrWhiteSpace(email) || !IsValidEmail(email) || email == "<EMAIL>")
            {
                MessageBox.Show("Please enter a valid email address.", "Invalid Email", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _emailTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(otp) || otp.Length != 6 || !System.Text.RegularExpressions.Regex.IsMatch(otp, "^\\d{6}$"))
            {
                MessageBox.Show("Please enter a valid 6-digit OTP code.", "Invalid OTP", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _otpTextBox.Focus();
                return;
            }

            _verifyOtpButton.Enabled = false;
            _emailTextBox.Enabled = false;
            _otpTextBox.Enabled = false;
            _statusLabel.Text = "Verifying OTP code...";

            try
            {
                // Use the licensing manager to verify OTP - consistent with magic link flow
                var (success, accessToken, refreshToken) = await _licensingManager.VerifyOtpAsync(email, otp);

                if (success && !string.IsNullOrEmpty(accessToken) && !string.IsNullOrEmpty(refreshToken))
                {
                    _statusLabel.Text = "OTP verified. Validating license...";
                    TokenStorage.StoreRefreshToken(refreshToken);
                    Debug.WriteLine("[LicenseDialog] Refresh token stored after OTP verification.");

                    LicenseStatus validationStatus = await _licensingManager.ValidateWithAccessTokenAsync(accessToken);

                    var sdkLicenseDetails = _licensingManager.CurrentLicense;
                    LicenseDetailsClient currentLicenseDetails = null;
                    if (sdkLicenseDetails != null)
                    {
                        currentLicenseDetails = new LicenseDetailsClient
                        {
                            LicenseKey = sdkLicenseDetails.LicenseKey,
                            Email = sdkLicenseDetails.Email,
                            LastKnownStatus = (LicenseStatusClient)sdkLicenseDetails.LastKnownStatus,
                            IsSubscriptionEffectivelyActive = sdkLicenseDetails.IsValid,
                            ExpiryDate = sdkLicenseDetails.ExpiryDateUtc,
                            LicenseTier = sdkLicenseDetails.LicenseTier,
                            TrialDaysLeft = sdkLicenseDetails.TrialDaysLeft,
                            StatusMessage = sdkLicenseDetails.ResultMessage,
                            ManageUrl = sdkLicenseDetails.ManageUrl,
                            UpgradeUrl = sdkLicenseDetails.UpgradeUrl,
                            ActivationId = sdkLicenseDetails.ActivationId,
                            ProductId = sdkLicenseDetails.ProductId
                        };
                    }

                    if (validationStatus == LicenseStatus.Active || validationStatus == LicenseStatus.TrialActive || validationStatus == LicenseStatus.GracePeriod)
                    {
                        await HandleSuccessfulAuthentication();
                    }
                    else
                    {
                        string validationError = currentLicenseDetails?.StatusMessage ?? "Failed to validate license after OTP.";
                        MessageBox.Show($"OTP verification succeeded, but license validation failed: {validationError}", "License Validation Failed", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        _statusLabel.Text = "License validation failed post-OTP.";
                    }
                }
                else
                {
                    MessageBox.Show("Failed to verify OTP. Please check the code and try again.", "OTP Verification Failed", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    _statusLabel.Text = "OTP verification failed.";
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[LicenseDialog] Exception from VerifyOtpAsync: {ex.ToString()}");
                MessageBox.Show($"An unexpected error occurred during OTP verification: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                _statusLabel.Text = "An unexpected error occurred.";
            }
            finally
            {
                _continueButton.Enabled = true;
                _verifyOtpButton.Enabled = true;
                _emailTextBox.Enabled = true;
                _otpTextBox.Enabled = true;
            }
        }
    }
}