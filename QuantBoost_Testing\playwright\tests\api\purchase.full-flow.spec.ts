import { test, expect } from '../../fixtures/combined.fixture';
import <PERSON><PERSON> from 'stripe';

/*
 Full purchase flow (API-level) test:
 1. Calls /api/checkout/create-payment-intent to create subscription + PI (incomplete)
 2. Confirms the PaymentIntent via Stripe SDK using a test card (4242)
 3. Simulates Stripe webhook delivery by directly POSTing real event payloads signed OR (fallback) polling webhook side-effects
 4. Polls Supabase tables to assert rows across schemas:
    - profiles (by stripe_customer_id)
    - subscriptions (stripe_subscription_id)
    - licenses (subscription_id or user_id)
    - webhook_events (payment_intent.succeeded and invoice.payment_succeeded)
    - charge_receipts (stripe_payment_intent_id)
    - payment_events (stripe_payment_intent_id)

 Requirements:
  - BASE_URL
  - STRIPE_SECRET_KEY_TEST (Stripe test secret)
  - STRIPE_WEBHOOK_SECRET (for signed webhook route) OR service role effects already triggered via existing system
  - SUPABASE_URL + SUPABASE_SERVICE_ROLE_KEY (for broad table access)

 Notes:
  - This test operates without browser; focuses on backend and DB integration.
  - If webhook forwarding (Stripe -> endpoint) is not occurring in this environment, you may need a manual simulation step.
*/

const PRICE_ID = 'price_1RC3HTE6FvhUKV1bE9D6zf6e';

const requireEnv = () => {
  if (!process.env.BASE_URL) test.skip(true, 'BASE_URL not set');
  if (!process.env.STRIPE_SECRET_KEY_TEST) test.skip(true, 'STRIPE_SECRET_KEY_TEST missing');
  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) test.skip(true, 'SUPABASE_SERVICE_ROLE_KEY missing for deep assertions');
};

async function postJSON(path: string, body: any) {
  const res = await fetch(`${process.env.BASE_URL}${path}`, {
    method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(body)
  });
  let json: any = {}; try { json = await res.json(); } catch {}
  return { status: res.status, json };
}

// Poll helper
async function poll<T>(label: string, fn: () => Promise<T | null | undefined>, predicate: (v: T) => boolean, attempts = 15, delayMs = 3000) {
  for (let i = 0; i < attempts; i++) {
    const val = await fn();
    if (val && predicate(val)) return val;
    await new Promise(r => setTimeout(r, delayMs));
  }
  return null;
}

test.describe.configure({ timeout: 120_000 });
test.describe('Full Purchase Flow (Backend + DB)', () => {
  test('creates subscription, processes payment, and populates all core tables', async ({ stripe, supabase }) => {
    test.setTimeout(120_000);
    requireEnv();
  if (!stripe) test.skip(true, 'Stripe fixture not initialized');
  if (!supabase) test.skip(true, 'Supabase client not available');

    const email = `flow_${Date.now()}@example.com`;

    // Step 1: Create payment intent via API
    const createPI = await postJSON('/api/checkout/create-payment-intent', { priceId: PRICE_ID, email, quantity: 1 });
    expect(createPI.status).toBe(200);
  const { paymentIntentId, clientSecret, subscriptionId, customerId } = createPI.json;
    expect(paymentIntentId).toBeTruthy();
    expect(subscriptionId).toBeTruthy();
    expect(customerId).toBeTruthy();

    // Step 2: Confirm PI using built-in Stripe test PaymentMethod ID (avoids raw PAN usage warning)
    // Stripe provides special reusable IDs like 'pm_card_visa' for testing.
    const confirmed = await stripe!.paymentIntents.confirm(paymentIntentId, {
      payment_method: 'pm_card_visa',
      return_url: 'https://example.com/post-pay'
    });
    expect(confirmed.status === 'succeeded' || confirmed.status === 'requires_capture').toBeTruthy();

    // Step 2.5: Simulate webhook events that would normally be sent by Stripe
    // Since webhooks aren't delivered during testing, we need to manually trigger them
    
    // Get the full subscription details from Stripe for webhook simulation
    const subscription = await stripe!.subscriptions.retrieve(subscriptionId);
    console.log(`🔧 Simulating webhooks for subscription: ${subscription.id}, customer: ${subscription.customer}`);

    // Create a more robust webhook simulation using the actual webhook endpoint
    const simulateWebhookEvent = async (eventType: string, eventData: any) => {
      console.log(`🎯 Simulating ${eventType} webhook...`);
      
      // Create a properly formatted Stripe event
      const stripeEvent = {
        id: `evt_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
        type: eventType,
        data: { object: eventData },
        created: Math.floor(Date.now() / 1000),
        livemode: false,
        object: 'event',
        pending_webhooks: 1,
        request: { id: null, idempotency_key: null },
  api_version: '2025-08-27.basil'
      };

      const result = await fetch(`${process.env.BASE_URL}/api/webhooks/stripe`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json', 
          'X-Stripe-Signature': 'test-signature-bypass' 
        },
        body: JSON.stringify(stripeEvent)
      });
      
      let responseBody: any = {};
      try {
        responseBody = await result.json();
      } catch {
        responseBody = { error: 'Failed to parse response' };
      }

      return { status: result.status, json: responseBody };
    };

    // Simulate customer.subscription.created webhook (this should create profile, subscription, and licenses)
    const subscriptionResult = await simulateWebhookEvent('customer.subscription.created', subscription);
    console.log(`🎯 Subscription webhook result: ${subscriptionResult.status}`);
    
    if (subscriptionResult.status !== 200 && subscriptionResult.status !== 400) {
      console.error('❌ Subscription webhook failed:', subscriptionResult.json);
    }

    // Simulate payment_intent.succeeded webhook for completeness
    const paymentResult = await simulateWebhookEvent('payment_intent.succeeded', confirmed);
    console.log(`🎯 Payment webhook result: ${paymentResult.status}`);

    // Small delay to allow any processing to complete
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 3: Poll Supabase for webhook-driven side-effects

    // profiles by stripe_customer_id
    const profile = await poll('profile', async () => {
      const { data } = await supabase!.from('profiles').select('id,email,stripe_customer_id').eq('stripe_customer_id', customerId).maybeSingle();
      return data || null;
    }, d => !!d?.id);
    expect(profile, 'profile created/linked').toBeTruthy();

    // Subscriptions: first attempt by internal UUID (id). If not found, derive Stripe subscription id from Stripe objects and query by stripe_subscription_id.
    let subscriptionPollAttempts = 0;
    let subscriptionRow = await poll('subscription(id)', async () => {
      subscriptionPollAttempts++;
      const { data } = await supabase!.from('subscriptions').select('*').eq('id', subscriptionId).maybeSingle();
      return data || null;
    }, d => !!d?.status, 15, 3000); // Longer polling for webhook effects

    let stripeSubId: string | undefined;
    if (!subscriptionRow) {
      // Derive Stripe subscription id: PaymentIntent -> Invoice -> Subscription
      try {
        const pi = await stripe!.paymentIntents.retrieve(paymentIntentId, { expand: ['invoice'] });
        const invoice: any = (pi as any).invoice || (pi.invoice ? await stripe!.invoices.retrieve(pi.invoice as string) : null);
        if (invoice) {
          stripeSubId = invoice.subscription as string;
        }
      } catch (err: any) {
        test.info().annotations.push({ type: 'diagnostic', description: `Unable to derive subscription from PI: ${err?.message}` });
      }

      if (stripeSubId) {
        subscriptionRow = await poll('subscription(stripe_subscription_id)', async () => {
          const { data } = await supabase!.from('subscriptions').select('*').eq('stripe_subscription_id', stripeSubId!).maybeSingle();
          return data || null;
        }, d => !!d?.status, 15, 3000); // Longer polling
      }
    }

    if (!subscriptionRow) {
      // Dump recent subscriptions for debugging (last 5 created in last 10 minutes)
      const sinceISO = new Date(Date.now() - 10 * 60 * 1000).toISOString();
      const { data: recent } = await supabase!.from('subscriptions').select('id,stripe_subscription_id,status,created_at,user_id').gte('created_at', sinceISO).order('created_at', { ascending: false }).limit(5);
      test.info().annotations.push({ type: 'diagnostic', description: `Recent subscriptions sample: ${JSON.stringify(recent)}` });
      test.info().annotations.push({ type: 'diagnostic', description: `Derived stripeSubId=${stripeSubId || 'n/a'} subscriptionId(internal)=${subscriptionId}` });
      test.info().annotations.push({ type: 'diagnostic', description: `Subscription poll attempts: ${subscriptionPollAttempts}` });
      
      // Also check if profile exists
      const { data: profileDebug } = await supabase!.from('profiles').select('id,email,stripe_customer_id').eq('stripe_customer_id', customerId).maybeSingle();
      test.info().annotations.push({ type: 'diagnostic', description: `Profile debug: ${JSON.stringify(profileDebug)}` });
    }
    expect(subscriptionRow, 'subscription row present (by id or stripe_subscription_id)').toBeTruthy();

    // licenses (expect at least one license assigned to user or pending creation)
    const licenseRow = await poll('license', async () => {
      const { data } = await supabase!.from('licenses').select('*').eq('subscription_id', subscriptionId).limit(1);
      return (data && data.length ? data[0] : null);
    }, d => !!d?.id);
    expect(licenseRow, 'license row created').toBeTruthy();

    // webhook_events entries for payment_intent.succeeded OR invoice.payment_succeeded
    const webhookEvent = await poll('webhook_events', async () => {
      const { data } = await supabase!.from('webhook_events').select('id,event_type').in('event_type', ['payment_intent.succeeded','invoice.payment_succeeded']).limit(1);
      return (data && data.length ? data[0] : null);
    }, d => !!d?.id);
    expect(webhookEvent, 'webhook event recorded').toBeTruthy();

    // charge_receipts by payment_intent id
    const chargeReceipt = await poll('charge_receipts', async () => {
      const { data } = await supabase!.from('charge_receipts').select('id,stripe_payment_intent_id').eq('stripe_payment_intent_id', paymentIntentId).maybeSingle();
      return data || null;
    }, d => !!d?.id);
    // Some flows may not generate charge_receipts immediately if webhook sequencing differs; allow soft assertion
    if (!chargeReceipt) test.info().annotations.push({ type: 'warning', description: 'charge_receipts row not found yet' });

    // payment_events row
    const paymentEvent = await poll('payment_events', async () => {
      const { data } = await supabase!.from('payment_events').select('id,stripe_payment_intent_id').eq('stripe_payment_intent_id', paymentIntentId).maybeSingle();
      return data || null;
    }, d => !!d?.id);
    if (!paymentEvent) test.info().annotations.push({ type: 'warning', description: 'payment_events row not found yet' });

    // Final consolidated assertions (must-haves)
    expect(profile?.email).toBe(email);
    if (subscriptionRow) {
      expect(subscriptionRow?.user_id).toBe(profile?.id);
    }
    if (licenseRow) {
      expect([subscriptionId, subscriptionRow?.id, subscriptionRow?.stripe_subscription_id].filter(Boolean)).toContain(licenseRow?.subscription_id);
    }
  });
});
