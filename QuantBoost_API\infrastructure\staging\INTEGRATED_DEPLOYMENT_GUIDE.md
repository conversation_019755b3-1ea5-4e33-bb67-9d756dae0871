# QuantBoost Integrated Staging Deployment Guide

This guide covers the complete deployment of the QuantBoost ecosystem including:
- **Frontend**: Next.js App Service B1 with Stripe integration (UPDATED - replaces Static Web Apps)
- **Backend API**: Node.js Container App with Supabase
- **VSTO Distribution**: Installer hosting and code signing infrastructure

## 🔄 IMPORTANT UPDATE: Static Web Apps → App Service Migration

**Why the change?**
- Static Web Apps has environment variable accessibility issues with Next.js API routes
- Payment processing was failing with "No payment intent found for subscription" errors
- App Service B1 provides reliable environment variable access for only $4.58/month additional cost

## 🏗️ Infrastructure Overview

### Resource Groups Created:
```
rg-quantboost-staging              # Frontend + API infrastructure
├── app-quantboost-frontend-staging    # App Service B1 (Frontend) - NEW
├── asp-quantboost-frontend-staging    # App Service Plan B1 - NEW
├── ca-quantboost-api-staging          # Container App (API)
├── kv-quantboost-staging-{suffix}     # Key Vault (Secrets)
├── acr-quantboost-staging-{suffix}    # Container Registry
├── ai-quantboost-staging              # Application Insights
└── law-quantboost-staging             # Log Analytics

rg-quantboost-distribution-staging # VSTO installer distribution
├── stqboostdiststg{suffix}            # Storage Account
├── cdn-quantboost-staging             # CDN Profile
├── quantboost-downloads-staging-{suffix} # CDN Endpoint
├── csa-quantboost-staging             # Code Signing Account
└── csp-staging-publictrust            # Certificate Profile
```

## 🚀 Deployment Steps

### Step 1: Deploy App Service Infrastructure (UPDATED)
```powershell
cd "C:\VS projects\QuantBoost\QuantBoost_API\infrastructure\staging"

# Deploy App Service B1 (replaces Static Web Apps)
.\deploy-app-service.ps1

# Or use the original script (now includes App Service)
.\deploy-staging.ps1
```

### Step 2: Deploy Frontend Application to App Service
```powershell
# Option A: Use GitHub Actions (Recommended)
# 1. Update .github/workflows/deploy-frontend-staging.yml
# 2. Push changes to trigger deployment

# Option B: Manual deployment
cd "C:\VS projects\QuantBoost\QuantBoost_Frontend"

# Build the application
npm run build

# Deploy to App Service using Azure CLI
az webapp deploy \
  --name app-quantboost-frontend-staging \
  --resource-group rg-quantboost-staging \
  --src-path ./ \
  --type zip
```

### Step 3: Build and Deploy API Container
```powershell
# From QuantBoost_API directory
cd "C:\VS projects\QuantBoost\QuantBoost_API"

# Get registry details from Terraform output
$registryName = terraform output -raw container_registry_name
$registryServer = terraform output -raw container_registry_server

# Build and push container
az acr build \
  --registry $registryName \
  --image quantboost-api:latest \
  .
```

### Step 4: Configure Stripe Webhooks
Update your Stripe webhook endpoint to point to the staging Static Web App:
```
Webhook URL: https://{staging-static-web-app-url}/api/webhooks/stripe
Events: customer.subscription.created, customer.subscription.updated, 
        customer.subscription.deleted, invoice.paid, invoice.payment_failed
```

### Step 5: Build and Upload VSTO Installer (Future)
```powershell
# Build the VSTO installer (when ready)
# Sign with Azure Code Signing
# Upload to staging storage account

$storageAccount = terraform output -raw distribution_storage_account_name
az storage blob upload \
  --account-name $storageAccount \
  --container-name releases \
  --name QuantBoost-staging.exe \
  --file ./path/to/QuantBoost-staging.exe
```

## 🔧 Configuration Details

### Frontend Environment Variables (Auto-configured):
- `NEXT_PUBLIC_SUPABASE_URL`: Supabase project URL
- `SUPABASE_SERVICE_KEY`: Service role key for server-side operations
- `STRIPE_PUBLISHABLE_KEY`: Test Stripe publishable key
- `STRIPE_SECRET_KEY`: Test Stripe secret key
- `STRIPE_WEBHOOK_SECRET`: Staging webhook signing secret
- `NEXT_PUBLIC_BASE_URL`: Staging frontend URL
- `NEXT_PUBLIC_AZURE_API_URL`: Staging API URL (auto-generated)

### API Environment Variables (Auto-configured):
- `NODE_ENV`: staging
- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key
- `STRIPE_SECRET_KEY`: Test Stripe secret key
- `STRIPE_WEBHOOK_SIGNING_SECRET`: Staging webhook secret
- `APPLICATIONINSIGHTS_CONNECTION_STRING`: Monitoring connection

### Distribution URLs:
- **CDN URL**: `https://{cdn-endpoint}/releases/QuantBoost-staging.exe`
- **Custom Domain**: `https://staging-download.quantboost.ai/QuantBoost-staging.exe`

## 🔒 Security Considerations

### Staging vs Production:
- **Staging**: Uses test Stripe keys, separate Supabase environment
- **Production**: Will use live Stripe keys, production Supabase
- **Code Signing**: Separate certificate profiles for staging/production

### Access Control:
- All secrets stored in Azure Key Vault
- Container Apps use system-assigned managed identities
- Code signing requires specific IAM roles

## 🧪 Testing Checklist

### Frontend Testing:
- [ ] Static Web App loads correctly
- [ ] Stripe checkout flow works with test keys
- [ ] Webhook events are received and processed
- [ ] Supabase data is populated correctly

### API Testing:
- [ ] Container App is accessible
- [ ] Health check endpoints respond
- [ ] Database connections work
- [ ] Stripe webhook processing functions

### Distribution Testing:
- [ ] Storage account accepts file uploads
- [ ] CDN serves files correctly
- [ ] Code signing account is configured
- [ ] Certificate profile is ready for validation

## 🔄 Migration to Production

When ready to deploy to production:

1. **Create production Terraform configuration** (similar structure)
2. **Update environment variables** to use live Stripe keys
3. **Configure production domains**:
   - Frontend: `quantboost.ai`
   - Downloads: `download.quantboost.ai`
4. **Complete code signing validation** (one-time manual process)
5. **Deploy production infrastructure**
6. **Test thoroughly** before switching DNS

## 📊 Monitoring and Logs

### Application Insights:
- Frontend and API share the same Application Insights instance
- Custom telemetry for subscription events
- Performance monitoring for both components

### Log Analytics:
- Container App logs
- Static Web App logs
- CDN access logs

### Alerts (Recommended):
- Failed webhook processing
- Container App health issues
- High error rates in frontend
- CDN availability issues

## 🎯 Next Steps

1. **Deploy staging infrastructure** using the provided scripts
2. **Test complete subscription flow** end-to-end
3. **Validate VSTO installer distribution** (when ready)
4. **Plan production migration** based on staging success
5. **Set up monitoring and alerting**

This integrated approach ensures all components of the QuantBoost ecosystem work together seamlessly while maintaining proper separation between staging and production environments.
