// --- REFACTORED frmTrace.cs - WPF-in-WinForms Host ---

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Forms;
using System.Windows.Forms.Integration; // The bridge for WPF-in-WinForms
using Excel = Microsoft.Office.Interop.Excel;
using QuantBoost_Excel.Features.ExcelTrace.Model;
using QuantBoost_Excel.Features.ExcelTrace.Logic;
using QuantBoost_Shared.Utilities;

namespace QuantBoost_Excel.Features.ExcelTrace.UI
{
    /// <summary>
    /// Simplified form that hosts the WPF TraceView control.
    /// This replaces the complex custom-drawn ListView with a modern, maintainable WPF UI.
    /// </summary>
    public partial class frmTrace : Form
    {
        #region Private Fields

        private TraceView _wpfView; // The instance of our WPF control
        private TraceNode _rootNode;
        private TracerEngine _tracerEngine;
        private bool _isInitialized;
        private string _currentCellAddress; // Track the current cell being traced

        // Track original colors for highlight restoration (preserved from original)
        private readonly Dictionary<string, int> _originalColors = new Dictionary<string, int>();

        // CRITICAL FIX: Track currently highlighted cell for proper cleanup
        private string _currentHighlightedAddress;

        // Settings menu for WinForms ContextMenuStrip
        private ContextMenuStrip _settingsMenu;

        // CRITICAL FIX: Store handlers as fields for proper cleanup
        private RoutedEventHandler _closeHandler;
        private RoutedEventHandler _navigateHandler;
        private RoutedEventHandler _settingsHandler;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the <see cref="frmTrace"/> class.
        /// </summary>
        public frmTrace()
        {
            // Initialize fields
            _tracerEngine = new TracerEngine();
            _isInitialized = false;

            // Standard WinForms Form setup
            this.Text = "Excel Trace";
            this.ClientSize = new System.Drawing.Size(900, 600); // Increased width from 700 to 900
            this.MinimumSize = new System.Drawing.Size(700, 400); // Increased minimum width
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable; // Use standard sizable border for better title bar spacing
            this.MaximizeBox = true; // Allow maximize for better user experience
            this.MinimizeBox = true; // Allow minimize
            this.ShowInTaskbar = false; // Don't show in taskbar (tool window behavior)

            // Set custom icon
            try
            {
                // Option 1: Use Excel icon as fallback
                this.Icon = Icon.ExtractAssociatedIcon(System.Windows.Forms.Application.ExecutablePath);

                // Option 2: If you have a custom icon resource, uncomment this:
                // this.Icon = Properties.Resources.ExcelTraceIcon;
            }
            catch
            {
                // Fallback to default if icon loading fails
            }

            // Settings menu will be created on-demand to ensure current settings are shown
            InitializeWpfHost();

            ErrorHandlingService.LogException(null, "frmTrace initialized successfully with WPF host");
        }

        #endregion

        #region WPF Host Initialization

        /// <summary>
        /// Creates the ElementHost, instantiates the WPF view, and connects them.
        /// This replaces the complex custom-drawn ListView with a modern WPF TreeView.
        /// </summary>
        private void InitializeWpfHost()
        {
            // 1. Create the host control that will contain our WPF UI.
            var elementHost = new ElementHost
            {
                Dock = DockStyle.Fill
            };

            // 2. Create an instance of our powerful WPF view.
            _wpfView = new TraceView();

            // CRITICAL FIX: Store handlers as fields for proper cleanup
            _closeHandler = (s, e) => { this.DialogResult = DialogResult.OK; this.Close(); };
            _navigateHandler = (s, e) =>
            {
                if (e.OriginalSource is TraceNode node)
                {
                    NavigateToNode(node);
                }
            };
            _settingsHandler = (s, e) =>
            {
                if (e.OriginalSource is System.Windows.Controls.Button settingsButton)
                {
                    ShowSettingsMenu(settingsButton);
                }
            };

            _wpfView.Close += _closeHandler;
            _wpfView.NavigateToNode += _navigateHandler;
            _wpfView.Settings += _settingsHandler;

            // 4. Place the WPF view inside the host.
            elementHost.Child = _wpfView;
            
            // 5. Add the host to the form's controls.
            this.Controls.Add(elementHost);

            // Subscribe to form events
            this.Load += FrmTrace_Load;
            this.FormClosed += FrmTrace_FormClosed;
        }

        #endregion

        #region Public Properties and Methods

        /// <summary>
        /// Gets the current cell address being traced.
        /// </summary>
        public string CurrentCellAddress => _currentCellAddress;

        /// <summary>
        /// Public entry point for the tracer engine to pass data to the UI.
        /// </summary>
        public void InitializeTrace(TraceNode rootNode, string cellAddress)
        {
            if (rootNode == null)
                throw new ArgumentNullException(nameof(rootNode));

            _rootNode = rootNode;
            _currentCellAddress = cellAddress;
            _isInitialized = true;
        }

        /// <summary>
        /// Overload for backward compatibility.
        /// </summary>
        public void InitializeTrace(TraceNode rootNode)
        {
            InitializeTrace(rootNode, rootNode?.FullAddress ?? "Unknown");
        }

        /// <summary>
        /// Updates the trace data for a new cell while keeping the window open.
        /// </summary>
        public void UpdateTrace(TraceNode rootNode, string cellAddress)
        {
            if (rootNode == null)
                throw new ArgumentNullException(nameof(rootNode));

            _rootNode = rootNode;
            _currentCellAddress = cellAddress;

            // Update the WPF view with new data
            if (_wpfView != null)
            {
                _wpfView.SetTraceData(_rootNode);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles the Form.Load event to populate the WPF UI.
        /// </summary>
        private void FrmTrace_Load(object sender, EventArgs e)
        {
            if (!_isInitialized || _rootNode == null)
            {
                System.Windows.Forms.MessageBox.Show("Trace data was not initialized correctly.", "Initialization Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
                return;
            }

            try
            {
                // Simply pass the data along to the WPF view.
                _wpfView.SetTraceData(_rootNode);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, "Error initializing WPF trace view");
                System.Windows.Forms.MessageBox.Show($"An unexpected error occurred while displaying the trace: {ex.Message}", "Display Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// TASK 3.1: Handles form closing to restore original cell colors.
        /// </summary>
        private void FrmTrace_FormClosed(object sender, FormClosedEventArgs e)
        {
            try
            {
                // Restore original colors for all highlighted cells
                foreach (var kvp in _originalColors)
                {
                    try
                    {
                        string fullAddress = kvp.Key;
                        Excel.Range range = null;
                        try
                        {
                            // Basic parsing of format like "[Workbook.xlsx]'Sheet Name'!A1"
                            string wbName = fullAddress.Substring(fullAddress.IndexOf('[') + 1, fullAddress.IndexOf(']') - fullAddress.IndexOf('[') - 1);
                            string sheetName = fullAddress.Substring(fullAddress.IndexOf(']') + 1, fullAddress.IndexOf('!') - fullAddress.IndexOf(']') - 1).Trim('\'');
                            string cellAddress = fullAddress.Substring(fullAddress.IndexOf('!') + 1);

                            Excel.Workbook wb = Globals.ThisAddIn.Application.Workbooks[wbName];
                            Excel.Worksheet ws = (Excel.Worksheet)wb.Worksheets[sheetName];
                            range = ws.Range[cellAddress];

                            range.Interior.Color = (double)kvp.Value;
                        }
                        finally
                        {
                            if (range != null && Marshal.IsComObject(range))
                                Marshal.ReleaseComObject(range);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log but don't prevent form closing - this is non-critical functionality
                        ErrorHandlingService.LogException(ex, $"Error restoring color for cell: {kvp.Key}. This is non-critical and form closing will continue.");
                    }
                }

                _originalColors.Clear();
                ErrorHandlingService.LogException(null, "Cell color restoration completed");
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error during cell color restoration");
            }
        }

        #endregion

        #region Settings Menu Methods

        /// <summary>
        /// Creates the WinForms ContextMenuStrip for the settings options.
        /// CRITICAL FIX: Always refresh settings from saved values to ensure persistence.
        /// </summary>
        private void CreateSettingsMenu()
        {
            try
            {
                // DEBUG: Log current settings values
                ErrorHandlingService.LogException(null, $"=== CREATING SETTINGS MENU DEBUG ===");
                ErrorHandlingService.LogException(null, $"HighlightNavigatedCells: {TraceSettings.HighlightNavigatedCells}");
                ErrorHandlingService.LogException(null, $"OpenLinkedWorkbooks: {TraceSettings.OpenLinkedWorkbooks}");
                ErrorHandlingService.LogException(null, $"UnhideRowsAndColumns: {TraceSettings.UnhideRowsAndColumns}");
                ErrorHandlingService.LogException(null, $"MaxTraceDepth: {TraceSettings.MaxTraceDepth}");

                // CRITICAL FIX: Always get current settings values to ensure persistence
                var tsmHighlightCells = new ToolStripMenuItem
                {
                    Text = "Highlight navigated cells",
                    CheckOnClick = true,
                    Checked = TraceSettings.HighlightNavigatedCells // This reads from saved settings
                };
                ErrorHandlingService.LogException(null, $"Created HighlightCells menu item: Checked={tsmHighlightCells.Checked}");

                tsmHighlightCells.Click += (s, e) => {
                    TraceSettings.HighlightNavigatedCells = tsmHighlightCells.Checked;
                    ErrorHandlingService.LogException(null, $"Settings: HighlightNavigatedCells changed to {tsmHighlightCells.Checked}");
                };

                var tsmOpenWorkbooks = new ToolStripMenuItem
                {
                    Text = "Open linked workbooks",
                    CheckOnClick = true,
                    Checked = TraceSettings.OpenLinkedWorkbooks // This reads from saved settings
                };
                ErrorHandlingService.LogException(null, $"Created OpenWorkbooks menu item: Checked={tsmOpenWorkbooks.Checked}");

                tsmOpenWorkbooks.Click += (s, e) => {
                    TraceSettings.OpenLinkedWorkbooks = tsmOpenWorkbooks.Checked;
                    ErrorHandlingService.LogException(null, $"Settings: OpenLinkedWorkbooks changed to {tsmOpenWorkbooks.Checked}");
                };

                var tsmUnhideRowsColumns = new ToolStripMenuItem
                {
                    Text = "Unhide rows && columns",
                    CheckOnClick = true,
                    Checked = TraceSettings.UnhideRowsAndColumns // This reads from saved settings
                };
                ErrorHandlingService.LogException(null, $"Created UnhideRowsColumns menu item: Checked={tsmUnhideRowsColumns.Checked}");

                tsmUnhideRowsColumns.Click += (s, e) => {
                    TraceSettings.UnhideRowsAndColumns = tsmUnhideRowsColumns.Checked;
                    ErrorHandlingService.LogException(null, $"Settings: UnhideRowsAndColumns changed to {tsmUnhideRowsColumns.Checked}");
                };

                var tsmKeepWindowOnTop = new ToolStripMenuItem
                {
                    Text = "Keep trace window on top",
                    CheckOnClick = true,
                    Checked = TraceSettings.KeepTraceWindowOnTop // This reads from saved settings
                };
                ErrorHandlingService.LogException(null, $"Created KeepWindowOnTop menu item: Checked={tsmKeepWindowOnTop.Checked}");

                tsmKeepWindowOnTop.Click += (s, e) => {
                    TraceSettings.KeepTraceWindowOnTop = tsmKeepWindowOnTop.Checked;
                    ErrorHandlingService.LogException(null, $"Settings: KeepTraceWindowOnTop changed to {tsmKeepWindowOnTop.Checked}");
                };

                // Create the ComboBox for trace depth
                var tscMaxDepth = new ToolStripComboBox("MaxDepthComboBox");
                tscMaxDepth.DropDownStyle = ComboBoxStyle.DropDownList;
                tscMaxDepth.Items.AddRange(Enumerable.Range(1, 10).Select(i => i.ToString()).ToArray());
                tscMaxDepth.SelectedItem = TraceSettings.MaxTraceDepth.ToString(); // This reads from saved settings
                ErrorHandlingService.LogException(null, $"Created MaxDepth combo: SelectedItem={tscMaxDepth.SelectedItem}");

                tscMaxDepth.SelectedIndexChanged += (s, e) =>
                {
                    if (int.TryParse(tscMaxDepth.SelectedItem.ToString(), out int newDepth))
                    {
                        TraceSettings.MaxTraceDepth = newDepth;
                        ErrorHandlingService.LogException(null, $"Settings: MaxTraceDepth changed to {newDepth}");
                    }
                };

                // Create the context menu strip and add all items
                _settingsMenu = new ContextMenuStrip();
                _settingsMenu.Items.AddRange(new ToolStripItem[] {
                    tsmHighlightCells,
                    tsmOpenWorkbooks,
                    tsmUnhideRowsColumns,
                    tsmKeepWindowOnTop,
                    new ToolStripSeparator(),
                    new ToolStripLabel("Max Trace Depth:"),
                    tscMaxDepth
                });

                ErrorHandlingService.LogException(null, $"Settings menu created with {_settingsMenu.Items.Count} items");
                ErrorHandlingService.LogException(null, $"=== END CREATING SETTINGS MENU DEBUG ===");
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error creating settings menu");
            }
        }

        /// <summary>
        /// Shows the settings ContextMenuStrip at the correct position relative to the WPF button.
        /// CRITICAL FIX: Always recreate menu to ensure current settings are displayed.
        /// </summary>
        /// <param name="wpfButton">The WPF button that was clicked.</param>
        private void ShowSettingsMenu(System.Windows.Controls.Button wpfButton)
        {
            try
            {
                ErrorHandlingService.LogException(null, $"=== SHOW SETTINGS MENU DEBUG ===");
                ErrorHandlingService.LogException(null, $"WPF Button: {wpfButton?.Name ?? "null"}");

                if (wpfButton == null)
                {
                    ErrorHandlingService.LogException(null, $"ERROR: wpfButton is null");
                    return;
                }

                // CRITICAL FIX: Always recreate the menu to ensure current settings are displayed
                _settingsMenu?.Dispose(); // Dispose old menu if it exists
                ErrorHandlingService.LogException(null, $"Disposed old settings menu");

                CreateSettingsMenu(); // Create fresh menu with current settings
                ErrorHandlingService.LogException(null, $"Created new settings menu");

                // 1. Get the screen coordinates of the WPF button.
                // PointToScreen(new Point(0,0)) gets the top-left corner.
                var screenPos = wpfButton.PointToScreen(new System.Windows.Point(0, 0));
                ErrorHandlingService.LogException(null, $"Screen position: {screenPos.X}, {screenPos.Y}");

                // 2. Convert the WPF screen coordinates (which use doubles) to WinForms Point (which use ints).
                var menuPos = new System.Drawing.Point((int)screenPos.X, (int)(screenPos.Y + wpfButton.ActualHeight));
                ErrorHandlingService.LogException(null, $"Menu position: {menuPos.X}, {menuPos.Y}");

                // 3. Show the WinForms ContextMenuStrip at the calculated position.
                _settingsMenu.Show(menuPos);
                ErrorHandlingService.LogException(null, $"Settings menu shown successfully");
                ErrorHandlingService.LogException(null, $"=== END SHOW SETTINGS MENU DEBUG ===");
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error showing settings menu");
            }
        }

        #endregion

        #region Navigation Methods

        /// <summary>
        /// TASK 4.1: Extracts workbook name from a FullAddress string.
        /// </summary>
        /// <param name="fullAddress">Address in format: [WorkbookName]SheetName!Address</param>
        /// <returns>The workbook name without brackets, or null if not found.</returns>
        private string ExtractWorkbookNameFromAddress(string fullAddress)
        {
            if (string.IsNullOrEmpty(fullAddress))
                return null;

            try
            {
                // Look for pattern [WorkbookName]
                var startIndex = fullAddress.IndexOf('[');
                var endIndex = fullAddress.IndexOf(']');

                if (startIndex >= 0 && endIndex > startIndex)
                {
                    return fullAddress.Substring(startIndex + 1, endIndex - startIndex - 1);
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"Error extracting workbook name from: {fullAddress}");
            }

            return null;
        }

        /// <summary>
        /// CRITICAL FIX: Clears previous cell highlights to ensure only current cell is highlighted.
        /// </summary>
        private void ClearPreviousHighlights()
        {
            if (string.IsNullOrEmpty(_currentHighlightedAddress))
                return;

            try
            {
                // Only clear the currently highlighted cell, not all previously highlighted cells
                if (_originalColors.ContainsKey(_currentHighlightedAddress))
                {
                    Excel.Range range = null;
                    try
                    {
                        // Parse the full address to get workbook, sheet, and cell
                        string fullAddress = _currentHighlightedAddress;
                        string wbName = fullAddress.Substring(fullAddress.IndexOf('[') + 1, fullAddress.IndexOf(']') - fullAddress.IndexOf('[') - 1);
                        string sheetName = fullAddress.Substring(fullAddress.IndexOf(']') + 1, fullAddress.IndexOf('!') - fullAddress.IndexOf(']') - 1).Trim('\'');
                        string cellAddress = fullAddress.Substring(fullAddress.IndexOf('!') + 1);

                        Excel.Workbook wb = Globals.ThisAddIn.Application.Workbooks[wbName];
                        Excel.Worksheet ws = (Excel.Worksheet)wb.Worksheets[sheetName];
                        range = ws.Range[cellAddress];

                        // Restore original color
                        range.Interior.Color = (double)_originalColors[_currentHighlightedAddress];

                        ErrorHandlingService.LogException(null, $"Cleared highlight from: {_currentHighlightedAddress}");
                    }
                    finally
                    {
                        if (range != null && Marshal.IsComObject(range))
                            Marshal.ReleaseComObject(range);
                    }
                }

                // Clear the current highlight tracking
                _currentHighlightedAddress = null;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"Error clearing previous highlight for: {_currentHighlightedAddress}");
                // Don't throw - this is non-critical functionality
            }
        }

        /// <summary>
        /// CRITICAL FIX: Restores focus to the trace window after Excel navigation.
        /// This prevents the Excel window from staying on top during array element navigation.
        /// </summary>
        private void RestoreTraceWindowFocus()
        {
            try
            {
                if (TraceSettings.KeepTraceWindowOnTop)
                {
                    // Use a small delay to ensure Excel navigation is complete
                    System.Threading.Tasks.Task.Delay(50).ContinueWith(_ =>
                    {
                        try
                        {
                            // Use Invoke to ensure we're on the UI thread
                            this.Invoke(new Action(() =>
                            {
                                // Bring the trace window back to the front
                                this.Activate();
                                this.BringToFront();
                                this.Focus();

                                ErrorHandlingService.LogException(null, "Restored focus to trace window");
                            }));
                        }
                        catch (Exception ex)
                        {
                            ErrorHandlingService.LogException(ex, "Error in delayed focus restoration");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error restoring trace window focus");
                // Don't throw - this is non-critical functionality
            }
        }

        /// <summary>
        /// Navigates to the specified node in Excel with enhanced functionality based on settings.
        /// </summary>
        /// <param name="node">The TraceNode to navigate to.</param>
        private void NavigateToNode(TraceNode node)
        {
            if (node == null || !node.CanNavigate)
            {
                System.Windows.Forms.MessageBox.Show("Cannot navigate to this cell.", "Navigation",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            Excel.Range targetRange = null; // Declare here to manage its lifetime
            try
            {
                ErrorHandlingService.LogException(null, $"Navigating to: {node.FullAddress}");

                // TASK 4.1: Check if we need to open external workbooks
                if (node.NodeType == NodeType.ExternalWorkbook &&
                    TraceSettings.OpenLinkedWorkbooks)
                {
                    try
                    {
                        // Extract workbook name from FullAddress format: [WorkbookName]SheetName!Address
                        var workbookName = ExtractWorkbookNameFromAddress(node.FullAddress);
                        if (!string.IsNullOrEmpty(workbookName))
                        {
                            // Check if workbook is already open
                            bool isWorkbookOpen = false;
                            try
                            {
                                var testWorkbook = Globals.ThisAddIn.Application.Workbooks[workbookName];
                                isWorkbookOpen = testWorkbook != null;
                                if (testWorkbook != null && Marshal.IsComObject(testWorkbook))
                                    Marshal.ReleaseComObject(testWorkbook);
                            }
                            catch
                            {
                                isWorkbookOpen = false;
                            }

                            if (!isWorkbookOpen)
                            {
                                ErrorHandlingService.LogException(null, $"Attempting to open external workbook: {workbookName}");

                                // Try to open the workbook
                                // Note: This assumes the workbook is in the same directory or accessible path
                                var openedWorkbook = Globals.ThisAddIn.Application.Workbooks.Open(workbookName);
                                if (openedWorkbook != null)
                                {
                                    ErrorHandlingService.LogException(null, $"Successfully opened external workbook: {workbookName}");
                                    if (Marshal.IsComObject(openedWorkbook))
                                        Marshal.ReleaseComObject(openedWorkbook);
                                }
                            }
                            else
                            {
                                ErrorHandlingService.LogException(null, $"External workbook already open: {workbookName}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorHandlingService.LogException(ex, $"Failed to open external workbook for: {node.FullAddress}");
                        // Continue with navigation even if workbook opening fails
                    }
                }

                // --- NEW ROBUST METHOD ---
                // 1. Get the workbook object by name.
                Excel.Workbook wb = Globals.ThisAddIn.Application.Workbooks[node.WorkbookName];
                // 2. Activate the workbook.
                wb.Activate();
                // 3. Get the worksheet object from that workbook.
                Excel.Worksheet ws = (Excel.Worksheet)wb.Worksheets[node.WorksheetName];
                // 4. Activate the worksheet.
                ws.Activate();
                // 5. Get the range using only the local cell address.
                targetRange = ws.Range[node.CellAddress];

                // Now that we have a reliable targetRange, proceed with selection/highlighting.
                var workbook = wb;
                var worksheet = ws;

                workbook.Activate();
                worksheet.Activate();

                // Unhide rows and columns if setting is enabled
                if (TraceSettings.UnhideRowsAndColumns)
                {
                    try
                    {
                        // Unhide the row and column containing the target cell
                        targetRange.EntireRow.Hidden = false;
                        targetRange.EntireColumn.Hidden = false;
                    }
                    catch (Exception unhideEx)
                    {
                        ErrorHandlingService.LogException(unhideEx, "Error unhiding rows/columns during navigation");
                        // Continue with navigation even if unhiding fails
                    }
                }

                // Select the range
                targetRange.Select();

                // CRITICAL FIX: Handle cell highlighting properly (temporary highlighting)
                if (TraceSettings.HighlightNavigatedCells)
                {
                    try
                    {
                        // CRITICAL FIX: Clear previous highlights before applying new ones
                        ClearPreviousHighlights();

                        // Store original color before applying highlight - EXPLICIT CAST to fix RuntimeBinderException
                        var originalColor = (int)(double)targetRange.Interior.Color; // Convert dynamic type to int
                        if (!_originalColors.ContainsKey(node.FullAddress))
                        {
                            _originalColors[node.FullAddress] = originalColor;
                        }

                        // Apply temporary highlighting (blue background)
                        targetRange.Interior.Color = ColorTranslator.ToOle(Color.FromArgb(179, 217, 255));

                        // Track this as the currently highlighted cell
                        _currentHighlightedAddress = node.FullAddress;

                        ErrorHandlingService.LogException(null, $"Applied highlight to: {node.FullAddress}");
                    }
                    catch (Exception highlightEx)
                    {
                        ErrorHandlingService.LogException(highlightEx, "Error highlighting cell during navigation");
                        // Continue with navigation even if highlighting fails
                    }
                }
                else
                {
                    // CRITICAL FIX: If highlighting is disabled, clear any existing highlights
                    ClearPreviousHighlights();
                }

                ErrorHandlingService.LogException(null, $"Successfully navigated to: {node.DisplayText}");

                // CRITICAL FIX: Restore focus to trace window after navigation
                RestoreTraceWindowFocus();
            }
            catch (COMException comEx)
            {
                ErrorHandlingService.LogException(comEx, $"COM error navigating to: {node.DisplayText}");
                System.Windows.Forms.MessageBox.Show($"Cannot navigate to {node.DisplayText}. The workbook or worksheet may be closed.",
                    "Navigation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, $"Error navigating to: {node.DisplayText}. Ensure workbook '{node.WorkbookName}' and sheet '{node.WorksheetName}' are accessible.");
                System.Windows.Forms.MessageBox.Show($"Error navigating to {node.DisplayText}: {ex.Message}",
                    "Navigation Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // FIX: Ensure the COM object is released
                if (targetRange != null && Marshal.IsComObject(targetRange))
                    Marshal.ReleaseComObject(targetRange);
            }
        }

        #endregion

        #region Cleanup

        /// <summary>
        /// Clean up any resources being used.
        /// CRITICAL FIX: Properly unsubscribe events to prevent memory leaks.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    // CRITICAL FIX: Properly unsubscribe events
                    if (_wpfView != null)
                    {
                        _wpfView.Close -= _closeHandler;
                        _wpfView.NavigateToNode -= _navigateHandler;
                        _wpfView.Settings -= _settingsHandler;
                    }

                    // Dispose managed resources
                    _settingsMenu?.Dispose();
                    _tracerEngine = null;
                    _rootNode = null;
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error disposing frmTrace");
            }
            finally
            {
                base.Dispose(disposing);
            }
        }

        #endregion
    }
}

// --- END OF FILE frmTrace.cs ---
