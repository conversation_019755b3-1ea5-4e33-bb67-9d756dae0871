# Azure App Service Migration Summary

## 🎯 Migration Overview

**Problem Solved:** Environment variable accessibility issues in Azure Static Web Apps causing payment processing failures.

**Solution:** Replace Azure Static Web Apps with Azure App Service B1 tier for reliable Next.js hosting.

## 📋 Changes Made to Terraform Infrastructure

### 1. Replaced Static Web Apps with App Service
```hcl
# REMOVED: azurerm_static_web_app.frontend
# ADDED: azurerm_service_plan.frontend (B1 tier)
# ADDED: azurerm_linux_web_app.frontend (Node.js 18)
```

### 2. Enhanced Key Vault Integration
```hcl
# ADDED: Key Vault access policy for App Service managed identity
# ADDED: Additional secrets for Stripe publishable key
```

### 3. Updated Environment Variables
```hcl
# App Service now has reliable access to:
- STRIPE_SECRET_KEY
- STRIPE_WEBHOOK_SECRET
- SUPABASE_SERVICE_KEY
- All NEXT_PUBLIC_* variables
```

## 💰 Cost Impact

| Service | Before | After | Difference |
|---------|--------|-------|------------|
| Static Web Apps | $9/month | $0 | -$9 |
| App Service B1 | $0 | $13.14/month | +$13.14 |
| **Total Change** | | | **+$4.14/month** |

**ROI:** The $4.14/month additional cost is immediately justified by fixing broken payment processing.

## 🚀 Migration Steps

### Step 1: Apply Terraform Changes
```powershell
cd "C:\VS projects\QuantBoost\QuantBoost_API\infrastructure\staging"

# Review changes
terraform plan

# Apply changes (creates App Service, keeps Static Web Apps for now)
terraform apply
```

### Step 2: Update Next.js Configuration
```javascript
// QuantBoost_Frontend/next.config.js
const nextConfig = {
  // Remove this line if present:
  // output: 'standalone',
  
  // Keep existing configuration
  trailingSlash: false,
  // ... rest of config
};
```

### Step 3: Update GitHub Actions Workflow
Create `.github/workflows/deploy-frontend-staging.yml`:
```yaml
name: Deploy Frontend to Azure App Service (Staging)

on:
  push:
    branches: [ main ]
    paths: [ 'QuantBoost_Frontend/**' ]

env:
  AZURE_WEBAPP_NAME: app-quantboost-frontend-staging
  NODE_VERSION: '18'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'QuantBoost_Frontend/package-lock.json'
    
    - name: Install and build
      run: |
        cd QuantBoost_Frontend
        npm ci
        npm run build
      env:
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY }}
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
    
    - name: Deploy to Azure App Service
      uses: azure/webapps-deploy@v2
      with:
        app-name: ${{ env.AZURE_WEBAPP_NAME }}
        publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE_STAGING }}
        package: './QuantBoost_Frontend'
```

### Step 4: Configure GitHub Secrets
Add to GitHub repository secrets:
- `AZURE_WEBAPP_PUBLISH_PROFILE_STAGING`: Download from Azure Portal → App Service → Get publish profile

### Step 5: Test Payment Processing
1. Deploy to App Service
2. Test checkout flow: https://app-quantboost-frontend-staging.azurewebsites.net/pricing
3. Verify environment variables are accessible: `/api/debug/env` (if implemented)
4. Confirm Stripe payments work without errors

### Step 6: Cleanup (Optional)
After confirming App Service works:
```powershell
# Remove Static Web Apps from Terraform (comment out resource)
# Then apply to destroy it
terraform apply
```

## 🔧 Infrastructure Resources Created

### App Service Plan
- **Name:** `asp-quantboost-frontend-staging`
- **SKU:** B1 (1 vCPU, 1.75GB RAM)
- **OS:** Linux
- **Cost:** $13.14/month

### App Service
- **Name:** `app-quantboost-frontend-staging`
- **Runtime:** Node.js 18 LTS
- **URL:** `https://app-quantboost-frontend-staging.azurewebsites.net`
- **Features:** Always On, Application Insights, Managed Identity

### Environment Variables Configured
```
NODE_ENV=production
STRIPE_SECRET_KEY=sk_test_51R9yKDE6FvhUKV1b...
STRIPE_WEBHOOK_SECRET=whsec_y84fUcgTM5taGcLwNBdOsucbsi6Suy8u
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51R9yKDE6FvhUKV1b...
NEXT_PUBLIC_SUPABASE_URL=https://izoutrnsxaaoueljiimu.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=...
```

## 🎯 Expected Outcomes

### ✅ Problems Resolved
- ❌ "No payment intent found for subscription" → ✅ Working payment processing
- ❌ "Missing Supabase environment variables" → ✅ Reliable environment variable access
- ❌ "Backend call failure" errors → ✅ Stable API routes

### ✅ Benefits Gained
- **Reliable Environment Variables:** Full access to `process.env` in API routes
- **Better Error Handling:** Comprehensive logging and debugging
- **Production Ready:** Proven platform for Next.js applications
- **Monitoring:** Integrated Application Insights
- **Scalability:** Can upgrade to S1 for auto-scaling when needed

## 🔍 Verification Checklist

After migration, verify:
- [ ] App Service is running and accessible
- [ ] Environment variables are set correctly in Azure Portal
- [ ] Payment processing works without errors
- [ ] Supabase integration is functional
- [ ] Application Insights is collecting data
- [ ] GitHub Actions deployment works
- [ ] Custom domain configured (if needed)

## 📞 Support and Troubleshooting

### Common Issues
1. **Build failures:** Check Node.js version in App Service (should be 18-lts)
2. **Environment variables not working:** Verify they're set in App Service → Configuration
3. **Payment processing still failing:** Check Application Insights logs for detailed errors

### Monitoring
- **Application Insights:** Azure Portal → Application Insights → Live Metrics
- **App Service Logs:** Azure Portal → App Service → Log stream
- **Cost Monitoring:** Azure Portal → Cost Management + Billing

### Rollback Plan
If issues arise:
1. Keep Static Web Apps running during migration
2. DNS can be quickly switched back
3. GitHub Actions can be reverted to previous workflow

## 🎉 Success Metrics

**Migration is successful when:**
- Payment processing works without "No payment intent found" errors
- Environment variables are accessible in API routes
- Subscription creation completes successfully
- No "Missing Supabase environment variables" errors in console
- Application performance is stable or improved

**Expected timeline:** 2-3 hours for complete migration and testing.
