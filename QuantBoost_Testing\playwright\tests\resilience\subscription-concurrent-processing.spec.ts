import { test, expect } from '../../fixtures/webhook-testing.fixture';
import { WebhookTestFactory, simulateStripeWebhook } from '../../fixtures/webhook-testing.fixture';
import { randomUUID } from 'crypto';

/**
 * Resilience: Concurrent processing of the SAME subscription.created event ID.
 * Ensures idempotency logic prevents duplicate subscription rows.
 */

test.describe('🛡️ Resilience - Concurrent Subscription Processing', () => {
  test('⚔️ handles concurrent identical events with single DB record', async ({ supabaseClient, stripe }) => {
    if (!stripe) { test.skip(true, 'Stripe key not configured'); return; }

    // Create real subscription object (not yet persisted via webhook)
    const realCustomer = await stripe.customers.create({
      email: `concurrent_${Date.now()}_${Math.random().toString(36).slice(2)}@quantboost-test.com`,
      name: 'Concurrent Test Customer'
    });
    const subscription = await WebhookTestFactory.createRealSubscription(stripe, 'price_1RC3HTE6FvhUKV1bE9D6zf6e', 1, {}, realCustomer.id);

    const eventId = `evt_concurrent_${Date.now()}`;

    // Fire N parallel simulated webhooks with identical event id + payload
    const parallel = 6;
    const results = await Promise.all(
      Array.from({ length: parallel }).map(() => simulateStripeWebhook('customer.subscription.created', subscription, { eventId }))
    );

    // All should return 200
    results.forEach(r => expect(r.status).toBe(200));

    // Query subscriptions by stripe_subscription_id
    const { data: subs, error } = await supabaseClient
      .from('subscriptions')
      .select('*')
      .eq('stripe_subscription_id', subscription.id);
    expect(error).toBeNull();
    expect(subs).toBeTruthy();
    expect(subs!.length).toBe(1);

    // Ensure only one webhook_events row stored for the event id (idempotency)
    const { data: events, error: eventsError } = await supabaseClient
      .from('webhook_events')
      .select('*')
      .eq('stripe_event_id', eventId);
    expect(eventsError).toBeNull();
    expect(events).toBeTruthy();
    expect(events!.length).toBe(1);
  });
});
