import { test, expect } from '@playwright/test';

const concurrent = Number(process.env.CHECKOUT_CONCURRENT_USERS || 5);
const enable = (process.env.ENABLE_LOAD_TESTS || 'false').toLowerCase() === 'true';

const ANNUAL_PRICE_ID = 'price_1RC3HTE6FvhUKV1bE9D6zf6e';

test.describe('Performance / Load', () => {
  test.skip(!enable, 'ENABLE_LOAD_TESTS not enabled');

  test('concurrent anonymous checkout page loads', async ({ browser }) => {
    const start = Date.now();
    const contexts = await Promise.all(Array.from({ length: concurrent }).map(() => browser.newContext()));
    const pages = await Promise.all(contexts.map(c => c.newPage()));
    await Promise.all(pages.map(p => p.goto(`/checkout/${ANNUAL_PRICE_ID}`)));
    const elapsed = Date.now() - start;
    console.log(`[load] ${concurrent} pages load in ${elapsed}ms`);
    expect(elapsed).toBeLessThan(15_000);
    await Promise.all(contexts.map(c => c.close()));
  });
});
