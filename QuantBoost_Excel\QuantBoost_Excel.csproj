﻿<Project ToolsVersion="17.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <!--
    This section defines project-level properties.

    AssemblyName
      Name of the output assembly.
    Configuration
      Specifies a default value for debug.
    OutputType
      Must be "Library" for VSTO.
    Platform
      Specifies what CPU the output of this project can run on.
    NoStandardLibraries
      Set to "false" for VSTO.
    RootNamespace
      In C#, this specifies the namespace given to new files. In VB, all objects are
      wrapped in this namespace at runtime.
  -->
  <PropertyGroup>
    <ProjectTypeGuids>{BAA0C2D2-18E2-41B9-852F-F413020CAA33};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{F8183AA1-B6D6-4CA2-9EC9-F5A660FBBCFB}</ProjectGuid>
    <OutputType>Library</OutputType>
    <NoStandardLibraries>false</NoStandardLibraries>
    <RootNamespace>QuantBoost_Excel</RootNamespace>
    <AssemblyName>QuantBoost_Excel</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <DefineConstants>VSTO40;UseOfficeInterop</DefineConstants>
    <ResolveComReferenceSilent>true</ResolveComReferenceSilent>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <PublishUrl>publish\</PublishUrl>
    <InstallUrl />
    <TargetCulture>en</TargetCulture>
    <ApplicationVersion>*******</ApplicationVersion>
    <AutoIncrementApplicationRevision>true</AutoIncrementApplicationRevision>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>days</UpdateIntervalUnits>
    <IsWebBootstrapper>False</IsWebBootstrapper>
    <ProductName>QuantBoost_Excel</ProductName>
    <PublisherName />
    <SupportUrl />
    <FriendlyName>QuantBoost_Excel</FriendlyName>
    <OfficeApplicationDescription />
    <LoadBehavior>3</LoadBehavior>
  </PropertyGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8.1">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8.1 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.VSTORuntime.4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft Visual Studio 2010 Tools for Office Runtime %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <PropertyGroup>
    <!--
      OfficeApplication
        Add-in host application
    -->
    <OfficeApplication>Excel</OfficeApplication>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Debug" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);DEBUG;TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Release" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <!--
    This section specifies references for the project.
  -->
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Web" />
    <Reference Include="System.Security" />
    <Reference Include="DocumentFormat.OpenXml, Version=2.18.0.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>packages\DocumentFormat.OpenXml.2.18.0\lib\net46\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.ProtectedData, Version=4.0.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.ProtectedData.4.7.0\lib\net461\System.Security.Cryptography.ProtectedData.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.v4.0.Framework, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.Tools.Applications.Runtime, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Common, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Excel, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.Common.v4.0.Utilities, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <Choose>
    <When Condition="$([System.String]::Copy(&quot;;$(DefineConstants);&quot;).ToLower().Contains(';useofficeinterop;')) or $([System.String]::Copy(&quot;,$(DefineConstants),&quot;).ToLower().Contains(',useofficeinterop,'))">
      <ItemGroup>
        <Reference Include="Office, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
          <Private>False</Private>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </Reference>
        <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
          <Private>False</Private>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </Reference>
        <Reference Include="Microsoft.Vbe.Interop, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
          <Private>False</Private>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </Reference>
      </ItemGroup>
    </When>
    <Otherwise>
      <ItemGroup>
        <COMReference Include="Microsoft.Office.Core">
          <Guid>{2DF8D04C-5BFA-101B-BDE5-00AA0044DE52}</Guid>
          <VersionMajor>2</VersionMajor>
          <VersionMinor>7</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
        <COMReference Include="Microsoft.Office.Interop.Excel">
          <Guid>{00020813-0000-0000-C000-000000000046}</Guid>
          <VersionMajor>1</VersionMajor>
          <VersionMinor>8</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
        <COMReference Include="Microsoft.Vbe.Interop">
          <Guid>{0002E157-0000-0000-C000-000000000046}</Guid>
          <VersionMajor>5</VersionMajor>
          <VersionMinor>3</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
      </ItemGroup>
    </Otherwise>
  </Choose>
  <ItemGroup>
    <Reference Include="stdole, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <!--
    This section defines the user source files that are part of the project.
     
    A "Compile" element specifies a source file to compile.
    An "EmbeddedResource" element specifies an .resx file for embedded resources.
    A "None" element specifies a file that is not to be passed to the compiler (for instance, 
    a text file or XML file).
    The "AppDesigner" element specifies the directory where the application properties files
    can be found.
  -->
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Features\ExcelTrace\UI\TraceView.xaml.cs">
      <DependentUpon>TraceView.xaml</DependentUpon>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="App.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="ThisAddIn.cs">
      <SubType>Code</SubType>
    </Compile>
    <None Include="QuantBoost_Excel_TemporaryKey.pfx" />
    <None Include="ThisAddIn.Designer.xml">
      <DependentUpon>ThisAddIn.cs</DependentUpon>
    </None>
    <Compile Include="ThisAddIn.Designer.cs">
      <DependentUpon>ThisAddIn.Designer.xml</DependentUpon>
    </Compile>
    <!-- Include UI source files -->
    <Compile Include="UI\MainRibbon.cs" />
    <!-- Include ExcelTrace feature files -->
    <Compile Include="Features\ExcelTrace\Model\TraceNode.cs" />
    <Compile Include="Features\ExcelTrace\Logic\TracerEngine.cs" />
    <Compile Include="Features\ExcelTrace\Logic\TraceSettings.cs" />
    <Compile Include="Features\ExcelTrace\UI\frmTrace.cs">
      <SubType>Form</SubType>
    </Compile>
    <!-- Include SizeAnalyzer feature files -->
    <Compile Include="Features\SizeAnalyzer\Logic\ExcelAnalysisModels.cs" />
    <Compile Include="Features\SizeAnalyzer\Logic\AnalysisService.cs" />
    <Compile Include="Features\SizeAnalyzer\ViewModels\AnalysisPaneViewModel.cs" />
    <Compile Include="Features\SizeAnalyzer\Views\AnalysisPaneView.xaml.cs">
      <DependentUpon>AnalysisPaneView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Features\SizeAnalyzer\UI\WpfHostControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Features\SizeAnalyzer\UI\AnalyzePane.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <Import Project="..\QuantBoost_Shared\QuantBoost_Shared.projitems" Label="Shared" />
  <ItemGroup>
    <EmbeddedResource Include="UI\MainRibbon.xml" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\QuantBoost_Licensing\QuantBoost_Licensing.csproj">
      <Project>{ec2d374b-a69b-4214-a1fc-7cef1ad5931c}</Project>
      <Name>QuantBoost_Licensing</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Resources\trace_16x16.png" />
    <EmbeddedResource Include="Resources\trace_24x24.png" />
    <EmbeddedResource Include="Resources\trace_32x32.png" />
    <EmbeddedResource Include="Resources\trace_40x40.png" />
    <EmbeddedResource Include="Resources\trace_48x48.png" />
    <EmbeddedResource Include="Resources\trace_64x64.png" />
    <EmbeddedResource Include="Resources\trace_80x80.png" />
    <EmbeddedResource Include="Resources\trace_96x96.png" />
    <EmbeddedResource Include="Resources\login_16x16.PNG" />
    <EmbeddedResource Include="Resources\login_24x24.PNG" />
    <EmbeddedResource Include="Resources\login_32x32.PNG" />
    <EmbeddedResource Include="Resources\login_40x40.PNG" />
    <EmbeddedResource Include="Resources\login_48x48.PNG" />
    <EmbeddedResource Include="Resources\login_64x64.PNG" />
    <EmbeddedResource Include="Resources\login_80x80.PNG" />
    <EmbeddedResource Include="Resources\login_96x96.PNG" />
    <EmbeddedResource Include="Resources\logout_16x16.PNG" />
    <EmbeddedResource Include="Resources\logout_32x32.PNG" />
    <EmbeddedResource Include="Resources\logout_40x40.PNG" />
    <EmbeddedResource Include="Resources\logout_48x48.PNG" />
    <EmbeddedResource Include="Resources\logout_64x64.PNG" />
    <EmbeddedResource Include="Resources\logout_80x80.PNG" />
    <EmbeddedResource Include="Resources\logout_96x96.PNG" />
    <EmbeddedResource Include="Resources\excel_size_16x16.PNG" />
    <EmbeddedResource Include="Resources\excel_size_24x24.PNG" />
    <EmbeddedResource Include="Resources\excel_size_32x32.PNG" />
    <EmbeddedResource Include="Resources\excel_size_40x40.PNG" />
    <EmbeddedResource Include="Resources\excel_size_48x48.PNG" />
    <EmbeddedResource Include="Resources\excel_size_64x64.PNG" />
    <EmbeddedResource Include="Resources\excel_size_80x80.PNG" />
    <EmbeddedResource Include="Resources\excel_size_96x96.PNG" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="Features\ExcelTrace\UI\TraceView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Features\SizeAnalyzer\Views\AnalysisPaneView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>

  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>QuantBoost_Excel_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>1300A16A5843DFF0219DDAA420FD56B09E8A6F75</ManifestCertificateThumbprint>
  </PropertyGroup>
  <!-- Include the build rules for a C# project. -->
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- Include additional build rules for an Office application add-in. -->
  <Import Project="$(VSToolsPath)\OfficeTools\Microsoft.VisualStudio.Tools.Office.targets" Condition="'$(VSToolsPath)' != ''" />
  <!-- This section defines VSTO properties that describe the host-changeable project properties. -->
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{BAA0C2D2-18E2-41B9-852F-F413020CAA33}">
        <ProjectProperties HostName="Excel" HostPackage="{29A7B9D7-A7F1-4328-8EF0-6B2D1A56B2C1}" OfficeVersion="15.0" VstxVersion="4.0" ApplicationType="Excel" Language="cs" TemplatesPath="" DebugInfoExeName="#Software\Microsoft\Office\16.0\Excel\InstallRoot\Path#excel.exe" DebugInfoCommandLine="/x" AddItemTemplatesGuid="{51063C3A-E220-4D12-8922-BDA915ACD783}" />
        <Host Name="Excel" GeneratedCodeNamespace="QuantBoost_Excel" IconIndex="0">
          <HostItem Name="ThisAddIn" Code="ThisAddIn.cs" CanonicalName="AddIn" CanActivate="false" IconIndex="1" Blueprint="ThisAddIn.Designer.xml" GeneratedCode="ThisAddIn.Designer.cs" />
        </Host>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>