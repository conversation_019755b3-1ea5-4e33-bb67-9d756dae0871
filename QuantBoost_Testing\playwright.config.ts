import { defineConfig, devices } from '@playwright/test';
import * as dotenv from 'dotenv';
import { resolve } from 'path';

// Load .env.local if present (preferred for local testing)
dotenv.config({ path: resolve(process.cwd(), '.env.local') });
// Fallback to .env if .env.local doesn't exist
dotenv.config({ path: resolve(process.cwd(), '.env') });

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

export default defineConfig({
  testDir: 'playwright/tests',
  timeout: 60_000,
  expect: { timeout: 10_000 },
  fullyParallel: false,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [ ['line'], ['html', { outputFolder: 'playwright/reports/html', open: 'never' }] ],
  use: {
    baseURL: BASE_URL,
    trace: 'retain-on-failure',
    video: 'retain-on-failure',
    screenshot: 'only-on-failure'
  },
  projects: [ { name: 'chromium', use: { ...devices['Desktop Chrome'] } } ],
  globalSetup: 'playwright/fixtures/global-setup.ts',
  globalTeardown: 'playwright/fixtures/global-teardown.ts'
});
