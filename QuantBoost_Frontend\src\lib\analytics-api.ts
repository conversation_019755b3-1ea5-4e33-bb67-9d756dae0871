import { DisputeMetrics, PaymentHealthMetrics, DisputeAnalytics, RefundAnalytics, CustomerLifecycleAnalytics, SystemAlert, AlertSummary, PaymentHealthCustomer, PaymentEvent, DisputeReasonStats, RiskAnalysis } from '@/types/analytics';

const API_BASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;

interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

class AnalyticsApiClient {
  private async makeRequest<T>(endpoint: string, token: string, params: Record<string, string> = {}): Promise<T> {
    const url = new URL(`${API_BASE_URL}/functions/v1/${endpoint}`);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, value);
      }
    });

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed: ${response.status} ${errorText}`);
    }

    const result: ApiResponse<T> = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'API request failed');
    }

    return result.data;
  }

  // Dispute Analytics
  async getDisputeDashboard(token: string, dateRange: number = 30): Promise<DisputeMetrics> {
    return this.makeRequest<DisputeMetrics>('dispute-analytics', token, {
      action: 'dashboard',
      dateRange: dateRange.toString(),
    });
  }

  async getDisputeList(token: string, params: {
    status?: string;
    priority?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<DisputeAnalytics[]> {
    return this.makeRequest<DisputeAnalytics[]>('dispute-analytics', token, {
      action: 'list',
      ...Object.fromEntries(Object.entries(params).map(([k, v]) => [k, v?.toString() || ''])),
    });
  }

  async getOverdueDisputes(token: string): Promise<DisputeAnalytics[]> {
    return this.makeRequest<DisputeAnalytics[]>('dispute-analytics', token, {
      action: 'overdue',
    });
  }

  async getDisputesByReason(token: string, dateRange: number = 30): Promise<DisputeReasonStats> {
    return this.makeRequest<DisputeReasonStats>('dispute-analytics', token, {
      action: 'by-reason',
      dateRange: dateRange.toString(),
    });
  }

  // Payment Analytics
  async getPaymentHealthDashboard(token: string, dateRange: number = 30): Promise<PaymentHealthMetrics> {
    return this.makeRequest<PaymentHealthMetrics>('payment-analytics', token, {
      action: 'dashboard',
      dateRange: dateRange.toString(),
    });
  }

  async getPaymentHealthMetrics(token: string, params: {
    userId?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<PaymentHealthCustomer[]> {
    return this.makeRequest<PaymentHealthCustomer[]>('payment-analytics', token, {
      action: 'health-metrics',
      ...Object.fromEntries(Object.entries(params).map(([k, v]) => [k, v?.toString() || ''])),
    });
  }

  async getCustomerLifecycleAnalytics(token: string, params: {
    userId?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<CustomerLifecycleAnalytics[]> {
    return this.makeRequest<CustomerLifecycleAnalytics[]>('payment-analytics', token, {
      action: 'customer-lifecycle',
      ...Object.fromEntries(Object.entries(params).map(([k, v]) => [k, v?.toString() || ''])),
    });
  }

  async getRefundAnalytics(token: string, params: {
    userId?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<RefundAnalytics[]> {
    return this.makeRequest<RefundAnalytics[]>('payment-analytics', token, {
      action: 'refund-analytics',
      ...Object.fromEntries(Object.entries(params).map(([k, v]) => [k, v?.toString() || ''])),
    });
  }

  async getPaymentEvents(token: string, params: {
    userId?: string;
    dateRange?: number;
    limit?: number;
    offset?: number;
  } = {}): Promise<PaymentEvent[]> {
    return this.makeRequest<PaymentEvent[]>('payment-analytics', token, {
      action: 'payment-events',
      ...Object.fromEntries(Object.entries(params).map(([k, v]) => [k, v?.toString() || ''])),
    });
  }

  async getRiskAnalysis(token: string, dateRange: number = 30): Promise<RiskAnalysis> {
    return this.makeRequest<RiskAnalysis>('payment-analytics', token, {
      action: 'risk-analysis',
      dateRange: dateRange.toString(),
    });
  }

  // Alert Management
  async getAlertDashboard(token: string): Promise<AlertSummary> {
    return this.makeRequest<AlertSummary>('alert-management', token, {
      action: 'dashboard',
    });
  }

  async getAlerts(token: string, params: {
    severity?: string;
    type?: string;
    read?: boolean;
    resolved?: boolean;
    limit?: number;
    offset?: number;
  } = {}): Promise<SystemAlert[]> {
    const queryParams: Record<string, string> = {
      action: 'list',
    };

    if (params.severity) queryParams.severity = params.severity;
    if (params.type) queryParams.type = params.type;
    if (params.read !== undefined) queryParams.read = params.read.toString();
    if (params.resolved !== undefined) queryParams.resolved = params.resolved.toString();
    if (params.limit) queryParams.limit = params.limit.toString();
    if (params.offset) queryParams.offset = params.offset.toString();

    return this.makeRequest<SystemAlert[]>('alert-management', token, queryParams);
  }

  async updateAlert(token: string, alertId: string, updates: {
    is_read?: boolean;
    is_resolved?: boolean;
  }): Promise<SystemAlert> {
    const url = new URL(`${API_BASE_URL}/functions/v1/alert-management`);
    url.searchParams.append('id', alertId);

    const response = await fetch(url.toString(), {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to update alert: ${response.status} ${errorText}`);
    }

    const result: ApiResponse<SystemAlert> = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to update alert');
    }

    return result.data;
  }

  async checkOverdueDisputes(token: string): Promise<void> {
    const url = new URL(`${API_BASE_URL}/functions/v1/alert-management`);
    url.searchParams.append('action', 'check-overdue');

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to check overdue disputes: ${response.status} ${errorText}`);
    }

    const result: ApiResponse<void> = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to check overdue disputes');
    }
  }
}

export const analyticsApi = new AnalyticsApiClient();
