# Payment Redirect Fix Documentation - Updated Architecture

## Problem Description

The QuantBoost checkout flow had a broken redirect after successful payment completion. Users were being redirected to the API container app URL instead of the frontend dashboard, causing CSP violations and route not found errors.

Additionally, the system needs to support both:
1. **Frontend payment flows** - Should redirect to the frontend dashboard
2. **VSTO add-in authentication** - Should handle authentication for the desktop add-in

### Expected Behavior
- **Payment Context**: After payment completion, users should be redirected to the frontend dashboard
- **VSTO Context**: Authentication should work for the desktop add-in without affecting payment flows

### Previous Broken Behavior
Users were redirected to:
```
https://ca-quantboost-api.greentree-6ebc0a43.westus3.azurecontainerapps.io/#access_token=...
```
This caused CSP violations and route not found errors.

## Root Cause Analysis

The issue was that Supabase only allows **one Site URL** to be configured, and it was set to:
```
https://ca-quantboost-api.greentree-6ebc0a43.westus3.azurecontainerapps.io
```

This Site URL is needed for the VSTO add-in magic link authentication, but it was causing payment flow redirects to go to the API instead of the frontend.

## Solution Architecture

### New Flow Design
1. **Supabase Site URL remains pointed to API** (for VSTO compatibility)
2. **API acts as a smart router** - determines context and redirects appropriately
3. **Payment magic links** go to API with payment context parameters
4. **VSTO magic links** go to API without payment context
5. **API callback route** (`/v1/auth/callback`) handles both scenarios

### Flow Diagram
```
[Magic Link] → [API /v1/auth/callback] → {
    if payment context → [Frontend Dashboard]
    if VSTO context   → [VSTO Auth Page]
    else              → [Generic Success]
}
```

## Implementation Details

### 1. New API Auth Callback Route
Added `GET /v1/auth/callback` to `routes/auth.routes.js`:

```javascript
router.get('/callback', async (req, res) => {
    const { payment, email, payment_intent, access_token, refresh_token, error } = req.query;
    
    // Payment context - redirect to frontend
    if (payment === 'success' && email && payment_intent) {
        const frontendUrl = process.env.FRONTEND_URL || 'https://app-quantboost-frontend-staging.azurewebsites.net';
        return res.redirect(`${frontendUrl}/dashboard?payment=success&email=${email}&payment_intent=${payment_intent}`);
    }
    
    // VSTO context - serve auth page with tokens
    if (access_token && refresh_token) {
        // Serve page that communicates tokens back to VSTO add-in
    }
    
    // Handle errors appropriately per context
});
```

### 2. Updated Frontend Post-Payment Login
Modified `src/app/api/auth/post-payment-login/route.ts`:

```typescript
const { data, error } = await supabase.auth.admin.generateLink({
  type: 'magiclink',
  email: email,
  options: {
    redirectTo: `/v1/auth/callback?payment=success&email=${email}&payment_intent=${paymentIntentId}`
  }
});
```

### 3. Environment Variables
Added `FRONTEND_URL` environment variable:
- **Local**: `http://localhost:3000`
- **Staging**: `https://app-quantboost-frontend-staging.azurewebsites.net`
- **Production**: Set in Azure App Service Configuration

## Azure Configuration Required

### API Container App (ca-quantboost-api)
Add environment variable:
```
FRONTEND_URL=https://app-quantboost-frontend-staging.azurewebsites.net
```

### Frontend App Service (app-quantboost-frontend-staging)
No additional environment variables needed for this fix.

## Benefits of This Architecture

### ✅ **Unified Authentication**
- Single Supabase Site URL supports both VSTO and frontend
- No need for complex Supabase configuration changes

### ✅ **Context-Aware Routing**
- API intelligently routes based on request context
- Payment flows go to frontend dashboard
- VSTO flows serve appropriate auth pages

### ✅ **Backwards Compatibility**
- VSTO add-in continues to work unchanged
- Existing magic link flows remain functional

### ✅ **Security**
- All redirects are server-controlled
- No client-side redirect manipulation
- Proper error handling per context

## Files Modified

### API Changes
1. `routes/auth.routes.js` - New `/v1/auth/callback` route

### Frontend Changes  
1. `src/app/api/auth/post-payment-login/route.ts` - Updated redirect URL
2. `src/app/checkout/[priceId]/page.tsx` - Enhanced error handling and logging

### Configuration
1. `.env.staging` - Added `FRONTEND_URL` for local development
2. `.env.local` - Added `FRONTEND_URL` for local development

## Testing Steps

### 1. Local Testing
```bash
# Terminal 1 - Start API
cd QuantBoost_API
npm start

# Terminal 2 - Start Frontend  
cd QuantBoost_Frontend
npm run dev

# Test payment flow
# Visit: http://localhost:3000/checkout/price_1RC3HTE6FvhUKV1bE9D6zf6e
```

### 2. Staging Testing
1. Deploy API with `FRONTEND_URL` environment variable
2. Deploy frontend
3. Test payment flow end-to-end
4. Verify VSTO add-in still works

### 3. Verification Points
- ✅ Payment magic links redirect to frontend dashboard
- ✅ VSTO magic links serve appropriate auth page
- ✅ No CSP violations
- ✅ No "Route not found" errors
- ✅ Proper error handling for both contexts

## VSTO Add-in Integration

The API callback route serves a special page for VSTO authentication that:

1. **Displays tokens** for VSTO to retrieve
2. **Uses postMessage** to communicate with parent window
3. **Stores in localStorage** as fallback
4. **Auto-closes window** after delay

This ensures VSTO add-in authentication continues to work seamlessly.

## Monitoring and Debugging

### Logs to Monitor
- `Auth callback received:` - Shows incoming callback parameters
- `Redirecting payment success to frontend:` - Payment redirects
- `Handling VSTO authentication callback` - VSTO auth handling

### Debug Parameters
Add `?debug=1` to callback URL to see additional logging information.

## Rollback Plan

If issues arise:
1. Revert `routes/auth.routes.js` to remove callback route
2. Revert `post-payment-login/route.ts` to previous version  
3. Redeploy both API and frontend

## Future Improvements

1. **Add callback route tests** to prevent regressions
2. **Implement request signing** for enhanced security
3. **Add callback analytics** for monitoring success rates
4. **Consider rate limiting** for callback endpoints
