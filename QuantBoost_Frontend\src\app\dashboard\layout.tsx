"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { LogoutButton } from '../../components/LogoutButton';
import { useSupabaseClient } from '../../hooks/useSupabaseClient';
import { cn } from '../../lib/utils';

interface NavigationItem {
  href: string;
  label: string;
  icon: string;
}

const baseNavigationItems: NavigationItem[] = [
  { href: '/dashboard', label: 'Dashboard', icon: '📊' },
  { href: '/dashboard/team', label: 'Team Management', icon: '👥' },
];

const salesNavigationItems: NavigationItem[] = [
  { href: '/dashboard/sales', label: 'Sales Dashboard', icon: '🏢' },
];

function Navigation() {
  const pathname = usePathname();
  const supabase = useSupabaseClient();
  const [userRole, setUserRole] = useState<string | null>(null);
  const [navigationItems, setNavigationItems] = useState<NavigationItem[]>(baseNavigationItems);

  useEffect(() => {
    const fetchUserRole = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          const { data: profile } = await supabase
            .from('profiles')
            .select('role')
            .eq('id', user.id)
            .single();

          const role = profile?.role || 'customer';
          setUserRole(role);

          // Add sales dashboard for sales/admin users
          if (['sales', 'admin'].includes(role)) {
            setNavigationItems([...baseNavigationItems, ...salesNavigationItems]);
          } else {
            setNavigationItems(baseNavigationItems);
          }
        }
      } catch (error) {
        console.error('Error fetching user role:', error);
        setNavigationItems(baseNavigationItems);
      }
    };

    fetchUserRole();
  }, [supabase]);

  // Store navigationItems in a global way so MobileNavigation can access it
  React.useEffect(() => {
    (window as any).dashboardNavigationItems = navigationItems;
  }, [navigationItems]);

  return (
    <nav className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <img src="/QuantBoost_LeftLogo_v0.png" alt="QuantBoost" className="h-8" />
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    pathname === item.href
                      ? 'border-blue-500 text-gray-900'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                    'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200'
                  )}
                >
                  <span className="mr-2">{item.icon}</span>
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
          <div className="flex items-center">
            <LogoutButton />
          </div>
        </div>
      </div>
    </nav>
  );
}

function MobileNavigation({ navigationItems }: { navigationItems: NavigationItem[] }) {
  const pathname = usePathname();
  const [items, setItems] = useState<NavigationItem[]>(navigationItems || baseNavigationItems);

  useEffect(() => {
    // Get navigationItems from global state set by Navigation component
    const interval = setInterval(() => {
      const globalItems = (window as any).dashboardNavigationItems;
      if (globalItems) {
        setItems(globalItems);
        clearInterval(interval);
      }
    }, 100);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="sm:hidden">
      <div className="pt-2 pb-3 space-y-1">
        {items.map((item: NavigationItem) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              pathname === item.href
                ? 'bg-blue-50 border-blue-500 text-blue-700'
                : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700',
              'block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200'
            )}
          >
            <span className="mr-2">{item.icon}</span>
            {item.label}
          </Link>
        ))}
      </div>
    </div>
  );
}

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <MobileNavigation navigationItems={[]} />
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {children}
        </div>
      </main>
    </div>
  );
}