﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.Encoding</name>
  </assembly>
  <members>
    <member name="T:System.Security.Cryptography.AsnEncodedData">
      <summary>ASN.1(Abstract Syntax Notation One) 인코딩된 데이터를 나타냅니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Byte[])">
      <summary>바이트 배열을 사용하여 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="rawData">ASN.1(Abstract Syntax Notation One) 인코딩된 데이터가 포함된 바이트 배열입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 클래스의 인스턴스를 사용하여 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="asnEncodedData">
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 클래스의 인스턴스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" />가 null입니다.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Security.Cryptography.Oid,System.Byte[])">
      <summary>
        <see cref="T:System.Security.Cryptography.Oid" /> 개체와 바이트 배열을 사용하여 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="oid">
        <see cref="T:System.Security.Cryptography.Oid" /> 개체</param>
      <param name="rawData">ASN.1(Abstract Syntax Notation One) 인코딩된 데이터가 포함된 바이트 배열입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.String,System.Byte[])">
      <summary>바이트 배열을 사용하여 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="oid">
        <see cref="T:System.Security.Cryptography.Oid" /> 정보를 나타내는 문자열입니다.</param>
      <param name="rawData">ASN.1(Abstract Syntax Notation One) 인코딩된 데이터가 포함된 바이트 배열입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체에서 정보를 복사합니다.</summary>
      <param name="asnEncodedData">새 개체의 기반으로 삼을 <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" />가 null인 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.Format(System.Boolean)">
      <summary>ASN.1(Abstract Syntax Notation One) 인코딩된 데이터의 서식 지정된 버전을 문자열로 반환합니다.</summary>
      <returns>ASN.1(Abstract Syntax Notation One) 인코딩된 데이터를 나타내는 서식 지정된 문자열입니다.</returns>
      <param name="multiLine">반환 문자열에 캐리지 리턴이 포함되어야 하는 경우 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.AsnEncodedData.Oid">
      <summary>
        <see cref="T:System.Security.Cryptography.AsnEncodedData" /> 개체의 <see cref="T:System.Security.Cryptography.Oid" /> 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.Oid" /> 개체</returns>
    </member>
    <member name="P:System.Security.Cryptography.AsnEncodedData.RawData">
      <summary>바이트 배열에 표현된 ASN.1(Abstract Syntax Notation One) 인코딩된 데이터를 가져오거나 설정합니다.</summary>
      <returns>ASN.1(Abstract Syntax Notation One) 인코딩된 데이터를 나타내는 바이트 배열입니다.</returns>
      <exception cref="T:System.ArgumentNullException">값이 null인 경우</exception>
    </member>
    <member name="T:System.Security.Cryptography.Oid">
      <summary>암호화 개체 식별자를 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.Security.Cryptography.Oid)">
      <summary>지정된 <see cref="T:System.Security.Cryptography.Oid" /> 개체를 사용하여 <see cref="T:System.Security.Cryptography.Oid" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="oid">새 개체 식별자를 만드는 데 사용할 개체 식별자 정보입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oid" />가 null인 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.String)">
      <summary>
        <see cref="T:System.Security.Cryptography.Oid" /> 개체의 문자열 값을 사용하여 <see cref="T:System.Security.Cryptography.Oid" /> 개체의 새 인스턴스를 초기화합니다.</summary>
      <param name="oid">개체 식별자입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.String,System.String)">
      <summary>지정된 값과 이름을 사용하여 <see cref="T:System.Security.Cryptography.Oid" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">점으로 구분된 식별자 번호입니다.</param>
      <param name="friendlyName">식별자의 이름입니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.Oid.FriendlyName">
      <summary>식별자의 이름을 가져오거나 설정합니다.</summary>
      <returns>식별자의 이름입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Oid.FromFriendlyName(System.String,System.Security.Cryptography.OidGroup)">
      <summary>지정된 그룹을 검색하여 OID 이름에서 <see cref="T:System.Security.Cryptography.Oid" /> 개체를 만듭니다.</summary>
      <returns>지정된 OID를 나타내는 개체입니다.</returns>
      <param name="friendlyName">식별자의 이름입니다.</param>
      <param name="group">검색할 그룹입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="friendlyName " />가 null입니다.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">OID를 찾을 수 없습니다.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Oid.FromOidValue(System.String,System.Security.Cryptography.OidGroup)">
      <summary>지정된 OID 값 및 그룹을 사용하여 <see cref="T:System.Security.Cryptography.Oid" /> 개체를 만듭니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.Oid" /> 개체의 새 인스턴스입니다.</returns>
      <param name="oidValue">OID 값입니다.</param>
      <param name="group">검색할 그룹입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oidValue" />가 null입니다.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">OID 값에 대한 이름이 없는 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.Oid.Value">
      <summary>점으로 구분된 식별자 번호를 가져오거나 설정합니다.</summary>
      <returns>점으로 구분된 식별자 번호입니다.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidCollection">
      <summary>
        <see cref="T:System.Security.Cryptography.Oid" /> 개체의 컬렉션을 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.#ctor">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.Add(System.Security.Cryptography.Oid)">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체에 <see cref="T:System.Security.Cryptography.Oid" /> 개체를 추가합니다.</summary>
      <returns>추가된 <see cref="T:System.Security.Cryptography.Oid" /> 개체의 인덱스입니다.</returns>
      <param name="oid">컬렉션에 추가할 <see cref="T:System.Security.Cryptography.Oid" /> 개체입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.CopyTo(System.Security.Cryptography.Oid[],System.Int32)">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체를 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체를 복사할 배열입니다.</param>
      <param name="index">복사 작업이 시작되는 위치입니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Count">
      <summary>컬렉션의 <see cref="T:System.Security.Cryptography.Oid" /> 개체의 수를 가져옵니다. </summary>
      <returns>컬렉션에 있는 <see cref="T:System.Security.Cryptography.Oid" /> 개체의 수입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체를 탐색하는 데 사용할 수 있는 <see cref="T:System.Security.Cryptography.OidEnumerator" /> 개체를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.OidEnumerator" /> 개체</returns>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Item(System.Int32)">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체에서 <see cref="T:System.Security.Cryptography.Oid" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.Oid" /> 개체</returns>
      <param name="index">컬렉션에 있는 <see cref="T:System.Security.Cryptography.Oid" /> 개체의 위치입니다.</param>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Item(System.String)">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체의 지정된 문자열 값과 일치하는 <see cref="P:System.Security.Cryptography.Oid.Value" /> 속성 값이나 <see cref="P:System.Security.Cryptography.Oid.FriendlyName" /> 속성 값이 들어 있는 첫 번째 <see cref="T:System.Security.Cryptography.Oid" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Security.Cryptography.Oid" /> 개체</returns>
      <param name="oid">
        <see cref="P:System.Security.Cryptography.Oid.Value" /> 속성이나 <see cref="P:System.Security.Cryptography.Oid.FriendlyName" /> 속성을 나타내는 문자열입니다.</param>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체를 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체를 복사할 대상 배열입니다.</param>
      <param name="index">복사 작업이 시작되는 위치입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 열거형 배열이 될 수 없는 경우또는<paramref name="array" />의 길이가 유효한 오프셋 길이가 아닌 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />값이 범위를 벗어난 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.Cryptography.OidCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.Cryptography.OidCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체를 탐색하는 데 사용할 수 있는 <see cref="T:System.Security.Cryptography.OidEnumerator" /> 개체를 반환합니다.</summary>
      <returns>컬렉션을 탐색하는 데 사용할 수 있는 <see cref="T:System.Security.Cryptography.OidEnumerator" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidEnumerator">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체 전체를 탐색할 수 있는 기능을 제공합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="P:System.Security.Cryptography.OidEnumerator.Current">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체의 현재 <see cref="T:System.Security.Cryptography.Oid" /> 개체를 가져옵니다.</summary>
      <returns>컬렉션의 현재 <see cref="T:System.Security.Cryptography.Oid" /> 개체입니다.</returns>
    </member>
    <member name="M:System.Security.Cryptography.OidEnumerator.MoveNext">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체의 다음 <see cref="T:System.Security.Cryptography.Oid" /> 개체로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우</exception>
    </member>
    <member name="M:System.Security.Cryptography.OidEnumerator.Reset">
      <summary>열거자를 초기 위치로 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우</exception>
    </member>
    <member name="P:System.Security.Cryptography.OidEnumerator.System#Collections#IEnumerator#Current">
      <summary>
        <see cref="T:System.Security.Cryptography.OidCollection" /> 개체의 현재 <see cref="T:System.Security.Cryptography.Oid" /> 개체를 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Security.Cryptography.Oid" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidGroup">
      <summary>Windows 암호화 개체 식별자(OID) 그룹을 식별합니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.All">
      <summary>모든 그룹입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Attribute">
      <summary>CRYPT_RDN_ATTR_OID_GROUP_ID로 표현되는 Windows 그룹입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.EncryptionAlgorithm">
      <summary>CRYPT_ENCRYPT_ALG_OID_GROUP_ID로 표현되는 Windows 그룹입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.EnhancedKeyUsage">
      <summary>CRYPT_ENHKEY_USAGE_OID_GROUP_ID로 표현되는 Windows 그룹입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.ExtensionOrAttribute">
      <summary>CRYPT_EXT_OR_ATTR_OID_GROUP_ID로 표현되는 Windows 그룹입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.HashAlgorithm">
      <summary>CRYPT_HASH_ALG_OID_GROUP_ID에 의해 표현되는 Windows 그룹입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.KeyDerivationFunction">
      <summary>CRYPT_KDF_OID_GROUP_ID로 표현되는 Windows 그룹입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Policy">
      <summary>CRYPT_POLICY_OID_GROUP_ID로 표현되는 Windows 그룹입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.PublicKeyAlgorithm">
      <summary>CRYPT_PUBKEY_ALG_OID_GROUP_ID로 표현되는 Windows 그룹입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.SignatureAlgorithm">
      <summary>CRYPT_SIGN_ALG_OID_GROUP_ID에 의해 표현되는 Windows 그룹입니다.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Template">
      <summary>CRYPT_TEMPLATE_OID_GROUP_ID로 표현되는 Windows 그룹입니다.</summary>
    </member>
  </members>
</doc>