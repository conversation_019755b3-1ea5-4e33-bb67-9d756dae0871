import * as z from 'zod';

// Country list for validation
export const COUNTRIES = [
  { code: 'US', name: 'United States' },
  { code: 'CA', name: 'Canada' },
  { code: 'GB', name: 'United Kingdom' },
  { code: 'AU', name: 'Australia' },
  { code: 'DE', name: 'Germany' },
  { code: 'FR', name: 'France' },
  { code: 'IT', name: 'Italy' },
  { code: 'ES', name: 'Spain' },
  { code: 'NL', name: 'Netherlands' },
  { code: 'SE', name: 'Sweden' },
  { code: 'NO', name: 'Norway' },
  { code: 'DK', name: 'Denmark' },
  { code: 'FI', name: 'Finland' },
  { code: 'CH', name: 'Switzerland' },
  { code: 'AT', name: 'Austria' },
  { code: 'BE', name: 'Belgium' },
  { code: 'IE', name: 'Ireland' },
  { code: 'NZ', name: 'New Zealand' },
  { code: 'JP', name: 'Japan' },
  { code: 'SG', name: 'Singapore' },
] as const;

export const COUNTRY_CODES = COUNTRIES.map(country => country.code) as [string, ...string[]];

// US States for validation
export const US_STATES = [
  { code: 'AL', name: 'Alabama' },
  { code: 'AK', name: 'Alaska' },
  { code: 'AZ', name: 'Arizona' },
  { code: 'AR', name: 'Arkansas' },
  { code: 'CA', name: 'California' },
  { code: 'CO', name: 'Colorado' },
  { code: 'CT', name: 'Connecticut' },
  { code: 'DE', name: 'Delaware' },
  { code: 'FL', name: 'Florida' },
  { code: 'GA', name: 'Georgia' },
  { code: 'HI', name: 'Hawaii' },
  { code: 'ID', name: 'Idaho' },
  { code: 'IL', name: 'Illinois' },
  { code: 'IN', name: 'Indiana' },
  { code: 'IA', name: 'Iowa' },
  { code: 'KS', name: 'Kansas' },
  { code: 'KY', name: 'Kentucky' },
  { code: 'LA', name: 'Louisiana' },
  { code: 'ME', name: 'Maine' },
  { code: 'MD', name: 'Maryland' },
  { code: 'MA', name: 'Massachusetts' },
  { code: 'MI', name: 'Michigan' },
  { code: 'MN', name: 'Minnesota' },
  { code: 'MS', name: 'Mississippi' },
  { code: 'MO', name: 'Missouri' },
  { code: 'MT', name: 'Montana' },
  { code: 'NE', name: 'Nebraska' },
  { code: 'NV', name: 'Nevada' },
  { code: 'NH', name: 'New Hampshire' },
  { code: 'NJ', name: 'New Jersey' },
  { code: 'NM', name: 'New Mexico' },
  { code: 'NY', name: 'New York' },
  { code: 'NC', name: 'North Carolina' },
  { code: 'ND', name: 'North Dakota' },
  { code: 'OH', name: 'Ohio' },
  { code: 'OK', name: 'Oklahoma' },
  { code: 'OR', name: 'Oregon' },
  { code: 'PA', name: 'Pennsylvania' },
  { code: 'RI', name: 'Rhode Island' },
  { code: 'SC', name: 'South Carolina' },
  { code: 'SD', name: 'South Dakota' },
  { code: 'TN', name: 'Tennessee' },
  { code: 'TX', name: 'Texas' },
  { code: 'UT', name: 'Utah' },
  { code: 'VT', name: 'Vermont' },
  { code: 'VA', name: 'Virginia' },
  { code: 'WA', name: 'Washington' },
  { code: 'WV', name: 'West Virginia' },
  { code: 'WI', name: 'Wisconsin' },
  { code: 'WY', name: 'Wyoming' },
  { code: 'DC', name: 'District of Columbia' },
] as const;

export const US_STATE_CODES = US_STATES.map(state => state.code) as [string, ...string[]];

// Canadian Provinces for validation
export const CA_PROVINCES = [
  { code: 'AB', name: 'Alberta' },
  { code: 'BC', name: 'British Columbia' },
  { code: 'MB', name: 'Manitoba' },
  { code: 'NB', name: 'New Brunswick' },
  { code: 'NL', name: 'Newfoundland and Labrador' },
  { code: 'NS', name: 'Nova Scotia' },
  { code: 'ON', name: 'Ontario' },
  { code: 'PE', name: 'Prince Edward Island' },
  { code: 'QC', name: 'Quebec' },
  { code: 'SK', name: 'Saskatchewan' },
  { code: 'NT', name: 'Northwest Territories' },
  { code: 'NU', name: 'Nunavut' },
  { code: 'YT', name: 'Yukon' },
] as const;

export const CA_PROVINCE_CODES = CA_PROVINCES.map(province => province.code) as [string, ...string[]];

// Phone number validation regex (international format)
const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;

// Postal code validation patterns
const postalCodePatterns = {
  US: /^\d{5}(-\d{4})?$/,
  CA: /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/,
  GB: /^[A-Za-z]{1,2}\d[A-Za-z\d]?\s?\d[A-Za-z]{2}$/,
  AU: /^\d{4}$/,
  DE: /^\d{5}$/,
  FR: /^\d{5}$/,
  IT: /^\d{5}$/,
  ES: /^\d{5}$/,
  NL: /^\d{4}\s?[A-Za-z]{2}$/,
  SE: /^\d{3}\s?\d{2}$/,
  NO: /^\d{4}$/,
  DK: /^\d{4}$/,
  FI: /^\d{5}$/,
  CH: /^\d{4}$/,
  AT: /^\d{4}$/,
  BE: /^\d{4}$/,
  IE: /^[A-Za-z]\d{2}\s?[A-Za-z\d]{4}$/,
  NZ: /^\d{4}$/,
  JP: /^\d{3}-\d{4}$/,
  SG: /^\d{6}$/,
};

// Checkout form validation schema without email (email collected separately)
export const checkoutFormWithoutEmailSchema = z.object({
  // Personal Information
  firstName: z.string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'First name contains invalid characters'),

  lastName: z.string()
    .min(1, 'Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'Last name contains invalid characters'),

  phone: z.string()
    .optional()
    .refine((val) => !val || phoneRegex.test(val.replace(/[\s\-\(\)]/g, '')), {
      message: 'Please enter a valid phone number'
    }),

  // Billing Address
  addressLine1: z.string()
    .min(1, 'Street address is required')
    .max(100, 'Street address is too long'),

  addressLine2: z.string()
    .max(100, 'Address line 2 is too long')
    .optional(),

  city: z.string()
    .min(1, 'City is required')
    .max(50, 'City name is too long')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'City contains invalid characters'),

  state: z.string()
    .min(1, 'State/Province is required')
    .max(50, 'State/Province is too long'),

  postalCode: z.string()
    .min(1, 'Postal code is required')
    .max(20, 'Postal code is too long'),

  country: z.enum(COUNTRY_CODES, {
    errorMap: () => ({ message: 'Please select a valid country' })
  }),

  // Team size for team plans
  quantity: z.number()
    .min(1, 'Quantity must be at least 1')
    .max(1000, 'Quantity cannot exceed 1000')
    .optional(),

  // Legal agreements
  acceptTerms: z.boolean()
    .refine(val => val === true, {
      message: 'You must accept the Terms of Service'
    }),

  acceptPrivacy: z.boolean()
    .refine(val => val === true, {
      message: 'You must accept the Privacy Policy'
    }),
});

// Type for checkout form data without email
export type CheckoutFormWithoutEmailData = z.infer<typeof checkoutFormWithoutEmailSchema>;

// Comprehensive checkout form validation schema (with email)
export const checkoutFormSchema = z.object({
  // Personal Information
  firstName: z.string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'First name contains invalid characters'),
  
  lastName: z.string()
    .min(1, 'Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'Last name contains invalid characters'),
  
  email: z.string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(254, 'Email address is too long'),
  
  phone: z.string()
    .optional()
    .refine((val) => !val || phoneRegex.test(val.replace(/[\s\-\(\)]/g, '')), {
      message: 'Please enter a valid phone number'
    }),

  // Billing Address
  addressLine1: z.string()
    .min(1, 'Street address is required')
    .max(100, 'Street address is too long'),

  addressLine2: z.string()
    .max(100, 'Address line 2 is too long')
    .optional(),
  
  city: z.string()
    .min(1, 'City is required')
    .max(50, 'City name is too long')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'City contains invalid characters'),
  
  state: z.string()
    .min(1, 'State/Province is required')
    .max(50, 'State/Province is too long'),
  
  postalCode: z.string()
    .min(1, 'Postal code is required')
    .max(20, 'Postal code is too long'),
  
  country: z.enum(COUNTRY_CODES, {
    errorMap: () => ({ message: 'Please select a valid country' })
  }),

  // Payment Information handled by Stripe PaymentElement

  // Terms and Privacy
  acceptTerms: z.boolean()
    .refine(val => val === true, {
      message: 'You must accept the terms of service'
    }),
  
  acceptPrivacy: z.boolean()
    .refine(val => val === true, {
      message: 'You must accept the privacy policy'
    }),

  // Team plan specific
  quantity: z.number()
    .min(1, 'Quantity must be at least 1')
    .max(50, 'Maximum 50 licenses allowed')
    .optional(),
}).refine((data) => {
  // Validate postal code based on country
  const pattern = postalCodePatterns[data.country as keyof typeof postalCodePatterns];
  if (pattern && !pattern.test(data.postalCode)) {
    return false;
  }
  return true;
}, {
  message: 'Invalid postal code format for selected country',
  path: ['postalCode']
}).refine((data) => {
  // Validate state/province for US and Canada
  if (data.country === 'US' && !US_STATE_CODES.includes(data.state)) {
    return false;
  }
  if (data.country === 'CA' && !CA_PROVINCE_CODES.includes(data.state)) {
    return false;
  }
  return true;
}, {
  message: 'Invalid state/province for selected country',
  path: ['state']
});

export type CheckoutFormData = z.infer<typeof checkoutFormSchema>;

// Helper function to get states/provinces based on country
export function getStatesForCountry(countryCode: string) {
  switch (countryCode) {
    case 'US':
      return US_STATES;
    case 'CA':
      return CA_PROVINCES;
    default:
      return [];
  }
}

// Helper function to format phone number
export function formatPhoneNumber(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }
  return phone;
}

// Helper function to validate postal code format
export function validatePostalCode(postalCode: string, countryCode: string): boolean {
  const pattern = postalCodePatterns[countryCode as keyof typeof postalCodePatterns];
  return pattern ? pattern.test(postalCode) : true;
}

// Security: Input sanitization functions
export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 1000); // Limit length
}

export function sanitizeName(name: string): string {
  return name
    .trim()
    .replace(/[^a-zA-Z\s\-'\.]/g, '') // Only allow letters, spaces, hyphens, apostrophes, periods
    .substring(0, 50); // Limit length
}

export function sanitizeEmail(email: string): string {
  return email
    .trim()
    .toLowerCase()
    .substring(0, 254); // RFC 5321 limit
}

export function sanitizePhone(phone: string): string {
  return phone
    .replace(/[^\d\+\-\(\)\s]/g, '') // Only allow digits, +, -, (), spaces
    .substring(0, 20); // Reasonable phone number length
}

export function sanitizeAddress(address: string): string {
  return address
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 100); // Reasonable address length
}
