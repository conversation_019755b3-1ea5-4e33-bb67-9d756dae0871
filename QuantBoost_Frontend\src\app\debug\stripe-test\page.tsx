'use client';

import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui';

export default function StripeTestPage() {
  const [stripeStatus, setStripeStatus] = useState<{
    loading: boolean;
    success: boolean;
    error: string | null;
    publishableKey: string | null;
    stripeInstance: any;
  }>({
    loading: true,
    success: false,
    error: null,
    publishableKey: null,
    stripeInstance: null
  });

  const [networkTests, setNetworkTests] = useState<{
    stripeJs: { status: string; error?: string };
    stripeApi: { status: string; error?: string };
  }>({
    stripeJs: { status: 'pending' },
    stripeApi: { status: 'pending' }
  });

  useEffect(() => {
    async function testStripeLoading() {
      try {
        // Test 1: Check if publishable key is available
        const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
        console.log('🔑 Stripe publishable key available:', !!publishableKey);
        
        if (!publishableKey) {
          setStripeStatus({
            loading: false,
            success: false,
            error: 'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY not found',
            publishableKey: null,
            stripeInstance: null
          });
          return;
        }

        // Test 2: Try to load Stripe
        console.log('🚀 Attempting to load Stripe...');
        const stripe = await loadStripe(publishableKey);
        
        if (stripe) {
          console.log('✅ Stripe loaded successfully');
          setStripeStatus({
            loading: false,
            success: true,
            error: null,
            publishableKey: publishableKey.substring(0, 20) + '...',
            stripeInstance: stripe
          });
        } else {
          console.log('❌ Stripe failed to load');
          setStripeStatus({
            loading: false,
            success: false,
            error: 'Stripe failed to initialize (returned null)',
            publishableKey: publishableKey.substring(0, 20) + '...',
            stripeInstance: null
          });
        }
      } catch (error) {
        console.error('❌ Error loading Stripe:', error);
        setStripeStatus({
          loading: false,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY?.substring(0, 20) + '...' || null,
          stripeInstance: null
        });
      }
    }

    async function testNetworkConnectivity() {
      // Test Stripe JS loading directly
      try {
        const response = await fetch('https://js.stripe.com/v3/', { method: 'HEAD' });
        setNetworkTests(prev => ({
          ...prev,
          stripeJs: { 
            status: response.ok ? 'success' : 'failed',
            error: response.ok ? undefined : `HTTP ${response.status}`
          }
        }));
      } catch (error) {
        setNetworkTests(prev => ({
          ...prev,
          stripeJs: { 
            status: 'blocked',
            error: error instanceof Error ? error.message : 'Network error'
          }
        }));
      }

      // Test Stripe API connectivity
      try {
        const response = await fetch('https://api.stripe.com/healthcheck', { method: 'HEAD' });
        setNetworkTests(prev => ({
          ...prev,
          stripeApi: { 
            status: response.ok ? 'success' : 'failed',
            error: response.ok ? undefined : `HTTP ${response.status}`
          }
        }));
      } catch (error) {
        setNetworkTests(prev => ({
          ...prev,
          stripeApi: { 
            status: 'blocked',
            error: error instanceof Error ? error.message : 'Network error'
          }
        }));
      }
    }

    testStripeLoading();
    testNetworkConnectivity();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'blocked': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'failed': return '❌';
      case 'blocked': return '🚫';
      default: return '⏳';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Stripe Integration Diagnostic</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Environment Configuration</h3>
            <div className="bg-gray-50 p-3 rounded">
              <p><strong>Publishable Key:</strong> {stripeStatus.publishableKey || 'Not available'}</p>
              <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Stripe Loading Status</h3>
            <div className="bg-gray-50 p-3 rounded">
              {stripeStatus.loading ? (
                <p>⏳ Loading Stripe...</p>
              ) : stripeStatus.success ? (
                <p className="text-green-600">✅ Stripe loaded successfully</p>
              ) : (
                <div>
                  <p className="text-red-600">❌ Stripe failed to load</p>
                  <p className="text-sm text-gray-600 mt-1">Error: {stripeStatus.error}</p>
                </div>
              )}
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Network Connectivity Tests</h3>
            <div className="bg-gray-50 p-3 rounded space-y-2">
              <div className="flex items-center justify-between">
                <span>Stripe JS (js.stripe.com):</span>
                <span className={getStatusColor(networkTests.stripeJs.status)}>
                  {getStatusIcon(networkTests.stripeJs.status)} {networkTests.stripeJs.status}
                  {networkTests.stripeJs.error && (
                    <span className="text-sm ml-2">({networkTests.stripeJs.error})</span>
                  )}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>Stripe API (api.stripe.com):</span>
                <span className={getStatusColor(networkTests.stripeApi.status)}>
                  {getStatusIcon(networkTests.stripeApi.status)} {networkTests.stripeApi.status}
                  {networkTests.stripeApi.error && (
                    <span className="text-sm ml-2">({networkTests.stripeApi.error})</span>
                  )}
                </span>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Troubleshooting Steps</h3>
            <div className="bg-blue-50 p-3 rounded">
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Try opening this page in an incognito/private window</li>
                <li>Disable browser extensions (especially ad blockers)</li>
                <li>Check if your network/firewall blocks Stripe domains</li>
                <li>Try a different browser or device</li>
                <li>Check browser console for additional error messages</li>
              </ol>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Browser Information</h3>
            <div className="bg-gray-50 p-3 rounded text-sm">
              <p><strong>User Agent:</strong> {typeof navigator !== 'undefined' ? navigator.userAgent : 'Server-side rendering'}</p>
              <p><strong>URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'Server-side rendering'}</p>
              <p><strong>Timestamp:</strong> {new Date().toISOString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
