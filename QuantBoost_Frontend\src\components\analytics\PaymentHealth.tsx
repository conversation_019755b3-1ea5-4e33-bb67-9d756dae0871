'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, CreditCard, AlertCircle, DollarSign, Percent } from 'lucide-react';
import { PaymentHealthMetrics } from '@/types/analytics';

interface PaymentHealthProps {
  metrics: PaymentHealthMetrics | null;
  isLoading: boolean;
}

export function PaymentHealth({ metrics, isLoading }: PaymentHealthProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div className="h-3 bg-gray-200 rounded animate-pulse w-2/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!metrics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-blue-500" />
            Payment Health
          </CardTitle>
          <CardDescription>No payment data available</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 95) return 'text-green-600';
    if (rate >= 85) return 'text-yellow-500';
    return 'text-red-600';
  };

  const getSuccessRateBadge = (rate: number) => {
    if (rate >= 95) return 'default';
    if (rate >= 85) return 'secondary';
    return 'destructive';
  };

  const getRiskScoreColor = (score: number) => {
    if (score <= 30) return 'text-green-600';
    if (score <= 60) return 'text-yellow-500';
    return 'text-red-600';
  };

  const getRiskScoreBadge = (score: number) => {
    if (score <= 30) return 'default';
    if (score <= 60) return 'secondary';
    return 'destructive';
  };

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getSuccessRateColor(metrics.success_rate_percentage)}`}>
              {metrics.success_rate_percentage.toFixed(1)}%
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={getSuccessRateBadge(metrics.success_rate_percentage)}>
                {metrics.success_rate_percentage >= 95 ? 'Excellent' : 
                 metrics.success_rate_percentage >= 85 ? 'Good' : 'Poor'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Payment Volume</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${metrics.total_amount_processed.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.successful_payments}/{metrics.total_payment_attempts} successful
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Risk Score</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getRiskScoreColor(metrics.avg_risk_score)}`}>
              {metrics.avg_risk_score.toFixed(0)}
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={getRiskScoreBadge(metrics.avg_risk_score)}>
                {metrics.avg_risk_score <= 30 ? 'Low Risk' :
                 metrics.avg_risk_score <= 60 ? 'Medium Risk' : 'High Risk'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Refund Rate</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.refund_rate_percentage.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              ${metrics.total_refund_amount.toLocaleString()} refunded
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Successful Payments</span>
              <div className="flex items-center gap-2">
                <span className="font-medium text-green-600">{metrics.successful_payments}</span>
                <TrendingUp className="h-3 w-3 text-green-600" />
              </div>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Failed Payments</span>
              <div className="flex items-center gap-2">
                <span className="font-medium text-red-600">{metrics.failed_payments}</span>
                <TrendingDown className="h-3 w-3 text-red-600" />
              </div>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">High Risk Payments</span>
              <Badge variant={metrics.high_risk_payments > 0 ? 'destructive' : 'default'}>
                {metrics.high_risk_payments}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Percent className="h-5 w-5" />
              Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Average Transaction</span>
              <span className="font-medium">
                ${metrics.total_payment_attempts > 0 ? 
                  (metrics.total_amount_processed / metrics.total_payment_attempts).toFixed(2) : '0'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Processing Volume</span>
              <span className="font-medium">{metrics.total_payment_attempts}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Failure Rate</span>
              <Badge variant={(100 - metrics.success_rate_percentage) > 10 ? 'destructive' : 'default'}>
                {(100 - metrics.success_rate_percentage).toFixed(1)}%
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
