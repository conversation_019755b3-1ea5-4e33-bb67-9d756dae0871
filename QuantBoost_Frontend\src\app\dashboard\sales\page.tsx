"use client";

import { useEffect, useState } from 'react';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, Building2, FileText, Users, DollarSign, Mail, Rocket, Eye } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import CreateEnterpriseCustomerForm from '@/app/dashboard/sales/CreateEnterpriseCustomerForm';
import CreateEnterpriseInvoiceForm from '@/app/dashboard/sales/CreateEnterpriseInvoiceForm';
import { AnalyticsDashboard } from '../../../components/analytics/AnalyticsDashboard';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface EnterpriseCustomer {
  id: string;
  company_name: string;
  contact_email: string;
  contact_name: string;
  license_quantity: number;
  license_tier: string;
  annual_contract_value: number;
  status: string;
  created_at: string;
  sales_rep: {
    email: string;
    first_name: string;
    last_name: string;
  };
}

interface EnterpriseInvoice {
  id: string;
  invoice_number: string;
  amount_due: number;
  status: string;
  due_date: string;
  stripe_hosted_url: string;
  stripe_pdf_url: string;
  enterprise_customer: {
    company_name: string;
  };
}

export default function SalesDashboardPage() {
  const [customers, setCustomers] = useState<EnterpriseCustomer[]>([]);
  const [recentInvoices, setRecentInvoices] = useState<EnterpriseInvoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateCustomer, setShowCreateCustomer] = useState(false);
  const [showCreateInvoice, setShowCreateInvoice] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<EnterpriseCustomer | null>(null);
  const [trials, setTrials] = useState<any[]>([]);
  const [trialSearch, setTrialSearch] = useState('');
  const [createTrialOpen, setCreateTrialOpen] = useState(false);
  const [inviteOpenFor, setInviteOpenFor] = useState<string | null>(null);
  const [inviteText, setInviteText] = useState('');
  const [creatingTrial, setCreatingTrial] = useState(false);
  const [inviting, setInviting] = useState(false);
  const [viewInvitesFor, setViewInvitesFor] = useState<string | null>(null);
  const [invites, setInvites] = useState<{ id: string; email: string; accepted_at: string | null }[]>([]);
  const supabase = useSupabaseClient();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        setError('Authentication required');
        return;
      }

      // Fetch enterprise customers
      const customersResponse = await fetch('/api/sales/enterprise-customers', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!customersResponse.ok) {
        throw new Error('Failed to fetch enterprise customers');
      }

      const customersData = await customersResponse.json();
      setCustomers(customersData);

      // Fetch recent invoices (you'd need to create an endpoint for this)
      // For now, we'll leave it empty
      setRecentInvoices([]);

      // Fetch trials
      const trialsRes = await fetch(`/api/sales/trials?q=${encodeURIComponent(trialSearch)}`, {
        headers: { 'Authorization': `Bearer ${session.access_token}` }
      });
      if (trialsRes.ok) {
        const rows = await trialsRes.json();
        setTrials(rows);
      }

    } catch (error) {
      console.error('Error fetching sales data:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleCustomerCreated = () => {
    setShowCreateCustomer(false);
    fetchData(); // Refresh the list
  };

  const handleInvoiceCreated = () => {
    setShowCreateInvoice(false);
    setSelectedCustomer(null);
    fetchData(); // Refresh the data
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'prospect':
        return 'bg-blue-100 text-blue-800';
      case 'suspended':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  async function createTrial(form: { company: string; contactEmail: string; domain?: string }) {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) { setError('Authentication required'); return; }
    setCreatingTrial(true);
    try {
      const res = await fetch('/api/sales/trials', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${session.access_token}` },
        body: JSON.stringify({ ...form })
      });
      if (!res.ok) throw new Error('Failed to create trial');
      setCreateTrialOpen(false);
      setTrialSearch('');
      await fetchData();
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Failed to create trial');
    } finally { setCreatingTrial(false); }
  }

  async function sendInvites(trialId: string) {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) { setError('Authentication required'); return; }
    setInviting(true);
    try {
      const res = await fetch(`/api/sales/trials/${trialId}/invite`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${session.access_token}` },
        body: JSON.stringify({ csv: inviteText })
      });
      if (!res.ok) throw new Error('Failed to send invites');
      setInviteOpenFor(null);
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Failed to send invites');
    } finally { setInviting(false); }
  }

  async function loadInvites(trialId: string) {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) { setError('Authentication required'); return; }
    const res = await fetch(`/api/sales/trials/${trialId}`, {
      headers: { 'Authorization': `Bearer ${session.access_token}` }
    });
    if (!res.ok) { setError('Failed to load invites'); return; }
    const js = await res.json();
    setInvites((js.invites || []).map((i: any) => ({ id: i.id, email: i.email, accepted_at: i.accepted_at })));
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Sales Dashboard</h3>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Sales Dashboard</h3>
          <p className="text-sm text-muted-foreground">
            Manage enterprise customers and invoices.
          </p>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error Loading Sales Data</CardTitle>
            <CardDescription className="text-red-600">{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={fetchData} 
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Calculate metrics
  const totalCustomers = customers.length;
  const activeCustomers = customers.filter(c => c.status === 'active').length;
  const totalACV = customers.reduce((sum, c) => sum + (c.annual_contract_value || 0), 0);
  const totalLicenses = customers.reduce((sum, c) => sum + c.license_quantity, 0);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Sales & Analytics Dashboard</h3>
          <p className="text-sm text-muted-foreground">
            Manage enterprise customers and monitor business metrics.
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={showCreateCustomer} onOpenChange={setShowCreateCustomer}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Customer
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create Enterprise Customer</DialogTitle>
                <DialogDescription>
                  Add a new enterprise customer to the system.
                </DialogDescription>
              </DialogHeader>
              <CreateEnterpriseCustomerForm onSuccess={handleCustomerCreated} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCustomers}</div>
            <p className="text-xs text-muted-foreground">
              {activeCustomers} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total ACV</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalACV.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Annual contract value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Licenses</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalLicenses}</div>
            <p className="text-xs text-muted-foreground">
              Across all customers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Deal Size</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${totalCustomers > 0 ? Math.round(totalACV / totalCustomers).toLocaleString() : '0'}
            </div>
            <p className="text-xs text-muted-foreground">
              Per customer
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Enterprise Customers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Enterprise Customers</CardTitle>
          <CardDescription>
            Manage your enterprise customer accounts and generate invoices.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {customers.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-sm text-muted-foreground">No enterprise customers yet.</p>
              <Button 
                onClick={() => setShowCreateCustomer(true)}
                variant="outline"
                className="mt-2"
              >
                Create Your First Customer
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Company</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Licenses</TableHead>
                  <TableHead>ACV</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {customers.map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{customer.company_name}</div>
                        <div className="text-sm text-muted-foreground">{customer.license_tier}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{customer.contact_name}</div>
                        <div className="text-sm text-muted-foreground">{customer.contact_email}</div>
                      </div>
                    </TableCell>
                    <TableCell>{customer.license_quantity}</TableCell>
                    <TableCell>${customer.annual_contract_value?.toLocaleString() || '0'}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(customer.status)}>
                        {customer.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedCustomer(customer);
                          setShowCreateInvoice(true);
                        }}
                      >
                        Create Invoice
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Trials Section (MVP) */}
      <Card>
        <CardHeader className="flex items-center justify-between">
          <div>
            <CardTitle>Enterprise Trials</CardTitle>
            <CardDescription>Quickly spin up a 30-day strategic trial and invite users.</CardDescription>
          </div>
          <div className="flex gap-2">
            <Input placeholder="Search company" value={trialSearch} onChange={(e) => setTrialSearch(e.target.value)} className="w-[220px]" />
            <Button onClick={fetchData} variant="outline">Search</Button>
            <Dialog open={createTrialOpen} onOpenChange={setCreateTrialOpen}>
              <DialogTrigger asChild>
                <Button><Plus className="h-4 w-4 mr-2"/> New Trial</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create Trial</DialogTitle>
                  <DialogDescription>30-day trial, seat limit 50 by default.</DialogDescription>
                </DialogHeader>
                <div className="space-y-3">
                  <Label>Company</Label>
                  <Input id="trial_company" />
                  <Label>Contact Email</Label>
                  <Input id="trial_email" type="email" />
                  <Label>Domain (optional)</Label>
                  <Input id="trial_domain" />
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setCreateTrialOpen(false)}>Cancel</Button>
                    <Button onClick={() => {
                      const company = (document.getElementById('trial_company') as HTMLInputElement).value;
                      const contactEmail = (document.getElementById('trial_email') as HTMLInputElement).value;
                      const domain = (document.getElementById('trial_domain') as HTMLInputElement).value;
                      createTrial({ company, contactEmail, domain });
                    }} disabled={creatingTrial}>{creatingTrial ? 'Creating...' : 'Create'}</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {trials.length === 0 ? (
            <div className="text-sm text-muted-foreground">No trials yet. Create one to get started.</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Company</TableHead>
                  <TableHead>Seats</TableHead>
                  <TableHead>Days Left</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {trials.map((t) => (
                  <TableRow key={t.id}>
                    <TableCell>{t.company}</TableCell>
                    <TableCell>{t.seats_used}/{t.seat_limit}</TableCell>
                    <TableCell><Badge>{t.days_left} days</Badge></TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(t.status)}>{t.status}</Badge>
                    </TableCell>
                    <TableCell className="flex gap-2">
                      <Dialog open={inviteOpenFor === t.id} onOpenChange={(o) => setInviteOpenFor(o ? t.id : null)}>
                        <DialogTrigger asChild>
                          <Button size="sm" variant="outline"><Mail className="h-4 w-4 mr-1"/>Invite</Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Bulk Invite</DialogTitle>
                            <DialogDescription>Paste emails separated by commas, spaces, or newlines.</DialogDescription>
                          </DialogHeader>
                          <Textarea value={inviteText} onChange={(e) => setInviteText(e.target.value)} rows={6} />
                          <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={() => setInviteOpenFor(null)}>Cancel</Button>
                            <Button onClick={() => sendInvites(t.id)} disabled={inviting}>{inviting ? 'Sending...' : 'Send Invites'}</Button>
                          </div>
                        </DialogContent>
                      </Dialog>
                      <Button size="sm" onClick={async () => {
                        const { data: { session } } = await supabase.auth.getSession();
                        if (!session) { setError('Authentication required'); return; }
                        const res = await fetch(`/api/sales/trials/${t.id}/convert`, {
                          method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${session.access_token}` },
                          body: JSON.stringify({ priceId: 'price_1RC3HTE6FvhUKV1bE9D6zf6e', quantity: t.seats_used || 1 })
                        });
                        const js = await res.json();
                        if (js.checkoutUrl) window.location.href = js.checkoutUrl;
                      }}>
                        <Rocket className="h-4 w-4 mr-1"/> Convert
                      </Button>
                      <Dialog open={viewInvitesFor === t.id} onOpenChange={(o) => { setViewInvitesFor(o ? t.id : null); if (o) loadInvites(t.id); }}>
                        <DialogTrigger asChild>
                          <Button size="sm" variant="outline"><Eye className="h-4 w-4 mr-1"/>View</Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Invites</DialogTitle>
                            <DialogDescription>Invite statuses for {t.company}</DialogDescription>
                          </DialogHeader>
                          {invites.length === 0 ? (
                            <div className="text-sm text-muted-foreground">No invites yet.</div>
                          ) : (
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Email</TableHead>
                                  <TableHead>Status</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {invites.map((i) => (
                                  <TableRow key={i.id}>
                                    <TableCell>{i.email}</TableCell>
                                    <TableCell>{i.accepted_at ? <Badge className="bg-green-100 text-green-800">Accepted</Badge> : <Badge>Pending</Badge>}</TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          )}
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Business Analytics Dashboard */}
      <Card>
        <CardHeader>
          <CardTitle>Business Analytics</CardTitle>
          <CardDescription>
            Monitor payment health, disputes, and system alerts
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <AnalyticsDashboard className="p-6" />
        </CardContent>
      </Card>

      {/* Create Invoice Dialog */}
      <Dialog open={showCreateInvoice} onOpenChange={setShowCreateInvoice}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Invoice</DialogTitle>
            <DialogDescription>
              Generate an invoice for {selectedCustomer?.company_name}.
            </DialogDescription>
          </DialogHeader>
          {selectedCustomer && (
            <CreateEnterpriseInvoiceForm 
              customer={selectedCustomer}
              onSuccess={handleInvoiceCreated} 
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
