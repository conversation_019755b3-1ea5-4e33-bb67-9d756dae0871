---
title: QuantBoost — Supabase Auth Email Templates
last_updated: 2025-08-28
owners: ["QuantBoost Frontend", "QuantBoost API"]
purpose: Central source of truth for all Supabase Auth transactional email templates and configuration.
tags: [supabase, auth, email, templates, magic-link, otp, pkce]
---

# Supabase Auth Email Templates (Production Canonical)

This document memorializes the finalized HTML templates, subject lines, and configuration for the five Supabase Auth email actions used by QuantBoost.

Applies to Supabase Project ID: `izoutrnsxaaoueljiimu`.

## Variables used (verified)

- `{{ .ConfirmationURL }}` — primary actionable URL generated by Supabase.
- `{{ .Token }}` — 6‑digit OTP that can be used as an alternative to the link.
- `{{ .TokenHash }}` — hashed OTP used when building custom confirm links (PKCE/SSR).
- `{{ .SiteURL }}` — project’s Site URL (Auth settings).
- `{{ .RedirectTo }}` — redirect URL passed from client calls; prefer when using `emailRedirectTo` or `signInWithOtp({ options: { emailRedirectTo } })`.
- `{{ .Email }}` — current email (email change template).
- `{{ .NewEmail }}` — new email (email change template).
- `{{ .UserName }}` — optional personalization; we default to "there" if missing.
- `{{ .Data }}` — user metadata object; not required but available for future personalization.

> Note: For prefetch-safe and server-side verification flows, you can redirect to an app route that uses `token_hash` and `type` with `supabase.auth.verifyOtp(...)`.

---

## 1) Magic Link (Passwordless Login)

Subject: `Sign in to QuantBoost`

HTML:

```html
<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8">
		<meta name="x-apple-disable-message-reformatting">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<title>Sign in to QuantBoost</title>
		<style>
			/* Base reset */
			body, table, td, a { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; }
			img { border: 0; outline: none; text-decoration: none; }
			table { border-collapse: collapse !important; }
			body { margin: 0 !important; padding: 0 !important; background: #f6f6f8; color: #0f172a; }
			a { color: #0ea5e9; text-decoration: none; }
			/* Button */
			.btn { background: #0ea5e9; color: #ffffff !important; padding: 12px 20px; border-radius: 10px; display: inline-block; font-weight: 600; }
			/* Card */
			.card { background: #ffffff; border-radius: 16px; padding: 32px; }
			.muted { color: #64748b; }
			.code { display: inline-block; font-size: 20px; letter-spacing: 3px; padding: 14px 18px; background: #0b1220; color: #ffffff; border-radius: 12px; font-weight: 700; }
			/* Dark mode */
			@media (prefers-color-scheme: dark) {
				body { background: #0b1220; color: #e2e8f0; }
				.card { background: #0f172a; }
				.muted { color: #94a3b8; }
			}
			/* Mobile */
			@media screen and (max-width: 480px) { .card { padding: 24px; } }
		</style>
	</head>
	<body>
		<div role="article" aria-roledescription="email" aria-label="Magic Link Sign In" style="padding:24px;">
			<div style="max-width:600px;margin:0 auto;">
						<div class="card">
							<table role="presentation" width="100%" cellpadding="0" cellspacing="0" style="margin:0 0 12px 0;">
								<tr>
									<td align="left" style="padding:0 0 8px 0;">
										<a href="{{ .SiteURL }}" target="_blank" rel="noopener" style="text-decoration:none;">
											<img src="https://download.quantboost.ai/assets/brand/QuantBoost_LogoOnly_280x280.png" width="140" alt="QuantBoost" style="display:block;border:0;outline:0;text-decoration:none;height:auto;">
										</a>
									</td>
								</tr>
							</table>
					<h1 style="margin:0 0 16px 0;font-size:24px;line-height:1.3;">Let’s get you signed in</h1>

					<p style="margin:0 0 20px 0;">Hi {{ .UserName | default "there" }},</p>
					<p style="margin:0 0 20px 0;">Use the secure link below to sign in to your QuantBoost account:</p>

					<p style="margin:0 0 24px 0;">
						<a class="btn" href="{{ .ConfirmationURL }}" target="_blank" rel="noopener">Sign in to QuantBoost</a>
					</p>

					<p class="muted" style="margin:0 0 16px 0;">If the button doesn’t work, copy and paste this link into your browser:</p>
					<p style="word-break:break-all;margin:0 0 24px 0;">
						<a href="{{ .ConfirmationURL }}" target="_blank" rel="noopener">{{ .ConfirmationURL }}</a>
					</p>

					<hr style="border:none;border-top:1px solid #e5e7eb;margin:24px 0;">

					<p class="muted" style="margin:0 0 12px 0;">Or sign in with this one-time code:</p>
					<p style="margin:0 0 24px 0;">
						<span class="code">{{ .Token }}</span>
					</p>

					<p class="muted" style="margin:0 0 4px 0;">This link and code will expire shortly for your security.</p>
					<p class="muted" style="margin:0 0 16px 0;">Didn’t request this? You can safely ignore this email.</p>

					<p style="margin:24px 0 0 0;">— The QuantBoost Team</p>
				</div>
				<p class="muted" style="text-align:center;font-size:12px;margin:16px 0 0 0;">
					You’re receiving this email because you attempted to sign in to QuantBoost.
				</p>
			</div>
		</div>
	</body>
	</html>
```

Alternate (prefetch-safe / server-side confirm using `token_hash`):

```html
<a class="btn" href="{{ .RedirectTo }}/auth/confirm?token_hash={{ .TokenHash }}&type=magiclink">Sign in</a>
```

---

## 2) Invite User

Subject: `You’re invited to QuantBoost`

HTML:

```html
<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>You’re invited to QuantBoost</title>
	<style>
		body { margin:0; padding:0; background:#f6f6f8; color:#0f172a; font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif; }
		.wrap { max-width:600px; margin:0 auto; padding:24px; }
		.card { background:#fff; border-radius:16px; padding:32px; }
		.btn { background:#0ea5e9; color:#fff !important; padding:12px 20px; border-radius:10px; display:inline-block; font-weight:600; }
		.muted { color:#64748b; }
		@media (prefers-color-scheme: dark) { body{background:#0b1220;color:#e2e8f0}.card{background:#0f172a}.muted{color:#94a3b8} }
	</style>
	</head>
	<body>
		<div class="wrap">
					<div class="card">
						<table role="presentation" width="100%" cellpadding="0" cellspacing="0" style="margin:0 0 12px 0;">
							<tr>
								<td align="left" style="padding:0 0 8px 0;">
									<a href="{{ .SiteURL }}" target="_blank" rel="noopener" style="text-decoration:none;">
										<img src="https://download.quantboost.ai/assets/brand/QuantBoost_LogoOnly_280x280.png" width="140" alt="QuantBoost" style="display:block;border:0;outline:0;text-decoration:none;height:auto;">
									</a>
								</td>
							</tr>
						</table>
				<h1 style="margin:0 0 16px 0;font-size:24px;">You’ve been invited</h1>
				<p style="margin:0 0 16px 0;">
					You’ve been invited to join QuantBoost for {{ .SiteURL }}. Click below to accept your invite and finish setting up your account.
				</p>
				<p style="margin:0 0 16px 0;">
					<a class="btn" href="{{ .ConfirmationURL }}" target="_blank" rel="noopener">Accept the invite</a>
				</p>
				<p class="muted" style="margin:0 0 8px 0;">After accepting, download the Windows installer to get started:</p>
				<p style="margin:0 0 16px 0;">
					<a class="btn" href="https://download.quantboost.ai/QuantBoost.exe" target="_blank" rel="noopener">Download the installer (.exe)</a>
				</p>
				<p class="muted" style="margin:0 0 24px 0;">If the button is blocked by your email client, copy and paste this link into your browser:
					<br><a href="https://stqboostdistjyhe39.blob.core.windows.net/releases/QuantBoost.exe" target="_blank" rel="noopener">https://stqboostdistjyhe39.blob.core.windows.net/releases/QuantBoost.exe</a>
				</p>
				<p class="muted" style="margin:0;">If you weren’t expecting this invite, you can ignore this email.</p>
				<p style="margin:24px 0 0 0;">— The QuantBoost Team</p>
			</div>
		</div>
	</body>
	</html>
```

Alternate server-side confirm:

```html
<a class="btn" href="{{ .SiteURL }}/auth/confirm?token_hash={{ .TokenHash }}&type=invite">Accept the invite</a>
```

---

## 3) Confirm Signup

Subject: `Confirm your email for QuantBoost`

HTML:

```html
<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8"><meta name="viewport" content="width=device-width">
	<title>Confirm your email</title>
	<style>
		body{margin:0;padding:0;background:#f6f6f8;color:#0f172a;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif}
		.wrap{max-width:600px;margin:0 auto;padding:24px}
		.card{background:#fff;border-radius:16px;padding:32px}
		.btn{background:#0ea5e9;color:#fff!important;padding:12px 20px;border-radius:10px;display:inline-block;font-weight:600}
		.muted{color:#64748b}
		@media (prefers-color-scheme: dark){body{background:#0b1220;color:#e2e8f0}.card{background:#0f172a}.muted{color:#94a3b8}}
	</style>
	</head>
	<body>
		<div class="wrap">
					<div class="card">
						<table role="presentation" width="100%" cellpadding="0" cellspacing="0" style="margin:0 0 12px 0;">
							<tr>
								<td align="left" style="padding:0 0 8px 0;">
									<a href="{{ .SiteURL }}" target="_blank" rel="noopener" style="text-decoration:none;">
										<img src="https://download.quantboost.ai/assets/brand/QuantBoost_LogoOnly_280x280.png" width="140" alt="QuantBoost" style="display:block;border:0;outline:0;text-decoration:none;height:auto;">
									</a>
								</td>
							</tr>
						</table>
				<h1 style="margin:0 0 16px 0;font-size:24px;">Confirm your email</h1>
				<p style="margin:0 0 16px 0;">Hi {{ .UserName | default "there" }}, thanks for signing up! Please confirm your email to activate your account.</p>
				<p style="margin:0 0 24px 0;"><a class="btn" href="{{ .ConfirmationURL }}" target="_blank" rel="noopener">Confirm email</a></p>
				<p class="muted" style="margin:0 0 16px 0;">If the button doesn’t work, copy and paste this link:</p>
				<p style="word-break:break-all;margin:0 0 16px 0;"><a href="{{ .ConfirmationURL }}" target="_blank">{{ .ConfirmationURL }}</a></p>
				<p class="muted" style="margin:0;">If you didn’t create this account, you can safely ignore this email.</p>
				<p style="margin:24px 0 0 0;">— The QuantBoost Team</p>
			</div>
		</div>
	</body>
	</html>
```

Prefetch-safe alternate:

```html
<a class="btn" href="{{ .SiteURL }}/confirm-signup?confirmation_url={{ .ConfirmationURL }}">Confirm your email</a>
```

---

## 4) Change Email Address

Subject: `Confirm your new email for QuantBoost`

HTML:

```html
<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8"><meta name="viewport" content="width=device-width">
	<title>Confirm email change</title>
	<style>
		body{margin:0;padding:0;background:#f6f6f8;color:#0f172a;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif}
		.wrap{max-width:600px;margin:0 auto;padding:24px}
		.card{background:#fff;border-radius:16px;padding:32px}
		.btn{background:#0ea5e9;color:#fff!important;padding:12px 20px;border-radius:10px;display:inline-block;font-weight:600}
		.muted{color:#64748b}
		@media (prefers-color-scheme: dark){body{background:#0b1220;color:#e2e8f0}.card{background:#0f172a}.muted{color:#94a3b8}}
	</style>
	</head>
	<body>
		<div class="wrap">
					<div class="card">
						<table role="presentation" width="100%" cellpadding="0" cellspacing="0" style="margin:0 0 12px 0;">
							<tr>
								<td align="left" style="padding:0 0 8px 0;">
									<a href="{{ .SiteURL }}" target="_blank" rel="noopener" style="text-decoration:none;">
										<img src="https://download.quantboost.ai/assets/brand/QuantBoost_LogoOnly_280x280.png" width="140" alt="QuantBoost" style="display:block;border:0;outline:0;text-decoration:none;height:auto;">
									</a>
								</td>
							</tr>
						</table>
				<h1 style="margin:0 0 16px 0;font-size:24px;">Confirm change of email</h1>
				<p style="margin:0 0 12px 0;">You requested to change your email:</p>
				<ul style="margin:0 0 16px 0;padding-left:20px;">
					<li>Current: <strong>{{ .Email }}</strong></li>
					<li>New: <strong>{{ .NewEmail }}</strong></li>
				</ul>
				<p style="margin:0 0 24px 0;"><a class="btn" href="{{ .ConfirmationURL }}" target="_blank" rel="noopener">Confirm email change</a></p>
				<p class="muted" style="margin:0;">If you didn’t request this change, ignore this email and your address will remain the same.</p>
				<p style="margin:24px 0 0 0;">— The QuantBoost Team</p>
			</div>
		</div>
	</body>
	</html>
```

---

## 5) Reauthentication (OTP)

Subject: `Your QuantBoost verification code`

HTML:

```html
<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8"><meta name="viewport" content="width=device-width">
	<title>Your verification code</title>
	<meta name="x-apple-disable-message-reformatting">
	<style>
		body{margin:0;padding:0;background:#ffffff;color:#0f172a;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif}
		.wrap{max-width:600px;margin:0 auto;padding:16px}
		.card{background:#ffffff;border:1px solid #e5e7eb;border-radius:12px;padding:16px}
		.muted{color:#64748b;font-size:14px}
		.code{display:inline-block;font-size:22px;letter-spacing:2px;padding:10px 14px;border:1px solid #cbd5e1;border-radius:8px;background:#fff;color:#0f172a;font-weight:800}
		@media (prefers-color-scheme: dark){body{background:#0b1220;color:#e2e8f0}.card{background:#0f172a;border-color:#1f2937}.muted{color:#94a3b8}}
	</style>
</head>
<body>
	<div role="article" aria-roledescription="email" style="padding:8px;">
		<!-- Preheader: improves text ratio and inbox preview -->
		<div style="display:none;max-height:0;overflow:hidden;opacity:0;color:transparent;">Use this QuantBoost 6‑digit code to continue. Expires soon.</div>
		<div class="wrap">
					<div class="card">
						<table role="presentation" width="100%" cellpadding="0" cellspacing="0" style="margin:0 0 10px 0;">
							<tr>
								<td align="left" style="padding:0 0 6px 0;">
									<a href="{{ .SiteURL }}" target="_blank" rel="noopener" style="text-decoration:none;">
										<img src="https://download.quantboost.ai/assets/brand/QuantBoost_LogoOnly_280x280.png" width="140" alt="QuantBoost" style="display:block;border:0;outline:0;text-decoration:none;height:auto;">
									</a>
								</td>
							</tr>
						</table>
						<p class="muted" style="margin:0 0 6px 0;">QuantBoost security</p>
				<p style="margin:0 0 8px 0;"><strong>Your verification code</strong></p>
				<p style="margin:0 0 8px 0;">We received a request to verify your identity for {{ .SiteURL }}. Enter this 6‑digit code:</p>
				<p style="margin:0 0 12px 0;"><span class="code">{{ .Token }}</span></p>
				<p class="muted" style="margin:0 0 8px 0;">This code works once and expires shortly.</p>
				<p class="muted" style="margin:0 0 8px 0;">If you didn’t request it, you can ignore this email.</p>
				<p class="muted" style="margin:0;">Help & security tips: {{ .SiteURL }}/help/security</p>
				<hr style="border:none;border-top:1px solid #e5e7eb;margin:12px 0;">
				<p class="muted" style="margin:0;font-size:12px;">Text fallback: Code {{ .Token }} for {{ .SiteURL }}</p>
			</div>
			<p class="muted" style="text-align:center;font-size:12px;margin:12px 0 0 0;">This is a transactional security email related to your account.</p>
		</div>
	</div>
</body>
</html>
```

---

## Subjects and local config (CLI/TOML)

We store content files and subjects via Supabase config (local dev). Adjust `content_path` to wherever you place the HTML files in your project.

```toml
[auth.email.template.magic_link]
subject = "Sign in to QuantBoost"
content_path = "./supabase/templates/magic_link.html"

[auth.email.template.invite]
subject = "You’re invited to QuantBoost"
content_path = "./supabase/templates/invite.html"

[auth.email.template.confirmation]
subject = "Confirm your email for QuantBoost"
content_path = "./supabase/templates/confirm_signup.html"

[auth.email.template.email_change]
subject = "Confirm your new email for QuantBoost"
content_path = "./supabase/templates/email_change.html"

[auth.email.template.reauthentication]
subject = "Your QuantBoost verification code"
content_path = "./supabase/templates/reauthentication.html"
```

Production setup is done in the Supabase Dashboard under Authentication → Templates. Copy the relevant HTML from this document into each template.

---

## Implementation notes

- Prefer `{{ .RedirectTo }}` when your client passes `emailRedirectTo` or `redirectTo`; otherwise `{{ .SiteURL }}` is fine.
- For PKCE or SSR confirmation, build your own link with `{{ .TokenHash }}` and handle verification on the server at `/auth/confirm?token_hash=...&type=...`, then redirect.
- To avoid link prefetching by mail providers, use the "prefetch-safe" variant that routes to an intermediate page requiring a user click.
- All templates include dark-mode friendly styles and accessible semantic markup.

### Testing locally

1) Place HTML files under `supabase/templates/` and point `content_path` accordingly.
2) Restart local stack after changes to pick up new templates.
3) Trigger flows:
	 - Magic Link: `signInWithOtp({ email, options: { emailRedirectTo } })`.
	 - Invite: Admin `inviteUserByEmail(email, { redirectTo })`.
	 - Confirm Signup: `signUp({ email, password }, { emailRedirectTo })` or passwordless sign-up.
	 - Change Email: `updateUser({ email: newEmail }, { emailRedirectTo })`.
	 - Reauthentication: `reauthenticate()` (or flows that request a new OTP).

---

## Change log

- 2025-08-28: Initial canonical set created; standardized branding, dark-mode, link + OTP fallback, PKCE-friendly alternates, and TOML config.

