﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Release</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <ProductVersion>3.14</ProductVersion>
    <ProjectGuid>{3FC1C7A6-2F64-4A40-9E42-6B4B95321B69}</ProjectGuid>
    <SchemaVersion>2.0</SchemaVersion>
    <OutputName>QuantBoost</OutputName>
    <OutputType>Package</OutputType>
    <DefineConstants>ExcelSourceDir=..\QuantBoost_Excel\bin\$(Configuration);PowerPointSourceDir=..\QuantBoost_PPTX\bin\$(Configuration)</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <OutputPath>C:\VS projects\QuantBoost\QuantBoost_WixInstaller\bin\x64\Release\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <OutputPath>C:\VS projects\QuantBoost\QuantBoost_WixInstaller\bin\x64\Release\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Product.wxs" />
    <Compile Include="TestReg.wxs" />
    <Content Include="License.rtf" />
    <Compile Include="Excel.Harvested.wxs" />
    <Compile Include="PowerPoint.Harvested.wxs" />
  </ItemGroup>
  <ItemGroup>
    <WixExtension Include="WixUIExtension">
      <HintPath>WixUIExtension</HintPath>
      <Name>WixUIExtension</Name>
    </WixExtension>
    <WixExtension Include="WixUtilExtension">
      <HintPath>WixUtilExtension</HintPath>
      <Name>WixUtilExtension</Name>
    </WixExtension>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.targets')" />
</Project>