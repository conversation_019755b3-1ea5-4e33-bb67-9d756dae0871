const express = require('express');
const router = express.Router();
const { supabase, supabaseAdmin } = require('../supabaseClient'); // Import both clients
const { sendSuccess, sendError } = require('../utils/responseHelpers'); // Adjust path as necessary
const { isValidEmail } = require('../utils/validationHelpers'); // Adjust path as necessary
// const { logLicenseEvent } = require('../utils/eventLogger'); // Uncomment if needed
const path = require('path'); // Added for magic-link-relay

// AA.2: Add POST /v1/auth/magic-link
router.post('/magic-link', async (req, res) => {
    const { email, options } = req.body;

    // --- Step 1: Basic validation for the email ---
    if (!email) {
        return sendError(res, 'Email is required.', 400);
    }
    if (!isValidEmail(email)) {
        return sendError(res, 'Invalid email format.', 400);
    }

    // --- Step 2: Securely validate the client's redirect URL ---
    const clientRedirectTo = options?.redirectTo;
    
    if (!clientRedirectTo) {
        return sendError(res, 'redirectTo option is required.', 400);
    }

    let clientUrl;
    try {
        clientUrl = new URL(clientRedirectTo);
    } catch (error) {
        return sendError(res, 'Invalid redirectTo URL format.', 400);
    }

    // This is the key security step: only allow redirects to trusted origins.
    const allowedOrigins = [
        'http://localhost', // Allows any port on localhost for local development
        'https://app-quantboost-frontend-staging.azurewebsites.net' // Production frontend
    ];

    // Debug logging to help with troubleshooting
    console.log('Magic link request debug:', {
        clientRedirectTo,
        clientOrigin: clientUrl.origin,
        clientOriginWithoutPort: clientUrl.origin.replace(/:\d+$/, ''),
        allowedOrigins,
        isAllowed: allowedOrigins.includes(clientUrl.origin.replace(/:\d+$/, ''))
    });

    // The .replace() part removes the port number (e.g., :61083) for the check
    if (!allowedOrigins.includes(clientUrl.origin.replace(/:\d+$/, ''))) {
        return sendError(res, 'Untrusted redirect origin.', 400);
    }

    // --- Step 3: Call Supabase using the validated client URL ---
    try {
        const { error } = await supabase.auth.signInWithOtp({
            email: email,
            options: {
                shouldCreateUser: true,
                // Use the validated URL from the client directly.
                emailRedirectTo: clientRedirectTo,
            },
        });

        if (error) {
            console.error('Error sending magic link:', error);
            const isJsonError = (error.originalError instanceof SyntaxError) ||
                                (error.message && error.message.toLowerCase().includes('json'));

            if (isJsonError) {
                return sendError(res, 'Temporary connectivity issue. Please try again shortly.', 503);
            }
            return sendError(res, error.message || 'Failed to send magic link.', error.status || 500);
        }

        return sendSuccess(res, { message: `Magic link sent to ${email} if the account exists or can be created. Please check your email.` }, 200);

    } catch (err) {
        console.error('Unexpected error in /magic-link route:', err);
        return sendError(res, 'An unexpected error occurred.', 500);
    }
});

// AA.3: Add POST /v1/auth/refresh-token
router.post('/refresh-token', async (req, res) => {
    const { refreshToken } = req.body;

    if (!refreshToken) {
        return sendError(res, 'Refresh token is required.', 400);
    }

    try {
        const { data, error } = await supabase.auth.refreshSession({ refresh_token: refreshToken });
        
        // ADD THIS LOGGING
        console.log('Supabase refreshSession raw response:', {
            hasData: !!data,
            hasSession: !!data?.session,
            accessTokenLength: data?.session?.access_token?.length,
            refreshTokenLength: data?.session?.refresh_token?.length,
            refreshTokenValue: data?.session?.refresh_token,
            fullSession: JSON.stringify(data?.session, null, 2)
        });

        if (error) {
            console.error('Error refreshing token:', error);
            const isJsonError = (error.originalError instanceof SyntaxError) || (error.message && error.message.toLowerCase().includes('json'));
            if (isJsonError) {
                return sendError(res, 'Temporary connectivity issue. Please try again shortly.', 503);
            }
            return sendError(res, error.message || 'Failed to refresh token.', error.status || 401);
        }

        if (data.session) {
            // Log before sending response
            console.log('Sending refresh response with:', {
                accessTokenLength: data.session.access_token?.length,
                refreshTokenLength: data.session.refresh_token?.length,
                refreshTokenFirst20: data.session.refresh_token?.substring(0, 20)
            });
            
            return sendSuccess(res, {
                accessToken: data.session.access_token,
                refreshToken: data.session.refresh_token,
                expiresIn: data.session.expires_in,
                user: data.user
            }, 200);
        } else {
            return sendError(res, 'Failed to refresh session, no session data returned.', 500);
        }

    } catch (err) {
        console.error('Unexpected error in /refresh-token route:', err);
        return sendError(res, 'An unexpected error occurred while refreshing token.', 500);
    }
});

// AA.4: Add POST /v1/auth/verify-otp
router.post('/verify-otp', async (req, res) => {
    const { email, token } = req.body;

    if (!email || !token) {
        return sendError(res, 'Email and OTP token are required.', 400);
    }
    if (!isValidEmail(email)) {
        return sendError(res, 'Invalid email format.', 400);
    }

    try {
        const { data, error } = await supabase.auth.verifyOtp({ 
            email: email, 
            token: token, 
            type: 'magiclink'
        });

        if (error) {
            console.error('Error verifying OTP:', error);
            const isJsonError = (error.originalError instanceof SyntaxError) || (error.message && error.message.toLowerCase().includes('json'));
            if (isJsonError) {
                return sendError(res, 'Temporary connectivity issue. Please try again shortly.', 503);
            }
            return sendError(res, error.message || 'Failed to verify OTP.', error.status || 401);
        }

        if (data.session) {
            return sendSuccess(res, {
                accessToken: data.session.access_token,
                refreshToken: data.session.refresh_token,
                expiresIn: data.session.expires_in,
                user: data.user
            }, 200);
        } else {
            return sendError(res, 'OTP verified, but failed to establish a session.', 401);
        }

    } catch (err) {
        console.error('Unexpected error in /verify-otp route:', err);
        return sendError(res, 'An unexpected error occurred while verifying OTP.', 500);
    }
});

// AA.5: Add POST /v1/auth/logout
router.post('/logout', async (req, res) => {
    const authHeader = req.headers.authorization;
    let accessToken = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
        accessToken = authHeader.split(' ')[1];
    }

    if (!accessToken) {
        return sendError(res, 'Access token is required in Authorization header to logout.', 401);
    }

    try {
        const { error } = await supabase.auth.signOut(accessToken); 

        if (error) {
            console.error('Error signing out:', error);
            const isJsonError = (error.originalError instanceof SyntaxError) || (error.message && error.message.toLowerCase().includes('json'));
            if (isJsonError) {
                return sendError(res, 'Temporary connectivity issue. Please try again shortly.', 503);
            }
            return sendError(res, error.message || 'Failed to logout.', error.status || 500);
        }

        return sendSuccess(res, { message: 'Successfully logged out.' }, 200);

    } catch (err) {
        console.error('Unexpected error in /logout route:', err);
        return sendError(res, 'An unexpected error occurred during logout.', 500);
    }
});

// AA.6: Add GET /v1/auth/magic-link-relay (serving HTML)
router.get('/magic-link-relay', (req, res) => {
    const relayPath = path.join(__dirname, '..', 'magic-link-relay.html');
    
    res.sendFile(relayPath, (err) => {
        if (err) {
            console.error("Error sending magic-link-relay.html:", err);
            sendError(res, "Relay page not found or error sending file.", err.status || 500);
        } else {
            console.log("Magic-link-relay.html sent successfully from auth.routes.js.");
        }
    });
});

// AA.7: Add POST /v1/auth/checkout-login - Auto-login after successful checkout
router.post('/checkout-login', async (req, res) => {
    console.log('🎯 CHECKOUT-LOGIN: Route hit with body:', JSON.stringify(req.body, null, 2));
    const { email, checkoutSessionId } = req.body;

    // --- Step 1: Basic validation ---
    console.log('🎯 CHECKOUT-LOGIN: Validating email:', email, 'checkoutSessionId:', checkoutSessionId);
    if (!email) {
        console.log('🎯 CHECKOUT-LOGIN: ERROR - No email provided');
        return sendError(res, 'Email is required.', 400);
    }
    if (!isValidEmail(email)) {
        console.log('🎯 CHECKOUT-LOGIN: ERROR - Invalid email format:', email);
        return sendError(res, 'Invalid email format.', 400);
    }
    if (!checkoutSessionId) {
        console.log('🎯 CHECKOUT-LOGIN: ERROR - No checkout session ID provided');
        return sendError(res, 'Checkout session ID is required.', 400);
    }

    try {
        console.log('🎯 CHECKOUT-LOGIN: Starting user lookup/creation for:', email);
        // --- Step 2: Create or get user without email verification ---
        // Since this is post-checkout, we trust the email from the payment process
        let user = null;
        let isNewUser = false;

        // First try to get existing user
        console.log('🎯 CHECKOUT-LOGIN: Fetching existing users...');
        const { data: existingUsers, error: fetchError } = await supabaseAdmin.auth.admin.listUsers();
        if (fetchError) {
            console.error('🎯 CHECKOUT-LOGIN: Error fetching users:', fetchError);
        } else {
            console.log('🎯 CHECKOUT-LOGIN: Found', existingUsers.users.length, 'total users');
            user = existingUsers.users.find(u => u.email === email);
            console.log('🎯 CHECKOUT-LOGIN: User exists:', !!user, user ? `(ID: ${user.id})` : '');
        }

        // If user doesn't exist, create them
        if (!user) {
            console.log('🎯 CHECKOUT-LOGIN: Creating new user for:', email);
            const { data: createData, error: createError } = await supabaseAdmin.auth.admin.createUser({
                email: email,
                email_confirm: true, // Skip email verification since payment was successful
            });

            if (createError) {
                console.error('🎯 CHECKOUT-LOGIN: Error creating user:', createError);
                return sendError(res, createError.message || 'Failed to create user.', createError.status || 500);
            }

            user = createData.user;
            isNewUser = true;
            console.log('🎯 CHECKOUT-LOGIN: Created new user with ID:', user.id);
        }

        // --- Step 3: Generate session for immediate login ---
        console.log('🎯 CHECKOUT-LOGIN: Generating magic link for user:', user.id);
        const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
            type: 'magiclink',
            email: email,
        });

        if (sessionError) {
            console.error('🎯 CHECKOUT-LOGIN: Error generating session:', sessionError);
            return sendError(res, sessionError.message || 'Failed to generate login session.', sessionError.status || 500);
        }

        console.log('🎯 CHECKOUT-LOGIN: Successfully generated magic link');
        // --- Step 4: Return session data for immediate frontend use ---
        const response = {
            message: 'Checkout login session created successfully',
            actionLink: sessionData.properties.action_link,
            email: email,
            userId: user.id,
            isNewUser: isNewUser,
            checkoutSessionId: checkoutSessionId,
            redirectTo: 'https://app-quantboost-frontend-staging.azurewebsites.net/dashboard'
        };
        
        console.log('🎯 CHECKOUT-LOGIN: Returning success response with actionLink length:', sessionData.properties.action_link.length);
        return sendSuccess(res, response, 200);

    } catch (err) {
        console.error('🎯 CHECKOUT-LOGIN: Unexpected error:', err);
        return sendError(res, 'An unexpected error occurred during checkout login.', 500);
    }
});

module.exports = router;