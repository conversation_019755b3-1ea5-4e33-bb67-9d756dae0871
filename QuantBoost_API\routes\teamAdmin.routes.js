const express = require('express');
const router = express.Router({ mergeParams: true }); // mergeParams is important for accessing :subscriptionId
const { authenticateJWT } = require('../middleware/authMiddleware');
const { createClient } = require('@supabase/supabase-js');
const { supabase, supabaseAdmin } = require('../supabaseClient');
const { sendSuccess, sendError } = require('../utils/responseHelpers');
const { isValidEmail } = require('../utils/validationHelpers'); // Added: require for validationHelpers
const { logLicenseEvent } = require('../utils/eventLogger'); // Added: require for eventLogger

// Apply JWT authentication to all routes in this file
router.use(authenticateJWT);

// Helper to ensure there are enough placeholder license rows (status 'unassigned')
// to match the subscription seat quantity. Some UIs derive "Available" from row count.
async function ensureSeatPlaceholders(subscriptionId, subscription) {
    try {
        const { count: existingCount, error: countErr } = await supabaseAdmin
            .from('licenses')
            .select('id', { count: 'exact', head: true })
            .eq('subscription_id', subscriptionId);

        if (countErr) {
            console.warn('ensureSeatPlaceholders: count error (ignored):', countErr.message || countErr);
            return;
        }

        const totalSeats = subscription?.quantity ?? 0;
        if (!totalSeats || existingCount >= totalSeats) return;

        const missing = totalSeats - existingCount;
        const nowIso = new Date().toISOString();
        const expiry = subscription.current_period_end ? new Date(subscription.current_period_end).toISOString() : null;

        const placeholders = Array.from({ length: missing }).map(() => ({
            subscription_id: subscriptionId,
            product_id: 'quantboost-suite',
            license_tier: 'team',
            status: 'unassigned',
            user_id: null,
            email: null,
            expiry_date: expiry,
            updated_at: nowIso,
        }));

        const { error: insertErr } = await supabaseAdmin
            .from('licenses')
            .insert(placeholders);

        if (insertErr) {
            console.warn('ensureSeatPlaceholders: insert error (ignored):', insertErr.message || insertErr);
        }
    } catch (e) {
        console.warn('ensureSeatPlaceholders: exception (ignored):', e?.message || e);
    }
}

// Helper function to create a user-authenticated Supabase client
function createUserSupabaseClient(jwtToken) {
    if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
        throw new Error('Missing Supabase environment variables');
    }
    
    // Use the modern accessToken approach instead of setSession()
    const client = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY, {
        accessToken: () => jwtToken, // Return the JWT token for authentication
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    });
    
    return client;
}

// Middleware to check if the authenticated user owns the subscription
// This will be used by all routes in this file that operate on a specific subscription
async function checkSubscriptionOwner(req, res, next) {
    const { subscriptionId } = req.params;
    const userId = req.user.id;

    console.log(`🔍 checkSubscriptionOwner - subscriptionId: ${subscriptionId}, userId: ${userId}`);

    if (!subscriptionId) {
        console.log('❌ Missing subscriptionId parameter');
        return sendError(res, 'Subscription ID is required.', 400);
    }

    try {
        console.log(`📋 Querying subscription: ${subscriptionId} for user: ${userId}`);
        
        // Extract JWT token from Authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            console.log('❌ No valid Authorization header found');
            return sendError(res, 'Authentication token required.', 401);
        }
        
        const jwtToken = authHeader.substring(7); // Remove 'Bearer ' prefix
        console.log(`🔑 Using JWT token for RLS authentication`);
        console.log(`🔍 JWT token length: ${jwtToken.length}, starts with: ${jwtToken.substring(0, 20)}...`);
        
        // Create a user-authenticated supabase client for this request
        const userSupabase = createUserSupabaseClient(jwtToken);
        
        // Now query with RLS enabled - user can only access their own subscriptions
        const { data: subscription, error } = await userSupabase
            .from('subscriptions')
            .select('id, user_id, status, plan_id, quantity, current_period_end')
            .eq('id', subscriptionId)
            .single(); // RLS policy should ensure user can only see their own subscriptions

        console.log(`📊 Query result - data:`, subscription);
        console.log(`📊 Query error:`, error);

        if (error || !subscription) {
            console.log(`❌ Subscription lookup failed - error: ${JSON.stringify(error)}, data: ${JSON.stringify(subscription)}`);
            return sendError(res, 'Subscription not found or you do not have permission to manage it.', 404);
        }

        // Defense in depth: Explicitly verify ownership even with RLS
        if (subscription.user_id !== userId) {
            console.log(`🚨 Security violation: User ${userId} attempted to access subscription owned by ${subscription.user_id}`);
            return sendError(res, 'You do not have permission to manage this subscription.', 403);
        }

        console.log(`✅ Subscription found: ${subscription.id} for user: ${subscription.user_id}`);

        // Optionally, check if the subscription plan allows team management
        // For example, if 'quantity' or a specific 'plan_id' indicates a team plan
        // if (subscription.quantity <= 1 && !isTeamPlan(subscription.plan_id)) {
        //     return sendError(res, 'This subscription does not support team management.', 403);
        // }

        if (!subscription.current_period_end) {
            // This is a critical piece of information for team license assignment
            console.warn(`Subscription ${subscriptionId} is missing current_period_end.`);
            // Depending on strictness, you might want to sendError here
        }

        req.subscription = subscription; // Attach subscription details to the request object
        next();
    } catch (err) {
        console.error('checking subscription owner:', err);
        return sendError(res, 'Internal server error while verifying subscription ownership.', 500);
    }
}

// GET /v1/me/subscriptions/:subscriptionId/team-licenses - Fetches all licenses associated with this subscription
router.get('/team-licenses', checkSubscriptionOwner, async (req, res) => {
    const { subscriptionId } = req.params;
    // The checkSubscriptionOwner middleware has already validated that the user owns this subscription
    // and attached the subscription details to req.subscription.

    try {
        // Ensure there are enough placeholder rows for this subscription so UI counts align
        await ensureSeatPlaceholders(subscriptionId, req.subscription);

        // Enhanced query to include activation information
        const { data: licenses, error: licensesError } = await supabaseAdmin
            .from('licenses')
            .select(`
                *,
                activation_count:license_activations(count)
            `)
            .eq('subscription_id', subscriptionId);

        if (licensesError) {
            console.error('Error fetching team licenses:', licensesError);
            return sendError(res, 'Failed to retrieve licenses for this subscription.', 500, licensesError.message);
        }

        if (!licenses || licenses.length === 0) {
            // It's not an error if a subscription has no licenses yet, could be a new team subscription
            return sendSuccess(res, 200, []); 
        }

        // Get activation counts for each license
        const licenseIds = licenses.map(l => l.id);
        const { data: activations, error: activationsError } = await supabaseAdmin
            .from('license_activations')
            .select('license_id, is_active')
            .in('license_id', licenseIds);

        if (activationsError) {
            console.error('Error fetching license activations:', activationsError);
            return sendError(res, 'Failed to retrieve license activation data.', 500, activationsError.message);
        }

        // Enhance licenses with activation data
        const enhancedLicenses = licenses.map(license => {
            const licenseActivations = activations?.filter(a => a.license_id === license.id) || [];
            const activeActivations = licenseActivations.filter(a => a.is_active);
            
            return {
                ...license,
                activation_count: activeActivations.length,
                max_activations: license.max_activations || 1,
                is_activated: activeActivations.length > 0,
                available_activations: (license.max_activations || 1) - activeActivations.length
            };
        });

        sendSuccess(res, 200, enhancedLicenses);
    } catch (err) {
        console.error('Unexpected error in GET /team-licenses:', err);
        return sendError(res, 'An unexpected error occurred while fetching team licenses.', 500);
    }
});

// POST /v1/me/subscriptions/:subscriptionId/team-licenses/assign - Assigns a license from the subscription to a user
router.post('/team-licenses/assign', checkSubscriptionOwner, async (req, res) => {
    const { subscriptionId } = req.params;
    const { license_id_to_assign, target_user_email } = req.body;
    const requestingUserId = req.user.id; // The subscription owner

    if (!license_id_to_assign || !target_user_email) {
        return sendError(res, 'License ID and target user email are required.', 400);
    }

    if (!isValidEmail(target_user_email)) {
        return sendError(res, 'Invalid target user email format.', 400);
    }

    try {
        // 1. Fetch the target user's profile to get their user_id
        const { data: targetUserProfile, error: userProfileError } = await supabaseAdmin
            .from('profiles') // Assuming your user profiles table is named 'profiles'
            .select('user_id, email')
            .eq('email', target_user_email)
            .single();

        if (userProfileError || !targetUserProfile) {
            // Option: Could allow assigning to an email even if user doesn't exist yet,
            // then user claims it. For now, require existing user.
            return sendError(res, `User with email ${target_user_email} not found.`, 404);
        }
        const targetUserId = targetUserProfile.user_id;

        // 2. Fetch the license to be assigned
        const { data: license, error: licenseFetchError } = await supabaseAdmin
            .from('licenses')
            .select('*')
            .eq('id', license_id_to_assign)
            .eq('subscription_id', subscriptionId) // Ensure it belongs to this subscription
            .single();        

        if (licenseFetchError || !license) {
            return sendError(res, 'License not found or it does not belong to this subscription.', 404);
        }

        // 3. Check if the license is assignable
        // If license is already active for a different user, block reassignment.
        if (license.status === 'active') {
            if (license.user_id && license.user_id !== targetUserId) {
                return sendError(res, `License ${license_id_to_assign} is already assigned to another user.`, 409);
            }
            if (license.user_id === targetUserId) {
                return sendError(res, `License ${license_id_to_assign} is already assigned and active for this user.`, 409);
            }
        }
        // If license is 'inactive' or 'unassigned', allow reassignment regardless of current user_id

        // 4. Update the license
        const updates = {
            user_id: targetUserId,
            email: target_user_email,
            status: 'active', // Directly activate for team member
            updated_at: new Date().toISOString(),
            // Ensure expiry_date is aligned with the subscription's current_period_end
            // req.subscription is available from checkSubscriptionOwner middleware
            expiry_date: req.subscription.current_period_end ? new Date(req.subscription.current_period_end).toISOString() : license.expiry_date,
        };

        const { data: updatedLicense, error: licenseUpdateError } = await supabaseAdmin
            .from('licenses')
            .update(updates)
            .eq('id', license_id_to_assign)
            .select()
            .single();

        if (licenseUpdateError) {
            console.error('Error assigning team license:', licenseUpdateError);
            return sendError(res, 'Failed to assign license.', 500, licenseUpdateError.message);
        }

        // 5. Send email to the assigned user (invite or magic link) and log the event
        try {
            const redirectTo = 'https://app-quantboost-frontend-staging.azurewebsites.net/dashboard';
            let emailSent = false;
            // Try sending a formal invite first (works for new users)
            try {
                const { error: inviteErr } = await supabaseAdmin.auth.admin.inviteUserByEmail(target_user_email, { redirectTo });
                if (inviteErr) {
                    console.warn('Assign: inviteUserByEmail failed, will fallback to magic link:', inviteErr);
                } else {
                    emailSent = true;
                    try { await logLicenseEvent(null, 'assign_email_sent', license.id, 'team_admin_assign', target_user_email, 'success_invite'); } catch {}
                }
            } catch (e) {
                console.warn('Assign: exception during inviteUserByEmail, will fallback:', e?.message || e);
            }

            if (!emailSent) {
                try {
                    const { error: magicErr } = await supabase.auth.signInWithOtp({
                        email: target_user_email,
                        options: { emailRedirectTo: redirectTo, shouldCreateUser: true },
                    });
                    if (magicErr) {
                        console.error('Assign: fallback magic link failed:', magicErr);
                        try { await logLicenseEvent(null, 'assign_email_error', license.id, 'team_admin_assign', target_user_email, 'error_email_send_failed'); } catch {}
                    } else {
                        try { await logLicenseEvent(null, 'assign_email_sent_magic', license.id, 'team_admin_assign', target_user_email, 'success_magiclink'); } catch {}
                    }
                } catch (e) {
                    console.error('Assign: exception during fallback magic link send:', e?.message || e);
                }
            }

            // Log assignment event
            try {
                await logLicenseEvent(
                    null,
                    'team_assigned',
                    license.id,
                    'team_admin_assign',
                    target_user_email,
                    'success_assigned'
                );
            } catch {}
        } catch (logError) {
            console.error("Failed to log team assignment event:", logError); 
            // Non-critical, so don't fail the whole request
        }

        sendSuccess(res, 200, updatedLicense);

    } catch (err) {
        console.error('Unexpected error in POST /team-licenses/assign:', err);
        return sendError(res, 'An unexpected error occurred while assigning the license.', 500);
    }
});

// POST /v1/me/subscriptions/:subscriptionId/team-licenses/invite - Invites a user to the subscription by creating a new license for them
router.post('/team-licenses/invite', checkSubscriptionOwner, async (req, res) => {
    const { subscriptionId } = req.params;
    const { target_email } = req.body;
    const requestingUserId = req.user.id; // The subscription owner

    if (!target_email || !isValidEmail(target_email)) {
        return sendError(res, 'A valid target user email is required.', 400);
    }

    try {
        // 0. If there's an available license slot already created (inactive/unassigned), consume it first
        const { data: availableLicense, error: availableErr } = await supabaseAdmin
            .from('licenses')
            .select('*')
            .eq('subscription_id', subscriptionId)
            .in('status', ['inactive', 'unassigned'])
            .order('created_at', { ascending: true })
            .limit(1)
            .single();

        if (availableErr && availableErr.code !== 'PGRST116') {
            // Ignore no-rows (PGRST116) but surface other errors
            console.error('Error checking for available license slot:', availableErr);
        }

        if (availableLicense) {
            // Convert the available slot into an invite for target_email
            const inviteUpdates = {
                status: 'assigned',
                email: target_email,
                user_id: null, // remains unclaimed until user signs in/activates
                updated_at: new Date().toISOString(),
                expiry_date: req.subscription.current_period_end ? new Date(req.subscription.current_period_end).toISOString() : availableLicense.expiry_date,
            };

            const { data: updatedSlot, error: updateErr } = await supabaseAdmin
                .from('licenses')
                .update(inviteUpdates)
                .eq('id', availableLicense.id)
                .select()
                .single();

            if (updateErr) {
                console.error('Error converting available slot to invite:', updateErr);
                return sendError(res, 'Failed to prepare license for invitation.', 500, updateErr.message);
            }

            // Attempt to send an invite email via Supabase Auth
            const redirectTo = 'https://app-quantboost-frontend-staging.azurewebsites.net/dashboard';
            let emailSent = false;
            try {
                const { error: inviteErr } = await supabaseAdmin.auth.admin.inviteUserByEmail(target_email, { redirectTo });
                if (inviteErr) {
                    console.warn('Invite email (admin.inviteUserByEmail) failed, will fallback to magic link:', inviteErr);
                } else {
                    emailSent = true;
                    try {
                        await logLicenseEvent(null, 'invite_email_sent', updatedSlot.id, 'team_admin_invite_from_slot', target_email, 'success_invite');
                    } catch {}
                }
            } catch (e) {
                console.warn('Exception during inviteUserByEmail, will fallback:', e?.message || e);
            }

            if (!emailSent) {
                try {
                    const { error: magicErr } = await supabase.auth.signInWithOtp({
                        email: target_email,
                        options: { emailRedirectTo: redirectTo, shouldCreateUser: true },
                    });
                    if (magicErr) {
                        console.error('Fallback magic link failed:', magicErr);
                        try { await logLicenseEvent(null, 'invite_email_error', updatedSlot.id, 'team_admin_invite_from_slot', target_email, 'error_email_send_failed'); } catch {}
                    } else {
                        try { await logLicenseEvent(null, 'invite_email_sent_magic', updatedSlot.id, 'team_admin_invite_from_slot', target_email, 'success_magiclink'); } catch {}
                    }
                } catch (e) {
                    console.error('Exception during fallback magic link send:', e?.message || e);
                }
            }

            try {
                await logLicenseEvent(
                    null,
                    'team_invited_from_available_slot',
                    updatedSlot.id,
                    'team_admin_invite_from_slot',
                    target_email,
                    'success_reused_slot'
                );
            } catch (logError) {
                console.error('Failed to log team invitation (from available slot) event:', logError);
            }

            return sendSuccess(res, 201, updatedSlot);
        }

        // 1. Check subscription capacity (no available slots to reuse)
        const { count: existingLicensesCount, error: countError } = await supabaseAdmin
            .from('licenses')
            .select('id', { count: 'exact', head: true })
            .eq('subscription_id', subscriptionId);

        if (countError) {
            console.error('Error counting existing licenses for subscription:', countError);
            return sendError(res, 'Could not verify subscription capacity.', 500, countError.message);
        }

        if (existingLicensesCount >= req.subscription.quantity) {
            return sendError(res, 'Subscription capacity reached. Cannot invite more users.', 409);
        }

        // 2. Determine Product ID and Tier
        const productIdToUse = "quantboost-suite"; // Single product ID as per user clarification
        const licenseTierToUse = "team";         // Licenses created via team invite are "team" tier

        // The console.warn messages for placeholder product_id and license_tier are removed
        // as these are now explicitly set based on the simplified approach.

        // 3. Create the new license record
        const newLicenseData = {
            subscription_id: subscriptionId,
            product_id: productIdToUse,
            status: 'assigned', // ready for invited user to claim
            email: target_email,
            user_id: null, // populated when user claims/activates
            expiry_date: req.subscription.current_period_end ? new Date(req.subscription.current_period_end).toISOString() : null,
            updated_at: new Date().toISOString(),
            license_tier: licenseTierToUse,
            // max_activations and license_key use DB defaults
        };

        const { data: createdLicense, error: licenseCreationError } = await supabaseAdmin
            .from('licenses')
            .insert(newLicenseData)
            .select()
            .single();

        if (licenseCreationError) {
            console.error('Error creating invited team license:', licenseCreationError);
            // Check for unique constraint violation on email if you have one for active/assigned licenses per product
            if (licenseCreationError.code === '23505') { // Unique violation
                 return sendError(res, `An active or assigned license already exists for ${target_email} for this product/subscription type.`, 409, licenseCreationError.message);
            }
            return sendError(res, 'Failed to create license for invitation.', 500, licenseCreationError.message);
        }

        // 4. Send invite email and log the event
        try {
            const redirectTo = 'https://app-quantboost-frontend-staging.azurewebsites.net/dashboard';
            let emailSent = false;
            try {
                const { error: inviteErr } = await supabaseAdmin.auth.admin.inviteUserByEmail(target_email, { redirectTo });
                if (inviteErr) {
                    console.warn('Invite email (admin.inviteUserByEmail) failed, will fallback to magic link:', inviteErr);
                } else {
                    emailSent = true;
                    try { await logLicenseEvent(null, 'invite_email_sent', createdLicense.id, 'team_admin_invite', target_email, 'success_invite'); } catch {}
                }
            } catch (e) {
                console.warn('Exception during inviteUserByEmail, will fallback:', e?.message || e);
            }

            if (!emailSent) {
                try {
                    const { error: magicErr } = await supabase.auth.signInWithOtp({
                        email: target_email,
                        options: { emailRedirectTo: redirectTo, shouldCreateUser: true },
                    });
                    if (magicErr) {
                        console.error('Fallback magic link failed:', magicErr);
                        try { await logLicenseEvent(null, 'invite_email_error', createdLicense.id, 'team_admin_invite', target_email, 'error_email_send_failed'); } catch {}
                    } else {
                        try { await logLicenseEvent(null, 'invite_email_sent_magic', createdLicense.id, 'team_admin_invite', target_email, 'success_magiclink'); } catch {}
                    }
                } catch (e) {
                    console.error('Exception during fallback magic link send:', e?.message || e);
                }
            }

            // Log the invite creation event
            try {
                await logLicenseEvent(
                    null,
                    'team_invited',
                    createdLicense.id,
                    'team_admin_invite',
                    target_email,
                    'success_created_invite'
                );
            } catch {}
        } catch (logError) {
            console.error("Failed during invite email send/logging:", logError); 
            // Non-critical, so don't fail the whole request
        }

        // TODO: Trigger an actual email invitation to target_email. This is outside the scope of this API endpoint's direct response.

        sendSuccess(res, 201, createdLicense); // 201 Created for new resource

    } catch (err) {
        console.error('Unexpected error in POST /team-licenses/invite:', err);
        return sendError(res, 'An unexpected error occurred while processing the invitation.', 500);
    }
});

// POST /v1/me/subscriptions/:subscriptionId/team-licenses/unassign
router.post('/team-licenses/unassign', checkSubscriptionOwner, async (req, res) => {
    const { subscriptionId } = req.params;
    const { license_id_to_unassign } = req.body;
    const requestingUserId = req.user.id; // The subscription owner

    if (!license_id_to_unassign) {
        return sendError(res, 'License ID to unassign is required.', 400);
    }

    try {
        // 1. Fetch the license to be unassigned
        const { data: license, error: licenseFetchError } = await supabaseAdmin
            .from('licenses')
            .select('*')
            .eq('id', license_id_to_unassign)
            .eq('subscription_id', subscriptionId) // Ensure it belongs to this subscription
        .single();
        if (licenseFetchError || !license) {
            return sendError(res, 'License not found or it does not belong to this subscription.', 404);
        }

        // 2. Check if the license is actually assigned
        if (!license.user_id && license.status !== 'active' && license.status !== 'assigned') {
            // If it's already unassigned or in a state that's not actively used by a team member
            return sendError(res, `License ${license_id_to_unassign} is not currently assigned to a user or is not active.`, 409);
        }
        const unassignedFromUserId = license.user_id; // Capture for logging before clearing
        const unassignedFromEmail = license.email; // Capture for logging

        // 3. Update the license to unassign it
        const updates = {
            user_id: null,
            email: null, // Clear the assigned email
            status: 'unassigned', // Or 'available', 'revoked' depending on desired lifecycle
            updated_at: new Date().toISOString(),
            // expiry_date remains tied to the subscription period, or could be cleared if the license slot is fully reset.
            // For now, keeping expiry_date as is, assuming the slot is still valid for the subscription period.
            // activated_at: null, // Optionally clear activation details if any
            // device_fingerprint: null, // Optionally clear device fingerprint
        };

        const { data: updatedLicense, error: licenseUpdateError } = await supabaseAdmin
            .from('licenses')
            .update(updates)
            .eq('id', license_id_to_unassign)
            .select()
            .single();

        if (licenseUpdateError) {
            console.error('Error unassigning team license:', licenseUpdateError);
            return sendError(res, 'Failed to unassign license.', 500, licenseUpdateError.message);
        }

        // 4. Log the event
        try {
            await logLicenseEvent(
                null,
                'team_unassigned',
                license.id,
                'team_admin_unassign',
                unassignedFromEmail || (unassignedFromUserId ? `user:${unassignedFromUserId}` : 'unknown'),
                'success_unassigned'
            );
        } catch (logError) {
            console.error("Failed to log team unassignment event:", logError);
        }
        // 5. Ensure placeholders exist up to seat quantity so UI counts remain consistent
        try {
            await ensureSeatPlaceholders(subscriptionId, req.subscription);
        } catch {}

        sendSuccess(res, 200, updatedLicense);

    } catch (err) {
        console.error('Unexpected error in POST /team-licenses/unassign:', err);
        return sendError(res, 'An unexpected error occurred while unassigning the license.', 500);
    }
});

// POST /v1/me/subscriptions/:subscriptionId/team-licenses/resend
// Resends the invitation or magic link email for a pending invitee (license with status 'assigned')
router.post('/team-licenses/resend', checkSubscriptionOwner, async (req, res) => {
    const { subscriptionId } = req.params;
    const { license_id, target_email } = req.body || {};

    if (!license_id && !target_email) {
        return sendError(res, 'Provide either license_id or target_email to resend invite.', 400);
    }

    try {
        // Locate the target license within this subscription
        let license;
        if (license_id) {
            const { data, error } = await supabaseAdmin
                .from('licenses')
                .select('*')
                .eq('id', license_id)
                .eq('subscription_id', subscriptionId)
                .single();
            if (error || !data) {
                return sendError(res, 'License not found for this subscription.', 404);
            }
            license = data;
        } else {
            // target_email path
            if (!isValidEmail(target_email)) {
                return sendError(res, 'Invalid email format.', 400);
            }
            const { data, error } = await supabaseAdmin
                .from('licenses')
                .select('*')
                .eq('subscription_id', subscriptionId)
                .eq('email', target_email)
                .order('updated_at', { ascending: false })
                .limit(1)
                .single();
            if (error || !data) {
                return sendError(res, `No license found for ${target_email} on this subscription.`, 404);
            }
            license = data;
        }

        // Only resend for pending invitees (status 'assigned' and not yet claimed)
        if (license.status !== 'assigned') {
            // It's safe to allow resend for inactive (edge cases), but we keep it strict to UI wording
            return sendError(res, 'Resend is only available for pending invites.', 409);
        }

        const email = license.email || target_email;
        if (!email || !isValidEmail(email)) {
            return sendError(res, 'The selected license does not have a valid email to resend to.', 400);
        }

        const redirectTo = 'https://app-quantboost-frontend-staging.azurewebsites.net/dashboard';
        let emailSent = false;

        // First try to send a formal invite (works for users that do not exist yet)
        try {
            const { error: inviteErr } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, { redirectTo });
            if (inviteErr) {
                console.warn('Resend: inviteUserByEmail failed, will fallback to magic link:', inviteErr);
            } else {
                emailSent = true;
                try { await logLicenseEvent(null, 'invite_resend_email_sent', license.id, 'team_admin_resend', email, 'success_invite'); } catch {}
            }
        } catch (e) {
            console.warn('Resend: exception during inviteUserByEmail, will fallback:', e?.message || e);
        }

        if (!emailSent) {
            try {
                const { error: magicErr } = await supabase.auth.signInWithOtp({
                    email,
                    options: { emailRedirectTo: redirectTo, shouldCreateUser: true },
                });
                if (magicErr) {
                    console.error('Resend: fallback magic link failed:', magicErr);
                    try { await logLicenseEvent(null, 'invite_resend_email_error', license.id, 'team_admin_resend', email, 'error_email_send_failed'); } catch {}
                } else {
                    try { await logLicenseEvent(null, 'invite_resend_email_sent_magic', license.id, 'team_admin_resend', email, 'success_magiclink'); } catch {}
                }
            } catch (e) {
                console.error('Resend: exception during fallback magic link send:', e?.message || e);
            }
        }

        return sendSuccess(res, 200, { status: 'resent', license_id: license.id, email });
    } catch (err) {
        console.error('Unexpected error in POST /team-licenses/resend:', err);
        return sendError(res, 'An unexpected error occurred while resending the invite.', 500);
    }
});

module.exports = router;
