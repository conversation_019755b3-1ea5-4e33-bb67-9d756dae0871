"use client";

import { motion, useReducedMotion, Variants } from "motion/react";

interface AnimatedQuantBoostLogoProps {
  size?: string | number;
  className?: string;
  showDrawAnimation?: boolean;
  onAnimationComplete?: () => void;
}

// Sequential animation variants with drawing effects for three stages: box → arrow → small boxes
const logoVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.9,
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut",
      staggerChildren: 0.5,  // 0.5s delay between each animation stage
      delayChildren: 0.3     // Initial delay before starting
    }
  },
  hover: {
    transition: {
      staggerChildren: 0.1,  // Stagger hover animations slightly
      delayChildren: 0
    }
  }
};

// Stage 1: Outside box (main body) with draw animation
const boxVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: 20,
    pathLength: 0,
    fill: "transparent",
    stroke: "#181818",
    strokeWidth: 3
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    pathLength: 1,
    fill: "#181818",
    stroke: "transparent", // Remove stroke after animation
    strokeWidth: 0,
    transition: {
      duration: 1.2,
      ease: "easeOut",
      pathLength: { duration: 0.8, ease: "easeInOut" },
      fill: { delay: 0.6, duration: 0.4, ease: "easeOut" },
      stroke: { delay: 0.8, duration: 0.2, ease: "easeOut" }, // Fade out stroke
      strokeWidth: { delay: 0.8, duration: 0.2, ease: "easeOut" },
      opacity: { duration: 0.3 },
      scale: { duration: 0.6, ease: "easeOut" },
      y: { duration: 0.6, ease: "easeOut" }
    }
  }
};

// Stage 2: Arrow in the middle with draw animation and hover effects
const arrowVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.7,
    rotate: -10,
    pathLength: 0,
    fill: "transparent",
    stroke: "#181818",
    strokeWidth: 2.5
  },
  visible: {
    opacity: 1,
    scale: 1,
    rotate: 0,
    pathLength: 1,
    fill: "#181818",
    stroke: "transparent", // Remove stroke after animation
    strokeWidth: 0,
    transition: {
      duration: 1.0,
      ease: "easeOut",
      pathLength: { duration: 0.6, ease: "easeInOut" },
      fill: { delay: 0.4, duration: 0.3, ease: "easeOut" },
      stroke: { delay: 0.6, duration: 0.2, ease: "easeOut" }, // Fade out stroke
      strokeWidth: { delay: 0.6, duration: 0.2, ease: "easeOut" },
      opacity: { duration: 0.2 },
      scale: { duration: 0.5, type: "spring", stiffness: 200 },
      rotate: { duration: 0.5, type: "spring", stiffness: 200 }
    }
  },
  hover: {
    scale: 1.15,
    rotate: 2,
    transition: {
      duration: 0.12,
      type: "spring",
      stiffness: 700,
      damping: 18
    }
  }
};

// Stage 3: Small boxes in top right with draw animation and hover effects
const smallBoxesVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.6,
    x: 10,
    y: -10,
    pathLength: 0,
    fill: "transparent",
    stroke: "#181818",
    strokeWidth: 2
  },
  visible: {
    opacity: 1,
    scale: 1,
    x: 0,
    y: 0,
    pathLength: 1,
    fill: "#181818",
    stroke: "transparent", // Remove stroke after animation
    strokeWidth: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut",
      pathLength: { duration: 0.5, ease: "easeInOut" },
      fill: { delay: 0.3, duration: 0.2, ease: "easeOut" },
      stroke: { delay: 0.5, duration: 0.2, ease: "easeOut" }, // Fade out stroke
      strokeWidth: { delay: 0.5, duration: 0.2, ease: "easeOut" },
      opacity: { duration: 0.2 },
      scale: { duration: 0.4, type: "spring", stiffness: 300 },
      x: { duration: 0.4, type: "spring", stiffness: 300 },
      y: { duration: 0.4, type: "spring", stiffness: 300 }
    }
  },
  hover: {
    scale: 1.15,
    x: 3,
    y: -3,
    transition: {
      duration: 0.08,
      type: "spring",
      stiffness: 800,
      damping: 15
    }
  }
};

// Reduced motion variants (no animation)
const reducedMotionVariants: Variants = {
  hidden: { opacity: 1, fill: "#181818", pathLength: 1 },
  visible: { opacity: 1, fill: "#181818", pathLength: 1 },
  idle: { scale: 1, opacity: 1, fill: "#181818", pathLength: 1 },
  hover: { scale: 1.02, opacity: 1, fill: "#181818", pathLength: 1 }
};

const AnimatedQuantBoostLogo: React.FC<AnimatedQuantBoostLogoProps> = ({
  size = "12rem",
  className = "",
  showDrawAnimation = true,
  onAnimationComplete
}) => {
  const prefersReducedMotion = useReducedMotion();
  
  const handleAnimationComplete = () => {
    onAnimationComplete?.();
  };

  // For sequential animations, we don't need animation stages
  // Just use variants directly based on showDrawAnimation and reducedMotion
  const getCurrentVariants = () => {
    if (prefersReducedMotion || !showDrawAnimation) {
      return reducedMotionVariants;
    }
    return logoVariants;
  };

  const getCurrentInitial = () => {
    if (prefersReducedMotion || !showDrawAnimation) {
      return "visible";
    }
    return "hidden";
  };

  const getCurrentAnimate = () => {
    if (prefersReducedMotion || !showDrawAnimation) {
      return "visible";
    }
    return "visible";
  };

  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 1712 1712"
      className={`block ${className}`}
      variants={getCurrentVariants()}
      initial={getCurrentInitial()}
      animate={getCurrentAnimate()}
      whileHover={showDrawAnimation && !prefersReducedMotion ? "hover" : undefined}
      onAnimationComplete={handleAnimationComplete}
      style={{ 
        originX: 0.5, 
        originY: 0.5,
        opacity: 1,
        display: 'block',
        cursor: 'pointer'
      }}
    >
      {/* Stage 1: Outside box (main body shape) */}
      <motion.path
        d="M 222.618 206.396 C 224.081 206.261 225.526 206.138 226.995 206.073 C 250.981 205.017 275.227 205.728 299.241 205.787 L 407.645 205.732 L 754.468 205.768 L 1023.68 205.767 L 1115.22 205.786 C 1130.99 205.802 1147.21 205.043 1162.94 206.128 C 1170.12 206.623 1177.53 208.646 1182.77 213.832 C 1186.89 217.912 1189.29 223.245 1190.47 228.862 C 1194.44 247.678 1190.7 293.956 1190.9 315.986 L 490.449 316.041 L 343.15 315.979 C 313.625 315.944 231.442 312.706 209.433 320.015 C 192.904 325.504 178.014 339.475 170.325 354.963 C 164.697 366.298 162.788 378.401 162.26 390.939 C 160.376 435.665 162.116 481.175 162.105 525.992 L 162.039 795.112 L 162.06 1205.49 L 162.054 1333.1 C 162.058 1359.66 158.917 1425.43 166.805 1447.96 C 173.317 1466.56 186.795 1480.86 204.518 1489.29 C 214.765 1494.16 225.302 1496.52 236.608 1496.94 C 260.106 1497.81 283.837 1497.2 307.355 1497.2 L 436.396 1497.21 L 826.973 1497.25 L 1177.58 1497.23 L 1290.19 1497.24 C 1312.84 1497.26 1335.7 1497.82 1358.32 1496.82 C 1366.15 1496.48 1373.01 1494.66 1380.34 1492.02 C 1390.99 1487.6 1400.47 1480.78 1408.06 1472.1 C 1417.7 1460.92 1423.2 1446.41 1424.89 1431.84 C 1428.2 1403.41 1426.18 1372.87 1426.18 1344.24 L 1426.16 1184.75 L 1426.19 1000.42 L 1426.29 941.637 C 1426.31 929.868 1426.8 917.883 1425.85 906.15 C 1441.68 903.787 1459.04 904.975 1475.06 904.95 C 1495.72 904.918 1516.67 904.203 1537.3 905.398 C 1538.56 1024.58 1537.4 1143.93 1537.35 1263.12 L 1537.26 1370.14 C 1537.22 1400.8 1538.5 1431.67 1532.91 1461.97 C 1527.37 1492.06 1514.16 1519.38 1494.99 1543.15 C 1467.97 1576.66 1422.28 1602.93 1379.15 1607.31 C 1351.97 1610.08 1323.92 1608.91 1296.61 1608.9 L 1165.48 1608.81 L 731.577 1608.83 L 378.475 1608.78 L 274.849 1608.8 C 252.481 1608.8 229.243 1609.87 207.05 1606.91 C 173.239 1602.4 140.901 1586.22 114.687 1564.77 C 77.9427 1534.69 54.9707 1489.29 50.5879 1442.23 C 48.2689 1417.34 49.4574 1391.74 49.5392 1366.76 L 49.6584 1251.81 L 49.6778 858.491 L 49.5916 530.675 L 49.4665 432.827 C 49.424 412.518 48.2752 391.359 50.8091 371.181 C 55.4274 334.405 69.1876 299.993 92.9532 271.364 C 126.38 231.097 171.138 211.056 222.618 206.396 z"
        fill={showDrawAnimation && !prefersReducedMotion ? "transparent" : "#181818"}
        variants={showDrawAnimation && !prefersReducedMotion ? boxVariants : undefined}
        style={{ 
          opacity: 1,
          vectorEffect: showDrawAnimation && !prefersReducedMotion ? 'non-scaling-stroke' : 'none'
        }}
      />

      {/* Stage 2: Arrow in the middle (directional element) */}
      <motion.path
        d="M 1154.46 541.337 C 1155.31 541.263 1156.17 541.191 1157.03 541.152 C 1162.85 540.89 1171.09 542.527 1175.34 546.684 C 1179.78 551.015 1181.23 556.858 1180.95 562.892 C 1180.35 576.244 1176.09 590.778 1173.88 604.095 C 1169.6 630.756 1165.57 657.456 1161.79 684.192 C 1153.69 741.294 1145.11 798.327 1136.03 855.282 C 1132.27 879.129 1129.46 903.422 1124.58 927.054 C 1123.32 933.136 1122.29 939.752 1117.41 944.077 C 1113.44 947.601 1107.74 948.988 1102.52 948.675 C 1096.14 948.293 1089.84 944.152 1084.94 940.271 C 1067.15 926.172 1051.91 907.144 1036.7 890.316 C 1021.2 873.443 1005.58 856.682 989.836 840.036 C 982.901 848.458 974.2 855.785 966.481 863.507 C 954.396 875.596 942.709 888.038 930.85 900.344 C 842.127 992.402 750.491 1081.97 660.096 1172.41 L 563.757 1268.7 C 545.463 1286.98 527.474 1306.72 507.762 1323.43 C 500.276 1329.78 493.374 1335.99 483.015 1335.86 C 467.236 1335.67 454.009 1318.96 443.72 1308.86 L 403.093 1268.45 C 394.718 1260.17 384.753 1251.8 378.099 1242.03 C 375.217 1237.8 372.865 1232.54 373.028 1227.32 C 373.198 1221.85 374.878 1217.46 378.332 1213.28 C 393.441 1195.01 411.872 1178.83 428.361 1161.77 C 471.744 1117.07 515.535 1072.77 559.73 1028.87 L 864.54 721.647 C 852.05 710.93 773.83 625.074 772.165 614.092 C 771.369 608.848 772.827 604.128 775.945 599.913 C 779.584 594.994 783.899 593.352 789.648 592.035 C 801.037 589.426 812.921 588.404 824.514 586.945 C 845.163 584.435 865.794 581.775 886.405 578.963 L 1154.46 541.337 z"
        fill={showDrawAnimation && !prefersReducedMotion ? "transparent" : "#181818"}
        variants={showDrawAnimation && !prefersReducedMotion ? arrowVariants : undefined}
        style={{ 
          opacity: 1,
          vectorEffect: showDrawAnimation && !prefersReducedMotion ? 'non-scaling-stroke' : 'none',
          transformOrigin: 'center center'
        }}
      />

      {/* Stage 3: Small boxes in top right (remaining elements) */}
      <motion.path
        d="M 1571.62 90.4306 C 1586.28 89.1978 1601.16 89.7004 1615.86 89.7268 L 1682.6 89.8814 C 1681.01 108.658 1685.7 192.105 1680.85 200.385 C 1644.13 198.779 1607.03 200.45 1570.25 199.974 C 1570.59 185.057 1568.07 96.7924 1571.62 90.4306 z M 1484.8 361.191 C 1519.68 358.715 1559.6 360.855 1595.07 360.929 C 1593.42 377.368 1594.47 394.491 1594.48 411.013 C 1594.49 431.39 1593.68 451.937 1594.71 472.288 C 1557.98 471.126 1521.02 472.178 1484.26 472.034 C 1486.62 445.055 1485 416.592 1484.77 389.461 C 1484.69 380.097 1484.05 370.514 1484.8 361.191 z M 1503.35 701.433 C 1517.09 699.831 1531.46 700.667 1545.28 700.733 L 1610.33 701.014 C 1610.21 725.275 1610.23 749.537 1610.39 773.797 C 1610.42 784.873 1611.44 796.996 1610.13 807.936 C 1609.84 810.325 1609.68 810.443 1607.89 811.87 C 1591.09 812.259 1513.02 815.237 1503.58 810.981 C 1503.07 810.144 1502.87 809.919 1502.59 808.839 C 1501.53 804.62 1501.42 705.121 1502.07 703.396 C 1502.35 702.665 1502.92 702.087 1503.35 701.433 z M 1373.84 204.407 C 1407.2 203.959 1440.56 203.955 1473.92 204.396 C 1473.56 232.187 1473.35 259.979 1473.28 287.773 C 1473.25 295.416 1474.96 308.398 1473.27 315.146 L 1471.16 316.263 C 1436.02 315.099 1400.78 316.546 1365.62 315.589 C 1366.91 290.405 1365.79 264.686 1365.74 239.447 C 1365.72 228.734 1365 217.454 1366.54 206.846 C 1368.38 204.925 1371.33 204.856 1373.84 204.407 z M 1232.8 361.206 C 1267.89 359.17 1304.11 360.738 1339.27 361.14 C 1336.47 385.397 1338.67 420.191 1338.75 445.579 C 1338.77 454.167 1339.53 463.596 1338.32 472.075 C 1302.84 471.007 1267.08 472.186 1231.57 471.981 C 1232.88 446.928 1232.15 421.66 1232.11 396.571 C 1232.09 384.921 1231.48 372.775 1232.8 361.206 z M 1379.56 537.172 C 1415.36 535.737 1451.32 537.546 1487.16 536.768 C 1485.35 563.167 1486.31 590.132 1486.55 616.581 C 1486.63 626.199 1487.49 636.268 1486.77 645.821 C 1450.89 647.257 1414.2 646.622 1378.28 645.944 C 1381.15 619.465 1379 590.923 1379.03 564.194 C 1379.04 555.422 1378.33 545.814 1379.56 537.172 z"
        fill={showDrawAnimation && !prefersReducedMotion ? "transparent" : "#181818"}
        variants={showDrawAnimation && !prefersReducedMotion ? smallBoxesVariants : undefined}
        style={{ 
          opacity: 1,
          vectorEffect: showDrawAnimation && !prefersReducedMotion ? 'non-scaling-stroke' : 'none',
          transformOrigin: 'center center'
        }}
      />
    </motion.svg>
  );
};

export default AnimatedQuantBoostLogo;