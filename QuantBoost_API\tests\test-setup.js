const path = require('path');
// Only load .env.test if we're running locally (not in CI)
if (!process.env.CI && !process.env.GITHUB_ACTIONS) {
  require('dotenv').config({ path: path.resolve(__dirname, '../.env.test') });
}

const { createClient } = require('@supabase/supabase-js');

// Test data
const validLicenseKey = process.env.TEST_VALID_LICENSE_KEY || 'test-key-valid';
const invalidLicenseKey = 'test-key-invalid';
const nonExistentLicenseKey = 'test-key-nonexistent';
const validProductId = "quantboost-suite";
const anotherProductId = 'other-product';

// Initialize Supabase test client (admin client)
const supabase_test_client = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY, {
    auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false
    }
});

// Test user shared object (can be populated by tests if needed globally)
let testUser = { email: null, password: null, token: null, id: null };

// Helper function to generate a unique email for testing
function generateTestEmail() {
    const randomString = Math.random().toString(36).substring(2, 10);
    return `testuser${randomString}@quantboost.ai`;
}

// Helper function to create a test user and get session details
// Expects adminClient (supabase_test_client), email, and password
async function createTestUserAndSession(adminClient, email, password) {
    let sessionDetails = { email: email, token: null, userId: null, error: null, password: password };    console.log(`[Test Setup] Attempting to create user: ${email}`);
    try {
        // Use admin.createUser to bypass email confirmation for test users
        const { data: signUpData, error: signUpError } = await adminClient.auth.admin.createUser({
            email: email,
            password: password,
            email_confirm: true,  // Auto-confirm email for test users
            user_metadata: { full_name: 'Test User' }
        });

        if (signUpError) {
            console.error(`[Test Setup] Error creating user ${email}: ${signUpError.message}`);
            sessionDetails.error = signUpError;
            if (signUpError.message.toLowerCase().includes('user already registered') || signUpError.message.toLowerCase().includes('user already exists')) {
                console.log(`[Test Setup] User ${email} already exists, attempting login.`);
                const { data: loginData, error: loginError } = await adminClient.auth.signInWithPassword({ email: email, password: password });
                if (loginError) {
                    console.error(`[Test Setup] Error logging in existing user ${email}: ${loginError.message}`);
                    sessionDetails.error = loginError;
                } else if (loginData && loginData.session && loginData.user) {
                    console.log(`[Test Setup] Successfully logged in existing user ${email}. User ID: ${loginData.user.id}`);
                    sessionDetails.token = loginData.session.access_token;
                    sessionDetails.userId = loginData.user.id;
                    sessionDetails.error = null; 
                } else {
                     console.warn(`[Test Setup] Login attempt for ${email} after user exists error did not return expected data.`);
                }
            }
        } else if (signUpData && signUpData.user) {
            console.log(`[Test Setup] Successfully created user ${email}. User ID: ${signUpData.user.id}. Now attempting login.`);
            sessionDetails.userId = signUpData.user.id;
            // Admin created users don't return sessions, so we need to sign in
            const { data: loginData, error: loginError } = await adminClient.auth.signInWithPassword({ email: email, password: password });
            if (loginError) {
                console.error(`[Test Setup] Error logging in newly created user ${email}: ${loginError.message}`);
                sessionDetails.error = loginError;
            } else if (loginData && loginData.session && loginData.user) {
                console.log(`[Test Setup] Successfully logged in newly created user ${email}. User ID: ${loginData.user.id}`);
                sessionDetails.token = loginData.session.access_token;
                sessionDetails.userId = loginData.user.id;
            } else {
                console.warn(`[Test Setup] Login attempt for newly created user ${email} did not return expected data.`);
            }
        } else {
            console.warn(`[Test Setup] User creation for ${email} did not return user data as expected.`);
        }
    } catch (e) {
        console.error(`[Test Setup Critical] Exception during createTestUserAndSession for ${email}: ${e.message}`);
        sessionDetails.error = e;
    }
    
    if (!sessionDetails.token) {
        console.warn(`[Test Setup Warning] Test user token NOT available for ${email} after all attempts. User ID: ${sessionDetails.userId || 'unknown'}. Error: ${sessionDetails.error ? sessionDetails.error.message : 'None'}.`);
    }
    return sessionDetails;
}

// Helper function to get a test user token (signs up if not exists, then signs in)
// Expects client (supabase_test_client), email, and password
async function getTestUserToken(client, email, password) {
    console.log(`[Test Setup] Attempting to create/login test user with email: ${email}`);
    let sessionData = { token: null, userId: null, error: null };

    let { data: signInData, error: signInError } = await client.auth.signInWithPassword({ email, password });

    if (signInError) {
        if (signInError.message.includes('Invalid login credentials') || signInError.status === 400) {
            const { data: signUpData, error: signUpError } = await client.auth.signUp({
                email,
                password,
                options: { data: { full_name: 'Test User Auto-Created' } }
            });
            if (signUpError) {
                sessionData.error = signUpError;
            } else if (signUpData && signUpData.session && signUpData.user) {
                sessionData.token = signUpData.session.access_token;
                sessionData.userId = signUpData.user.id;
            } else if (signUpData && signUpData.user && !signUpData.session) {
                const { data: postSignUpLoginData, error: postSignUpLoginError } = await client.auth.signInWithPassword({ email, password });
                if (postSignUpLoginError) {
                    sessionData.error = postSignUpLoginError;
                } else if (postSignUpLoginData && postSignUpLoginData.session && postSignUpLoginData.user) {
                    sessionData.token = postSignUpLoginData.session.access_token;
                    sessionData.userId = postSignUpLoginData.user.id;
                }
            }
        } else {
            sessionData.error = signInError;
        }
    } else if (signInData && signInData.session && signInData.user) {
        sessionData.token = signInData.session.access_token;
        sessionData.userId = signInData.user.id;
    } else {
        // Sign-in did not return session or user data as expected
    }

    if (!sessionData.token) {
         console.warn(`[Test Setup Warning] Test user token NOT available for ${email} after all attempts. User ID: ${sessionData.userId || 'unknown'}. Error: ${sessionData.error ? sessionData.error.message : 'None'}.`);
    }
    return sessionData;
}

module.exports = {
    supabase_test_client,
    testUser,
    validLicenseKey,
    invalidLicenseKey,
    nonExistentLicenseKey,
    validProductId,
    anotherProductId,
    generateTestEmail,
    createTestUserAndSession,
    getTestUserToken
};
