'use client';

import { useState, useEffect } from 'react';
import { useSupabaseClient } from './useSupabaseClient';
import { analyticsApi } from '@/lib/analytics-api';
import { DisputeMetrics, PaymentHealthMetrics, SystemAlert, AlertSummary, PaymentHealthCustomer, DisputeAnalytics } from '@/types/analytics';

interface UseAnalyticsOptions {
  dateRange?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useAnalytics(options: UseAnalyticsOptions = {}) {
  const { dateRange = 30, autoRefresh = false, refreshInterval = 60000 } = options;
  const supabase = useSupabaseClient();
  
  const [disputeMetrics, setDisputeMetrics] = useState<DisputeMetrics | null>(null);
  const [paymentHealthMetrics, setPaymentHealthMetrics] = useState<PaymentHealthMetrics | null>(null);
  const [alertSummary, setAlertSummary] = useState<AlertSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const getToken = async () => {
    const session = await supabase.auth.getSession();
    if (!session.data.session?.access_token) {
      throw new Error('No authentication token available');
    }
    return session.data.session.access_token;
  };

  const fetchDashboardData = async () => {
    try {
      setError(null);
      const token = await getToken();
      
      const [disputes, payments, alerts] = await Promise.all([
        analyticsApi.getDisputeDashboard(token, dateRange),
        analyticsApi.getPaymentHealthDashboard(token, dateRange),
        analyticsApi.getAlertDashboard(token),
      ]);
      
      setDisputeMetrics(disputes);
      setPaymentHealthMetrics(payments);
      setAlertSummary(alerts);
    } catch (err) {
      console.error('Failed to fetch dashboard data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshData = () => {
    setIsLoading(true);
    return fetchDashboardData();
  };

  useEffect(() => {
    fetchDashboardData();
  }, [dateRange]);

  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchDashboardData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, dateRange]);

  return {
    disputeMetrics,
    paymentHealthMetrics,
    alertSummary,
    isLoading,
    error,
    refreshData,
  };
}

export function useDisputes() {
  const supabase = useSupabaseClient();
  const [disputes, setDisputes] = useState<DisputeAnalytics[]>([]);
  const [overdueDisputes, setOverdueDisputes] = useState<DisputeAnalytics[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getToken = async () => {
    const session = await supabase.auth.getSession();
    if (!session.data.session?.access_token) {
      throw new Error('No authentication token available');
    }
    return session.data.session.access_token;
  };

  const fetchDisputes = async (params?: {
    status?: string;
    priority?: string;
    limit?: number;
    offset?: number;
  }) => {
    try {
      setIsLoading(true);
      setError(null);
      const token = await getToken();
      const data = await analyticsApi.getDisputeList(token, params);
      setDisputes(data);
      return data;
    } catch (err) {
      console.error('Failed to fetch disputes:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch disputes');
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const fetchOverdueDisputes = async () => {
    try {
      const token = await getToken();
      const data = await analyticsApi.getOverdueDisputes(token);
      setOverdueDisputes(data);
      return data;
    } catch (err) {
      console.error('Failed to fetch overdue disputes:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch overdue disputes');
      return [];
    }
  };

  return {
    disputes,
    overdueDisputes,
    isLoading,
    error,
    fetchDisputes,
    fetchOverdueDisputes,
  };
}

export function usePaymentHealth() {
  const supabase = useSupabaseClient();
  const [customers, setCustomers] = useState<PaymentHealthCustomer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getToken = async () => {
    const session = await supabase.auth.getSession();
    if (!session.data.session?.access_token) {
      throw new Error('No authentication token available');
    }
    return session.data.session.access_token;
  };

  const fetchPaymentHealth = async (params?: {
    userId?: string;
    limit?: number;
    offset?: number;
  }) => {
    try {
      setIsLoading(true);
      setError(null);
      const token = await getToken();
      const data = await analyticsApi.getPaymentHealthMetrics(token, params);
      setCustomers(data);
      return data;
    } catch (err) {
      console.error('Failed to fetch payment health:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch payment health');
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  return {
    customers,
    isLoading,
    error,
    fetchPaymentHealth,
  };
}

export function useAlerts() {
  const supabase = useSupabaseClient();
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getToken = async () => {
    const session = await supabase.auth.getSession();
    if (!session.data.session?.access_token) {
      throw new Error('No authentication token available');
    }
    return session.data.session.access_token;
  };

  const fetchAlerts = async (params?: {
    severity?: string;
    type?: string;
    read?: boolean;
    resolved?: boolean;
    limit?: number;
    offset?: number;
  }) => {
    try {
      setIsLoading(true);
      setError(null);
      const token = await getToken();
      const data = await analyticsApi.getAlerts(token, params);
      setAlerts(data);
      return data;
    } catch (err) {
      console.error('Failed to fetch alerts:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch alerts');
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const updateAlert = async (alertId: string, updates: {
    is_read?: boolean;
    is_resolved?: boolean;
  }) => {
    try {
      const token = await getToken();
      const updatedAlert = await analyticsApi.updateAlert(token, alertId, updates);
      
      // Update local state
      setAlerts(prev => prev.map(alert => 
        alert.id === alertId ? updatedAlert : alert
      ));
      
      return updatedAlert;
    } catch (err) {
      console.error('Failed to update alert:', err);
      setError(err instanceof Error ? err.message : 'Failed to update alert');
      throw err;
    }
  };

  const markAsRead = (alertId: string) => updateAlert(alertId, { is_read: true });
  const markAsResolved = (alertId: string) => updateAlert(alertId, { is_resolved: true });
  const markAsUnread = (alertId: string) => updateAlert(alertId, { is_read: false });

  return {
    alerts,
    isLoading,
    error,
    fetchAlerts,
    updateAlert,
    markAsRead,
    markAsResolved,
    markAsUnread,
  };
}
