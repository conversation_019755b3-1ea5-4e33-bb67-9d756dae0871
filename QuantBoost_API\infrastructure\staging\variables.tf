variable "location" {
  description = "The Azure region where frontend resources will be created"
  type        = string
  default     = "Central US"
}

variable "distribution_location" {
  description = "The Azure region for distribution assets (Storage, CDN)"
  type        = string
  default     = "East US"
}

variable "supabase_url" {
  description = "The Supabase project URL"
  type        = string
  sensitive   = true
}

variable "supabase_anon_key" {
  description = "The Supabase anonymous key"
  type        = string
  sensitive   = true
}

variable "supabase_service_role_key" {
  description = "The Supabase service role key"
  type        = string
  sensitive   = true
}

variable "stripe_publishable_key" {
  description = "The Stripe publishable key"
  type        = string
  sensitive   = true
}

variable "stripe_secret_key" {
  description = "The Stripe secret key"
  type        = string
  sensitive   = true
}

variable "stripe_webhook_signing_secret" {
  description = "The Stripe webhook signing secret"
  type        = string
  sensitive   = true
}

variable "jwt_secret" {
  description = "JWT secret for authentication"
  type        = string
  sensitive   = true
}
