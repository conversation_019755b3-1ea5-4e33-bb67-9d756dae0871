{"version": 4, "terraform_version": "1.9.8", "serial": 61, "lineage": "caf8a18e-16fc-3073-3e84-7281ae3d1402", "outputs": {"app_service_default_hostname": {"value": "app-quantboost-frontend-staging.azurewebsites.net", "type": "string"}, "app_service_name": {"value": "app-quantboost-frontend-staging", "type": "string"}, "app_service_plan_name": {"value": "asp-quantboost-frontend-staging", "type": "string"}, "app_service_principal_id": {"value": "559f09fa-d35e-4e75-8c4d-a5c359adc968", "type": "string"}, "container_registry_name": {"value": "acrquantbooststaging9gn6ng", "type": "string"}, "container_registry_server": {"value": "acrquantbooststaging9gn6ng.azurecr.io", "type": "string"}, "distribution_cdn_url_custom": {"value": "https://staging-download.quantboost.ai/QuantBoost-staging.exe", "type": "string"}, "distribution_cdn_url_default": {"value": "https://quantboost-downloads-staging-9gn6ng.azureedge.net/releases/QuantBoost-staging.exe", "type": "string"}, "distribution_resource_group_name": {"value": "rg-quantboost-distribution-staging", "type": "string"}, "distribution_storage_account_name": {"value": "stqboostdiststg9gn6ng", "type": "string"}, "dns_instructions": {"value": "Create a CNAME record for 'staging-download.quantboost.ai' pointing to 'quantboost-downloads-staging-9gn6ng.azureedge.net'", "type": "string"}, "key_vault_name": {"value": "kv-qb-staging-9gn6ng", "type": "string"}, "resource_group_name": {"value": "rg-quantboost-frontend-staging", "type": "string"}, "trusted_signing_account_name": {"value": "tsa-qb-stg-9gn6ng", "type": "string"}, "upload_instructions": {"value": "Upload QuantBoost-staging.exe to Storage Account 'stqboostdiststg9gn6ng' into the container 'releases'", "type": "string"}}, "resources": [{"mode": "data", "type": "azurerm_client_config", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"client_id": "813f6a93-a6f7-4853-b808-1ab4cb1bb4e2", "id": "Y2xpZW50Q29uZmlncy9jbGllbnRJZD04MTNmNmE5My1hNmY3LTQ4NTMtYjgwOC0xYWI0Y2IxYmI0ZTI7b2JqZWN0SWQ9OWU5ZjE0YWUtMjNmNy00MTYxLTlhNTUtMTI0NWI4NjQwM2Q0O3N1YnNjcmlwdGlvbklkPWZkOGM3MjQ5LTVhYTYtNDhlOS1iYzhkLTMxYTU4ODI1NjgyYTt0ZW5hbnRJZD1iY2Q3M2YzMC1lNzRmLTQzYjktOWVhMy1iNTNmNGIwYjQ4NGU=", "object_id": "9e9f14ae-23f7-4161-9a55-1245b86403d4", "subscription_id": "fd8c7249-5aa6-48e9-bc8d-31a58825682a", "tenant_id": "bcd73f30-e74f-43b9-9ea3-b53f4b0b484e", "timeouts": null}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "azurerm_application_insights", "name": "staging", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 2, "attributes": {"app_id": "db984b79-160f-4428-b9ff-d9db121b48b2", "application_type": "Node.JS", "connection_string": "InstrumentationKey=2e301cc9-9e5b-422f-bb74-c5178985c46b;IngestionEndpoint=https://centralus-2.in.applicationinsights.azure.com/;LiveEndpoint=https://centralus.livediagnostics.monitor.azure.com/;ApplicationId=db984b79-160f-4428-b9ff-d9db121b48b2", "daily_data_cap_in_gb": 100, "daily_data_cap_notifications_disabled": false, "disable_ip_masking": false, "force_customer_storage_for_profiler": false, "id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-frontend-staging/providers/Microsoft.Insights/components/ai-quantboost-staging", "instrumentation_key": "2e301cc9-9e5b-422f-bb74-c5178985c46b", "internet_ingestion_enabled": true, "internet_query_enabled": true, "local_authentication_disabled": false, "location": "centralus", "name": "ai-quantboost-staging", "resource_group_name": "rg-quantboost-frontend-staging", "retention_in_days": 90, "sampling_percentage": 100, "tags": null, "timeouts": null, "workspace_id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-frontend-staging/providers/Microsoft.OperationalInsights/workspaces/law-quantboost-staging"}, "sensitive_attributes": [[{"type": "get_attr", "value": "connection_string"}], [{"type": "get_attr", "value": "instrumentation_key"}]], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["azurerm_log_analytics_workspace.staging", "azurerm_resource_group.staging"]}]}, {"mode": "managed", "type": "azurerm_cdn_endpoint", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 1, "attributes": {"content_types_to_compress": [], "delivery_rule": [], "fqdn": "quantboost-downloads-staging-9gn6ng.azureedge.net", "geo_filter": [], "global_delivery_rule": [], "id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-distribution-staging/providers/Microsoft.Cdn/profiles/cdn-quantboost-staging/endpoints/quantboost-downloads-staging-9gn6ng", "is_compression_enabled": false, "is_http_allowed": true, "is_https_allowed": true, "location": "eastus", "name": "quantboost-downloads-staging-9gn6ng", "optimization_type": "", "origin": [{"host_name": "stqboostdiststg9gn6ng.blob.core.windows.net", "http_port": 80, "https_port": 443, "name": "storage-origin"}], "origin_host_header": "", "origin_path": "", "probe_path": "", "profile_name": "cdn-quantboost-staging", "querystring_caching_behaviour": "IgnoreQueryString", "resource_group_name": "rg-quantboost-distribution-staging", "tags": {}, "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["azurerm_cdn_profile.main", "azurerm_resource_group.distribution", "azurerm_storage_account.distribution", "random_string.suffix"]}]}, {"mode": "managed", "type": "azurerm_cdn_profile", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 1, "attributes": {"id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-distribution-staging/providers/Microsoft.Cdn/profiles/cdn-quantboost-staging", "location": "eastus", "name": "cdn-quantboost-staging", "resource_group_name": "rg-quantboost-distribution-staging", "sku": "Standard_Microsoft", "tags": {}, "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["azurerm_resource_group.distribution"]}]}, {"mode": "managed", "type": "azurerm_container_app_environment", "name": "staging", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"custom_domain_verification_id": "F554CB72F16EAE67235CFB4A11C4656E68EC714EE89EED12C4086BCF53240FB5", "dapr_application_insights_connection_string": "", "default_domain": "purplebay-8a2f19d2.centralus.azurecontainerapps.io", "docker_bridge_cidr": "", "id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-frontend-staging/providers/Microsoft.App/managedEnvironments/cae-quantboost-staging", "identity": [], "infrastructure_resource_group_name": "", "infrastructure_subnet_id": "", "internal_load_balancer_enabled": false, "location": "centralus", "log_analytics_workspace_id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-frontend-staging/providers/Microsoft.OperationalInsights/workspaces/law-quantboost-staging", "logs_destination": "log-analytics", "mutual_tls_enabled": false, "name": "cae-quantboost-staging", "platform_reserved_cidr": "", "platform_reserved_dns_ip_address": "", "resource_group_name": "rg-quantboost-frontend-staging", "static_ip_address": "************", "tags": null, "timeouts": null, "workload_profile": [], "zone_redundancy_enabled": false}, "sensitive_attributes": [[{"type": "get_attr", "value": "dapr_application_insights_connection_string"}]], "private": "********************************************************************************************************************************************************************************", "dependencies": ["azurerm_log_analytics_workspace.staging", "azurerm_resource_group.staging"]}]}, {"mode": "managed", "type": "azurerm_container_registry", "name": "staging", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 2, "attributes": {"admin_enabled": true, "admin_password": "****************************************************", "admin_username": "acrquantbooststaging9gn6ng", "anonymous_pull_enabled": false, "data_endpoint_enabled": false, "data_endpoint_host_names": [], "encryption": [], "export_policy_enabled": true, "georeplications": [], "id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-frontend-staging/providers/Microsoft.ContainerRegistry/registries/acrquantbooststaging9gn6ng", "identity": [], "location": "centralus", "login_server": "acrquantbooststaging9gn6ng.azurecr.io", "name": "acrquantbooststaging9gn6ng", "network_rule_bypass_option": "AzureServices", "network_rule_set": [], "public_network_access_enabled": true, "quarantine_policy_enabled": false, "resource_group_name": "rg-quantboost-frontend-staging", "retention_policy_in_days": 0, "sku": "Basic", "tags": null, "timeouts": null, "trust_policy_enabled": false, "zone_redundancy_enabled": false}, "sensitive_attributes": [[{"type": "get_attr", "value": "admin_password"}]], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["azurerm_resource_group.staging", "random_string.suffix"]}]}, {"mode": "managed", "type": "azurerm_linux_web_app", "name": "frontend", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 1, "attributes": {"app_settings": {"APPINSIGHTS_INSTRUMENTATIONKEY": "2e301cc9-9e5b-422f-bb74-c5178985c46b", "APPLICATIONINSIGHTS_CONNECTION_STRING": "InstrumentationKey=2e301cc9-9e5b-422f-bb74-c5178985c46b;IngestionEndpoint=https://centralus-2.in.applicationinsights.azure.com/;LiveEndpoint=https://centralus.livediagnostics.monitor.azure.com/;ApplicationId=db984b79-160f-4428-b9ff-d9db121b48b2", "NEXT_PUBLIC_AZURE_API_URL": "https://ca-quantboost-api-staging.placeholder.azurecontainerapps.io", "NEXT_PUBLIC_BASE_URL": "https://app-quantboost-frontend-staging.azurewebsites.net", "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": "***REMOVED***", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "***REMOVED***", "NEXT_PUBLIC_SUPABASE_URL": "https://izoutrnsxaaoueljiimu.supabase.co", "NODE_ENV": "production", "SCM_DO_BUILD_DURING_DEPLOYMENT": "true", "STRIPE_SECRET_KEY": "***REMOVED***", "STRIPE_WEBHOOK_SECRET": "whsec_y84fUcgTM5taGcLwNBdOsucbsi6Suy8u", "SUPABASE_SERVICE_KEY": "***REMOVED***", "WEBSITE_NODE_DEFAULT_VERSION": "18-lts"}, "auth_settings": [], "auth_settings_v2": [], "backup": [], "client_affinity_enabled": false, "client_certificate_enabled": false, "client_certificate_exclusion_paths": "", "client_certificate_mode": "Required", "connection_string": [], "custom_domain_verification_id": "F554CB72F16EAE67235CFB4A11C4656E68EC714EE89EED12C4086BCF53240FB5", "default_hostname": "app-quantboost-frontend-staging.azurewebsites.net", "enabled": true, "ftp_publish_basic_authentication_enabled": true, "hosting_environment_id": "", "https_only": true, "id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-frontend-staging/providers/Microsoft.Web/sites/app-quantboost-frontend-staging", "identity": [{"identity_ids": null, "principal_id": "559f09fa-d35e-4e75-8c4d-a5c359adc968", "tenant_id": "bcd73f30-e74f-43b9-9ea3-b53f4b0b484e", "type": "SystemAssigned"}], "key_vault_reference_identity_id": "SystemAssigned", "kind": "app,linux", "location": "centralus", "logs": [], "name": "app-quantboost-frontend-staging", "outbound_ip_address_list": ["************", "**************", "*************", "************", "**************"], "outbound_ip_addresses": "************,**************,*************,************,**************", "possible_outbound_ip_address_list": ["************", "**************", "*************", "************", "*************", "*************", "*************", "*************", "************", "**************", "*************", "**************", "**************", "**************", "**************", "**************", "*************", "*************", "*************", "**************", "**************", "**************"], "possible_outbound_ip_addresses": "************,**************,*************,************,*************,*************,*************,*************,************,**************,*************,**************,**************,**************,**************,**************,*************,*************,*************,**************,**************,**************", "public_network_access_enabled": true, "resource_group_name": "rg-quantboost-frontend-staging", "service_plan_id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-frontend-staging/providers/Microsoft.Web/serverFarms/asp-quantboost-frontend-staging", "site_config": [{"always_on": true, "api_definition_url": "", "api_management_api_id": "", "app_command_line": "", "application_stack": [{"docker_image_name": "", "docker_registry_password": "", "docker_registry_url": "", "docker_registry_username": "", "dotnet_version": "", "go_version": "", "java_server": "", "java_server_version": "", "java_version": "", "node_version": "18-lts", "php_version": "", "python_version": "", "ruby_version": ""}], "auto_heal_setting": [], "container_registry_managed_identity_client_id": "", "container_registry_use_managed_identity": false, "cors": [], "default_documents": ["Default.htm", "Default.html", "Default.asp", "index.htm", "index.html", "iisstart.htm", "default.aspx", "index.php", "hostingstart.html"], "detailed_error_logging_enabled": false, "ftps_state": "Disabled", "health_check_eviction_time_in_min": 0, "health_check_path": "", "http2_enabled": false, "ip_restriction": [], "ip_restriction_default_action": "Allow", "linux_fx_version": "NODE|18-lts", "load_balancing_mode": "LeastRequests", "local_mysql_enabled": false, "managed_pipeline_mode": "Integrated", "minimum_tls_version": "1.2", "remote_debugging_enabled": false, "remote_debugging_version": "VS2022", "scm_ip_restriction": [], "scm_ip_restriction_default_action": "Allow", "scm_minimum_tls_version": "1.2", "scm_type": "None", "scm_use_main_ip_restriction": false, "use_32_bit_worker": true, "vnet_route_all_enabled": false, "websockets_enabled": false, "worker_count": 1}], "site_credential": [{"name": "$app-quantboost-frontend-staging", "password": "heWe15vb95B4ahDW3GgD3Qm9Jk2FYG9nk494AqGN6xNFTlpnRBojJz2F35xw"}], "sticky_settings": [], "storage_account": [], "tags": {"Component": "frontend", "Environment": "staging"}, "timeouts": null, "virtual_network_backup_restore_enabled": false, "virtual_network_subnet_id": "", "vnet_image_pull_enabled": false, "webdeploy_publish_basic_authentication_enabled": true, "zip_deploy_file": ""}, "sensitive_attributes": [[{"type": "get_attr", "value": "site_credential"}], [{"type": "get_attr", "value": "app_settings"}, {"type": "index", "value": {"value": "APPINSIGHTS_INSTRUMENTATIONKEY", "type": "string"}}], [{"type": "get_attr", "value": "app_settings"}, {"type": "index", "value": {"value": "APPLICATIONINSIGHTS_CONNECTION_STRING", "type": "string"}}], [{"type": "get_attr", "value": "app_settings"}, {"type": "index", "value": {"value": "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY", "type": "string"}}], [{"type": "get_attr", "value": "app_settings"}, {"type": "index", "value": {"value": "NEXT_PUBLIC_SUPABASE_ANON_KEY", "type": "string"}}], [{"type": "get_attr", "value": "app_settings"}, {"type": "index", "value": {"value": "NEXT_PUBLIC_SUPABASE_URL", "type": "string"}}], [{"type": "get_attr", "value": "app_settings"}, {"type": "index", "value": {"value": "STRIPE_SECRET_KEY", "type": "string"}}], [{"type": "get_attr", "value": "app_settings"}, {"type": "index", "value": {"value": "STRIPE_WEBHOOK_SECRET", "type": "string"}}], [{"type": "get_attr", "value": "app_settings"}, {"type": "index", "value": {"value": "SUPABASE_SERVICE_KEY", "type": "string"}}], [{"type": "get_attr", "value": "custom_domain_verification_id"}], [{"type": "get_attr", "value": "site_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "application_stack"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "docker_registry_password"}]], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["azurerm_application_insights.staging", "azurerm_log_analytics_workspace.staging", "azurerm_resource_group.staging", "azurerm_service_plan.frontend"]}]}, {"mode": "managed", "type": "azurerm_log_analytics_workspace", "name": "staging", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 3, "attributes": {"allow_resource_only_permissions": true, "cmk_for_query_forced": false, "daily_quota_gb": -1, "data_collection_rule_id": "", "id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-frontend-staging/providers/Microsoft.OperationalInsights/workspaces/law-quantboost-staging", "identity": [], "immediate_data_purge_on_30_days_enabled": false, "internet_ingestion_enabled": true, "internet_query_enabled": true, "local_authentication_disabled": true, "local_authentication_enabled": false, "location": "centralus", "name": "law-quantboost-staging", "primary_shared_key": "B5pPd49EkRFYoBVB5oFcjyVF2dQZXBwzex+UeUqfgryv+oLng6peUhtu1Lr84fsbWaWiTOT8pFGPz/ierU66XQ==", "reservation_capacity_in_gb_per_day": null, "resource_group_name": "rg-quantboost-frontend-staging", "retention_in_days": 30, "secondary_shared_key": "MWE5dmOKDWIh0OWWqkavs9p0THbH+l07g+3npUVum/2aW7C8Hz8NOdf5Ql58mocI8/aroy2MXjetKcMxCM60eQ==", "sku": "PerGB2018", "tags": null, "timeouts": null, "workspace_id": "214e5552-01c3-4fea-9b24-92a2767d023f"}, "sensitive_attributes": [[{"type": "get_attr", "value": "secondary_shared_key"}], [{"type": "get_attr", "value": "primary_shared_key"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIzIn0=", "dependencies": ["azurerm_resource_group.staging"]}]}, {"mode": "managed", "type": "azurerm_resource_group", "name": "distribution", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-distribution-staging", "location": "eastus", "managed_by": "", "name": "rg-quantboost-distribution-staging", "tags": {"environment": "staging", "purpose": "Distribution"}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo1NDAwMDAwMDAwMDAwLCJkZWxldGUiOjU0MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjo1NDAwMDAwMDAwMDAwfX0="}]}, {"mode": "managed", "type": "azurerm_resource_group", "name": "staging", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-frontend-staging", "location": "centralus", "managed_by": "", "name": "rg-quantboost-frontend-staging", "tags": null, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo1NDAwMDAwMDAwMDAwLCJkZWxldGUiOjU0MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjo1NDAwMDAwMDAwMDAwfX0="}]}, {"mode": "managed", "type": "azurerm_service_plan", "name": "frontend", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 1, "attributes": {"app_service_environment_id": "", "id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-frontend-staging/providers/Microsoft.Web/serverFarms/asp-quantboost-frontend-staging", "kind": "linux", "location": "centralus", "maximum_elastic_worker_count": 1, "name": "asp-quantboost-frontend-staging", "os_type": "Linux", "per_site_scaling_enabled": false, "premium_plan_auto_scale_enabled": false, "reserved": true, "resource_group_name": "rg-quantboost-frontend-staging", "sku_name": "B1", "tags": {"Component": "frontend", "Environment": "staging"}, "timeouts": null, "worker_count": 1, "zone_balancing_enabled": false}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["azurerm_resource_group.staging"]}]}, {"mode": "managed", "type": "azurerm_storage_account", "name": "distribution", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 4, "attributes": {"access_tier": "Hot", "account_kind": "StorageV2", "account_replication_type": "LRS", "account_tier": "Standard", "allow_nested_items_to_be_public": true, "allowed_copy_scope": "", "azure_files_authentication": [], "blob_properties": [{"change_feed_enabled": false, "change_feed_retention_in_days": 0, "container_delete_retention_policy": [], "cors_rule": [], "default_service_version": "", "delete_retention_policy": [], "last_access_time_enabled": false, "restore_policy": [], "versioning_enabled": false}], "cross_tenant_replication_enabled": false, "custom_domain": [], "customer_managed_key": [], "default_to_oauth_authentication": false, "dns_endpoint_type": "Standard", "edge_zone": "", "https_traffic_only_enabled": true, "id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-distribution-staging/providers/Microsoft.Storage/storageAccounts/stqboostdiststg9gn6ng", "identity": [], "immutability_policy": [], "infrastructure_encryption_enabled": false, "is_hns_enabled": false, "large_file_share_enabled": false, "local_user_enabled": true, "location": "eastus", "min_tls_version": "TLS1_2", "name": "stqboostdiststg9gn6ng", "network_rules": [], "nfsv3_enabled": false, "primary_access_key": "****************************************************************************************", "primary_blob_connection_string": "DefaultEndpointsProtocol=https;BlobEndpoint=https://stqboostdiststg9gn6ng.blob.core.windows.net/;AccountName=stqboostdiststg9gn6ng;AccountKey=****************************************************************************************", "primary_blob_endpoint": "https://stqboostdiststg9gn6ng.blob.core.windows.net/", "primary_blob_host": "stqboostdiststg9gn6ng.blob.core.windows.net", "primary_blob_internet_endpoint": "", "primary_blob_internet_host": "", "primary_blob_microsoft_endpoint": "", "primary_blob_microsoft_host": "", "primary_connection_string": "DefaultEndpointsProtocol=https;AccountName=stqboostdiststg9gn6ng;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "primary_dfs_endpoint": "https://stqboostdiststg9gn6ng.dfs.core.windows.net/", "primary_dfs_host": "stqboostdiststg9gn6ng.dfs.core.windows.net", "primary_dfs_internet_endpoint": "", "primary_dfs_internet_host": "", "primary_dfs_microsoft_endpoint": "", "primary_dfs_microsoft_host": "", "primary_file_endpoint": "https://stqboostdiststg9gn6ng.file.core.windows.net/", "primary_file_host": "stqboostdiststg9gn6ng.file.core.windows.net", "primary_file_internet_endpoint": "", "primary_file_internet_host": "", "primary_file_microsoft_endpoint": "", "primary_file_microsoft_host": "", "primary_location": "eastus", "primary_queue_endpoint": "https://stqboostdiststg9gn6ng.queue.core.windows.net/", "primary_queue_host": "stqboostdiststg9gn6ng.queue.core.windows.net", "primary_queue_microsoft_endpoint": "", "primary_queue_microsoft_host": "", "primary_table_endpoint": "https://stqboostdiststg9gn6ng.table.core.windows.net/", "primary_table_host": "stqboostdiststg9gn6ng.table.core.windows.net", "primary_table_microsoft_endpoint": "", "primary_table_microsoft_host": "", "primary_web_endpoint": "https://stqboostdiststg9gn6ng.z13.web.core.windows.net/", "primary_web_host": "stqboostdiststg9gn6ng.z13.web.core.windows.net", "primary_web_internet_endpoint": "", "primary_web_internet_host": "", "primary_web_microsoft_endpoint": "", "primary_web_microsoft_host": "", "provisioned_billing_model_version": "", "public_network_access_enabled": true, "queue_encryption_key_type": "Service", "queue_properties": [{"cors_rule": [], "hour_metrics": [{"enabled": false, "include_apis": false, "retention_policy_days": 0, "version": "1.0"}], "logging": [{"delete": false, "read": false, "retention_policy_days": 0, "version": "1.0", "write": false}], "minute_metrics": [{"enabled": false, "include_apis": false, "retention_policy_days": 0, "version": "1.0"}]}], "resource_group_name": "rg-quantboost-distribution-staging", "routing": [], "sas_policy": [], "secondary_access_key": "****************************************************************************************", "secondary_blob_connection_string": "", "secondary_blob_endpoint": "", "secondary_blob_host": "", "secondary_blob_internet_endpoint": "", "secondary_blob_internet_host": "", "secondary_blob_microsoft_endpoint": "", "secondary_blob_microsoft_host": "", "secondary_connection_string": "DefaultEndpointsProtocol=https;AccountName=stqboostdiststg9gn6ng;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "secondary_dfs_endpoint": "", "secondary_dfs_host": "", "secondary_dfs_internet_endpoint": "", "secondary_dfs_internet_host": "", "secondary_dfs_microsoft_endpoint": "", "secondary_dfs_microsoft_host": "", "secondary_file_endpoint": "", "secondary_file_host": "", "secondary_file_internet_endpoint": "", "secondary_file_internet_host": "", "secondary_file_microsoft_endpoint": "", "secondary_file_microsoft_host": "", "secondary_location": "", "secondary_queue_endpoint": "", "secondary_queue_host": "", "secondary_queue_microsoft_endpoint": "", "secondary_queue_microsoft_host": "", "secondary_table_endpoint": "", "secondary_table_host": "", "secondary_table_microsoft_endpoint": "", "secondary_table_microsoft_host": "", "secondary_web_endpoint": "", "secondary_web_host": "", "secondary_web_internet_endpoint": "", "secondary_web_internet_host": "", "secondary_web_microsoft_endpoint": "", "secondary_web_microsoft_host": "", "sftp_enabled": false, "share_properties": [{"cors_rule": [], "retention_policy": [{"days": 7}], "smb": []}], "shared_access_key_enabled": true, "static_website": [], "table_encryption_key_type": "Service", "tags": {}, "timeouts": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "primary_blob_connection_string"}], [{"type": "get_attr", "value": "secondary_blob_connection_string"}], [{"type": "get_attr", "value": "secondary_access_key"}], [{"type": "get_attr", "value": "primary_connection_string"}], [{"type": "get_attr", "value": "primary_access_key"}], [{"type": "get_attr", "value": "secondary_connection_string"}]], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["azurerm_resource_group.distribution", "random_string.suffix"]}]}, {"mode": "managed", "type": "azurerm_storage_container", "name": "releases", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 1, "attributes": {"container_access_type": "blob", "default_encryption_scope": "$account-encryption-key", "encryption_scope_override_enabled": true, "has_immutability_policy": false, "has_legal_hold": false, "id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-distribution-staging/providers/Microsoft.Storage/storageAccounts/stqboostdiststg9gn6ng/blobServices/default/containers/releases", "metadata": {}, "name": "releases", "resource_manager_id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-distribution-staging/providers/Microsoft.Storage/storageAccounts/stqboostdiststg9gn6ng/blobServices/default/containers/releases", "storage_account_id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-distribution-staging/providers/Microsoft.Storage/storageAccounts/stqboostdiststg9gn6ng", "storage_account_name": "", "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["azurerm_resource_group.distribution", "azurerm_storage_account.distribution", "random_string.suffix"]}]}, {"mode": "managed", "type": "azurerm_trusted_signing_account", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"account_uri": "https://eus.codesigning.azure.net/", "id": "/subscriptions/fd8c7249-5aa6-48e9-bc8d-31a58825682a/resourceGroups/rg-quantboost-distribution-staging/providers/Microsoft.CodeSigning/codeSigningAccounts/tsa-qb-stg-9gn6ng", "location": "eastus", "name": "tsa-qb-stg-9gn6ng", "resource_group_name": "rg-quantboost-distribution-staging", "sku_name": "Basic", "tags": {"Environment": "staging", "Purpose": "VSTO installer signing"}, "timeouts": null}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************", "dependencies": ["azurerm_resource_group.distribution", "random_string.suffix"]}]}, {"mode": "managed", "type": "random_string", "name": "suffix", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 2, "attributes": {"id": "9gn6ng", "keepers": null, "length": 6, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": null, "result": "9gn6ng", "special": false, "upper": false}, "sensitive_attributes": []}]}], "check_results": null}