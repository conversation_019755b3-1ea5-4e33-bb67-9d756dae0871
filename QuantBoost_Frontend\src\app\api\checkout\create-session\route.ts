import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

function signExchangePayload(email: string): string {
  const secret = process.env.EXCHANGE_SESSION_SECRET;
  if (!secret) throw new Error('Missing EXCHANGE_SESSION_SECRET');
  const exp = Math.floor(Date.now() / 1000) + 60 * 5; // 5 min expiry
  const payload = `${email.toLowerCase()}|${exp}`;
  const sig = crypto.createHmac('sha256', secret).update(payload).digest('base64url');
  return `${payload}|${sig}`; // format: email|exp|sig
}

export async function POST(req: NextRequest) {
  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: '2025-08-27.basil' as any });
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!,
    { auth: { autoRefreshToken: false, persistSession: false } }
  );

  try {
    const { priceId, userId, email, quantity = 1 } = await req.json();

    if (!priceId) {
      return NextResponse.json({ error: 'Missing priceId' }, { status: 400 });
    }

    let stripeCustomerId: string | undefined;

    if (userId && email) {
      const { data: profile } = await supabase
        .from('profiles')
        .select('stripe_customer_id')
        .eq('id', userId)
        .maybeSingle();

      if (profile?.stripe_customer_id) {
        stripeCustomerId = profile.stripe_customer_id;
      } else {
        const customer = await stripe.customers.create({
          email: email,
          metadata: { user_id: userId },
        });
        stripeCustomerId = customer.id;
        await supabase
          .from('profiles')
          .update({ stripe_customer_id: stripeCustomerId })
          .eq('id', userId);
      }
    }

    // Single product/price per frequency: support Individual (qty=1) and Team (qty>1) via quantity
    const initialQty = typeof quantity === 'number' && quantity > 0 ? quantity : 1;
    const lineItem: Stripe.Checkout.SessionCreateParams.LineItem = { 
      price: priceId, 
      quantity: initialQty,
      adjustable_quantity: { enabled: true, minimum: 1, maximum: 50 }
    };

    // Generate secure exchange code if email present
    let exchangeCode: string | undefined; let expectedEmailParam = '';
    if (email) {
      try {
        exchangeCode = signExchangePayload(email);
        expectedEmailParam = `&expected_email=${encodeURIComponent(email.toLowerCase())}&exchange_code=${encodeURIComponent(exchangeCode)}`;
      } catch (e) {
        console.error('Failed to sign exchange payload', e);
      }
    }

    const baseSuccess = `${process.env.NEXT_PUBLIC_BASE_URL}/dashboard?session_id={CHECKOUT_SESSION_ID}`;
    const success_url = exchangeCode ? `${baseSuccess}${expectedEmailParam}` : baseSuccess;

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription',
      client_reference_id: userId ?? undefined,
      customer: stripeCustomerId,
      metadata: { ...(email ? { email } : {}), ...(userId ? { user_id: userId } : {}) },
      line_items: [lineItem],
      success_url,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/pricing`,
    });

    return NextResponse.json({ sessionId: session.id, url: session.url, exchangeInjected: !!exchangeCode });
  } catch (error) {
    console.error('Error creating Stripe Checkout session:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}