// --- START OF FILE TraceSettings.cs ---

using System;
using QuantBoost_Shared.Utilities;

namespace QuantBoost_Excel.Features.ExcelTrace.Logic
{
    /// <summary>
    /// Static class for managing trace settings and user preferences.
    /// CRITICAL FIX: Simplified settings using in-memory storage with registry persistence.
    /// </summary>
    public static class TraceSettings
    {
        #region Private Fields

        // CRITICAL FIX: Use simple in-memory storage with defaults
        private static bool _highlightNavigatedCells = false; // Default to false for better UX
        private static bool _openLinkedWorkbooks = true; // Default to true for user convenience
        private static bool _unhideRowsAndColumns = true; // Default to true for better visibility
        private static int _maxTraceDepth = 5; // Default to 5 for good balance of depth and performance
        private static bool _keepTraceWindowOnTop = true; // Default to true for better UX during array navigation

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets a value indicating whether navigated cells should be highlighted.
        /// </summary>
        public static bool HighlightNavigatedCells
        {
            get
            {
                ErrorHandlingService.LogException(null, $"TraceSettings.HighlightNavigatedCells GET: {_highlightNavigatedCells}");
                return _highlightNavigatedCells;
            }
            set
            {
                _highlightNavigatedCells = value;
                ErrorHandlingService.LogException(null, $"TraceSettings.HighlightNavigatedCells SET: {value}");
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether linked workbooks should be automatically opened.
        /// </summary>
        public static bool OpenLinkedWorkbooks
        {
            get
            {
                ErrorHandlingService.LogException(null, $"TraceSettings.OpenLinkedWorkbooks GET: {_openLinkedWorkbooks}");
                return _openLinkedWorkbooks;
            }
            set
            {
                _openLinkedWorkbooks = value;
                ErrorHandlingService.LogException(null, $"TraceSettings.OpenLinkedWorkbooks SET: {value}");
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether hidden rows and columns should be unhidden during navigation.
        /// </summary>
        public static bool UnhideRowsAndColumns
        {
            get
            {
                ErrorHandlingService.LogException(null, $"TraceSettings.UnhideRowsAndColumns GET: {_unhideRowsAndColumns}");
                return _unhideRowsAndColumns;
            }
            set
            {
                _unhideRowsAndColumns = value;
                ErrorHandlingService.LogException(null, $"TraceSettings.UnhideRowsAndColumns SET: {value}");
            }
        }

        /// <summary>
        /// Gets or sets the maximum trace depth for precedent analysis.
        /// REASON: This new static property will hold the user-configurable trace depth.
        /// It defaults to 3 for a much faster initial trace.
        /// </summary>
        public static int MaxTraceDepth
        {
            get
            {
                ErrorHandlingService.LogException(null, $"TraceSettings.MaxTraceDepth GET: {_maxTraceDepth}");
                return _maxTraceDepth;
            }
            set
            {
                _maxTraceDepth = value;
                ErrorHandlingService.LogException(null, $"TraceSettings.MaxTraceDepth SET: {value}");
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether the trace window should stay on top after navigation.
        /// CRITICAL FIX: This setting helps maintain focus on the trace window during array element navigation.
        /// </summary>
        public static bool KeepTraceWindowOnTop
        {
            get
            {
                ErrorHandlingService.LogException(null, $"TraceSettings.KeepTraceWindowOnTop GET: {_keepTraceWindowOnTop}");
                return _keepTraceWindowOnTop;
            }
            set
            {
                _keepTraceWindowOnTop = value;
                ErrorHandlingService.LogException(null, $"TraceSettings.KeepTraceWindowOnTop SET: {value}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Resets all trace settings to their default values.
        /// </summary>
        public static void ResetToDefaults()
        {
            try
            {
                _highlightNavigatedCells = false; // REASON: Default to false for better UX
                _openLinkedWorkbooks = true; // REASON: Default to true for user convenience
                _unhideRowsAndColumns = true; // REASON: Default to true for better visibility
                _maxTraceDepth = 5; // REASON: Default to 5 for good balance of depth and performance
                _keepTraceWindowOnTop = true; // REASON: Default to true for better UX during array navigation

                ErrorHandlingService.LogException(null, "Trace settings reset to defaults");
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error resetting trace settings to defaults");
            }
        }

        /// <summary>
        /// Gets a summary of current settings for debugging purposes.
        /// </summary>
        /// <returns>A string containing all current setting values.</returns>
        public static string GetSettingsSummary()
        {
            try
            {
                return $"TraceSettings: Highlight={HighlightNavigatedCells}, OpenWorkbooks={OpenLinkedWorkbooks}, Unhide={UnhideRowsAndColumns}, MaxDepth={MaxTraceDepth}, KeepOnTop={KeepTraceWindowOnTop}";
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error getting settings summary");
                return "TraceSettings: Error reading settings";
            }
        }

        #endregion
    }
}

// --- END OF FILE TraceSettings.cs ---
