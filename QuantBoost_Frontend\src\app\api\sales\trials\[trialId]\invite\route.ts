import { NextRequest } from 'next/server';
import { getSupabaseAdmin, getAppBaseUrl } from '@/lib/supabaseServer';
import { bulkInviteSchema, extractEmailsFromCsv } from '@/lib/salesTrialsValidation';
import { emailService } from '@/lib/email';

export async function POST(req: NextRequest, ctx: { params: Promise<{ trialId: string }> }) {
  const body = await req.json();
  const parsed = bulkInviteSchema.safeParse(body);
  if (!parsed.success) return Response.json({ error: parsed.error.flatten() }, { status: 400 });

  const supa = getSupabaseAdmin();
  const { trialId } = await ctx.params;

  // Validate trial exists and active
  const { data: trial, error: tErr } = await supa
    .from('enterprise_trials')
    .select('*')
    .eq('id', trialId)
    .single();
  if (tErr) return Response.json({ error: 'Trial not found' }, { status: 404 });
  if (trial.status !== 'active') return Response.json({ error: 'Trial not active' }, { status: 400 });

  const emails = (
    parsed.data.emails && parsed.data.emails.length > 0
      ? parsed.data.emails
      : extractEmailsFromCsv(parsed.data.csv || '')
  ).slice(0, 100); // safety cap

  // Insert invites and fetch tokens
  const inserts = emails.map((email) => ({ trial_id: trialId, email }));
  const { data: created, error: insErr } = await supa
    .from('trial_invites')
    .insert(inserts)
    .select('id, email, invite_token');
  if (insErr) return Response.json({ error: insErr.message }, { status: 500 });

  // Send emails (best-effort; do not expose tokens in response)
  const base = getAppBaseUrl();
  const results = await Promise.all(
    (created || []).map(async (row: any) => {
      const url = `${base}/invite/${encodeURIComponent(row.invite_token)}`;
      const res = await emailService.sendTeamInvitation({
        targetEmail: row.email,
        activationUrl: url,
        teamName: 'QuantBoost Strategic Trial',
        expirationDays: 30,
      });
      return { id: row.id, email: row.email, sent: res.success };
    })
  );

  return Response.json({ count: emails.length, sent: results.filter(r => r.sent).length });
}
