using System;

namespace QuantBoost_Powerpoint_Addin.ExcelLink
{
    public enum ExcelLinkType
    {
        Chart,
        Range, // Represents a copied range, likely pasted as Image/Metafile
        OleObject, // For embedded workbooks/sheets
        Shape, // For linking specific Excel shapes (e.g., text boxes, drawing objects)
        Text, // For linking plain text content from cells
        Unknown
        // PrintArea // Deferred for now
    }

    /// <summary>
    /// Represents the metadata for a single linked Excel item (chart, range, OLE object) stored in Custom XML.
    /// </summary>
    public class ExcelLinkMetadata
    {
        public string Version { get; set; } = "1.1"; // Updated version for new structure
        public string Product { get; set; } = "excel-link";
        public string LinkId { get; set; } // Unique ID for this link
        public ExcelLinkType LinkType { get; set; } = ExcelLinkType.Unknown;

        // Source Info
        public string SourceFilePath { get; set; } // Full path to the source Excel file
        public string SourceFileHash { get; set; } // Hash of the source file for change detection
        public DateTime? SourceFileLastModifiedUtc { get; set; }
        public string WorksheetName { get; set; }
        public string SourceIdentifier { get; set; } // e.g., Chart Name, Range Address (A1:C10), or null for OLE

        // PowerPoint Info
        public string PowerPointShapeId { get; set; } // Name property of the shape in PowerPoint holding the content
        public int SlideIndex { get; set; } // 1-based index of the slide containing the shape

        // State & Settings
        public bool IsActive { get; set; } = true; // Is the link active for refresh?
        public bool PreserveFormatting { get; set; } = true;
        public DateTime? LastRefreshedUtc { get; set; }
        public string ModifiedBy { get; set; } // User who last modified/refreshed
        public bool FallbackUsedLastRefresh { get; set; } = false;
        public string ErrorState { get; set; }
        public string Notes { get; set; }

        /// <summary>
        /// Creates a shallow copy of the ExcelLinkMetadata object.
        /// </summary>
        public ExcelLinkMetadata Clone()
        {
            return (ExcelLinkMetadata)this.MemberwiseClone();
        }
    }
}
