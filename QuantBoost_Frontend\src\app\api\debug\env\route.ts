import { NextResponse } from 'next/server';

export async function GET() {
  // Check which environment variables are available
  const envCheck = {
    // Stripe variables
    STRIPE_SECRET_KEY: !!process.env.STRIPE_SECRET_KEY,
    STRIPE_WEBHOOK_SECRET: !!process.env.STRIPE_WEBHOOK_SECRET,
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: !!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
    
    // Supabase variables
    NEXT_PUBLIC_SUPABASE_URL: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    SUPABASE_SERVICE_KEY: !!process.env.SUPABASE_SERVICE_KEY,
    
    // Other variables
    NEXT_PUBLIC_AZURE_API_URL: !!process.env.NEXT_PUBLIC_AZURE_API_URL,
    NEXT_PUBLIC_BASE_URL: !!process.env.NEXT_PUBLIC_BASE_URL,
    
    // Show partial values for debugging (first 10 chars)
    stripe_secret_partial: process.env.STRIPE_SECRET_KEY?.substring(0, 10) || 'NOT_SET',
    supabase_url_partial: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 20) || 'NOT_SET',
    supabase_service_partial: process.env.SUPABASE_SERVICE_KEY?.substring(0, 10) || 'NOT_SET',
    
    // Environment info
    NODE_ENV: process.env.NODE_ENV,
    QUANTBOOST_ENV: process.env.QUANTBOOST_ENV,
    runtime: 'azure-static-web-apps',
    environment_check: {
      isDevelopment: process.env.NODE_ENV === 'development',
      isTest: process.env.NODE_ENV === 'test',
      isProduction: process.env.NODE_ENV === 'production',
      isQuantBoostStaging: process.env.QUANTBOOST_ENV === 'staging',
      isStagingByUrl: process.env.NEXT_PUBLIC_BASE_URL?.includes('staging'),
      webhookTestBypassEnabled: (process.env.NODE_ENV === 'development' || 
                                process.env.NODE_ENV === 'test' ||
                                process.env.QUANTBOOST_ENV === 'staging' ||
                                process.env.NEXT_PUBLIC_BASE_URL?.includes('staging'))
    },
    timestamp: new Date().toISOString()
  };

  return NextResponse.json(envCheck);
}
