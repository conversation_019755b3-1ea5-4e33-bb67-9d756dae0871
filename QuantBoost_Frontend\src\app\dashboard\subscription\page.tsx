"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';


import { useEffect, useState } from 'react';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { apiClient, Subscription, License, isApiSuccess, handleApiError } from '@/lib/api';
import Stripe from 'stripe';

interface SubscriptionWithLicenses extends Subscription {
  licenses: License[];
}

interface ChargeReceipt {
  id: string;
  stripe_charge_id: string;
  subscription_id: string;
  amount: number;
  currency: string;
  receipt_url: string;
  receipt_number: string;
  status: string;
  created_at: string;
}

export default function SubscriptionPage() {
  const [loading, setLoading] = useState(false);
  const [invoices, setInvoices] = useState<Stripe.Invoice[]>([]);
  const [receipts, setReceipts] = useState<ChargeReceipt[]>([]);
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithLicenses[]>([]);
  const [selectedSubscription, setSelectedSubscription] = useState<SubscriptionWithLicenses | null>(null);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabaseClient();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          setError('User not authenticated');
          return;
        }

        // Fetch subscriptions from Azure API
        const subscriptionsResponse = await apiClient.getUserSubscriptions();
        if (!isApiSuccess(subscriptionsResponse)) {
          const errorMsg = handleApiError(subscriptionsResponse, 'Failed to fetch subscriptions');
          setError(errorMsg);
          return;
        }

        const userSubscriptions = subscriptionsResponse.data;
        
        // Determine if this is a team or individual subscription setup
        const hasTeamSubscription = userSubscriptions.some(sub => sub.quantity > 1);
        
        // Fetch licenses for each subscription
        const subscriptionsWithLicenses: SubscriptionWithLicenses[] = [];
        
        if (hasTeamSubscription) {
          // Handle team subscriptions - fetch licenses for each subscription
          for (const subscription of userSubscriptions) {
            const licensesResponse = await apiClient.getTeamLicenses(subscription.id);
            const licenses = isApiSuccess(licensesResponse) ? licensesResponse.data : [];
            subscriptionsWithLicenses.push({
              ...subscription,
              licenses
            });
          }
        } else {
          // Handle individual subscriptions - get all user licenses and associate with first subscription
          const licensesResponse = await apiClient.getUserLicenses();
          const allUserLicenses = isApiSuccess(licensesResponse) ? licensesResponse.data : [];
          
          for (const subscription of userSubscriptions) {
            // For individual subscriptions, all licenses belong to the user regardless of subscription_id
            subscriptionsWithLicenses.push({
              ...subscription,
              licenses: allUserLicenses
            });
          }
        }

        setSubscriptions(subscriptionsWithLicenses);
        
        // Set the first active subscription as selected
        const activeSubscription = subscriptionsWithLicenses.find(sub => 
          sub.status === 'active' || sub.status === 'trialing'
        );
        if (activeSubscription) {
          setSelectedSubscription(activeSubscription);
        }

        // Fetch invoices using Stripe customer ID from profiles
        const { data: customerData } = await supabase
          .from('profiles')
          .select('stripe_customer_id')
          .eq('id', user.id)
          .single();

        if (customerData?.stripe_customer_id) {
          const invoicesResponse = await fetch('/api/billing/invoices', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ customerId: customerData.stripe_customer_id }),
          });
          
          if (invoicesResponse.ok) {
            const invoiceData = await invoicesResponse.json();
            setInvoices(invoiceData);
          }

          // Fetch receipts for the user
          const receiptsResponse = await fetch('/api/billing/receipts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId: user.id }),
          });
          
          if (receiptsResponse.ok) {
            const receiptData = await receiptsResponse.json();
            setReceipts(receiptData);
          }
        }

      } catch (error) {
        console.error('Error fetching subscription data:', error);
        setError(error instanceof Error ? error.message : 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [supabase]);

  const handleManageSubscription = async () => {
    if (!selectedSubscription) return;
    
    setLoading(true);
    try {
      // Use Azure API for billing portal session
      const response = await apiClient.createBillingPortalSession(window.location.href);
      if (isApiSuccess(response)) {
        window.location.href = response.data.url;
      } else {
        setError(handleApiError(response, 'Failed to create billing portal session'));
      }
    } catch (error) {
      console.error('Error managing subscription:', error);
      setError('Failed to redirect to billing portal');
    } finally {
      setLoading(false);
    }
  };

  const getLicenseStatusCounts = (licenses: License[]) => {
    return {
      active: licenses.filter(l => l.status === 'active').length,
      assigned: licenses.filter(l => l.email && l.email !== 'not-activated' && l.email !== '').length,
      unassigned: licenses.filter(l => !l.email || l.email === 'not-activated' || l.email === '').length,
    };
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Subscription</h3>
          <p className="text-sm text-muted-foreground">
            Manage your subscription and billing information.
          </p>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error Loading Subscription</CardTitle>
            <CardDescription className="text-red-600">{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => window.location.reload()} 
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Subscription</h3>
        <p className="text-sm text-muted-foreground">
          Manage your subscription and billing information.
        </p>
      </div>

      {/* Subscription Selection */}
      {subscriptions.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle>Select Subscription</CardTitle>
            <CardDescription>
              You have multiple subscriptions. Select one to manage.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {subscriptions.map((subscription) => (
                <div 
                  key={subscription.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedSubscription?.id === subscription.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedSubscription(subscription)}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{subscription.plan_id}</p>
                      <p className="text-sm text-muted-foreground">
                        {subscription.quantity} seat{subscription.quantity !== 1 ? 's' : ''}
                      </p>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      subscription.status === 'active' 
                        ? 'bg-green-100 text-green-800'
                        : subscription.status === 'trialing'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {subscription.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Plan */}
      <Card>
        <CardHeader>
          <CardTitle>Current Plan</CardTitle>
          <CardDescription>
            {selectedSubscription ? 
              `You are currently on the ${selectedSubscription.plan_id} plan.` : 
              'Loading your plan details...'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {selectedSubscription ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">Status</p>
                  <p className="text-sm text-muted-foreground">{selectedSubscription.status}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Seats</p>
                  <p className="text-sm text-muted-foreground">{selectedSubscription.quantity}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Current Period End</p>
                  <p className="text-sm text-muted-foreground">
                    {selectedSubscription.current_period_end && selectedSubscription.current_period_end !== 'null' ? 
                      new Date(Number(selectedSubscription.current_period_end) * 1000).toLocaleDateString() :
                      'Not set'
                    }
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Plan ID</p>
                  <p className="text-sm text-muted-foreground">{selectedSubscription.plan_id}</p>
                </div>
              </div>

              {/* License Usage */}
              {selectedSubscription.licenses.length > 0 && (
                <div>
                  <p className="text-sm font-medium mb-2">License Usage</p>
                  {(() => {
                    const counts = getLicenseStatusCounts(selectedSubscription.licenses);
                    return (
                      <div className="grid grid-cols-3 gap-2 text-sm">
                        <div className="text-center p-2 bg-green-50 rounded">
                          <p className="font-medium text-green-800">{counts.active}</p>
                          <p className="text-green-600">Active</p>
                        </div>
                        <div className="text-center p-2 bg-yellow-50 rounded">
                          <p className="font-medium text-yellow-800">{counts.assigned}</p>
                          <p className="text-yellow-600">Assigned</p>
                        </div>
                        <div className="text-center p-2 bg-gray-50 rounded">
                          <p className="font-medium text-gray-800">{counts.unassigned}</p>
                          <p className="text-gray-600">Available</p>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}
            </div>
          ) : (
            <p>No active subscription found.</p>
          )}
        </CardContent>
        <CardFooter>
          <Button 
            onClick={handleManageSubscription} 
            disabled={loading || !selectedSubscription}
          >
            {loading ? 'Redirecting...' : 'Manage Subscription'}
          </Button>
        </CardFooter>
      </Card>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>
            View your payment receipts and invoices.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {invoices.length === 0 && receipts.length === 0 ? (
            <p className="text-sm text-muted-foreground">No billing history found.</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Document</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {/* Show receipts for individual/team subscribers (under 20 licenses) */}
                {receipts.map((receipt) => (
                  <TableRow key={`receipt-${receipt.id}`}>
                    <TableCell>{new Date(receipt.created_at).toLocaleDateString()}</TableCell>
                    <TableCell>${(receipt.amount / 100).toFixed(2)}</TableCell>
                    <TableCell>
                      <span className="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800">
                        Paid
                      </span>
                    </TableCell>
                    <TableCell>
                      {receipt.receipt_url ? (
                        <a 
                          href={receipt.receipt_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline"
                        >
                          View Receipt
                        </a>
                      ) : (
                        <span className="text-gray-400">Not available</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
                
                {/* Show invoices only for enterprise subscribers (20+ licenses) */}
                {selectedSubscription && selectedSubscription.quantity >= 20 && invoices
                  .filter(invoice => invoice.status !== 'paid') // Only show unpaid invoices
                  .map((invoice) => (
                  <TableRow key={`invoice-${invoice.id}`}>
                    <TableCell>{new Date(invoice.created * 1000).toLocaleDateString()}</TableCell>
                    <TableCell>${((invoice.amount_paid || invoice.amount_due || 0) / 100).toFixed(2)}</TableCell>
                    <TableCell>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        invoice.status === 'open'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {invoice.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      {invoice.invoice_pdf ? (
                        <a 
                          href={invoice.invoice_pdf} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline"
                        >
                          View Invoice
                        </a>
                      ) : invoice.hosted_invoice_url ? (
                        <a 
                          href={invoice.hosted_invoice_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline"
                        >
                          View Invoice
                        </a>
                      ) : (
                        <span className="text-gray-400">Not available</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
