// --- START OF FILE LicenseDetailsClient.cs ---
// Located in: QuantBoost_Shared/UI/LicenseDetailsClient.cs
// Target: .NET Framework 4.8.1, C# 7.3

using System;

namespace QuantBoost_Shared.UI
{
    /// <summary>
    /// Defines the license status relevant to the User Interface.
    /// This decouples the UI from the specific states in the core Licensing SDK.
    /// </summary>
    public enum LicenseStatusClient
    {
        Unknown,        // Default or error state
        NotActivated,   // No valid key found or provided
        Active,         // Fully licensed and active
        Trial,          // Valid trial period active
        Expired,        // License or trial has expired
        Invalid,        // Key provided is invalid or not found
        GracePeriod,    // Expired but within grace period (optional, treat as Active?)
        NetworkError    // Could not validate due to network issue
        // Add other states if the UI needs to differentiate further
    }

    /// <summary>
    /// A simplified view model holding license details needed by the UI components.
    /// Adheres to C# 7.3 (no nullable reference type annotations).
    /// Populated by mapping from QuantBoost.Licensing.LicenseDetails.
    /// </summary>
    public class LicenseDetailsClient
    {
        /// <summary>
        /// The license key associated with this status (may be null if none found/validated).
        /// Perform null checks before use in UI.
        /// </summary>
        public string LicenseKey { get; set; }

        /// <summary>
        /// The email address associated with the license (may be null).
        /// Perform null checks before use in UI.
        /// </summary>
        public string Email { get; set; } // Changed from UserName

        /// <summary>
        /// The simplified status relevant to the UI.
        /// </summary>
        public LicenseStatusClient LastKnownStatus { get; set; } = LicenseStatusClient.Unknown;

        /// <summary>
        /// A simple flag indicating if the license currently permits feature usage
        /// (e.g., Active, Trial, potentially GracePeriod). Calculated during mapping.
        /// </summary>
        public bool IsSubscriptionEffectivelyActive { get; set; } // Renamed for clarity vs SDK IsValid

        /// <summary>
        /// The expiry date of the license or trial, if applicable.
        /// </summary>
        public DateTime? ExpiryDate { get; set; } // Maps from ExpiryDateUtc

        /// <summary>
        /// The license tier (e.g., "Pro", "Trial") (may be null).
        /// Perform null checks before use in UI.
        /// </summary>
        public string LicenseTier { get; set; } // Added

        /// <summary>
        /// Number of trial days remaining, if applicable.
        /// </summary>
        public int? TrialDaysLeft { get; set; } // Added

        /// <summary>
        /// A user-friendly message regarding the status (may be null).
        /// Perform null checks before use in UI.
        /// </summary>
        public string StatusMessage { get; set; } // Added (maps from ResultMessage)

        // Add UpgradeUrl, ManageUrl as string properties if the UI needs them
        public string ManageUrl { get; set; }
        public string UpgradeUrl { get; set; }
        public string ActivationId { get; set; }
        public string ProductId { get; set; }
        public DateTime? GracePeriodEndsUtc { get; set; }


        // Constructor (optional, can rely on property initializers)
        public LicenseDetailsClient()
        {
            // Default values are set via property initializers or are null/0/false by default
            LastKnownStatus = LicenseStatusClient.Unknown;
            IsSubscriptionEffectivelyActive = false;
        }
    }
}
// --- END OF FILE LicenseDetailsClient.cs ---