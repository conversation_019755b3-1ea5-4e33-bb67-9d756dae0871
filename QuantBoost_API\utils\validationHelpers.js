// Helper functions for validation

const crypto = require('crypto');

// Basic email validation
function isValidEmail(email) {
    if (!email || typeof email !== 'string') return false;
    // Simple, reliable email validation regex that matches most legitimate email formats
    // This is more lenient and consistent with .NET MailAddress validation used on client
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(String(email).toLowerCase());
}

// Generates a consistent device fingerprint from a machine ID
function getDeviceFingerprint(machineId) {
    if (!machineId || typeof machineId !== 'string') {
        // console.warn('[getDeviceFingerprint] Machine ID is missing or not a string. Returning a default fingerprint.');
        // Return a default or throw an error, depending on how critical this is.
        // For now, returning a generic fingerprint to avoid breaking flows that might expect a string.
        return 'unknown_device_fingerprint'; 
    }
    // Create a SHA256 hash of the machineId for a consistent fingerprint
    // Using a salt could be considered if you want to make it harder to reverse-engineer machine IDs from fingerprints,
    // but for simple consistency, direct hashing is fine.
    // const salt = process.env.DEVICE_FINGERPRINT_SALT || 'default-salt-for-fingerprint'; // Example salt
    // return crypto.createHash('sha256').update(machineId + salt).digest('hex');
    return crypto.createHash('sha256').update(machineId).digest('hex');
}

module.exports = {
    isValidEmail,
    getDeviceFingerprint
};
