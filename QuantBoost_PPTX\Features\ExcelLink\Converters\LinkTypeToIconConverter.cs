using System;
using System.Globalization;
using System.Windows.Data;

namespace QuantBoost_Powerpoint_Addin.Features.ExcelLink.Converters
{
    /// <summary>
    /// Converts link type strings to icon representations.
    /// </summary>
    public class LinkTypeToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string linkType)
            {
                return linkType switch
                {
                    "Embedded Chart" => "📊",
                    "Data Range" => "📋",
                    "Excel Shape" => "🔷",
                    "OLE Object" => "📎",
                    _ => "❓"
                };
            }
            return "❓";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}