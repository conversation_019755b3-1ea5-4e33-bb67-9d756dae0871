// filepath: c:\\VS projects\\QuantBoost\\QuantBoost.Licensing\\CustomExceptions.cs
using System;

namespace QuantBoost_Licensing
{
    public class AccessTokenExpiredException : Exception
    {
        public AccessTokenExpiredException() { }
        public AccessTokenExpiredException(string message) : base(message) { }
        public AccessTokenExpiredException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class AuthenticationRequiredException : Exception
    {
        public AuthenticationRequiredException() { }
        public AuthenticationRequiredException(string message) : base(message) { }
        public AuthenticationRequiredException(string message, Exception innerException) : base(message, innerException) { }
    }
}
