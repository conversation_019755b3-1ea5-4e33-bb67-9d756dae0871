'use client';

import { useState, useEffect, memo, useMemo, useCallback, useRef } from 'react';

import { loadStripe } from '@stripe/stripe-js';
import { Elements, PaymentElement, LinkAuthenticationElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { baseElementsOptions, sharedPaymentElementOptions, mapStripeError } from '@/lib/stripe/elementsConfig';
import { Button, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui';
import { Shield, Lock, CreditCard, User, Users } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
// Legacy validation imports removed.

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

// Robust email regex (requires at least two-letter TLD)
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/i;

// Product details - Individual and Team plans use same price IDs
// Note: Monthly price display assumes $15/month (can be updated easily if business price changes).
const PRODUCTS = {
  'price_1RC3HTE6FvhUKV1bE9D6zf6e': {
    name: 'Annual',
    price: '$120',
    period: 'per year',
    description: 'Full access, billed annually.',
    features: ['All Excel Modules', 'All PowerPoint Modules', 'Standard Support'],
    unitPrice: 120,
  },
  'price_1RyhsAE6FvhUKV1bImD5Ft34': {
    name: 'Monthly',
    price: '$15',
    period: 'per month',
    description: 'Full access, billed monthly.',
    features: ['All Excel Modules', 'All PowerPoint Modules', 'Standard Support'],
    unitPrice: 15,
  },
};

// Team vs Individual is derived solely from quantity (1 = Individual; >1 = Team)




// Email capture section (phase 1)
// Root cause note:
//   Logs show <NAME_EMAIL>. The Stripe LinkAuthenticationElement fires onChange with ev.complete
//   once it detects what it considers a syntactically valid email. Our handler immediately finalizes the email
//   (onComplete) without giving the user a chance to finish typing the common ".com" TLD. Because the regex allows
//   any 2+ char TLD, "gmail.co" is treated as complete and locked in early. We mitigate by (1) adding a short debounce
//   for completion and (2) applying targeted domain typo corrections (gmail.co -> gmail.com) before committing.
//   This preserves legitimate country domains while fixing the high-frequency accidental truncation.
// Enhanced email capture component that stays mounted throughout checkout so we can detect Stripe Link logout
// (which emits complete=false) and force user to re-enter an email before payment confirmation.
const EmailCaptureSection = memo(function EmailCaptureSection({
  onComplete,
  onIncomplete,
  initialEmail,
  locked,
  onRequestChange
}: {
  onComplete: (email: string) => void;
  onIncomplete: () => void;
  initialEmail?: string;
  locked: boolean;
  onRequestChange: () => void;
}) {
  // Debounce timer stable across renders
  const completionTimerRef = useRef<number | null>(null);

  const normalizeEmail = (email: string): string => {
    const lower = email.toLowerCase();
    // Target only very common accidental truncations / transpositions; keep list minimal to avoid false positives.
    const DOMAIN_CORRECTIONS: Record<string, string> = {
      'gmail.co': 'gmail.com',
      'gamil.com': 'gmail.com',
      'gnail.com': 'gmail.com',
      'gmai.com': 'gmail.com',
      'gmail.con': 'gmail.com'
    };
    const parts = lower.split('@');
    if (parts.length === 2) {
      const [local, domain] = parts;
      const correctedDomain = DOMAIN_CORRECTIONS[domain];
      if (correctedDomain) {
        return `${local}@${correctedDomain}`;
      }
    }
    return lower;
  };

  const handleChange = (event: any) => {
    if (locked) return; // Ignore Stripe Link events once locked
    const raw = event?.value?.email?.trim();
    // Clear any pending timer
    if (completionTimerRef.current) {
      window.clearTimeout(completionTimerRef.current);
      completionTimerRef.current = null;
    }
    if (!event?.complete) {
      // User cleared the field or clicked "log out of Link" -> treat as incomplete and force re-capture
      onIncomplete();
    }
    if (event?.complete && raw && EMAIL_REGEX.test(raw)) {
      // Debounce by 150ms to allow user to type final 'm' in common '.com' endings before locking in
    completionTimerRef.current = window.setTimeout(() => {
        const normalized = normalizeEmail(raw);
        onComplete(normalized);
      }, 150);
    }
  };

  useEffect(() => () => { if (completionTimerRef.current) window.clearTimeout(completionTimerRef.current); }, []);

  if (locked) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-gray-600" />
            <div>
              <div className="text-sm font-medium">{initialEmail}</div>
              <div className="text-[11px] text-gray-500">Account email locked for this purchase</div>
            </div>
          </div>
          <button type="button" onClick={onRequestChange} className="text-xs text-blue-600 hover:underline">Change</button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
        <User className="h-4 w-4" />
        Enter your email to create your secure account
      </div>
  <div className="p-4 border rounded-lg bg-gray-50/50" data-testid="qb-email-capture">
        <LinkAuthenticationElement
          onChange={handleChange}
          options={{ defaultValues: { email: initialEmail || '' } }}
        />
      </div>
      <p className="text-xs text-gray-500">We'll use this email for your account, billing, and secure access.</p>
    </div>
  );
});

interface CheckoutFormProps {
  priceId: string;
  product: typeof PRODUCTS[keyof typeof PRODUCTS];
  initialQuantity?: number;
  onQuantityChange?: (quantity: number) => void;
}

interface CheckoutFormInnerProps extends CheckoutFormProps {
  initialQuantity?: number;
  email: string;
  onQuantityChange?: (quantity: number) => void;
}

// Legacy EmailCollectionStepProps removed

// Helper function to extract numeric price from string format
const extractNumericPrice = (priceString: string): number => {
  const numericValue = priceString.replace(/[^0-9.]/g, '');
  return parseFloat(numericValue) || 0;
};

// Helper function to calculate total price for display
const calculateDisplayPrice = (product: typeof PRODUCTS[keyof typeof PRODUCTS], quantity: number, isTeamPlan: boolean): string => {
  const unitPrice = product.unitPrice;
  if (isTeamPlan && quantity > 1) {
    const totalPrice = unitPrice * quantity;
    return `$${totalPrice}`;
  }
  return product.price;
};

// Helper function to get dynamic product information based on team vs individual
const getDynamicProductInfo = (baseProduct: typeof PRODUCTS[keyof typeof PRODUCTS], quantity: number, isTeamPlan: boolean) => {
  const planType = isTeamPlan ? 'Team' : 'Individual';
  const displayPrice = calculateDisplayPrice(baseProduct, quantity, isTeamPlan);
  
  // Create features array with appropriate support level
  const baseFeatures = baseProduct.features.filter(feature => feature !== 'Standard Support');
  const features = isTeamPlan 
    ? [...baseFeatures, 'Team License Management', 'Priority Support']
    : [...baseFeatures, 'Standard Support'];
  
  return {
    name: `${planType} ${baseProduct.name}`,
    price: displayPrice,
    period: baseProduct.period, // Keep the original period format
    description: isTeamPlan 
      ? `Full access for ${quantity} user${quantity > 1 ? 's' : ''}, billed ${baseProduct.name.toLowerCase()}.`
      : baseProduct.description,
    features: features,
    unitPrice: baseProduct.unitPrice,
    quantity: quantity,
    totalPrice: baseProduct.unitPrice * quantity
  };
};

// (Legacy email collection JSX removed)

// New two-stage checkout form with setup + final client secrets
function CheckoutForm({ priceId, product, initialQuantity = 1, onQuantityChange }: CheckoutFormProps) {
  const [email, setEmail] = useState('');
  const [emailComplete, setEmailComplete] = useState(false);
  const [emailLocked, setEmailLocked] = useState(false); // lock after intent created
  const [emailChangeRequested, setEmailChangeRequested] = useState(false);
  const [setupClientSecret, setSetupClientSecret] = useState<string | null>(null);
  const [finalClientSecret, setFinalClientSecret] = useState<string | null>(null);
  const [lastIntentEmail, setLastIntentEmail] = useState<string | null>(null); // Which email the current payment intent was created for
  const [isLoading, setIsLoading] = useState(false); // final intent creation
  const [error, setError] = useState<string | null>(null);
  const [teamSize, setTeamSize] = useState(initialQuantity);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  const [marketingOptIn, setMarketingOptIn] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [firstTouched, setFirstTouched] = useState(false);
  const [lastTouched, setLastTouched] = useState(false);
  const router = useRouter();

  const isTeamPlan = teamSize > 1;
  const dynamicProduct = getDynamicProductInfo(product, teamSize, isTeamPlan);
  const displayPrice = dynamicProduct.price;

  // Fetch setup intent on mount
  useEffect(() => {
    const fetchSetup = async () => {
      if (setupClientSecret) return;
      try {
        const res = await fetch('/api/checkout/create-setup-intent', { method: 'POST' });
        if (!res.ok) throw new Error('Failed to initialize checkout.');
        const data = await res.json();
        setSetupClientSecret(data.clientSecret);
      } catch (e: any) {
        setError(e.message || 'Failed to initialize checkout');
      }
    };
    fetchSetup();
  }, [setupClientSecret]);

  // Create (or recreate) payment intent when we have a confirmed email and either no intent yet
  // or the email has changed since last intent creation.
  useEffect(() => {
    const createFinal = async () => {
      if (!emailComplete || !email || !EMAIL_REGEX.test(email)) return;
      if (emailChangeRequested) return; // don't create while editing
      if (isLoading) return;
      if (finalClientSecret && lastIntentEmail === email) return; // Already have a PI for this email
      console.log('[checkout-email] creating payment intent for', email);
      setIsLoading(true);
      setError(null);
      
      let retryCount = 0;
      const maxRetries = 3; // Limit total retries to prevent infinite loops
      
      try {
        const poll = async (): Promise<void> => {
          const res = await fetch('/api/checkout/create-payment-intent', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ priceId, quantity: teamSize, email, customerInfo: { firstName, lastName } })
          });
          
          if (res.status === 202) {
            // Pending – implement bounded retry with incremental backoff
            const data = await res.json();
            retryCount++;
            
            // If server has exhausted its attempts or we've retried too many times, fail
            if (retryCount >= maxRetries || (data.attempts && data.attempts >= data.maxAttempts)) {
              throw new Error('Payment initialization is taking longer than expected. Please refresh the page and try again.');
            }
            
            // Calculate backoff delay
            const delay = Math.min(1000 + retryCount * 1000, 5000); // 1s, 2s, 3s, 4s, 5s max
            console.log(`[checkout-email] payment intent pending; retry ${retryCount}/${maxRetries} in ${delay}ms`);
            await new Promise(r => setTimeout(r, delay));
            return poll();
          }
          
          if (!res.ok) {
            const txt = await res.text();
            throw new Error(txt || 'Failed to initialize payment');
          }
          
          const data = await res.json();
          if (!data.clientSecret) {
            throw new Error('Invalid response from payment service');
          }
          
          setFinalClientSecret(data.clientSecret);
          setLastIntentEmail(email);
          setEmailLocked(true);
          console.log('[checkout-email] payment intent ready; email locked');
        };
        
        await poll();
      } catch (e: any) {
        console.error('[checkout-email] error creating payment intent:', e);
        setError(e.message || 'Failed to initialize payment');
        setIsLoading(false); // Reset loading state on error
      } finally {
        setIsLoading(false);
      }
    };
    createFinal();
  }, [emailComplete, email, finalClientSecret, isLoading, priceId, teamSize, lastIntentEmail, emailChangeRequested, firstName, lastName]);

  const handleTeamSizeChange = (n: number) => {
    if (finalClientSecret) return; // lock when final secret obtained
    setTeamSize(n);
    onQuantityChange?.(n);
  };

  const clientSecret = finalClientSecret || setupClientSecret;
  if (!clientSecret) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
        Loading checkout…
      </div>
    );
  }

  return (
    <Elements stripe={stripePromise} options={baseElementsOptions(clientSecret)} key={clientSecret}>
      <div className="space-y-6">
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md text-sm text-red-700">
            <div className="flex justify-between items-start gap-4">
              <span>{error}</span>
              <button onClick={() => setError('')} className="text-red-600 hover:underline text-xs">Dismiss</button>
            </div>
          </div>
        )}

        <EmailCaptureSection
          initialEmail={email}
          locked={emailLocked && !emailChangeRequested}
          onRequestChange={() => {
            console.log('[checkout-email] change email requested');
            setEmailLocked(false);
            setEmailChangeRequested(true);
            setFinalClientSecret(null); // discard existing intent
          }}
          onComplete={(em) => {
            console.log('[checkout-email] email complete', em);
            setEmail(em);
            setEmailComplete(true);
            setEmailChangeRequested(false);
            if (finalClientSecret && lastIntentEmail && lastIntentEmail !== em) {
              console.log('[checkout-email] resetting intent due to different email');
              setFinalClientSecret(null);
            }
          }}
          onIncomplete={() => {
            if (emailLocked && !emailChangeRequested) {
              // Ignore transient incomplete events once locked
              console.log('[checkout-email] transient incomplete ignored (locked)');
              return;
            }
            console.log('[checkout-email] email incomplete; clearing state');
            if (emailComplete) {
              setEmailComplete(false);
            }
            if (email) setEmail('');
            if (finalClientSecret) {
              setFinalClientSecret(null);
            }
          }}
        />

  {emailComplete && !finalClientSecret && !emailChangeRequested && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            Initializing secure payment…
          </div>
        )}

        {!finalClientSecret && isTeamPlan && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
              <Users className="h-4 w-4" />
              Team Size
            </div>
            <div className="flex items-center gap-3">
              <input
                type="range"
                min={1}
                max={50}
                value={teamSize}
                disabled={!!finalClientSecret}
                onChange={(e) => handleTeamSizeChange(parseInt(e.target.value) || 1)}
                className="flex-1"
              />
              <span className="text-sm w-10 text-right">{teamSize}</span>
            </div>
          </div>
        )}

        {/* Minimal name collection to ensure Stripe receives cardholder name for receipts/customer */}
        {finalClientSecret && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div>
              <label className="block text-xs text-gray-600 mb-1">First name</label>
              <input
                type="text"
                className={`w-full border rounded px-3 py-2 text-sm ${firstTouched && !firstName.trim() ? 'border-red-500' : ''}`}
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                onBlur={() => setFirstTouched(true)}
                placeholder="Jane"
                data-testid="qb-first-name"
                aria-invalid={firstTouched && !firstName.trim()}
                aria-describedby="qb-first-name-help"
              />
              {firstTouched && !firstName.trim() && (
                <p id="qb-first-name-help" className="mt-1 text-xs text-red-600">Please enter your first name.</p>
              )}
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">Last name</label>
              <input
                type="text"
                className={`w-full border rounded px-3 py-2 text-sm ${lastTouched && !lastName.trim() ? 'border-red-500' : ''}`}
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                onBlur={() => setLastTouched(true)}
                placeholder="Doe"
                data-testid="qb-last-name"
                aria-invalid={lastTouched && !lastName.trim()}
                aria-describedby="qb-last-name-help"
              />
              {lastTouched && !lastName.trim() && (
                <p id="qb-last-name-help" className="mt-1 text-xs text-red-600">Please enter your last name.</p>
              )}
            </div>
          </div>
        )}

  {finalClientSecret && emailComplete && !emailChangeRequested && (
            <CheckoutPaymentForm
              displayPrice={displayPrice}
              termsAccepted={termsAccepted}
              setTermsAccepted={setTermsAccepted}
              privacyAccepted={privacyAccepted}
              setPrivacyAccepted={setPrivacyAccepted}
              marketingOptIn={marketingOptIn}
              setMarketingOptIn={setMarketingOptIn}
              isSubmitting={isSubmitting}
              setIsSubmitting={setIsSubmitting}
              email={email}
              emailComplete={emailComplete}
              router={router}
              setError={setError}
              finalClientSecret={!!finalClientSecret}
              firstName={firstName}
              lastName={lastName}
            />
        )}
      </div>
    </Elements>
  );
}

function CheckoutPaymentForm({ displayPrice, termsAccepted, setTermsAccepted, privacyAccepted, setPrivacyAccepted, marketingOptIn, setMarketingOptIn, isSubmitting, setIsSubmitting, email, router, setError, emailComplete, finalClientSecret, firstName, lastName }: {
  displayPrice: string;
  termsAccepted: boolean;
  setTermsAccepted: (v: boolean) => void;
  privacyAccepted: boolean;
  setPrivacyAccepted: (v: boolean) => void;
  marketingOptIn: boolean;
  setMarketingOptIn: (v: boolean) => void;
  isSubmitting: boolean;
  setIsSubmitting: (v: boolean) => void;
  email: string;
  emailComplete: boolean;
  router: any;
  setError: (msg: string) => void;
  finalClientSecret: boolean;
  firstName: string;
  lastName: string;
}) {
  const stripe = useStripe();
  const elements = useElements();

  // Test readiness hook for Playwright: signal when Payment Element is mounted & elements object ready
  useEffect(() => {
    if (elements && !(window as any).__QB_PAYMENT_ELEMENT_READY) {
      (window as any).__QB_PAYMENT_ELEMENT_READY = true;
      // Diagnostic console log intentionally simple for test scraping
      // eslint-disable-next-line no-console
      console.log('[qb-test] payment element ready');
    }
  }, [elements]);

  // Expose lightweight diagnostic state for E2E tests
  useEffect(() => {
    (window as any).__QB_CHECKOUT_STATE = {
      emailComplete,
      finalClientSecret,
      termsAccepted,
      privacyAccepted,
      isSubmitting
    };
  }, [emailComplete, finalClientSecret, termsAccepted, privacyAccepted, isSubmitting]);

  const confirm = useCallback(async () => {
    if (!stripe || !elements) return;
    // Defensive: ensure valid email before confirming
    if (!EMAIL_REGEX.test(email)) {
      setError('Please enter a valid email address (e.g. <EMAIL>).');
      return;
    }
    const fn = (firstName || '').trim();
    const ln = (lastName || '').trim();
    if (!fn || !ln) {
      setError('Please enter your first and last name.');
      return;
    }
    setIsSubmitting(true);
    setError('');
    try {
      const fullName = `${fn} ${ln}`; // guaranteed non-empty due to check above
      const { error: confirmError, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          // Always route through payment-success page which handles robust session establishment
          return_url: `${window.location.origin}/auth/payment-success?email=${encodeURIComponent(email)}`,
          payment_method_data: {
            billing_details: {
              email: email,
              name: fullName
            }
          }
        },
        redirect: 'if_required'
      });
      if (confirmError) {
        const stripeCode = (confirmError as any)?.code;
        setError(mapStripeError(stripeCode, confirmError.message));
      } else if (paymentIntent?.status === 'succeeded') {
        // If Stripe does not redirect (e.g., card did not require additional auth), manually redirect to payment-success
        router.push(`/auth/payment-success?payment=success&email=${encodeURIComponent(email)}&payment_intent=${paymentIntent.id}`);
      }
    } catch (e: any) {
      setError(e.message || 'Payment failed');
    } finally {
      setIsSubmitting(false);
    }
  }, [stripe, elements, router, email, firstName, lastName, setError, setIsSubmitting]);

  return (
    <div className="space-y-4" data-testid="qb-payment-stage">
      <div className="p-4 border rounded-lg bg-gray-50/50" data-testid="qb-payment-element">
        <PaymentElement
          options={sharedPaymentElementOptions}
        />
      </div>
      <div className="space-y-3">
        <label className="flex items-start gap-2 text-xs text-gray-600" data-testid="qb-terms-wrapper">
          <input data-testid="qb-terms" type="checkbox" className="mt-0.5" checked={termsAccepted} onChange={(e) => setTermsAccepted(e.target.checked)} />
          <span>I agree to the <a href="/terms" target="_blank" className="underline">Terms of Service</a></span>
        </label>
        <label className="flex items-start gap-2 text-xs text-gray-600" data-testid="qb-privacy-wrapper">
          <input data-testid="qb-privacy" type="checkbox" className="mt-0.5" checked={privacyAccepted} onChange={(e) => setPrivacyAccepted(e.target.checked)} />
          <span>I agree to the <a href="/privacy" target="_blank" className="underline">Privacy Policy</a></span>
        </label>
        <label className="flex items-start gap-2 text-xs text-gray-600">
          <input data-testid="qb-marketing" type="checkbox" className="mt-0.5" checked={marketingOptIn} onChange={(e) => setMarketingOptIn(e.target.checked)} />
          <span>Send me occasional product updates (optional)</span>
        </label>
      </div>
      <Button
        type="button"
        onClick={confirm}
        data-testid="qb-complete-button"
        disabled={!stripe || !elements || isSubmitting || !termsAccepted || !privacyAccepted || !emailComplete || !EMAIL_REGEX.test(email) || !(firstName?.trim()) || !(lastName?.trim())}
        className="w-full h-12 text-base font-semibold bg-black hover:bg-gray-800 text-white"
      >
        {isSubmitting ? 'Processing…' : `Complete Purchase - ${displayPrice}`}
      </Button>
      <div className="flex items-center justify-center gap-2 text-xs text-gray-500 mt-2">
        <Shield className="h-3 w-3" />
        <span>Secured by Stripe • SSL Encrypted</span>
      </div>
    </div>
  );
}

export default function CheckoutPage() {
  // Use the optimized checkout page with React Hook Form
  return <CheckoutPageOriginal />;
}

function CheckoutPageOriginal() {
  const params = useParams();
  const searchParams = useSearchParams();
  const priceId = params.priceId as string;
  const quantityParam = searchParams.get('quantity');
  const initialQuantity = quantityParam ? parseInt(quantityParam, 10) : 1;
  
  // State to track current quantity for dynamic updates
  const [currentQuantity, setCurrentQuantity] = useState(initialQuantity);

  const baseProduct = PRODUCTS[priceId as keyof typeof PRODUCTS];
  const isTeamPlan = currentQuantity > 1; // Use current quantity instead of initial
  
  // Get dynamic product information based on team vs individual and current quantity
  const product = baseProduct ? getDynamicProductInfo(baseProduct, currentQuantity, isTeamPlan) : null;

  if (!baseProduct || !product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invalid Product</CardTitle>
            <CardDescription>
              The requested product could not be found.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link href="/pricing">Back to Pricing</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="px-4 lg:px-6 h-20 flex items-center border-b bg-white/70 backdrop-blur-sm">
        <div className="flex items-center gap-6 w-full">
          <Link href="/" className="flex items-center justify-center">
            <Image src="/QuantBoost_LogoOnly_v0.png" alt="QuantBoost Logo" width={40} height={40} />
            <span className="ml-2 text-xl font-semibold">QuantBoost</span>
          </Link>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Complete Your Purchase</h1>
          <nav className="ml-auto flex items-center gap-2">
            <Button variant="ghost" asChild>
              <Link href="/pricing">← Back to Pricing</Link>
            </Button>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-4 sm:py-6 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8 items-start">
          {/* Product Summary - Sticky Sidebar */}
          <div className="lg:col-span-2 order-2 lg:order-1">
            <div className="lg:sticky lg:top-8">
              {/* Heading moved to global header */}

              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                      <span className="text-primary-foreground font-bold text-sm">QB</span>
                    </div>
                    {product.name}
                  </CardTitle>
                  <CardDescription>{product.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="text-center p-4 bg-muted/50 rounded-lg">
                      <div className="text-4xl font-bold text-primary">{product.price}</div>
                      <div className="text-sm text-muted-foreground">{product.period}</div>
                      {isTeamPlan && currentQuantity > 1 && (
                        <div className="text-xs text-muted-foreground mt-1">
                          ${baseProduct.unitPrice} × {currentQuantity} users
                        </div>
                      )}
                    </div>

                    <div>
                      <h3 className="font-semibold mb-3 flex items-center gap-2">
                        <Shield className="h-4 w-4 text-primary" />
                        Included Features:
                      </h3>
                      <ul className="space-y-2">
                        {product.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-3 text-sm">
                            <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0"></div>
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="border-t pt-4">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <Lock className="h-4 w-4" />
                        <span>30-day money-back guarantee</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <Shield className="h-4 w-4" />
                        <span>Cancel anytime</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <CreditCard className="h-4 w-4" />
                        <span>Secure payment processing</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Shield className="h-4 w-4" />
                        <span>Stripe Secure</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Trust Indicators */}
              <div className="text-center space-y-3">
                <div className="flex items-center justify-center gap-3 text-xs text-muted-foreground flex-wrap">
                  <div className="flex items-center gap-1">
                    <Shield className="h-3 w-3" />
                    <span>256-bit SSL</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Lock className="h-3 w-3" />
                    <span>PCI DSS</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <CreditCard className="h-3 w-3" />
                    <span>Stripe Secure</span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Trusted by thousands of professionals worldwide
                </p>
              </div>
            </div>
          </div>

          {/* Checkout Form */}
          <div className="lg:col-span-3 order-1 lg:order-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl sm:text-2xl">Secure Checkout</CardTitle>
                <CardDescription>
                  Complete your information below to finalize your purchase
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CheckoutForm 
                  priceId={priceId} 
                  product={baseProduct} 
                  initialQuantity={initialQuantity}
                  onQuantityChange={setCurrentQuantity}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}