const request = require('supertest');
const { app, serverInstance } = require('../index');
const { supabase, supabaseAdmin } = require('../supabaseClient'); // Main Supabase client for DB checks
const {
    supabase_test_client, // Admin client for setup
    validLicenseKey,
    validProductId,
    generateTestEmail, // For creating unique user emails
    createTestUserAndSession, // Helper for user creation and session retrieval
} = require('./test-setup');

describe('POST /v1/licenses/activate (Key-Based)', () => {
    beforeEach(async () => {
        if (!supabaseAdmin) {
            console.error("Supabase admin client not available for beforeEach in /v1/licenses/activate tests.");
            return;
        }
        // Clean up any existing test licenses and their activations
        // First get the license ID if it exists
        const { data: existingLicense } = await supabaseAdmin
            .from('licenses')
            .select('id')
            .eq('license_key', validLicenseKey)
            .maybeSingle();
        
        if (existingLicense) {
            await supabaseAdmin.from('license_activations').delete().eq('license_id', existingLicense.id);
        }
        const { error: deleteError } = await supabaseAdmin
            .from('licenses')
            .delete()
            .match({ license_key: validLicenseKey });
        if (deleteError && deleteError.code !== 'PGRST116') { // PGRST116: No rows found
            console.error('Error deleting license in beforeEach for activate tests:', deleteError);
        }
    });

    it('should activate a new license with a valid product ID', async () => {
        const apiKey = process.env.MASTER_API_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
        if (!supabaseAdmin || !apiKey) {
            console.warn('[Test Skip] Supabase admin client or API_KEY not available for license activation test.');
            return;
        }
        const response = await request(app)
            .post('/v1/licenses/activate')
            .set('x-api-key', apiKey)
            .send({ licenseKey: validLicenseKey, productId: validProductId });
        expect(response.status).toBe(201);
        expect(response.body.data).toHaveProperty('message', 'License activated successfully');
        expect(response.body.data).toHaveProperty('license_key', validLicenseKey);
        const { data, error } = await supabaseAdmin.from('licenses').select('*').eq('license_key', validLicenseKey).single();
        expect(error).toBeNull();
        expect(data).toBeDefined();
        expect(data.product_id).toBe(validProductId);
        expect(data.status).toBe('active');
    });

    it('should return 400 if license key is missing', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }        const response = await request(app)
            .post('/v1/licenses/activate')
            .set('x-api-key', apiKey)
            .send({ productId: validProductId });
        expect(response.status).toBe(400);
        expect(response.body.error.message).toEqual('License key and product ID are required');
    });

    it('should return 400 if product ID is missing', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }        const response = await request(app)
            .post('/v1/licenses/activate')
            .set('x-api-key', apiKey)
            .send({ licenseKey: validLicenseKey });
        expect(response.status).toBe(400);
        expect(response.body.error.message).toEqual('License key and product ID are required');
    });

    it('should return 409 if license key already exists and is active', async () => {
        const apiKey = process.env.MASTER_API_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
        if (!supabaseAdmin || !apiKey) { throw new Error("Supabase admin client or API_KEY not available for test"); }
        
        // First, ensure clean state
        await supabaseAdmin.from('licenses').delete().match({ license_key: validLicenseKey });
        
        // Create an active license directly to ensure the check happens
        await supabaseAdmin.from('licenses').insert([{
            license_key: validLicenseKey,
            product_id: validProductId,
            status: 'active',
            user_id: null
        }]);
        
        const response = await request(app) // Attempt to activate again
            .post('/v1/licenses/activate')
            .set('x-api-key', apiKey)
            .send({ licenseKey: validLicenseKey, productId: validProductId });
        expect(response.status).toBe(409);
        expect(response.body.error.message).toBe('License key already exists and is active');
    });

    it('should reactivate an existing, inactive license with the same product ID', async () => {
        if (!supabaseAdmin) { throw new Error("Supabase admin client not available for test"); }
        
        // First, ensure clean state
        await supabaseAdmin.from('licenses').delete().match({ license_key: validLicenseKey });
        
        // Create an inactive license
        const { error: insertError } = await supabaseAdmin
            .from('licenses')
            .insert([{ license_key: validLicenseKey, product_id: validProductId, status: 'inactive', user_id: null }]);
        if (insertError) { throw insertError; }
        
        const response = await request(app)
            .post('/v1/licenses/activate')
            .set('x-api-key', process.env.SUPABASE_KEY)
            .send({ licenseKey: validLicenseKey, productId: validProductId });
        expect(response.status).toBe(200);
        expect(response.body.data).toHaveProperty('message', 'License reactivated successfully');
        const { data, error: selectError } = await supabaseAdmin.from('licenses').select('*').eq('license_key', validLicenseKey).single();
        expect(selectError).toBeNull();
        expect(data.status).toBe('active');
        expect(data.product_id).toBe(validProductId);
    });

    it('should return 409 if trying to reactivate an existing, inactive license with a DIFFERENT product ID', async () => {
        if (!supabaseAdmin) { throw new Error("Supabase admin client not available for test"); }
        
        // First, ensure clean state
        await supabaseAdmin.from('licenses').delete().match({ license_key: validLicenseKey });
        
        const originalProductId = 'prod_original';
        const { error: insertError } = await supabaseAdmin
            .from('licenses')
            .insert([{ license_key: validLicenseKey, product_id: originalProductId, status: 'inactive', user_id: null }]);
        if (insertError) { throw insertError; }
        const differentProductId = 'prod_different';
        
        const response = await request(app)
            .post('/v1/licenses/activate')
            .set('x-api-key', process.env.SUPABASE_KEY)
            .send({ licenseKey: validLicenseKey, productId: differentProductId });
        expect(response.status).toBe(409);
        expect(response.body.error.message).toBe('License key exists with a different product ID. Cannot reactivate with a new product ID.');
        const { data, error: selectError } = await supabaseAdmin.from('licenses').select('*').eq('license_key', validLicenseKey).single();
        expect(selectError).toBeNull();
        expect(data.status).toBe('inactive');
        expect(data.product_id).toBe(originalProductId);
    });
});

describe('POST /v1/licenses/validate (Key-Based)', () => {
    const inactiveKeyForValidation = 'test-key-inactive-for-validation';
    beforeAll(async () => {
        const apiKey = process.env.MASTER_API_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
        if (!supabaseAdmin || !apiKey) {
            console.error("Supabase admin client or API_KEY not available for beforeAll in /v1/licenses/validate.");
            return;
        }
        await supabaseAdmin.from('licenses').delete().match({ license_key: validLicenseKey });
        const { error } = await supabaseAdmin
            .from('licenses')
            .insert([{ license_key: validLicenseKey, product_id: validProductId, status: 'active', user_id: null, email: '<EMAIL>' }]);
        if (error) { throw new Error(`Failed to set up active license ${validLicenseKey}: ${error.message}`); }
        
        await supabaseAdmin.from('licenses').delete().match({ license_key: inactiveKeyForValidation });
        const { error: inactiveError } = await supabaseAdmin
            .from('licenses')
            .insert([{ license_key: inactiveKeyForValidation, product_id: validProductId, status: 'inactive', user_id: null, email: '<EMAIL>' }]);
        if (inactiveError) { console.error('Error inserting inactive license for validation tests:', inactiveError); }
    });

    it('should validate an active license with correct product ID', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }        const response = await request(app)            .post('/v1/licenses/validate')
            .set('x-api-key', apiKey)
            .send({ licenseKey: validLicenseKey, productId: validProductId, machineId: 'test-machine-1' });
        expect(response.status).toBe(200);
        expect(response.body.data).toHaveProperty('message', 'License status (key): active_perpetual');
    });
    
    it('should return 400 if license key is missing', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }        const response = await request(app)
            .post('/v1/licenses/validate')
            .set('x-api-key', apiKey)
            .send({ productId: validProductId, machineId: 'test-machine-1' });
        expect(response.status).toBe(400);
        expect(response.body.error.message).toBe('License key, product ID, and machine ID are required');
    });

    it('should return 400 if product ID is missing', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }        const response = await request(app)
            .post('/v1/licenses/validate')
            .set('x-api-key', apiKey)
            .send({ licenseKey: validLicenseKey, machineId: 'test-machine-1' });
        expect(response.status).toBe(400);
        expect(response.body.error.message).toBe('License key, product ID, and machine ID are required');
    });

    it('should return 400 if machine ID is missing', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }        const response = await request(app)
            .post('/v1/licenses/validate')
            .set('x-api-key', apiKey)
            .send({ licenseKey: validLicenseKey, productId: validProductId });
        expect(response.status).toBe(400);
        expect(response.body.error.message).toBe('License key, product ID, and machine ID are required');
    });

    it('should return 404 if license key does not exist', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }        const response = await request(app)
            .post('/v1/licenses/validate')
            .set('x-api-key', apiKey)
            .send({ licenseKey: 'non-existent-key', productId: validProductId, machineId: 'test-machine-1' });
        expect(response.status).toBe(404);
        expect(response.body.error.message).toBe('License key not found or product ID mismatch.');
    });

    it('should return 403 if license is inactive', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }        const response = await request(app)
            .post('/v1/licenses/validate')
            .set('x-api-key', apiKey)
            .send({ licenseKey: inactiveKeyForValidation, productId: validProductId, machineId: 'test-machine-1' });
        expect(response.status).toBe(403);
        expect(response.body.error.message).toBe('License is not active. Status: inactive');
    });

    it('should return 403 if product ID does not match', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }        const response = await request(app)
            .post('/v1/licenses/validate')
            .set('x-api-key', apiKey)
            .send({ licenseKey: validLicenseKey, productId: 'mismatched-product-id', machineId: 'test-machine-1' });
        expect(response.status).toBe(403);
        expect(response.body.error.message).toBe('Product ID does not match the license');
    });    it('should validate the manually seeded active license (manual-seed-key-003)', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }        const seededKey = 'manual-seed-key-003'; 
        const seededProductId = 'prod_manual_seed_003'; // Use the actual product ID for this seeded key
        
        // Clean up any existing activations for this test key to ensure a fresh test
        if (supabase) {
            await supabase.from('license_activations').delete().match({ 
                license_id: (await supabase.from('licenses').select('id').eq('license_key', seededKey).single()).data?.id 
            });
        }
        
        const response = await request(app)
            .post('/v1/licenses/validate')
            .set('x-api-key', apiKey)
            .send({ licenseKey: seededKey, productId: seededProductId, machineId: 'test-machine-seed' });
        
        if (response.status !== 200) {
            console.warn(`[Test Info] Seeded key ${seededKey} validation failed with status ${response.status}. Ensure it's correctly seeded in the database as active for product ${seededProductId}.`);
        }
        expect(response.status).toBe(200);
        expect(response.body.data).toHaveProperty('message', 'License status (key): active_perpetual');
    });
});

describe('POST /v1/licenses/validate-with-token', () => {
    const tokenTestProductId = 'quantboost-suite-token-test';
    const tokenTestLicenseKey = 'test-key-for-token-validation';
    const inactiveKeyForTokenTest = `${tokenTestLicenseKey}-inactive`;
    let localTestUser = { email: null, password: 'testpassword123', token: null, id: null };

    beforeAll(async () => {
        if (!supabase_test_client || !supabase) {
            console.error("Supabase client(s) not available for beforeAll in /v1/licenses/validate-with-token.");
            return;
        }
        localTestUser.email = generateTestEmail(); // Unique email for this test suite run

        const sessionDetails = await createTestUserAndSession(supabase_test_client, localTestUser.email, localTestUser.password);
        if (sessionDetails.error || !sessionDetails.token) {
            throw new Error(`Failed to setup user for token tests: ${sessionDetails.error?.message || 'No token'}`);
        }
        localTestUser.token = sessionDetails.token;
        localTestUser.id = sessionDetails.userId;

        // Clean up and set up licenses for this user
        await supabaseAdmin.from('licenses').delete().eq('user_id', localTestUser.id);
        
        const { error: insertActiveError } = await supabaseAdmin.from('licenses').insert({
            license_key: tokenTestLicenseKey, product_id: tokenTestProductId, status: 'active', user_id: localTestUser.id, email: localTestUser.email,
            license_tier: 'full', // Make it a full license, not trial
            expiry_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString() // 1 year from now
        });
        if (insertActiveError) throw new Error(`Failed to insert active license for token tests: ${insertActiveError.message}`);

        const { error: insertInactiveError } = await supabaseAdmin.from('licenses').insert({
            license_key: inactiveKeyForTokenTest, product_id: tokenTestProductId, status: 'inactive', user_id: localTestUser.id, email: localTestUser.email
        });
        if (insertInactiveError) throw new Error(`Failed to insert inactive license for token tests: ${insertInactiveError.message}`);
    });

    afterAll(async () => {
        if (supabase_test_client && localTestUser.id) {
            await supabase_test_client.auth.admin.deleteUser(localTestUser.id);
        }
        if (supabase) {
            await supabaseAdmin.from('licenses').delete().eq('user_id', localTestUser.id);
        }
    });    it('should return 400 if no access token is provided in the body', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }
        const response = await request(app)
            .post('/v1/licenses/validate-with-token')
            .set('x-api-key', apiKey)
            .send({ productId: tokenTestProductId, machineId: 'machine123' });
        expect(response.status).toBe(400);
        expect(response.body.error.message).toBe('Access token, product ID, and machine ID are required');
    });    it('should return 403 if an invalid access token is provided', async () => {
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }
        const response = await request(app)
            .post('/v1/licenses/validate-with-token')
            .set('x-api-key', apiKey)
            .send({ accessToken: 'invalid.jwt.token', productId: tokenTestProductId, machineId: 'machine123' });
        expect(response.status).toBe(403);
        expect(response.body.error.message).toBe('Invalid or expired access token.');
    });    it('should validate successfully with a valid access token for an active license', async () => {
        if (!localTestUser.token) { throw new Error("Test user token not available for validation test."); }
        const apiKey = process.env.SUPABASE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }
        const response = await request(app)
            .post('/v1/licenses/validate-with-token')
            .set('x-api-key', apiKey)
            .send({ accessToken: localTestUser.token, productId: tokenTestProductId, machineId: 'machine123' });        expect(response.status).toBe(200);
        expect(response.body.data).toHaveProperty('message', 'License status (access token): trial_active');
        expect(response.body.data).toHaveProperty('status', 'trial_active');
         // The endpoint might return one of the user's licenses for that product_id.
         // If multiple (active/inactive), the logic should prefer active.
        expect([tokenTestLicenseKey, inactiveKeyForTokenTest]).toContain(response.body.data.license_key);
    });    it('should return 403 if the token is valid but the relevant license for the product ID is inactive', async () => {
        if (!localTestUser.token) { throw new Error("Test user token not available."); }
        const apiKey = process.env.MASTER_API_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
        if (!apiKey) { throw new Error("SUPABASE_KEY not set for test"); }
        // To test this, we need a product ID for which this user *only* has an inactive license.
        const inactiveOnlyProductId = `${tokenTestProductId}-inactive-only`;
        const specificInactiveKey = `${tokenTestLicenseKey}-specific-inactive`;

        await supabaseAdmin.from('licenses').delete().match({ license_key: specificInactiveKey }); // Clean first
        await supabase.from('licenses').insert({
            license_key: specificInactiveKey, product_id: inactiveOnlyProductId, status: 'inactive', user_id: localTestUser.id, email: localTestUser.email
        });        const response = await request(app)
            .post('/v1/licenses/validate-with-token')
            .set('x-api-key', apiKey)
            .send({ accessToken: localTestUser.token, productId: inactiveOnlyProductId, machineId: 'machine123' });
        
        expect(response.status).toBe(404);
        expect(response.body.error.message).toBe('No license found for this user and product combination. Please check your account or contact support if you believe this is an error.');
        
        await supabaseAdmin.from('licenses').delete().match({ license_key: specificInactiveKey }); // Cleanup
    });    it('should return 403 if the token is valid but product ID does not match any license for the user', async () => {
        if (!localTestUser.token) { throw new Error("Test user token not available."); }
        const apiKey = process.env.MASTER_API_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
        if (!apiKey) { throw new Error("MASTER_API_KEY not set for test"); }
        const nonExistentProductIdForUser = 'product-id-user-does-not-have';
        const response = await request(app)
            .post('/v1/licenses/validate-with-token')
            .set('x-api-key', apiKey)
            .send({ accessToken: localTestUser.token, productId: nonExistentProductIdForUser, machineId: 'machine123' });        expect(response.status).toBe(404); 
        expect(response.body.error.message).toBe('No license found for this user and product combination. Please check your account or contact support if you believe this is an error.');
    });
});

// Global afterAll for the entire licenses.test.js file
afterAll((done) => {
    if (serverInstance) {
        serverInstance.close(done);
    } else {
        done();
    }
});
