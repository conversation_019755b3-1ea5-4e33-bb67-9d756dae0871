# ==============================================================================
# Provider & Data Source Configuration
# ==============================================================================
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.100"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

provider "azurerm" {
  features {}
}

data "azurerm_client_config" "current" {}

resource "random_string" "suffix" {
  length  = 6
  special = false
  upper   = false
}

################################################################################
# SECTION 1: QUANTBOOST API INFRASTRUCTURE
################################################################################

resource "azurerm_resource_group" "api" {
  count    = var.deploy_api ? 1 : 0
  name     = "rg-quantboost-api-${var.environment}"
  location = var.api_location
  tags = {
    environment = var.environment
    purpose     = "API"
  }
}

resource "azurerm_key_vault" "main" {
  count              = var.deploy_api ? 1 : 0
  name                = "kv-quantboost-api-${var.environment}-${random_string.suffix.result}"
  location            = azurerm_resource_group.api[0].location
  resource_group_name = azurerm_resource_group.api[0].name
  tenant_id           = data.azurerm_client_config.current.tenant_id
  sku_name            = "standard"

  soft_delete_retention_days = 7
  purge_protection_enabled   = true
  enable_rbac_authorization  = true
}

# NOTE: Prefer RBAC over legacy access policies; grant identities appropriate roles outside this file.

resource "azurerm_container_registry" "main" {
  count               = var.deploy_api ? 1 : 0
  name                = "acrquantboost${random_string.suffix.result}"
  resource_group_name = azurerm_resource_group.api[0].name
  location            = azurerm_resource_group.api[0].location
  sku                 = "Basic"
  admin_enabled       = false
}

resource "azurerm_log_analytics_workspace" "main" {
  count               = var.deploy_api ? 1 : 0
  name                = "law-quantboost-${var.environment}"
  location            = azurerm_resource_group.api[0].location
  resource_group_name = azurerm_resource_group.api[0].name
  sku                 = "PerGB2018"
  retention_in_days   = 30
}

resource "azurerm_application_insights" "main" {
  count               = var.deploy_api ? 1 : 0
  name                = "ai-quantboost-api-${var.environment}"
  location            = azurerm_resource_group.api[0].location
  resource_group_name = azurerm_resource_group.api[0].name
  workspace_id        = azurerm_log_analytics_workspace.main[0].id
  application_type    = "other"
}

resource "azurerm_container_app_environment" "main" {
  count                      = var.deploy_api ? 1 : 0
  name                       = "cae-quantboost-${var.environment}"
  location                   = azurerm_resource_group.api[0].location
  resource_group_name        = azurerm_resource_group.api[0].name
  log_analytics_workspace_id = azurerm_log_analytics_workspace.main[0].id
}

# Secrets will be managed manually in Azure Portal (requested). Terraform won't create KV secrets to avoid secrets in state.
# If enabling API deployment later, either:
#  - switch these to data sources referencing existing secrets, or
#  - manage secret refs via CI and avoid embedding in Terraform.
#
# resource "azurerm_key_vault_secret" "supabase_url"                 { ... }
# resource "azurerm_key_vault_secret" "supabase_anon_key"           { ... }
# resource "azurerm_key_vault_secret" "supabase_service_role_key"   { ... }
# resource "azurerm_key_vault_secret" "jwt_secret"                  { ... }
# resource "azurerm_key_vault_secret" "stripe_secret_key"           { ... }
# resource "azurerm_key_vault_secret" "stripe_webhook_secret"       { ... }

resource "azurerm_container_app" "api" {
  count                        = var.deploy_api ? 1 : 0
  name                         = "ca-quantboost-api-${var.environment}"
  container_app_environment_id = azurerm_container_app_environment.main[0].id
  resource_group_name          = azurerm_resource_group.api[0].name
  revision_mode                = "Single"

  identity { type = "SystemAssigned" }

  # Use Managed Identity for ACR (grant AcrPull via RBAC separately); omit registry creds

  # Secret references point to Key Vault; manage secrets manually and switch to data.azurerm_key_vault_secret for lookups when enabling API deployment.
  # Example (when enabling):
  # data "azurerm_key_vault_secret" "supabase_url" { name = "supabase-url" key_vault_id = azurerm_key_vault.main[0].id }
  # secret { name = "supabase-url" key_vault_secret_id = data.azurerm_key_vault_secret.supabase_url.id }

  template {
    container {
      name   = "quantboost-api"
  image  = "${azurerm_container_registry.main[0].login_server}/quantboost-api:latest"
      cpu    = 0.5
      memory = "1Gi"

      env {
        name  = "NODE_ENV"
        value = "production"
      }
      env {
        name  = "PORT"
        value = "3000"
      }
      env {
        name        = "SUPABASE_URL"
        secret_name = "supabase-url"
      }
      env {
        name        = "SUPABASE_ANON_KEY"
        secret_name = "supabase-anon-key"
      }
      env {
        name        = "SUPABASE_SERVICE_ROLE_KEY"
        secret_name = "supabase-service-role-key"
      }
      env {
        name        = "JWT_SECRET"
        secret_name = "jwt-secret"
      }
      env {
        name        = "STRIPE_SECRET_KEY"
        secret_name = "stripe-secret-key"
      }
      env {
        name        = "STRIPE_WEBHOOK_SIGNING_SECRET"
        secret_name = "stripe-webhook-signing-secret"
      }
      env {
        name  = "APPLICATIONINSIGHTS_CONNECTION_STRING"
        value = azurerm_application_insights.main[0].connection_string
      }
    }

    min_replicas = 1
    max_replicas = 3

    http_scale_rule {
      name                = "http-concurrent-requests"
      concurrent_requests = 50
    }
  }

  ingress {
    external_enabled = true
    target_port      = 3000

    traffic_weight {
      percentage      = 100
      latest_revision = true
    }
  }
}

# RBAC: allow Container App MI to pull from ACR
resource "azurerm_role_assignment" "acr_pull" {
  count               = var.deploy_api ? 1 : 0
  scope                = azurerm_container_registry.main[0].id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_container_app.api[0].identity[0].principal_id
}

# RBAC: allow Container App MI to read secrets from Key Vault
resource "azurerm_role_assignment" "kv_secrets_user" {
  count               = var.deploy_api ? 1 : 0
  scope                = azurerm_key_vault.main[0].id
  role_definition_name = "Key Vault Secrets User"
  principal_id         = azurerm_container_app.api[0].identity[0].principal_id
}

################################################################################
# SECTION 2: DISTRIBUTION (STORAGE + FRONT DOOR)
################################################################################

resource "azurerm_resource_group" "distribution" {
  name     = "rg-quantboost-distribution-${var.environment}"
  location = var.distribution_location
  tags = {
    environment = var.environment
    purpose     = "Distribution"
  }
}

resource "azurerm_storage_account" "distribution" {
  name                     = "stqboostdist${random_string.suffix.result}"
  resource_group_name      = azurerm_resource_group.distribution.name
  location                 = azurerm_resource_group.distribution.location
  account_tier             = "Standard"
  account_replication_type = "GRS"
  # Public blob access is allowed initially to enable Front Door origin.
  # Consider disabling and switching to Private Link in a later hardening step.
}

resource "azurerm_storage_container" "releases" {
  name                  = "releases"
  storage_account_name  = azurerm_storage_account.distribution.name
  container_access_type = "blob"
}

resource "azurerm_cdn_frontdoor_profile" "main" {
  name                = "afd-quantboost-${var.environment}"
  resource_group_name = azurerm_resource_group.distribution.name
  sku_name            = "Standard_AzureFrontDoor"
  tags = {
    environment = var.environment
    purpose     = "Distribution"
  }
}

resource "azurerm_cdn_frontdoor_endpoint" "main" {
  name                         = "quantboost-downloads-${random_string.suffix.result}"
  cdn_frontdoor_profile_id     = azurerm_cdn_frontdoor_profile.main.id
  enabled                      = true
}

resource "azurerm_cdn_frontdoor_origin_group" "main" {
  name                     = "og-storage"
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.main.id

  load_balancing {
    sample_size                 = 4
    successful_samples_required = 3
  }

  health_probe {
    protocol            = "Https"
    interval_in_seconds = 120
    request_type        = "HEAD"
    path                = "/"
  }
}

resource "azurerm_cdn_frontdoor_origin" "storage" {
  name                           = "storage-origin"
  cdn_frontdoor_origin_group_id  = azurerm_cdn_frontdoor_origin_group.main.id
  host_name                      = azurerm_storage_account.distribution.primary_blob_host
  origin_host_header             = azurerm_storage_account.distribution.primary_blob_host
  enabled                        = true
  http_port                      = 80
  https_port                     = 443
  certificate_name_check_enabled = true
  priority                       = 1
  weight                         = 1000

  # TODO: Consider using Private Link to lock origin.
  # private_link { ... }
}

resource "azurerm_cdn_frontdoor_route" "main" {
  name                              = "route-all"
  cdn_frontdoor_endpoint_id         = azurerm_cdn_frontdoor_endpoint.main.id
  cdn_frontdoor_origin_group_id     = azurerm_cdn_frontdoor_origin_group.main.id
  cdn_frontdoor_origin_ids          = [azurerm_cdn_frontdoor_origin.storage.id]
  # Associate the route with the custom domain
  cdn_frontdoor_custom_domain_ids   = [azurerm_cdn_frontdoor_custom_domain.download.id]

  patterns_to_match                 = ["/*"]
  https_redirect_enabled            = true
  supported_protocols               = ["Http", "Https"]
  link_to_default_domain            = true
}

resource "azurerm_cdn_frontdoor_custom_domain" "download" {
  name                     = "download-${var.environment}"
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.main.id
  host_name                = var.custom_domain_host_name

  tls {
    certificate_type    = "ManagedCertificate"
    minimum_tls_version = "TLS12"
  }
}

// Removed: custom domain association managed directly in route via cdn_frontdoor_custom_domain_ids

################################################################################
# SECTION 3: CODE SIGNING (Deferred)
################################################################################

# NOTE: The current azurerm provider version does not expose Code Signing/Trusted Signing
# resources. Create these in the Azure Portal for now, or use the azapi provider/ARM template.
# When supported in azurerm, reintroduce resources here.

################################################################################
# SECTION 4: OUTPUTS
################################################################################

output "api_resource_group_name" { value = try(azurerm_resource_group.api[0].name, null) }
output "container_registry_name" { value = try(azurerm_container_registry.main[0].name, null) }
output "container_registry_server" { value = try(azurerm_container_registry.main[0].login_server, null) }
output "container_app_fqdn" { value = try(azurerm_container_app.api[0].latest_revision_fqdn, null) }
output "key_vault_name" { value = try(azurerm_key_vault.main[0].name, null) }
output "application_insights_connection_string" {
  value     = try(azurerm_application_insights.main[0].connection_string, null)
  sensitive = true
}

output "distribution_resource_group_name" { value = azurerm_resource_group.distribution.name }
output "distribution_cdn_url_default" { value = "https://${azurerm_cdn_frontdoor_endpoint.main.host_name}/QuantBoost.exe" }
output "distribution_cdn_url_custom" { value = "https://${var.custom_domain_host_name}/QuantBoost.exe" }
output "upload_instructions" { value = "Upload QuantBoost.exe to Storage Account '${azurerm_storage_account.distribution.name}' into the container '${azurerm_storage_container.releases.name}'." }
output "dns_instructions" { value = "Create a CNAME record for '${var.custom_domain_host_name}' pointing to '${azurerm_cdn_frontdoor_endpoint.main.host_name}'." }

# Code signing outputs are deferred until resources are managed via Terraform.
