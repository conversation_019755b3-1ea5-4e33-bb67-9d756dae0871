import { NextRequest } from 'next/server';
import { getSupabaseAdmin } from '@/lib/supabaseServer';
import { createTrialSchema } from '@/lib/salesTrialsValidation';

export async function GET(req: NextRequest) {
  const supa = getSupabaseAdmin();
  const { searchParams } = new URL(req.url);
  const q = (searchParams.get('q') || '').trim();

  const { data, error } = await supa
    .from('enterprise_trials')
    .select('id, customer_id, status, seat_limit, seats_used, expires_at, created_at, enterprise_customers:customer_id ( company_name, contact_email )')
    .order('created_at', { ascending: false });

  if (error) return Response.json({ error: error.message }, { status: 500 });

  const rows = (data || []).filter((r: any) =>
    !q || (r.enterprise_customers?.company_name || '').toLowerCase().includes(q.toLowerCase())
  ).map((r: any) => {
    const days_left = Math.max(0, Math.ceil((new Date(r.expires_at).getTime() - Date.now()) / (1000*60*60*24)));
    return {
      id: r.id,
      customer_id: r.customer_id,
      company: r.enterprise_customers?.company_name,
      contact_email: r.enterprise_customers?.contact_email,
      status: r.status,
      seat_limit: r.seat_limit,
      seats_used: r.seats_used,
      days_left,
      expires_at: r.expires_at,
      created_at: r.created_at,
    };
  });
  return Response.json(rows);
}

export async function POST(req: NextRequest) {
  const body = await req.json();
  const parsed = createTrialSchema.safeParse(body);
  if (!parsed.success) return Response.json({ error: parsed.error.flatten() }, { status: 400 });
  const { company, contactEmail, domain, seatLimit } = parsed.data;

  const supa = getSupabaseAdmin();

  // Find or create enterprise_customer
  let customerId: string | null = null;
  {
    const { data: existing, error } = await supa
      .from('enterprise_customers')
      .select('id')
      .eq('company_name', company)
      .maybeSingle();
    if (error && error.code !== 'PGRST116') return Response.json({ error: error.message }, { status: 500 });
    if (existing?.id) customerId = existing.id;
  }
  if (!customerId) {
    const { data, error } = await supa
      .from('enterprise_customers')
      .insert({ company_name: company, contact_email: contactEmail })
      .select('id')
      .single();
    if (error) return Response.json({ error: error.message }, { status: 500 });
    customerId = data.id;
  }

  // Create trial (30 days)
  const expiresAt = new Date(Date.now() + 30*24*60*60*1000).toISOString();
  const { data: trial, error: trialErr } = await supa
    .from('enterprise_trials')
    .insert({ customer_id: customerId, seat_limit: seatLimit ?? 50, expires_at: expiresAt })
    .select('*')
    .single();
  if (trialErr) return Response.json({ error: trialErr.message }, { status: 500 });

  return Response.json({ trial, customer: { id: customerId, company_name: company, contact_email: contactEmail } }, { status: 201 });
}
