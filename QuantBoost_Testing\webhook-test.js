#!/usr/bin/env node

/**
 * 🚀 QuantBoost Webhook Testing Quick Start
 * 
 * This script provides easy commands to test all 29 webhook events systematically
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { spawn } = require('child_process');
const path = require('path');

const TEST_COMMANDS = {
  // Individual test categories
  'infrastructure': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "infrastructure"',
  'subscription': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "Subscription Lifecycle"',
  'payment': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "Payment Processing"',
  'dispute': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "Dispute Management"',
  'refund': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "Refund Processing"',
  'fraud': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "Fraud & Risk"',
  'setup': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "Setup & Payment"',
  'customer': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "Customer Management"',
  'advanced': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "Advanced"',
  
  // Priority-based testing
  'high-priority': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "HIGH PRIORITY"',
  'medium-priority': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "MEDIUM PRIORITY"',
  'low-priority': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "LOW PRIORITY"',
  
  // Comprehensive testing
  'all-webhooks': 'npx playwright test tests/webhook-comprehensive.spec.ts',
  'error-handling': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "ERROR HANDLING"',
  'performance': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "Performance"',
  'coverage-report': 'npx playwright test tests/webhook-comprehensive.spec.ts --grep "Coverage Report"',
  
  // Simplified testing
  'simplified': 'npx playwright test tests/webhook-coverage-simplified.spec.ts'
};

function runCommand(command) {
  return new Promise((resolve, reject) => {
    console.log(`\n🚀 Running: ${command}\n`);
    
    const child = spawn(command, [], { 
      shell: true, 
      stdio: 'inherit',
      cwd: process.cwd()
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function main() {
  const command = process.argv[2];
  
  if (!command || command === 'help' || command === '--help') {
    console.log(`
🎯 QuantBoost Webhook Testing Commands
=====================================

📋 Test Categories:
  infrastructure    - Test webhook infrastructure setup
  subscription      - Test subscription lifecycle events (5 events)
  payment          - Test payment processing events (4 events) 
  dispute          - Test dispute management events (5 events)
  refund           - Test refund processing events (5 events)
  fraud            - Test fraud & risk events (2 events)
  setup            - Test setup & payment method events (3 events)
  customer         - Test customer management events (1 event)
  advanced         - Test advanced workflow events (4 events)

🎯 Priority Testing:
  high-priority    - Test critical business events (9 events)
  medium-priority  - Test important business events (14 events)
  low-priority     - Test supporting events (6 events)

🚀 Comprehensive Testing:
  all-webhooks     - Test all 29 webhook events (full suite)
  error-handling   - Test error scenarios and edge cases
  performance      - Test webhook processing performance
  coverage-report  - Generate webhook coverage report

📝 Quick Testing:
  simplified       - Run simplified webhook tests

Examples:
  node webhook-test.js high-priority     # Test critical events first
  node webhook-test.js all-webhooks      # Test all 29 events
  node webhook-test.js subscription      # Test subscription events only
  node webhook-test.js coverage-report   # Generate final report
    `);
    return;
  }

  if (!TEST_COMMANDS[command]) {
    console.error(`❌ Unknown command: ${command}`);
    console.log('Run "node webhook-test.js help" to see available commands');
    process.exit(1);
  }

  try {
    console.log(`🎯 Starting QuantBoost webhook testing: ${command}`);
    console.log(`📁 Working directory: ${process.cwd()}`);
    
    // Check environment variables
    const requiredEnvVars = [
      'BASE_URL',
      'SUPABASE_URL', 
      'SUPABASE_SERVICE_ROLE_KEY'
    ];
    
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    if (missingVars.length > 0) {
      console.error(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
      console.log(`
📋 Required Environment Variables:
  BASE_URL=http://localhost:3000
  SUPABASE_URL=https://izoutrnsxaaoueljiimu.supabase.co
  SUPABASE_SERVICE_ROLE_KEY=eyJhbG...
  
💡 Tip: Create a .env.local file with these variables
      `);
      process.exit(1);
    }

    await runCommand(TEST_COMMANDS[command]);
    
    console.log(`\n✅ Webhook testing completed successfully!`);
    console.log(`📊 Run "node webhook-test.js coverage-report" for final validation\n`);
    
  } catch (error) {
    console.error(`\n❌ Webhook testing failed:`, error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Webhook testing interrupted');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Webhook testing terminated');
  process.exit(0);
});

main().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});