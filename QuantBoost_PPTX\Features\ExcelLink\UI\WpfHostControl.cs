using System;
using System.Windows.Forms;
using System.Windows.Forms.Integration;
using QuantBoost_Powerpoint_Addin.Features.ExcelLink.Views;

namespace QuantBoost_Powerpoint_Addin.Features.ExcelLink.UI
{
    /// <summary>
    /// WinForms UserControl that hosts the WPF LinkManagerView.
    /// This control bridges the gap between VSTO (WinForms) and modern WPF UI.
    /// </summary>
    public partial class WpfHostControl : UserControl
    {
        private ElementHost _elementHost;
        private LinkManagerView _wpfView;

        /// <summary>
        /// Initializes a new instance of the WpfHostControl class.
        /// </summary>
        public WpfHostControl()
        {
            InitializeComponent();
            InitializeWpfContent();
        }

        /// <summary>
        /// Gets the hosted WPF view for external access.
        /// </summary>
        public LinkManagerView WpfView => _wpfView;

        /// <summary>
        /// Initializes the WPF content within the ElementHost.
        /// </summary>
        private void InitializeWpfContent()
        {
            try
            {
                // Create the ElementHost control
                _elementHost = new ElementHost
                {
                    Dock = DockStyle.Fill,
                    BackColor = System.Drawing.Color.Transparent
                };

                // Create the WPF view
                _wpfView = new LinkManagerView();

                // Host the WPF view in the ElementHost
                _elementHost.Child = _wpfView;

                // Add the ElementHost to this control
                this.Controls.Add(_elementHost);
            }
            catch (Exception ex)
            {
                // If WPF hosting fails, show an error message
                var errorLabel = new Label
                {
                    Text = $"Failed to load WPF content: {ex.Message}",
                    Dock = DockStyle.Fill,
                    TextAlign = System.Drawing.ContentAlignment.MiddleCenter,
                    ForeColor = System.Drawing.Color.Red
                };
                this.Controls.Add(errorLabel);
            }
        }

        /// <summary>
        /// Triggers link loading from external sources (e.g., ribbon button).
        /// </summary>
        public void LoadLinks()
        {
            _wpfView?.RefreshLinks();
        }

        /// <summary>
        /// Refreshes the current links from external sources.
        /// </summary>
        public void RefreshLinks()
        {
            _wpfView?.RefreshLinks();
        }

        /// <summary>
        /// Clears current results from external sources.
        /// </summary>
        public void ClearResults()
        {
            _wpfView?.ClearResults();
        }

        /// <summary>
        /// Triggers export from external sources.
        /// </summary>
        public void ExportResults()
        {
            _wpfView?.ExportResults();
        }

        /// <summary>
        /// Gets whether there are links currently loaded.
        /// </summary>
        public bool HasLinks
        {
            get
            {
                return _wpfView?.ViewModel?.HasLinks == true;
            }
        }

        /// <summary>
        /// Gets the current link count.
        /// </summary>
        public int LinkCount
        {
            get
            {
                return _wpfView?.ViewModel?.LinkCount ?? 0;
            }
        }

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _elementHost?.Dispose();
                _wpfView = null;
            }
            base.Dispose(disposing);
        }
    }

    /// <summary>
    /// Designer-generated partial class for WpfHostControl.
    /// </summary>
    partial class WpfHostControl
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // WpfHostControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.Control;
            this.Name = "WpfHostControl";
            this.Size = new System.Drawing.Size(650, 350);
            this.ResumeLayout(false);
        }
    }
}