"use client";
import { useEffect, useState } from 'react';
import { useSupabaseClient } from '@/hooks/useSupabaseClient';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function InviteAcceptPage({ params }: { params: Promise<{ token: string }> }) {
  const [status, setStatus] = useState<'idle'|'loading'|'success'|'error'>('idle');
  const [message, setMessage] = useState<string>('');
  const [token, setToken] = useState<string>('');
  const supabase = useSupabaseClient();

  useEffect(() => {
    (async () => {
      const { token } = await params;
      setToken(token);
      setStatus('loading');
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          setMessage('Please sign in first, then refresh this page.');
          setStatus('error');
          return;
        }
        const res = await fetch('/api/invite/accept', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${session.access_token}` },
          body: JSON.stringify({ token })
        });
        if (!res.ok) {
          const js = await res.json();
          setMessage(js.error || 'Failed to accept invite');
          setStatus('error');
          return;
        }
        setStatus('success');
        setMessage('Invitation accepted! Your access is now active.');
      } catch (e: any) {
        setMessage(e.message || 'Unexpected error');
        setStatus('error');
      }
    })();
  }, [params, supabase]);

  return (
    <div className="container mx-auto max-w-xl py-10">
      <Card>
        <CardHeader>
          <CardTitle>Accept Trial Invitation</CardTitle>
          <CardDescription>Token: {token?.slice(0,8)}…</CardDescription>
        </CardHeader>
        <CardContent>
          {status === 'loading' && <p>Processing your invitation…</p>}
          {status === 'success' && (
            <div className="space-y-3">
              <p className="text-green-700">{message}</p>
              <Button onClick={() => (window.location.href = '/dashboard')}>Go to Dashboard</Button>
            </div>
          )}
          {status === 'error' && (
            <div className="space-y-3">
              <p className="text-red-700">{message}</p>
              <Button onClick={() => window.location.reload()}>Retry</Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
