const express = require('express');
const router = express.Router();
const { supabase } = require('../supabaseClient');
const { sendSuccess, sendError } = require('../utils/responseHelpers');
const { authenticateJWT } = require('../middleware/authMiddleware');

// Apply JWT authentication to all routes in this file
router.use(authenticateJWT);

// GET /me/licenses - Fetches all licenses for the authenticated user
router.get('/licenses', async (req, res) => {
    const userId = req.user.id;
    const userEmail = req.user.email;

    if (!userId) {
        // This should ideally not happen if authenticateJW<PERSON> is working correctly
        return sendError(res, 401, 'User not authenticated.');
    }

    console.log(`🔍 [LICENSES] Fetching licenses for user: ${userId} (${userEmail})`);

    try {
        // Extract the JWT token from the Authorization header
        const authHeader = req.headers.authorization;
        const token = authHeader ? authHeader.split(' ')[1] : null;
        
        console.log(`🔍 [LICENSES] Using auth token: ${token ? 'PRESENT' : 'MISSING'}`);
        
        // Create a Supabase client with the user's auth token to properly apply RLS
        const { createClient } = require('@supabase/supabase-js');
        const SUPABASE_URL = process.env.SUPABASE_URL;
        const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;
        
        const userSupabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
            global: {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            }
        });

        const { data: licenses, error } = await userSupabase
            .from('licenses')
            .select('*') // Select all columns for now, adjust as needed for user portal
            .eq('user_id', userId);

        console.log(`🔍 [LICENSES] Query result - error:`, error);
        console.log(`🔍 [LICENSES] Query result - licenses count:`, licenses?.length || 0);
        console.log(`🔍 [LICENSES] Query result - licenses data:`, JSON.stringify(licenses, null, 2));

        if (error) {
            console.error('Error fetching licenses for user:', error);
            return sendError(res, 500, 'Failed to fetch licenses.');
        }

        if (!licenses || licenses.length === 0) {
            console.log(`🔍 [LICENSES] No licenses found for user ${userId} (${userEmail})`);
            return sendSuccess(res, [], 'No licenses found for this user.');
        }

        console.log(`🔍 [LICENSES] Returning ${licenses.length} licenses for user ${userId} (${userEmail})`);
        sendSuccess(res, licenses);
    } catch (err) {
        console.error('Unexpected error fetching licenses:', err);
        sendError(res, 500, 'An unexpected error occurred while fetching licenses.');
    }
});

// GET /me/licenses/:licenseId/activations - Fetches activations for a specific license owned by the user
router.get('/licenses/:licenseId/activations', async (req, res) => {
    const userId = req.user.id;
    const { licenseId } = req.params;

    if (!userId) {
        return sendError(res, 401, 'User not authenticated.');
    }

    if (!licenseId) {
        return sendError(res, 400, 'License ID is required.');
    }

    try {
        // First, verify that the license belongs to the user
        const { data: license, error: licenseError } = await supabase
            .from('licenses')
            .select('id')
            .eq('id', licenseId)
            .eq('user_id', userId)
            .single();

        if (licenseError || !license) {
            console.error('Error fetching license or license not found for user:', licenseError);
            return sendError(res, 404, 'License not found or not owned by user.');
        }

        // If license is confirmed to be owned by the user, fetch its activations
        const { data: activations, error: activationsError } = await supabase
            .from('license_activations')
            .select('*') // Select all columns for now
            .eq('license_id', licenseId);

        if (activationsError) {
            console.error('Error fetching activations for license:', activationsError);
            return sendError(res, 500, 'Failed to fetch license activations.');
        }

        if (!activations || activations.length === 0) {
            return sendSuccess(res, [], 'No activations found for this license.');
        }

        sendSuccess(res, activations);
    } catch (err) {
        console.error('Unexpected error fetching license activations:', err);
        sendError(res, 500, 'An unexpected error occurred while fetching license activations.');
    }
});

// POST /me/activations/:activationId/deactivate - Deactivates a specific machine activation owned by the user
router.post('/activations/:activationId/deactivate', async (req, res) => {
    const userId = req.user.id;
    const { activationId } = req.params;

    if (!userId) {
        return sendError(res, 401, 'User not authenticated.');
    }

    if (!activationId) {
        return sendError(res, 400, 'Activation ID is required.');
    }

    try {
        // First, get the activation and its associated license to verify ownership
        const { data: activation, error: activationError } = await supabase
            .from('license_activations')
            .select(`
                id,
                is_active,
                license_id,
                licenses ( user_id )
            `)
            .eq('id', activationId)
            .single();

        if (activationError || !activation) {
            console.error('Error fetching activation or activation not found:', activationError);
            return sendError(res, 404, 'Activation not found.');
        }

        // Check if the license associated with the activation belongs to the current user
        if (!activation.licenses || activation.licenses.user_id !== userId) {
            return sendError(res, 403, 'Forbidden: You do not own the license associated with this activation.');
        }

        // Check if already inactive
        if (!activation.is_active) {
            return sendSuccess(res, { id: activation.id, is_active: false }, 'Activation is already inactive.');
        }

        // Deactivate the activation
        const { data: updatedActivation, error: updateError } = await supabase
            .from('license_activations')
            .update({ is_active: false, updated_at: new Date().toISOString() })
            .eq('id', activationId)
            .select('id, is_active')
            .single();

        if (updateError || !updatedActivation) {
            console.error('Error deactivating license activation:', updateError);
            return sendError(res, 500, 'Failed to deactivate license activation.');
        }

        // Log the deactivation event
        // Consider moving to a helper if used elsewhere: logLicenseEvent(license_id, event_type, details)
        const { error: logError } = await supabase.from('license_events').insert({
            license_id: activation.license_id,
            event_type: 'user_deactivated_machine',
            details: { activation_id: activationId, deactivated_by_user_id: userId }
        });

        if (logError) {
            console.error('Error logging deactivation event:', logError); 
            // Non-critical, so proceed with success response
        }

        sendSuccess(res, updatedActivation, 'Activation deactivated successfully.');

    } catch (err) {
        console.error('Unexpected error during deactivation:', err);
        sendError(res, 500, 'An unexpected error occurred during deactivation.');
    }
});

module.exports = router;
