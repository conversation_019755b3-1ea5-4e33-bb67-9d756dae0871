import { describe, it, expect, vi, beforeEach } from 'vitest';

// NOTE: Avoid path alias in vitest for route import to prevent resolution failures
// We will dynamically import after mocking <PERSON><PERSON> so that the route file sees the mocked class.
let POST: any;

// Helper to build a NextRequest-like object
function buildRequest(body: any) {
  return {
    json: async () => body
  } as any;
}

declare const global: any;

describe('create-payment-intent route', () => {
  const mockPaymentIntentId = 'pi_test_123';
  const mockInvoiceId = 'in_test_123';
  const mockSubscriptionId = 'sub_test_123';
  const mockCustomerId = 'cus_test_123';

  beforeEach(async () => {
    vi.resetAllMocks();
    process.env.STRIPE_SECRET_KEY = 'sk_test_123';
    process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY = 'pk_test_123';
    // Clear previous module import cache for the route
    POST = undefined;
  });

  function mockStripeSequence({ delayResolutionAttempts = 2 }: { delayResolutionAttempts?: number } = {}) {
    // Build a mock stripe client structure used by route
    const customers = {
      list: vi.fn().mockResolvedValue({ data: [] }),
      create: vi.fn().mockResolvedValue({ id: mockCustomerId })
    };

    const subsCreate = vi.fn().mockResolvedValue({
      id: mockSubscriptionId,
      latest_invoice: mockInvoiceId,
      items: { data: [{ price: { id: 'price_1RC3HTE6FvhUKV1bE9D6zf6e' } }] }
    });

    const subscriptions = {
      list: vi.fn().mockResolvedValue({ data: [] }),
      create: subsCreate
    };

    // Payment intent resolution attempts
    let attempt = 0;
    const invoices = {
      retrieve: vi.fn().mockImplementation(async () => {
        attempt += 1;
        if (attempt <= delayResolutionAttempts) {
          return { id: mockInvoiceId }; // no payment_intent yet
        }
        return { id: mockInvoiceId, payment_intent: { id: mockPaymentIntentId, client_secret: 'cs_test_123' } };
      })
    };

    const paymentIntents = {
      retrieve: vi.fn().mockResolvedValue({ id: mockPaymentIntentId, client_secret: 'cs_test_123' })
    };

    vi.doMock('stripe', () => ({
      default: vi.fn().mockImplementation(() => ({
        customers,
        subscriptions,
        invoices,
        paymentIntents
      }))
    }));
  }

  async function importRoute() {
    if (!POST) {
      // Mock env module prior to dynamic import
      vi.doMock('@/lib/env', () => {
        return {
          ENV: {
            STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
            NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
          },
          validateStripeConfig: () => ({ isValid: true, missing: [] }),
          getEnvironmentDebugInfo: () => ({})
        } as any;
      });
      // Relative path from src/tests to route file
      // @ts-ignore - dynamic runtime import for vitest
      const mod = await import('../app/api/checkout/create-payment-intent/route');
      POST = mod.POST;
    }
  }

  it('returns 200 with clientSecret after delayed hydration', async () => {
    process.env.QB_PI_WAIT_OVERRIDES = '1,1,1,1,1';
  mockStripeSequence({ delayResolutionAttempts: 3 });
  await importRoute();
    const req = buildRequest({ priceId: 'price_1RC3HTE6FvhUKV1bE9D6zf6e', email: '<EMAIL>', quantity: 1 });
    const res: any = await POST(req);
    const json = await res.json();
    expect(res.status).toBe(200);
    expect(json.clientSecret).toBe('cs_test_123');
  });

  it('returns 503 (or transient 500) when PI never materializes', async () => {
    process.env.QB_PI_WAIT_OVERRIDES = '1,1';
  mockStripeSequence({ delayResolutionAttempts: 999 });
  await importRoute();
    const req = buildRequest({ priceId: 'price_1RC3HTE6FvhUKV1bE9D6zf6e', email: '<EMAIL>', quantity: 1 });
    const res: any = await POST(req);
    const json = await res.json();
    // Depending on timing / mock, we either exhaust retries (503) or hit a mocked path error (500).
    expect([500,503]).toContain(res.status);
    if (res.status === 503) {
      expect(json.retriable).toBe(true);
    }
  });
});
