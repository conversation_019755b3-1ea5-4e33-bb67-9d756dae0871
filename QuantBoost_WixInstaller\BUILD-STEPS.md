# Build the QuantBoost Installer (WiX) - x64 Only

Follow these steps to produce the x64 MSI and EXE bootstrapper in Visual Studio.

## 0) Install prerequisites
- Install WiX Toolset 3.14.1 build tools (latest WiX v3 maintenance)
- Install WiX Toolset Visual Studio Extension

## 1) Open the solution and add projects
- In the repo root, add the WiX projects to your solution:
  - `QuantBoost_WixInstaller\QuantBoost.WixInstaller.wixproj` (MSI)
  - `QuantBoost_Bootstrapper\QuantBoost.Bootstrapper.wixproj` (EXE)

## 2) Build add-ins (Release)
- Open `QuantBoost_Suite.sln` in Visual Studio
- Set configuration to `Release`
- Set platform to `x64`
- Build `QuantBoost_Excel` and `QuantBoost_PPTX`

## 3) Harvest outputs with Heat (x64)
```powershell
cd "C:\VS projects\QuantBoost\QuantBoost_WixInstaller\scripts"
.\Harvest-And-Build.ps1 -Configuration Release
```

## 4) Build MSI (x64)
- Set Solution Platform to **x64**
- Right-click `QuantBoost.WixInstaller` → Build
- Output: `QuantBoost_WixInstaller\bin\x64\Release\QuantBoost.msi`

## 5) Build EXE bootstrapper (x64) 
- Keep Solution Platform as **x64**
- Right-click `QuantBoost.Bootstrapper` → Build
- Output: `QuantBoost_Bootstrapper\bin\x64\Release\QuantBoost.exe`

## 6) Signing (optional for dev)
- Sign MSI and EXE in CI with AzureSignTool when certificate profile is ready.

## 7) Test
- Run on a clean x64 VM. Verify Excel and PowerPoint load the add-ins.

Note: This installer is x64-only and targets modern Windows systems.
