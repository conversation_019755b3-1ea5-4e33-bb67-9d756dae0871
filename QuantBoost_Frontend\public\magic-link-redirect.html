<!DOCTYPE html>
<html>
<head>
    <title>Redirecting...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f7f8fa;
        }
        .container {
            text-align: center;
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 400px;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Completing Authentication...</h2>
        <p>Please wait while we redirect you to the application.</p>
    </div>

    <script>
        function redirectToFrontend() {
            try {
                // Get the hash parameters from the current URL
                const hash = window.location.hash.substring(1);
                
                if (hash) {
                    // Redirect to our frontend callback with the hash parameters
                    const frontendUrl = 'http://localhost:3000/auth/callback#' + hash;
                    window.location.replace(frontendUrl);
                } else {
                    // If no hash, redirect to login page
                    window.location.replace('http://localhost:3000/auth/login');
                }
            } catch (error) {
                console.error('Redirect error:', error);
                // Fallback redirect
                window.location.replace('http://localhost:3000/auth/login');
            }
        }

        // Wait a moment for the page to load, then redirect
        setTimeout(redirectToFrontend, 1000);
    </script>
</body>
</html>
