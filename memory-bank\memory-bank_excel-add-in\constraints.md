---
title: Constraints for QuantBoost Excel Add-in
purpose: Identifies known and anticipated constraints for the development and operation of the QuantBoost Excel Add-in.
projects: ["excel-add-in"]
source_analysis: "Documentation review of CleanExcel Module Development Guide.md, Excel Trace Development Guide.md, Excel Trace Technical Specifications.md, Sheet Size Analyzer Development Guide.md, QuantBoost Installer Deployment Guide.md, and project guidelines."
status: bootstrapping
last_updated: 2025-05-13T17:25:00Z # Will be updated by agent
tags: ["excel-add-in", "constraints", "technical", "licensing", "deployment"]
---

## Known and Anticipated Constraints

These constraints are based on the provided development documentation and project guidelines.

### Technical Constraints (MANDATORY)
*   **Target Framework:** **MUST** target **.NET Framework 4.8.1**. Do **NOT** use APIs or features exclusive to later .NET versions.
*   **Language Version:** **MUST** use **C# 7.3**. Do **NOT** use C# 8.0+ features (e.g., `record`s, init-only setters, default interface methods, simplified `using` declarations, switch expressions, `await using`).
*   **OpenXML SDK:** **MUST** use **DocumentFormat.OpenXml SDK v2.18.0** for OpenXML operations.
    *   **PROHIBITED:** Using any APIs from **OpenXML SDK v3.x** or `System.IO.Packaging` directly.
*   **UI Framework:** WinForms for Ribbon, Custom Task Panes, Standard Dialogs, Custom Toasts.
*   **VSTO Technology:** Windows-only, dependent on VSTO runtime.
*   **Office Version Compatibility:** Microsoft Excel (Microsoft 365 Desktop, Windows 10/11).
*   **COM Interop Limitations:**
    *   Performance bottlenecks with large datasets.
    *   Reliability affected by Excel's state.
    *   Limited access in protected workbooks/sheets.
    *   **CRITICAL:** All COM objects **MUST** be released explicitly (`Marshal.ReleaseComObject` or wrappers).
    *   All COM interactions **MUST** be on the main UI thread.
*   **Single-Threaded UI:** All UI updates must be marshalled to the main thread.

### Licensing and Deployment Constraints
*   **Shared Licensing SDK Dependency:** **MUST** use `QuantBoost.Licensing.dll`.
    *   Licensing logic is **solely** handled by this SDK.
    *   An active license (Active, TrialActive, GracePeriod) unlocks **ALL** features. No per-feature/tier gating within this add-in.
*   **API Connectivity:** Requires connectivity to QuantBoost Licensing API via the SDK.
*   **Unified Installer:** Deployed via a unified MSI (`QuantBoostSuite_x.y.z.msi`).
*   **User Permissions for Installation:** Per-machine installs require admin rights.
*   **Code Signing:** All DLLs and MSI must be code-signed.

### Feature-Specific Constraints
*   **`CleanExcel` - No Undo:** User must be warned; deletion requires active license.
*   **`Excel Trace` - Hotkey:** Registered only if license is active. Tracing requires active license.
*   **`Sheet Size Analyzer` - Estimate Disclosure:** UI must clearly indicate if COM fallback (estimate) was used.

### Forbidden Practices
*   **NO** C# 8.0+ language features.
*   **NO** .NET Core / .NET 5+ specific APIs.
*   **NO** OpenXML SDK v3.x APIs or `System.IO.Packaging`.
*   **NO** `Globals.ThisAddIn` access in `QuantBoostExcelRibbon` constructor.
*   **NO** long-running/blocking operations on UI thread.
*   **NO** leaking COM objects.
*   **NO** ignoring exceptions, especially `COMException`.
*   **NO** UI updates directly from background threads (use `AsyncHelper` or `IProgress<T>`).
*   **NO** custom licensing logic outside `QuantBoost.Licensing.dll`.
*   **NO** tiered feature gating within the add-in.

### Development Process Constraints
*   Dependency on stable `QuantBoost.Licensing.SDK` and backend API.
*   Comprehensive testing matrix required.
