import { test, expect } from '../../fixtures/combined.fixture';
// combined fixture supersedes separate stripe + supabase fixtures
import { fillLinkEmail, fillCardDetails, submitPayment, handleThreeDSIfPresent, waitForReceiptOrSuccess, waitForPaymentReady, waitForIntentReady, collectStripeDiagnostics, acceptRequiredConsents } from '../../utils/stripe-elements';

// Price IDs from frontend implementation
const ANNUAL_PRICE_ID = 'price_1RC3HTE6FvhUKV1bE9D6zf6e';

// Helper to skip if env incomplete
const requireEnv = () => {
  if (!process.env.BASE_URL) test.skip(true, 'BASE_URL not set');
};

test.describe('Individual Plan Checkout', () => {
  test('happy path annual purchase (card success)', async ({ page, testCards, findLicensesByEmail, supabase }) => {
    requireEnv();
    test.skip(!process.env.STRIPE_SECRET_KEY_TEST, 'Stripe secret missing -> skipping card confirmation');
    const email = `e2e_ind_${Date.now()}@example.com`;
    await page.goto(`/checkout/${ANNUAL_PRICE_ID}`);

    const emailFilled = await fillLinkEmail(page, email); expect(emailFilled).toBeTruthy();
    // Wait for intent finalization (client secret creation)
    await waitForIntentReady(page, 15000);
    const ready = await waitForPaymentReady(page, 20000);
    if (!ready) {
      const diag = await collectStripeDiagnostics(page);
      test.skip(true, 'Payment Element not ready - diag: ' + JSON.stringify({ fc: diag.frameCount, pe: diag.paymentContainerExists }));
    }

  // Focus payment element container (helps when unified Payment Element needs initial click)
  const paymentContainer = await page.$('.PaymentElement, [data-testid=PaymentElement]');
  if (paymentContainer) { await paymentContainer.click({ position: { x: 10, y: 10 } }).catch(()=>{}); }
  const cardFilled = await fillCardDetails(page, { number: testCards.SUCCESS, exp: '12/30', cvc: '123', postal: '10001' });
    expect(cardFilled).toBeTruthy();

  await acceptRequiredConsents(page);
  const submitted = await submitPayment(page);
    expect(submitted).toBeTruthy();

    // Handle potential 3DS (not expected for 4242 but future-proof)
    await handleThreeDSIfPresent(page, 5000);

    // Wait for success/receipt UI (placeholder heuristic selectors)
    const success = await waitForReceiptOrSuccess(page, 20000);
    expect(success).toBeTruthy();

    // If Supabase configured WITH service role key (anon key may not have RLS bypass), poll for license row creation
    if (supabase && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      let found = false;
      for (let i = 0; i < 15; i++) {
        const rows = await findLicensesByEmail(email);
        if (rows.length) { found = true; break; }
        await page.waitForTimeout(1500);
      }
      expect(found, 'license record created').toBeTruthy();
    } else if (supabase) {
      test.info().annotations.push({ type: 'note', description: 'Supabase anon key provided; skipping license assertion (needs service role).' });
    }
  });

  test('declined card shows error', async ({ page, testCards }) => {
    requireEnv();
    test.skip(!process.env.STRIPE_SECRET_KEY_TEST, 'Needs Stripe to simulate decline');
    const email = `e2e_decl_${Date.now()}@example.com`;
    await page.goto(`/checkout/${ANNUAL_PRICE_ID}`);
    const emailFilled = await fillLinkEmail(page, email); expect(emailFilled).toBeTruthy();
  await waitForIntentReady(page, 15000);
  const readyDecline = await waitForPaymentReady(page, 20000);
  if (!readyDecline) { const diag = await collectStripeDiagnostics(page); test.skip(true, 'Payment Element not ready (decline) ' + JSON.stringify({ fc: diag.frameCount })); }
  const paymentContainer = await page.$('.PaymentElement, [data-testid=PaymentElement]');
  if (paymentContainer) { await paymentContainer.click({ position: { x: 10, y: 10 } }).catch(()=>{}); }
  const filled = await fillCardDetails(page, { number: testCards.DECLINE, exp: '12/30', cvc: '123' }); expect(filled).toBeTruthy();
  await acceptRequiredConsents(page);
  const submitted = await submitPayment(page); expect(submitted).toBeTruthy();
    // Expect an error message from Stripe (generic pattern)
    await expect(page.locator('text=/declined|your card was declined|card was declined/i')).toBeVisible({ timeout: 15000 });
  });

  test('email validation rejects malformed address', async ({ page }) => {
    requireEnv();
    await page.goto(`/checkout/${ANNUAL_PRICE_ID}`);
  const emailFilled = await fillLinkEmail(page, 'bad-email');
  expect(emailFilled).toBeTruthy();
    // Expect no payment element yet (since email invalid)
    await page.waitForTimeout(1000);
    const paymentVisible = await page.$('.PaymentElement, [data-testid=PaymentElement]');
    expect(paymentVisible).toBeFalsy();
  });

  test('3DS card requires authentication and succeeds', async ({ page, testCards }) => {
    requireEnv();
    test.skip(!process.env.STRIPE_SECRET_KEY_TEST, 'Stripe secret missing -> skipping 3DS');
    const email = `e2e_3ds_${Date.now()}@example.com`;
    await page.goto(`/checkout/${ANNUAL_PRICE_ID}`);
    const emailFilled = await fillLinkEmail(page, email); expect(emailFilled).toBeTruthy();
  await waitForIntentReady(page, 15000);
  const ready3ds = await waitForPaymentReady(page, 20000);
  if (!ready3ds) { const diag = await collectStripeDiagnostics(page); test.skip(true, 'Payment Element not ready (3DS) ' + JSON.stringify({ fc: diag.frameCount })); }
  const paymentContainer = await page.$('.PaymentElement, [data-testid=PaymentElement]');
  if (paymentContainer) { await paymentContainer.click({ position: { x: 10, y: 10 } }).catch(()=>{}); }
  const filled = await fillCardDetails(page, { number: testCards.THREE_D_SECURE, exp: '12/30', cvc: '123' }); expect(filled).toBeTruthy();
  await acceptRequiredConsents(page);
  const submitted = await submitPayment(page); expect(submitted).toBeTruthy();
    await handleThreeDSIfPresent(page, 15000);
    const success = await waitForReceiptOrSuccess(page, 20000); expect(success).toBeTruthy();
  });
});
