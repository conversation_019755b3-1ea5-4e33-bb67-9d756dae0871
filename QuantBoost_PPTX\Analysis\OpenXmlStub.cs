using DocumentFormat.OpenXml.Packaging;

namespace QuantBoost_Powerpoint_Addin.Analysis
{
    public static class OpenXmlStub
    {
        public static void TestOpenXmlReference(string pptxPath)
        {
            // This method just tries to open a presentation file using OpenXML SDK
            using (PresentationDocument doc = PresentationDocument.Open(pptxPath, false))
            {
                // No-op: just confirms the SDK is referenced and usable
            }
        }
    }
}
