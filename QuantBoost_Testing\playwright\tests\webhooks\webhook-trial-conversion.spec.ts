import { test, expect } from '../../fixtures/combined.fixture';
import { WebhookTestFactory, simulateStripeWebhook } from '../../fixtures/webhook-testing.fixture';

/**
 * E2E-ish webhook spec for enterprise trial conversion
 * Creates a synthetic checkout.session.completed event containing metadata.trialId and posts to the webhook.
 * Verifies enterprise_trials.status flips to 'converted' exactly once.
 */
test.describe('Enterprise Trial Conversion Webhook', () => {
  test('marks trial as converted exactly once', async ({ supabaseClient }) => {
    // Precondition: create a fake enterprise customer + trial directly via Supabase for isolated test
    const { data: customer, error: custErr } = await supabaseClient
      .from('enterprise_customers')
      .insert({ company_name: `Test Co ${Date.now()}`, contact_email: `owner_${Date.now()}@example.com` })
      .select('*')
      .single();
    expect(custErr).toBeNull();

    const expiresAt = new Date(Date.now() + 30*24*60*60*1000).toISOString();
    const { data: trial, error: trialErr } = await supabaseClient
      .from('enterprise_trials')
      .insert({ customer_id: customer.id, seat_limit: 10, expires_at: expiresAt })
      .select('*')
      .single();
    expect(trialErr).toBeNull();

    // Build a synthetic checkout.session.completed event
    const session = WebhookTestFactory.createTestCheckoutSession(
      `cus_test_${Date.now()}`,
      `sub_test_${Date.now()}`,
      { 
        customer_details: { email: customer.contact_email, name: customer.company_name },
        metadata: { trialId: trial.id },
      }
    );

    const res1 = await simulateStripeWebhook('checkout.session.completed', session, { eventId: `evt_${Date.now()}` });
    expect(res1.status).toBeGreaterThanOrEqual(200);
    expect(res1.status).toBeLessThan(300);

    // Verify DB flipped to converted
    const { data: trialAfter, error: readErr } = await supabaseClient
      .from('enterprise_trials')
      .select('status, converted_at')
      .eq('id', trial.id)
      .single();
    expect(readErr).toBeNull();
    expect(trialAfter?.status).toBe('converted');
    expect(trialAfter?.converted_at).toBeTruthy();

    // Send duplicate event to assert idempotency -> 409
    const res2 = await simulateStripeWebhook('checkout.session.completed', session, { eventId: res1.body?.id || `evt_dup_${Date.now()}` });
    expect(res2.status).toBe(409);
  });
});
