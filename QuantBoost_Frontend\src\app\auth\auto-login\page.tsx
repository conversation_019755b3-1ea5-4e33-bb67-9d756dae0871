'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';

function AutoLoginContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string>('');
  const supabase = useSupabaseClient();

  useEffect(() => {
    const performAutoLogin = async () => {
      try {
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const email = searchParams.get('email');
        const paymentIntent = searchParams.get('payment_intent');

        if (!accessToken || !refreshToken) {
          throw new Error('Missing authentication tokens');
        }

        // Set the session using the tokens from the magic link
        const { data, error } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken
        });

        if (error) {
          throw new Error(`Authentication failed: ${error.message}`);
        }

        if (!data.session) {
          throw new Error('No session created');
        }

        console.log('✅ Auto-login successful for payment completion');
        setStatus('success');

        // Small delay to show success message, then redirect
        setTimeout(() => {
          router.push(`/dashboard?payment=success&email=${encodeURIComponent(email || '')}&payment_intent=${paymentIntent || ''}`);
        }, 1500);

      } catch (err) {
        console.error('Auto-login failed:', err);
        setError(err instanceof Error ? err.message : 'Authentication failed');
        setStatus('error');

        // Fallback to payment success page after delay
        setTimeout(() => {
          const email = searchParams.get('email');
          const paymentIntent = searchParams.get('payment_intent');
          router.push(`/auth/payment-success?email=${encodeURIComponent(email || '')}&payment_intent=${paymentIntent || ''}&auto_login_failed=true`);
        }, 3000);
      }
    };

    performAutoLogin();
  }, [searchParams, router, supabase.auth]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 text-center">
        {status === 'loading' && (
          <div className="space-y-4">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
            <h2 className="text-2xl font-bold text-gray-900">
              Signing you in...
            </h2>
            <p className="text-gray-600">
              Your payment was successful! We're logging you in automatically.
            </p>
          </div>
        )}

        {status === 'success' && (
          <div className="space-y-4">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <span className="text-green-600 text-2xl">✅</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900">
              Welcome to QuantBoost!
            </h2>
            <p className="text-gray-600">
              You're all set! Redirecting to your dashboard...
            </p>
          </div>
        )}

        {status === 'error' && (
          <div className="space-y-4">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <span className="text-red-600 text-2xl">❌</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900">
              Authentication Issue
            </h2>
            <p className="text-gray-600">
              {error || 'We encountered an issue signing you in automatically.'}
            </p>
            <p className="text-sm text-gray-500">
              Redirecting to manual login...
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default function AutoLoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    }>
      <AutoLoginContent />
    </Suspense>
  );
}
