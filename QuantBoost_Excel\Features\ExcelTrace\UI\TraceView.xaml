﻿
<UserControl x:Class="QuantBoost_Excel.Features.ExcelTrace.UI.TraceView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:localModel="clr-namespace:QuantBoost_Excel.Features.ExcelTrace.Model"
             xmlns:local="clr-namespace:QuantBoost_Excel.Features.ExcelTrace.UI"
             mc:Ignorable="d"
             d:DesignHeight="480" d:DesignWidth="700"
             FontFamily="Segoe UI" FontSize="14" Background="White"
             TextOptions.TextFormattingMode="Ideal" TextOptions.TextRenderingMode="ClearType"
             UseLayoutRounding="True">

<UserControl.Resources>
    <!-- CRITICAL FIX: Add converter for dynamic indentation -->
    <local:DepthToIndentationConverter x:Key="DepthToIndentationConverter"/>

    <!-- REFACTOR: Add converter for determining node expandability -->
    <local:NodeExpandabilityConverter x:Key="NodeExpandabilityConverter"/>

    <!-- Theme Brushes -->
    <SolidColorBrush x:Key="ThemePrimaryBrush" Color="#0078D7"/>
    <SolidColorBrush x:Key="ThemeSelectedBackgroundBrush" Color="#DDEEFF"/>
    <SolidColorBrush x:Key="ThemeBorderBrush" Color="#CCCCCC"/>
    <SolidColorBrush x:Key="ThemeHeaderBackgroundBrush" Color="#F0F0F0"/>
    <SolidColorBrush x:Key="ThemeHighlightBrush" Color="#80B3D9FF"/><!-- Semi-transparent blue for formula highlighting -->

    <!-- REFACTOR: Enhanced Expander ToggleButton Style with Plus/Minus icons for better visual distinction -->
    <!-- OPTION 1: Plus/Minus in Square (Classic Windows Style) - Currently Active -->
    <Style x:Key="ExpandCollapseToggleStyle" TargetType="ToggleButton">
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Width" Value="16"/>
        <Setter Property="Height" Value="16"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Border x:Name="ExpanderBorder"
                            Background="White"
                            BorderBrush="#999999"
                            BorderThickness="1"
                            CornerRadius="1"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Width="14" Height="14">
                        <Grid>
                            <!-- Horizontal line (always visible) -->
                            <Rectangle x:Name="HorizontalLine"
                                      Fill="#333333"
                                      Height="1"
                                      Width="8"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                            <!-- Vertical line (hidden when expanded) -->
                            <Rectangle x:Name="VerticalLine"
                                      Fill="#333333"
                                      Height="8"
                                      Width="1"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- When expanded (IsChecked=True), hide vertical line to show minus -->
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="VerticalLine" Property="Visibility" Value="Collapsed"/>
                        </Trigger>
                        <!-- Hover effect -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ExpanderBorder" Property="BorderBrush" Value="#0078D7"/>
                            <Setter TargetName="ExpanderBorder" Property="Background" Value="#F0F8FF"/>
                        </Trigger>
                        <!-- Pressed effect -->
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ExpanderBorder" Property="Background" Value="#E0E0E0"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- OPTION 2: Triangular Chevrons (Modern Style) - Alternative -->
    <Style x:Key="ExpandCollapseToggleStyleChevron" TargetType="ToggleButton">
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Width" Value="16"/>
        <Setter Property="Height" Value="16"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Border Background="Transparent">
                        <Path x:Name="ChevronPath"
                              Fill="#555555"
                              Stroke="#555555"
                              StrokeThickness="1.5"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Data="M 3 1 L 7 5 L 3 9" /> <!-- Right-pointing chevron -->
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- When expanded, rotate to point down -->
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ChevronPath" Property="Data" Value="M 1 3 L 5 7 L 9 3"/>
                        </Trigger>
                        <!-- Hover effect -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ChevronPath" Property="Fill" Value="#0078D7"/>
                            <Setter TargetName="ChevronPath" Property="Stroke" Value="#0078D7"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- OPTION 3: Rounded Plus/Minus (Material Design Style) - Alternative -->
    <Style x:Key="ExpandCollapseToggleStyleRounded" TargetType="ToggleButton">
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Width" Value="16"/>
        <Setter Property="Height" Value="16"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Border x:Name="RoundedBorder"
                            Background="#F5F5F5"
                            BorderBrush="#CCCCCC"
                            BorderThickness="1"
                            CornerRadius="8"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Width="16" Height="16">
                        <Grid>
                            <!-- Horizontal line (always visible) -->
                            <Rectangle x:Name="HorizontalLineRounded"
                                      Fill="#666666"
                                      Height="2"
                                      Width="8"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      RadiusX="1" RadiusY="1"/>
                            <!-- Vertical line (hidden when expanded) -->
                            <Rectangle x:Name="VerticalLineRounded"
                                      Fill="#666666"
                                      Height="8"
                                      Width="2"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      RadiusX="1" RadiusY="1"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- When expanded, hide vertical line -->
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="VerticalLineRounded" Property="Visibility" Value="Collapsed"/>
                        </Trigger>
                        <!-- Hover effect -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="RoundedBorder" Property="Background" Value="#E3F2FD"/>
                            <Setter TargetName="RoundedBorder" Property="BorderBrush" Value="#2196F3"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--
    TO SWITCH EXPANDER STYLES:
    Replace "ExpandCollapseToggleStyle" in the TreeViewItem template below with:
    - "ExpandCollapseToggleStyle" (current) = Plus/Minus in Square (Classic Windows)
    - "ExpandCollapseToggleStyleChevron" = Triangular Chevrons (Modern)
    - "ExpandCollapseToggleStyleRounded" = Rounded Plus/Minus (Material Design)
    -->

    <!-- FINAL FIX: TreeViewItem Style with proper visual tree lines and indentation -->
    <Style TargetType="TreeViewItem">
        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="1"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TreeViewItem">
                    <StackPanel>
                        <Border Name="Bd" Background="{TemplateBinding Background}" Padding="{TemplateBinding Padding}" Height="24">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <!-- Column for the visual tree lines (e.g., "│ ├─") -->
                                    <ColumnDefinition Width="Auto" />
                                    <!-- Column for the expander toggle button (+/-) -->
                                    <ColumnDefinition Width="Auto" />
                                    <!-- Column for the main header content -->
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <!--
                                PART 1: THE VISUAL TREE LINES
                                This TextBlock displays the string ("│ ├─") from your converter.
                                The DataContext of a TreeViewItem is the TraceNode object.
                                -->
                                <TextBlock Grid.Column="0" VerticalAlignment="Center" Margin="0,0,2,0"
                                           Text="{Binding Path=Depth, Converter={StaticResource DepthToIndentationConverter}}"
                                           FontFamily="Consolas" Foreground="#999"/>

                                <!-- PART 2: THE EXPANDER -->
                                <ToggleButton Grid.Column="1" x:Name="Expander" Style="{StaticResource ExpandCollapseToggleStyle}"
                                              Width="19"
                                              IsChecked="{Binding Path=IsExpanded, RelativeSource={RelativeSource AncestorType=TreeViewItem}, Mode=TwoWay}">
                                    <ToggleButton.Visibility>
                                        <MultiBinding Converter="{StaticResource NodeExpandabilityConverter}">
                                            <Binding Path="HasItems" RelativeSource="{RelativeSource AncestorType=TreeViewItem}"/>
                                            <Binding Path="ChildrenLoaded"/>
                                            <Binding Path="Formula"/>
                                            <Binding Path="DisplayValue"/>
                                            <Binding Path="RawValue"/>
                                        </MultiBinding>
                                    </ToggleButton.Visibility>
                                </ToggleButton>

                                <!-- PART 3: THE CONTENT -->
                                <ContentPresenter Grid.Column="2" x:Name="PART_Header" ContentSource="Header"
                                                  HorizontalAlignment="Stretch" VerticalAlignment="Center"/>
                            </Grid>
                        </Border>

                        <!--
                        PART 4: THE CHILD ITEMS
                        The ItemsPresenter is indented to align under the parent's content.
                        The margin should be the combined width of the tree lines and the expander.
                        A good starting point is 38 (e.g., 19 for tree lines + 19 for expander).
                        -->
                        <ItemsPresenter x:Name="ItemsHost" Margin="38,0,0,0"/>
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsExpanded" Value="false">
                            <Setter TargetName="ItemsHost" Property="Visibility" Value="Collapsed"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="true">
                            <Setter TargetName="Bd" Property="Background" Value="{StaticResource ThemeSelectedBackgroundBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ENHANCED: Production-quality TreeView template with icons and better styling -->
    <HierarchicalDataTemplate DataType="{x:Type localModel:TraceNode}" ItemsSource="{Binding Children}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="3*" MinWidth="200"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="1*" MinWidth="100"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="4,0">
                <!-- ENHANCED: Sharp vector icons using Segoe Fluent Icons for professional appearance -->
                <TextBlock FontSize="14" Margin="0,0,6,0" VerticalAlignment="Center"
                          FontFamily="Segoe Fluent Icons" Foreground="#444444">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock">
                            <!-- Default: Document icon -->
                            <Setter Property="Text" Value="&#xE7C3;"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding NodeType}" Value="Root">
                                    <!-- Target icon -->
                                    <Setter Property="Text" Value="&#xE71E;"/>
                                    <Setter Property="Foreground" Value="#0078D7"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding NodeType}" Value="SameSheet">
                                    <!-- Chart icon -->
                                    <Setter Property="Text" Value="&#xE9D2;"/>
                                    <Setter Property="Foreground" Value="#107C10"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding NodeType}" Value="DifferentSheet">
                                    <!-- Page icon -->
                                    <Setter Property="Text" Value="&#xE8A5;"/>
                                    <Setter Property="Foreground" Value="#FF8C00"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding NodeType}" Value="ExternalWorkbook">
                                    <!-- Folder icon -->
                                    <Setter Property="Text" Value="&#xE8B7;"/>
                                    <Setter Property="Foreground" Value="#605E5C"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding NodeType}" Value="Range">
                                    <!-- Grid icon -->
                                    <Setter Property="Text" Value="&#xE9D9;"/>
                                    <Setter Property="Foreground" Value="#8764B8"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding NodeType}" Value="Error">
                                    <!-- Warning icon -->
                                    <Setter Property="Text" Value="&#xE7BA;"/>
                                    <Setter Property="Foreground" Value="#D13438"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding NodeType}" Value="Summary">
                                    <!-- Info icon -->
                                    <Setter Property="Text" Value="&#xE946;"/>
                                    <Setter Property="Foreground" Value="#0078D7"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding NodeType}" Value="ArrayElement">
                                    <!-- Number icon -->
                                    <Setter Property="Text" Value="&#xE8EF;"/>
                                    <Setter Property="Foreground" Value="#486991"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>

                <TextBlock Text="{Binding DisplayText}" TextTrimming="CharacterEllipsis" VerticalAlignment="Center">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding HasError}" Value="True">
                                    <Setter Property="Foreground" Value="Red"/>
                                    <Setter Property="FontStyle" Value="Italic"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding NodeType}" Value="Root">
                                    <Setter Property="FontWeight" Value="Normal"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </StackPanel>

            <!-- CRITICAL FIX: Remove GridSplitter from template to fix keyboard navigation -->
            <!-- GridSplitter in data template interferes with TreeView keyboard navigation -->
            <Border Grid.Column="1" Width="5" Background="Transparent"/>

            <TextBlock Grid.Column="2" Text="{Binding DisplayValue}"
                      HorizontalAlignment="Right" VerticalAlignment="Center" Margin="4,0"
                      FontFamily="Segoe UI" FontSize="14">
                <TextBlock.Style>
                    <Style TargetType="TextBlock">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding HasError}" Value="True">
                                <Setter Property="Foreground" Value="Red"/>
                                <Setter Property="FontStyle" Value="Italic"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBlock.Style>
            </TextBlock>
        </Grid>
    </HierarchicalDataTemplate>

    <!-- Flat button style similar to ToolBar.ButtonStyle -->
    <Style x:Key="FlatButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#E0E0E0"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#D0D0D0"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Settings button style with border box -->
    <Style x:Key="SettingsButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource ThemeBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}"
                            CornerRadius="3">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#F5F5F5"/>
                            <Setter Property="BorderBrush" Value="{StaticResource ThemePrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#E8E8E8"/>
                            <Setter Property="BorderBrush" Value="{StaticResource ThemePrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource ThemePrimaryBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</UserControl.Resources>

<!-- Main Layout Grid -->
<Grid>
    <Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="*"/>
        <RowDefinition Height="Auto"/>
    </Grid.RowDefinitions>

    <!-- Define shared column widths at the root level -->
    <Grid.ColumnDefinitions>
        <ColumnDefinition x:Name="PrecedentColumnDef" Width="3*" MinWidth="200"/>
        <ColumnDefinition Width="5"/>
        <ColumnDefinition x:Name="ValueColumnDef" Width="1*" MinWidth="100"/>
    </Grid.ColumnDefinitions>

    <!-- Formula Bar -->
    <Border Grid.Row="0" Grid.ColumnSpan="3" Padding="8" Background="{StaticResource ThemeHeaderBackgroundBrush}" BorderBrush="{StaticResource ThemeBorderBrush}" BorderThickness="0,0,0,1">
        <Grid SnapsToDevicePixels="True">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <RichTextBox Name="FormulaRichTextBox" IsReadOnly="true"
                         VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Disabled"
                         FontFamily="Segoe UI" FontSize="12"
                         BorderThickness="0" Padding="4,3"
                         Background="White" SelectionBrush="{StaticResource ThemePrimaryBrush}"
                         SnapsToDevicePixels="True"
                         TextOptions.TextFormattingMode="Display"
                         TextOptions.TextHintingMode="Fixed"
                         TextOptions.TextRenderingMode="ClearType">
                <RichTextBox.Document>
                    <FlowDocument PageWidth="NaN">
                        <Paragraph TextAlignment="Left" Margin="0"/>
                    </FlowDocument>
                </RichTextBox.Document>
            </RichTextBox>
        </Grid>
    </Border>

    <!-- Column Headers -->
    <Border Grid.Row="1" Grid.Column="0" Background="{StaticResource ThemeHeaderBackgroundBrush}">
        <TextBlock Text="Precedents" FontWeight="SemiBold" Padding="24,4"/>
    </Border>


    <GridSplitter Grid.Row="1" Grid.RowSpan="2" Grid.Column="1"
                  Width="5" Background="#EAEAEA"
                  HorizontalAlignment="Stretch" VerticalAlignment="Stretch"
                  ResizeDirection="Columns"/>  

    <Border Grid.Row="1" Grid.Column="2" Background="{StaticResource ThemeHeaderBackgroundBrush}">
        <TextBlock Text="Value" FontWeight="SemiBold" HorizontalAlignment="Right" Padding="4"/>
    </Border>

    <!-- CRITICAL FIX: TreeView with proper keyboard navigation configuration and performance improvements -->
    <TreeView Name="PrecedentTreeView" Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3"
              BorderThickness="0" Padding="5"
              SelectedItemChanged="PrecedentTreeView_SelectedItemChanged"
              PreviewKeyDown="PrecedentTreeView_PreviewKeyDown"
              MouseDoubleClick="PrecedentTreeView_MouseDoubleClick"
              KeyboardNavigation.DirectionalNavigation="Cycle"
              KeyboardNavigation.TabNavigation="Local"
              Focusable="True"
              VirtualizingStackPanel.IsVirtualizing="True"
              VirtualizingStackPanel.VirtualizationMode="Recycling"
              ScrollViewer.CanContentScroll="True">
        <!-- CRITICAL FIX: Explicit ItemContainerStyle to prevent binding errors -->
        <TreeView.ItemContainerStyle>
            <Style TargetType="TreeViewItem" BasedOn="{StaticResource {x:Type TreeViewItem}}">
                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="VerticalAlignment" Value="Top"/>
            </Style>
        </TreeView.ItemContainerStyle>
        <TreeView.ContextMenu>
            <ContextMenu>
                <MenuItem Header="Navigate to Cell" Click="NavigateToCell_Click" FontWeight="Bold"/>
                <MenuItem Header="Expand Array" Click="ExpandArray_Click" Name="ExpandArrayMenuItem"/>
                <Separator/>
                <MenuItem Header="Copy Cell Address" Click="CopyCellAddress_Click"/>
                <MenuItem Header="Copy Cell Value" Click="CopyCellValue_Click"/>
                <Separator/>
                <MenuItem Header="Expand All" Click="ExpandAll_Click"/>
                <MenuItem Header="Collapse All" Click="CollapseAll_Click"/>
            </ContextMenu>
        </TreeView.ContextMenu>
        <TreeView.ItemsPanel>
            <ItemsPanelTemplate>
                <VirtualizingStackPanel />
            </ItemsPanelTemplate>
        </TreeView.ItemsPanel>
    </TreeView>
    
    <!-- Footer with status information -->
    <Border Grid.Row="3" Grid.ColumnSpan="3" Padding="12,8" MinHeight="50" Background="{StaticResource ThemeHeaderBackgroundBrush}" BorderBrush="{StaticResource ThemeBorderBrush}" BorderThickness="0,1,0,0">
        <Grid>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center">
                <Button Name="SettingsButton" Content="Settings" Click="SettingsButton_Click" Style="{StaticResource SettingsButtonStyle}"/>
                <TextBlock Name="StatusTextBlock" Text="Ready" VerticalAlignment="Center" Margin="15,0,0,0"
                          FontSize="12" Foreground="Gray"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                <Button Name="CloseButton" Content="Close" Width="80" Height="28" IsDefault="True" IsCancel="True"
                        Background="{StaticResource ThemePrimaryBrush}" Foreground="White" BorderThickness="0"
                        Margin="0,0,8,0" Click="CloseButton_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</Grid>

</UserControl>
