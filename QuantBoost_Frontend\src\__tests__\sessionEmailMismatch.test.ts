import { describe, it, expect } from 'vitest';
import { shouldSwitchAccount, normalizeEmail } from '../lib/auth/sessionEmailMismatch';

describe('sessionEmailMismatch helpers', () => {
  it('normalizes email correctly', () => {
    expect(normalizeEmail(' <EMAIL> ')).toBe('<EMAIL>');
  });

  it('does not require switch when no purchase email', () => {
    expect(shouldSwitchAccount('<EMAIL>', null)).toBe(false);
  });

  it('does not require switch when no current session', () => {
    expect(shouldSwitchAccount(null, '<EMAIL>')).toBe(false);
  });

  it('requires switch when emails differ', () => {
    expect(shouldSwitchAccount('<EMAIL>', '<EMAIL>')).toBe(true);
  });

  it('does not require switch when emails equal ignoring case/whitespace', () => {
    expect(shouldSwitchAccount('<EMAIL>', ' <EMAIL> ')).toBe(false);
  });
});
