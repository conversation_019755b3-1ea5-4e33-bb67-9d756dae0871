<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi"
  xmlns:util="http://schemas.microsoft.com/wix/UtilExtension">
  <!-- Burn bootstrapper EXE that chains prerequisites and our MSI. Build this from a WiX Bootstrapper project. -->
  <Bundle Name="QuantBoost Installer"
          Version="0.1.0"
          Manufacturer="QuantBoost.ai"
          UpgradeCode="1B4C9B2B-4FD1-4B97-9A8D-8C2D5E8B9A33">

    <!-- Standard BA with RTF license (WiX v3 pattern) -->
    <BootstrapperApplicationRef Id="WixStandardBootstrapperApplication.RtfLicense" />
    <WixVariable Id="WixStdbaLicenseRtf" Value="License.rtf" />

  <Chain>
      <!-- .NET Framework 4.8.1 web installer (example using WixNetFxExtension constants) -->
      <!-- If targeting .NET Framework 4.8.1 is not required for your add-ins, you can remove this. -->
  <PackageGroupRef Id="NetFx481" />

      <!-- VSTO Runtime (vstor_redist.exe). You can host this on your CDN or reference Microsoft download. -->
      <PackageGroupRef Id="VSTORuntime" />

      <!-- Main MSI produced by Product.wxs project -->
      <MsiPackage Id="QuantBoostMsi" SourceFile="$(var.QuantBoost.WixInstaller.TargetPath)" DisplayInternalUI="no" Visible="no"/>
    </Chain>
  </Bundle>

  <!-- Package groups for prerequisites -->
  <Fragment>
    <!-- .NET Framework 4.8.1: replace with accurate detection and URL if needed. -->
    <PackageGroup Id="NetFx481">
  <ExePackage Id="NetFx481Web"
                  DisplayName="Microsoft .NET Framework 4.8.1"
                  Compressed="no"
                  PerMachine="yes"
                  Vital="yes"
      DetectCondition="(NetFxRelease32 &gt;= 533320) OR (NetFxRelease64 &gt;= 533320)"
                  DownloadUrl="https://go.microsoft.com/fwlink/?linkid=2203301"
                  InstallCommand="/passive /norestart"
                  RepairCommand="/passive /norestart"
                  UninstallCommand="/uninstall /passive /norestart"
                  ReturnCodes="0,3010">
        <ExitCode Value="0" Result="success" />
        <ExitCode Value="3010" Result="success" Reboot="required" />
      </ExePackage>
    </PackageGroup>

    <!-- VSTO Runtime -->
    <PackageGroup Id="VSTORuntime">
  <ExePackage Id="VSTORedist"
                  DisplayName="Microsoft Visual Studio Tools for Office Runtime"
                  Compressed="no"
                  PerMachine="yes"
                  Vital="yes"
      DetectCondition="(VstoInstall32 &gt;= 1) OR (VstoInstall64 &gt;= 1)"
                  DownloadUrl="https://download.microsoft.com/download/2/6/C/26CD6C66-03B1-4965-9B37-1B4B8C47D1C8/vstor_redist.exe"
                  InstallCommand="/passive /norestart"
                  ReturnCodes="0,3010">
        <ExitCode Value="0" Result="success" />
        <ExitCode Value="3010" Result="success" Reboot="required" />
      </ExePackage>
    </PackageGroup>
  </Fragment>

  <!-- Detection variables for Burn (WiX Util Extension) -->
  <Fragment>
    <!-- NetFx 4.8.1 Release registry value (DWORD), search both registry views -->
    <util:RegistrySearch Id="NetFxReleaseSearch32"
                         Variable="NetFxRelease32"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full"
                         Value="Release"
                         Result="value" />
    <util:RegistrySearch Id="NetFxReleaseSearch64"
                         Variable="NetFxRelease64"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full"
                         Value="Release"
                         Win64="yes"
                         Result="value" />
    <!-- VSTO runtime presence (DWORD Install=1), search both registry views -->
    <util:RegistrySearch Id="VstoInstallSearch32"
                         Variable="VstoInstall32"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\VSTO Runtime Setup\v4"
                         Value="Install"
                         Result="value" />
    <util:RegistrySearch Id="VstoInstallSearch64"
                         Variable="VstoInstall64"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\VSTO Runtime Setup\v4"
                         Value="Install"
                         Win64="yes"
                         Result="value" />
  </Fragment>
</Wix>
