using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Security; // Added for SecurityException
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web; // For HttpUtility

namespace QuantBoost_Shared.Utilities
{
    public class MagicLinkAuthReceiver : IDisposable
    {
        private readonly HttpListener _listener;
        private readonly TaskCompletionSource<Dictionary<string, string>> _tcs;
        private readonly string _expectedState; // Optional: for CSRF protection
        private CancellationTokenSource _cts;

        public string ListeningUrl { get; private set; }

        public MagicLinkAuthReceiver(string listeningHost = "localhost", int port = 0, string expectedState = null)
        {
            _tcs = new TaskCompletionSource<Dictionary<string, string>>();
            _expectedState = expectedState;

            if (port == 0) // 0 means find a free port
            {
                port = GetRandomUnusedPort();
            }
            ListeningUrl = $"http://{listeningHost}:{port}/auth-callback/";

            _listener = new HttpListener();
            _listener.Prefixes.Add(ListeningUrl);
        }

        private static int GetRandomUnusedPort()
        {
            var listener = new System.Net.Sockets.TcpListener(IPAddress.Loopback, 0);
            listener.Start();
            int port = ((IPEndPoint)listener.LocalEndpoint).Port;
            listener.Stop();
            return port;
        }

        public Task<Dictionary<string, string>> StartListeningAsync(CancellationTokenSource cts = null)
        {
            _cts = cts ?? new CancellationTokenSource();
            if (_cts.Token.IsCancellationRequested)
            {
                _tcs.TrySetCanceled();
                return _tcs.Task;
            }

            try
            {
                _listener.Start();
                Debug.WriteLine($"[MagicLinkAuthReceiver] Listener started on {ListeningUrl}");

                // Register cancellation
                _cts.Token.Register(() =>
                {
                    Debug.WriteLine("[MagicLinkAuthReceiver] Cancellation requested via CancellationToken.");
                    if (!_tcs.Task.IsCompleted) // Try to cancel TCS only if not already completed
                    {
                        if (_tcs.TrySetCanceled()) Debug.WriteLine("[MagicLinkAuthReceiver] TCS set to Canceled due to token.");
                    }
                    StopListening(); // Ensure listener is stopped on cancellation
                });
                
                // Asynchronously run the listening loop
                Task.Run(ListenLoopAsync); 
            }
            catch (HttpListenerException ex)
            {
                Debug.WriteLine($"[MagicLinkAuthReceiver] HttpListenerException (often due to port conflict or permissions): {ex.Message}");
                _tcs.TrySetException(new InvalidOperationException($"Failed to start listener on {ListeningUrl}. Port might be in use or admin rights required.", ex));
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[MagicLinkAuthReceiver] Error starting listener: {ex.Message}");
                _tcs.TrySetException(ex);
            }
            return _tcs.Task;
        }

        private async Task ListenLoopAsync()
        {
            Debug.WriteLine("[MagicLinkAuthReceiver.ListenLoopAsync] Starting listen loop.");
            try
            {
                while (_listener.IsListening && !_tcs.Task.IsCompleted && !_cts.IsCancellationRequested)
                {
                    HttpListenerContext context = null;
                    try
                    {
                        context = await _listener.GetContextAsync().ConfigureAwait(false);
                    }
                    catch (HttpListenerException hlex)
                    {
                        Debug.WriteLine($"[MagicLinkAuthReceiver.ListenLoopAsync] HttpListenerException in GetContextAsync (listener likely stopped or aborted): {hlex.Message}, ErrorCode: {hlex.ErrorCode}");
                        if (_cts.IsCancellationRequested && !_tcs.Task.IsCompleted) _tcs.TrySetCanceled();
                        break; 
                    }
                    catch (ObjectDisposedException odex)
                    {
                        Debug.WriteLine($"[MagicLinkAuthReceiver.ListenLoopAsync] ObjectDisposedException in GetContextAsync (listener disposed): {odex.Message}");
                        if (_cts.IsCancellationRequested && !_tcs.Task.IsCompleted) _tcs.TrySetCanceled();
                        break; 
                    }
                    // Other exceptions will propagate to the outer catch.

                    if (context != null)
                    {
                        // Await HandleHttpRequestAsync to process the request.
                        // Its finally block will conditionally call StopListening().
                        // If StopListening() is called, _listener.IsListening will become false,
                        // and the loop will terminate on the next iteration.
                        await HandleHttpRequestAsync(context).ConfigureAwait(false);
                    }
                }

                // After loop, if cancellation was requested and task not done, set TCS to canceled.
                if (_cts.IsCancellationRequested && !_tcs.Task.IsCompleted)
                {
                    _tcs.TrySetCanceled();
                }
            }
            catch (Exception ex) 
            {
                Debug.WriteLine($"[MagicLinkAuthReceiver.ListenLoopAsync] Unhandled exception in listen loop: {ex.ToString()}");
                if (!_tcs.Task.IsCompleted) _tcs.TrySetException(ex);
            }
            finally
            {
                Debug.WriteLine("[MagicLinkAuthReceiver.ListenLoopAsync] Listen loop finished.");
                if (_listener.IsListening)
                {
                    Debug.WriteLine("[MagicLinkAuthReceiver.ListenLoopAsync] Listener found active in finally block, ensuring it's stopped.");
                    StopListening();
                }
                // If the loop exited for any reason and the TCS is still not completed,
                // attempt to set it to an appropriate state.
                if (!_tcs.Task.IsCompleted)
                {
                     if (_cts.IsCancellationRequested) {
                        _tcs.TrySetCanceled();
                     } else {
                        _tcs.TrySetException(new InvalidOperationException("Listening loop terminated without completing the authentication task."));
                     }
                }
            }
        }

        // Changed signature: HttpListenerContext is passed directly
        private async Task HandleHttpRequestAsync(HttpListenerContext context)
        {
            // HttpListenerContext context = null; // REMOVED - context is now a parameter
            // bool isJsRedirect = false; // This was already hoisted correctly

            // try block starts here in the original code            // Hoist isJsRedirect and hasTokensInQuery to be accessible in the finally block
            bool isJsRedirect = false; 
            bool hasTokensInQuery = false;

            try
            {
                // If cancellation is requested before we even get the context, exit.
                // This check might be less critical now as context is passed in,
                // but doesn't hurt if HandleHttpRequestAsync is called when cancellation already pending.
                if (_cts.IsCancellationRequested)
                {
                    if (_tcs.TrySetCanceled()) Debug.WriteLine($"[MagicLinkAuthReceiver] TCS set to Canceled (pre-context in HandleHttpRequestAsync). Task completed: {_tcs.Task.IsCompleted}");
                    context?.Response.Close(); // Ensure response is closed if we bail early
                    return;
                }

                // context = await getContextTask.ConfigureAwait(false); // REMOVED - context is now a parameter
                if (context == null) // Should not happen if called from ListenLoopAsync correctly
                {
                    if (!_tcs.Task.IsCompleted)
                    {
                         if (_tcs.TrySetException(new InvalidOperationException("HandleHttpRequestAsync received null context."))) Debug.WriteLine($"[MagicLinkAuthReceiver] TCS set to Exception (null context in HandleHttpRequestAsync). Task completed: {_tcs.Task.IsCompleted}");
                    }
                    return;
                }

                var request = context.Request;
                var response = context.Response;
                string responseHtml = string.Empty;
                var tokens = new Dictionary<string, string>();

                // If cancellation is requested after getting context but before processing.
                if (_cts.IsCancellationRequested)
                {
                    if (_tcs.TrySetCanceled()) Debug.WriteLine($"[MagicLinkAuthReceiver] TCS set to Canceled (post-context). Task completed: {_tcs.Task.IsCompleted}");
                    // response will be closed in finally
                    return;
                }
                  Debug.WriteLine($"[MagicLinkAuthReceiver] Received request: {request.Url}");

                // Parse query parameters to check for tokens or redirect flag
                var queryParams = request.Url != null && !string.IsNullOrEmpty(request.Url.Query) 
                    ? HttpUtility.ParseQueryString(request.Url.Query) 
                    : new System.Collections.Specialized.NameValueCollection();                // Check if this is the second request (from our JS redirect) OR if tokens are directly in query params
                isJsRedirect = queryParams.Get("from_js_redirect") == "true";
                hasTokensInQuery = !string.IsNullOrEmpty(queryParams.Get("access_token"));if (isJsRedirect || hasTokensInQuery)
                {
                    Debug.WriteLine($"[MagicLinkAuthReceiver] Token-bearing request detected. isJsRedirect: {isJsRedirect}, hasTokensInQuery: {hasTokensInQuery}");
                    // Don't redefine queryParams - use the one already parsed above
                    string receivedState = queryParams.Get("state");

                    if (!string.IsNullOrEmpty(_expectedState) && _expectedState != receivedState)
                    {
                        Debug.WriteLine($"[MagicLinkAuthReceiver] State mismatch. Expected: '{_expectedState}', Received: '{receivedState}'");
                        responseHtml = HtmlResponses.ErrorPage("Authentication failed: Invalid state parameter.");
                        if (_tcs.TrySetException(new SecurityException("State parameter mismatch."))) Debug.WriteLine($"[MagicLinkAuthReceiver] TCS set to Exception (state mismatch). Task completed: {_tcs.Task.IsCompleted}");
                    }
                    else
                    {
                        string accessToken = queryParams.Get("access_token");
                        string refreshToken = queryParams.Get("refresh_token");
                        // Supabase might also return error, error_code, error_description in query on failed magic link verification
                        string error = queryParams.Get("error");
                        string errorDescription = queryParams.Get("error_description");

                        if (!string.IsNullOrEmpty(error))
                        {
                            Debug.WriteLine($"[MagicLinkAuthReceiver] Error received from Supabase redirect: {error} - {errorDescription}");
                            responseHtml = HtmlResponses.ErrorPage($"Authentication failed: {errorDescription} ({error})");
                            if (_tcs.TrySetException(new Exception($"Supabase authentication error: {errorDescription} ({error})"))) Debug.WriteLine($"[MagicLinkAuthReceiver] TCS set to Exception (Supabase error). Task completed: {_tcs.Task.IsCompleted}");
                        }
                        else if (!string.IsNullOrEmpty(accessToken)) // refreshToken might be optional for some flows, but access_token is key
                        {
                            tokens["access_token"] = accessToken;
                            if (!string.IsNullOrEmpty(refreshToken)) tokens["refresh_token"] = refreshToken;
                            tokens["expires_in"] = queryParams.Get("expires_in");
                            tokens["token_type"] = queryParams.Get("token_type");
                            if(receivedState != null) tokens["state"] = receivedState;

                            Debug.WriteLine($"[MagicLinkAuthReceiver] Tokens received via JS redirect. AccessToken: {!string.IsNullOrEmpty(accessToken)}, RefreshToken: {!string.IsNullOrEmpty(refreshToken)}");
                            responseHtml = HtmlResponses.SuccessPage;
                            if (_tcs.TrySetResult(tokens)) Debug.WriteLine($"[MagicLinkAuthReceiver] TCS set to Result. Task completed: {_tcs.Task.IsCompleted}");
                        }
                        else
                        {
                            Debug.WriteLine("[MagicLinkAuthReceiver] Access token not found in query parameters after JS redirect.");
                            responseHtml = HtmlResponses.ErrorPage("Authentication failed: Access token not found after redirect.");
                            if (_tcs.TrySetException(new Exception("Required access_token not found in callback after JS redirect."))) Debug.WriteLine($"[MagicLinkAuthReceiver] TCS set to Exception (token not found). Task completed: {_tcs.Task.IsCompleted}");
                        }
                    }
                }
                else
                {
                    // This is the first request, tokens are expected in the fragment.
                    // Serve the HTML page that will use JavaScript to read the fragment and redirect.
                    Debug.WriteLine("[MagicLinkAuthReceiver] First request. Serving JS redirect page to capture fragment.");
                    responseHtml = HtmlResponses.FragmentRedirectPage;
                    // We don't complete the TCS here; it will be completed by the second request.
                    // Listener must remain open.
                }

                byte[] buffer = Encoding.UTF8.GetBytes(responseHtml);
                response.ContentType = "text/html"; // Set ContentType before ContentLength64
                response.ContentLength64 = buffer.Length;
                using (Stream output = response.OutputStream)
                {
                    await output.WriteAsync(buffer, 0, buffer.Length).ConfigureAwait(false);
                }
            }
            catch (ObjectDisposedException) when (!_listener.IsListening && (_cts?.IsCancellationRequested ?? false))
            {
                Debug.WriteLine("[MagicLinkAuthReceiver] Listener was stopped during request handling (likely due to cancellation).");
                if (_tcs.TrySetCanceled()) Debug.WriteLine($"[MagicLinkAuthReceiver] TCS set to Canceled (ObjectDisposedException). Task completed: {_tcs.Task.IsCompleted}");
            }
            catch (HttpListenerException hlex) when (hlex.ErrorCode == 995 && (_cts?.IsCancellationRequested ?? false)) // ERROR_OPERATION_ABORTED due to cancellation
            {
                 Debug.WriteLine($"[MagicLinkAuthReceiver] HttpListenerException (ERROR_OPERATION_ABORTED due to cancellation): {hlex.Message}");
                 if (_tcs.TrySetCanceled()) Debug.WriteLine($"[MagicLinkAuthReceiver] TCS set to Canceled (HttpListenerException 995). Task completed: {_tcs.Task.IsCompleted}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[MagicLinkAuthReceiver] Error handling request: {ex.ToString()}");
                if (!_tcs.Task.IsCompleted)
                {
                    if (_tcs.TrySetException(ex)) Debug.WriteLine($"[MagicLinkAuthReceiver] TCS set to Exception (general catch). Task completed: {_tcs.Task.IsCompleted}");
                }
                
                // Check if we can still write to the response stream.
                // Avoid writing if response is already closed or headers potentially sent.
                // OutputStream.CanWrite is a good general check here.
                if (context?.Response != null && context.Response.OutputStream.CanWrite)
                {
                    try
                    {
                        var errorHtml = HtmlResponses.ErrorPage($"An internal error occurred: {ex.Message}");
                        byte[] buffer = Encoding.UTF8.GetBytes(errorHtml);
                        context.Response.ContentType = "text/html";
                        context.Response.ContentLength64 = buffer.Length;
                        using (Stream output = context.Response.OutputStream)
                        {
                            await output.WriteAsync(buffer, 0, buffer.Length).ConfigureAwait(false);
                        }
                    } catch (Exception responseEx) {
                        Debug.WriteLine($"[MagicLinkAuthReceiver] Could not send error response to browser: {responseEx.Message}");
                    }
                }
            }
            finally
            {
                context?.Response.Close();                // Conditionally stop the listener
                // Stop if tokens were processed (either via JS redirect OR direct query params) OR if the overall task is already completed
                // This ensures the listener stays open for the JS redirect after the first request,
                // unless the task was already resolved due to an early error/cancellation.
                if (isJsRedirect || hasTokensInQuery || (_tcs.Task.IsCompleted))
                {
                    Debug.WriteLine($"[MagicLinkAuthReceiver] Conditionally stopping listener. isJsRedirect: {isJsRedirect}, hasTokensInQuery: {hasTokensInQuery}, TaskIsCompleted: {_tcs.Task.IsCompleted}, TaskStatus: {_tcs.Task.Status}");
                    StopListening();
                }
                else
                {
                    Debug.WriteLine($"[MagicLinkAuthReceiver] Listener kept open for JS redirect. isJsRedirect: {isJsRedirect}, hasTokensInQuery: {hasTokensInQuery}, TaskIsCompleted: {_tcs.Task.IsCompleted}, TaskStatus: {_tcs.Task.Status}");
                }
            }
        }

        public void StopListening()
        {
            try
            {
                if (_listener != null && _listener.IsListening)
                {
                    Debug.WriteLine("[MagicLinkAuthReceiver] Stopping listener.");
                    _listener.Stop();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[MagicLinkAuthReceiver] Error stopping listener: {ex.Message}");
            }
        }

        public void Dispose()
        {
            StopListening();
            (_listener as IDisposable)?.Dispose();
            _cts?.Dispose();
        }

        private static class HtmlResponses
        {
            public const string SuccessPage = @"
                <!DOCTYPE html><html><head><title>Auth Success</title><style>body{font-family:sans-serif;display:flex;justify-content:center;align-items:center;height:90vh;margin:0;background-color:#f0f0f0;} .container{text-align:center;padding:20px;background-color:white;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,0.1);}</style></head>
                <body><div class='container'><h1>Authentication Successful!</h1><p>You can now close this browser window and return to the QuantBoost add-in.</p><p><small>This window should close automatically shortly.</small></p></div>
                <script>setTimeout(() => window.close(), 1500);</script></body></html>";

            public static string ErrorPage(string message) => $@"
                <!DOCTYPE html><html><head><title>Auth Error</title><style>body{{font-family:sans-serif;display:flex;justify-content:center;align-items:center;height:90vh;margin:0;background-color:#f0f0f0;}} .container{{text-align:center;padding:20px;background-color:white;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,0.1);}} .error{{color:red;}}</style></head>
                <body><div class='container'><h1>Authentication Failed</h1><p class='error'>{HttpUtility.HtmlEncode(message)}</p><p>Please close this window and try again from the QuantBoost add-in. If the problem persists, contact support.</p></div></body></html>";
            
            public const string FragmentRedirectPage = @"
                <!DOCTYPE html><html><head><title>Processing Authentication...</title><style>body{font-family:sans-serif;display:flex;justify-content:center;align-items:center;height:90vh;margin:0;background-color:#f0f0f0;} .container{text-align:center;padding:20px;background-color:white;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,0.1);}</style></head>
                <body><div class='container'><h1>Processing Authentication...</h1><p>Please wait while we securely process your authentication details.</p></div>
                <script>
                    (function() {{
                        if (window.location.hash) {{
                            const fragment = window.location.hash.substring(1); // Remove #
                            const params = new URLSearchParams(fragment);
                            if (params.has('access_token')) {{
                                // Reconstruct the URL with fragment parameters as query parameters
                                const newUrl = window.location.pathname + '?' + fragment + '&from_js_redirect=true';
                                window.location.href = newUrl;
                            }} else {{
                                document.body.innerHTML = '<div class=\'container\'><h1>Authentication Error</h1><p style=\'color:red;\'>Required token not found in URL fragment.</p><p>Please close this window and try again.</p></div>';
                            }}
                        }} else {{
                            document.body.innerHTML = '<div class=\'container\'><h1>Authentication Error</h1><p style=\'color:red;\'>No authentication details found in URL.</p><p>Please close this window and try again.</p></div>';
                        }}
                    }})();
                </script></body></html>";
        }
    }
}