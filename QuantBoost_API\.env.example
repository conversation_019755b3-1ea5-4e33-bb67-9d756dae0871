# This file is a template for developers.
# It should be copied to a .env file for local development.
# The .env file is ignored by Git and should NEVER be committed.

# Supabase Configuration
# Get these from your Supabase project settings
SUPABASE_URL="https://your-project-ref.supabase.co"
SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key" # Only for backend

# Stripe Configuration
# Get these from your Stripe dashboard
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# API Server Configuration
PORT=3001
CORS_ORIGIN="http://localhost:3000"
MASTER_API_KEY="a-secure-random-string-for-internal-api-calls"

# JWT Secret (generate a strong random string)
JWT_SECRET="your-super-secret-jwt-string"
