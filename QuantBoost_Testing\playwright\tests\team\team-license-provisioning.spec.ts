import { test, expect } from '../../fixtures/webhook-testing.fixture';
import { WebhookTestFactory, simulateStripeWebhook, WebhookDatabaseVerifier } from '../../fixtures/webhook-testing.fixture';
import { randomUUID } from 'crypto';

/**
 * Team License Provisioning Test
 *
 * Verifies that when a subscription with quantity > 1 is processed via
 * customer.subscription.created (or checkout.session.completed if needed),
 * licenses are created with:
 *  - Count == quantity
 *  - status == 'inactive' (team licenses start unassigned)
 *  - team_admin populated with purchaser email
 *  - individual license email field null (unassigned)
 */

 test.describe('👥 Team Subscription License Provisioning', () => {
  let testCustomer: any; let testProfile: any;

  test.beforeEach(async ({ supabaseClient, stripe }) => {
    if (!stripe) test.skip(true, 'Stripe key not configured');

    // Real customer for realism
    const real = await stripe!.customers.create({
      email: `teamtest_${Date.now()}_${Math.random().toString(36).slice(2)}@quantboost-test.com`,
      name: 'Team Plan Customer'
    });
    testCustomer = { id: real.id, email: real.email };

    const testUserId = randomUUID();
    const { data: authUser, error: authError } = await supabaseClient.auth.admin.createUser({
      id: testUserId,
      email: testCustomer.email,
      email_confirm: true
    });
    if (authError) throw new Error('Auth user creation failed: ' + authError.message);

    const profileData = {
      id: testUserId,
      email: testCustomer.email,
      first_name: 'Team',
      last_name: 'Owner',
      stripe_customer_id: testCustomer.id,
      is_team_admin: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: profile, error } = await supabaseClient.from('profiles').upsert(profileData).select().single();
    if (error) throw new Error('Profile creation failed: ' + error.message);
    testProfile = profile;
  });

  test('🚀 creates inactive team licenses matching quantity', async ({ supabaseClient, stripe }) => {
    const quantity = 4; // simulate a 4-seat team plan
    const subscription = await WebhookTestFactory.createRealSubscription(stripe, 'price_1RCQeBE6FvhUKV1bUN94Oihf', quantity, {}, testCustomer.id);

    // Simulate subscription.created webhook
    const result = await simulateStripeWebhook('customer.subscription.created', subscription);
    expect(result.status).toBe(200);

    // Verify subscription record
    const dbSub = await WebhookDatabaseVerifier.waitForDatabaseUpdate(() => WebhookDatabaseVerifier.verifySubscriptionCreated(supabaseClient, subscription.id));
  // Enforce exact expected quantity now that enhanced diagnostics are deployed
  expect(dbSub.quantity).toBe(quantity);
    expect(dbSub.status).toBeDefined();
    // Attempt to fetch licenses (they may be created during checkout.session.completed instead in prod)
    let { data: licenses, error: licErr } = await supabaseClient
      .from('licenses')
      .select('*')
      .eq('subscription_id', dbSub.id);

    // If none created after subscription.created, trigger a synthetic checkout.session.completed
    if (!licErr && licenses && licenses.length === 0) {
      const checkoutSession = {
        id: `cs_test_${Date.now()}`,
        object: 'checkout.session',
        subscription: subscription.id,
        customer: subscription.customer,
        customer_details: { email: testCustomer.email, name: 'Team Plan Customer' },
        payment_status: 'paid',
        status: 'complete',
        mode: 'subscription',
        amount_total: 120000,
        currency: 'usd',
        created: Math.floor(Date.now() / 1000)
      } as any;
      const checkoutResult = await simulateStripeWebhook('checkout.session.completed', checkoutSession);
      expect(checkoutResult.status, 'checkout.session.completed webhook should succeed').toBe(200);

      // Re-fetch licenses after triggering checkout session
      ({ data: licenses, error: licErr } = await supabaseClient
        .from('licenses')
        .select('*')
        .eq('subscription_id', dbSub.id));
    }

    expect(licErr).toBeNull();
    expect(licenses, 'licenses array present').toBeTruthy();
  expect(licenses!.length).toBe(quantity);

    // All should be inactive & unassigned with team_admin set
    for (const lic of licenses!) {
      expect(lic.status).toBe('inactive');
      expect(lic.team_admin).toBe(testCustomer.email);
      expect(lic.email).toBeNull();
      expect(lic.license_tier).toContain('Team');
    }
  });
});
