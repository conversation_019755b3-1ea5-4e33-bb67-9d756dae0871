using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Input;
using QuantBoost_Powerpoint_Addin.Analysis;
using QuantBoost_Powerpoint_Addin.Features.SizeAnalyzer.Models;
using QuantBoost_Powerpoint_Addin.Features.SizeAnalyzer.Mvvm;
using QuantBoost_Shared.Utilities;

namespace QuantBoost_Powerpoint_Addin.Features.SizeAnalyzer.ViewModels
{
    /// <summary>
    /// ViewModel for the PowerPoint Size Analyzer WPF UI.
    /// Implements MVVM pattern with proper data binding and command handling.
    /// </summary>
    public class AnalysisPaneViewModel : INotifyPropertyChanged
    {
        #region Fields
        private readonly AnalysisService _analysisService;
        private CancellationTokenSource _cancellationTokenSource;
        
        private ObservableCollection<SlideAnalysisDisplayItem> _analysisResults;
        private string _statusText;
        private bool _isAnalyzing;
        private int _progressPercent;
        private long _totalSize;
        private string _totalSizeDisplay;
        #endregion

        #region Properties
        /// <summary>
        /// Gets the collection of analysis results for data binding.
        /// </summary>
        public ObservableCollection<SlideAnalysisDisplayItem> AnalysisResults
        {
            get => _analysisResults;
            private set
            {
                if (_analysisResults != value)
                {
                    _analysisResults = value;
                    OnPropertyChanged(nameof(AnalysisResults));
                    OnPropertyChanged(nameof(HasResults));
                }
            }
        }

        /// <summary>
        /// Gets or sets the current status text.
        /// </summary>
        public string StatusText
        {
            get => _statusText;
            set
            {
                if (_statusText != value)
                {
                    _statusText = value;
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// Gets or sets whether analysis is currently running.
        /// </summary>
        public bool IsAnalyzing
        {
            get => _isAnalyzing;
            set
            {
                if (_isAnalyzing != value)
                {
                    _isAnalyzing = value;
                    OnPropertyChanged(nameof(IsAnalyzing));
                    OnPropertyChanged(nameof(IsNotAnalyzing));
                    
                    // Update command states
                    if (AnalyzeCommand is RelayCommand analyzeCmd)
                        analyzeCmd.RaiseCanExecuteChanged();
                    if (ExportCommand is RelayCommand exportCmd)
                        exportCmd.RaiseCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// Gets whether analysis is not currently running (for UI binding).
        /// </summary>
        public bool IsNotAnalyzing => !IsAnalyzing;

        /// <summary>
        /// Gets or sets the current progress percentage.
        /// </summary>
        public int ProgressPercent
        {
            get => _progressPercent;
            set
            {
                if (_progressPercent != value)
                {
                    _progressPercent = value;
                    OnPropertyChanged(nameof(ProgressPercent));
                }
            }
        }

        /// <summary>
        /// Gets or sets the total size in bytes.
        /// </summary>
        public long TotalSize
        {
            get => _totalSize;
            set
            {
                if (_totalSize != value)
                {
                    _totalSize = value;
                    OnPropertyChanged(nameof(TotalSize));
                    UpdateTotalSizeDisplay();
                }
            }
        }

        /// <summary>
        /// Gets the formatted total size display string.
        /// </summary>
        public string TotalSizeDisplay
        {
            get => _totalSizeDisplay;
            private set
            {
                if (_totalSizeDisplay != value)
                {
                    _totalSizeDisplay = value;
                    OnPropertyChanged(nameof(TotalSizeDisplay));
                }
            }
        }

        /// <summary>
        /// Gets whether there are analysis results available.
        /// </summary>
        public bool HasResults => AnalysisResults?.Count > 0;

        /// <summary>
        /// Gets the command to start/cancel analysis.
        /// </summary>
        public ICommand AnalyzeCommand { get; }

        /// <summary>
        /// Gets the command to export results to CSV.
        /// </summary>
        public ICommand ExportCommand { get; }
        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the AnalysisPaneViewModel class.
        /// </summary>
        public AnalysisPaneViewModel()
        {
            _analysisService = new AnalysisService();
            AnalysisResults = new ObservableCollection<SlideAnalysisDisplayItem>();
            StatusText = "Ready to analyze.";
            TotalSizeDisplay = "Total Size: N/A";
            
            AnalyzeCommand = new RelayCommand(
                execute: async _ => await ExecuteAnalyzeAsync(),
                canExecute: _ => !IsAnalyzing
            );

            ExportCommand = new RelayCommand(
                execute: _ => ExecuteExport(),
                canExecute: _ => HasResults && !IsAnalyzing
            );
        }
        #endregion

        #region Methods
        /// <summary>
        /// Executes the analysis operation.
        /// </summary>
        private async Task ExecuteAnalyzeAsync()
        {
            if (IsAnalyzing)
            {
                // Cancel current analysis
                _cancellationTokenSource?.Cancel();
                return;
            }

            IsAnalyzing = true;
            AnalysisResults.Clear();
            TotalSize = 0;
            ProgressPercent = 0;

            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = new CancellationTokenSource();

            try
            {
                var progress = new Progress<ProgressState>(state =>
                {
                    StatusText = state.CurrentStep ?? "Processing...";
                    ProgressPercent = state.PercentComplete;
                });

                var (slideSummaries, overallSummary) = await _analysisService.AnalyzePresentationAsync(
                    string.Empty, // Path not used in new implementation
                    progress, 
                    _cancellationTokenSource.Token);

                // Post-process to calculate percentages and convert to display items
                long total = slideSummaries.Sum(s => s.SizeBytes);
                TotalSize = total;

                foreach (var summary in slideSummaries.OrderByDescending(s => s.SizeBytes))
                {
                    var displayItem = ConvertToDisplayItem(summary, total);
                    AnalysisResults.Add(displayItem);
                }

                // Manually trigger property change notifications after populating the collection
                OnPropertyChanged(nameof(HasResults));
                if (ExportCommand is RelayCommand exportCmd)
                    exportCmd.RaiseCanExecuteChanged();

                StatusText = $"Analysis complete. Found {slideSummaries.Count} slides with proportional size allocation.";
                ProgressPercent = 100;
            }
            catch (OperationCanceledException)
            {
                StatusText = "Analysis cancelled.";
                ProgressPercent = 0;
            }
            catch (Exception ex)
            {
                StatusText = $"Error: {ex.Message}";
                ProgressPercent = 0;
            }
            finally
            {
                IsAnalyzing = false;
            }
        }

        /// <summary>
        /// Converts a SlideAnalysisSummary to a display item.
        /// </summary>
        private SlideAnalysisDisplayItem ConvertToDisplayItem(SlideAnalysisSummary summary, long totalSize)
        {
            string contentType = DetermineContentType(summary);
            double percentage = totalSize > 0 ? ((double)summary.SizeBytes / totalSize * 100) : 0;

            return new SlideAnalysisDisplayItem
            {
                SlideName = summary.Title,
                ContentType = contentType,
                SizeBytes = summary.SizeBytes,
                SizePercentage = percentage,
                ContentDetails = $"Images: {summary.ImageCount}, Charts: {summary.ChartCount}, Media: {summary.MediaCount}, Objects: {summary.EmbeddedObjectCount}, Links: {summary.ExcelLinkCount}",
                ImageCount = summary.ImageCount,
                ChartCount = summary.ChartCount,
                MediaCount = summary.MediaCount,
                EmbeddedObjectCount = summary.EmbeddedObjectCount,
                ExcelLinkCount = summary.ExcelLinkCount
            };
        }

        /// <summary>
        /// Determines the content type description for a slide.
        /// </summary>
        private string DetermineContentType(SlideAnalysisSummary summary)
        {
            if (summary.MediaCount > 0) return "Media-Rich";
            if (summary.ImageCount > 0) return "Image-Heavy";
            if (summary.ChartCount > 0) return "Chart-Based";
            if (summary.EmbeddedObjectCount > 0) return "Object-Rich";
            if (summary.ExcelLinkCount > 0) return "Data-Linked";
            return summary.HasContent ? "Content" : "Minimal";
        }

        /// <summary>
        /// Executes the export operation.
        /// </summary>
        private void ExecuteExport()
        {
            try
            {
                // Show save file dialog
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*";
                    saveDialog.Title = "Export Analysis Results";
                    saveDialog.FileName = $"PresentationAnalysis_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        ExportToCsv(saveDialog.FileName);
                        MessageBox.Show($"Analysis results exported successfully to:\n{saveDialog.FileName}",
                                      "Export Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error exporting results: {ex.Message}", "Export Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Exports the analysis results to a CSV file.
        /// </summary>
        private void ExportToCsv(string filePath)
        {
            using (var writer = new System.IO.StreamWriter(filePath))
            {
                // Write header
                writer.WriteLine("Slide Name,Content Type,Size (KB),Percentage,Images,Charts,Media,Objects,Excel Links");

                // Write data rows
                foreach (var item in AnalysisResults)
                {
                    writer.WriteLine($"\"{item.SlideName}\",\"{item.ContentType}\",{item.SizeKB:F1},{item.SizePercentage:F1}%,{item.ImageCount},{item.ChartCount},{item.MediaCount},{item.EmbeddedObjectCount},{item.ExcelLinkCount}");
                }
            }
        }

        /// <summary>
        /// Updates the total size display string.
        /// </summary>
        private void UpdateTotalSizeDisplay()
        {
            if (TotalSize > 0)
            {
                double sizeKB = TotalSize / 1024.0;
                double sizeMB = sizeKB / 1024.0;
                
                if (sizeMB >= 1.0)
                    TotalSizeDisplay = $"Total Size: {sizeMB:F1} MB";
                else
                    TotalSizeDisplay = $"Total Size: {sizeKB:F1} KB";
            }
            else
            {
                TotalSizeDisplay = "Total Size: N/A";
            }
        }
        #endregion

        #region INotifyPropertyChanged Implementation
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion
    }
}
