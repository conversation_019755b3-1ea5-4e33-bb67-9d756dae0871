/**
 * Environment variable configuration for Azure Static Web Apps
 * This module handles environment variable access with fallbacks and validation
 */

// Helper function to get environment variable with fallbacks
function getEnvVar(key: string, fallback?: string): string | undefined {
  // Try different ways to access environment variables in Azure Static Web Apps
  const value = process.env[key] || 
                (typeof window !== 'undefined' ? undefined : process.env[key]) ||
                fallback;
  
  return value;
}

// Validate and export environment variables
export const ENV = {
  // Stripe Configuration
  STRIPE_SECRET_KEY: getEnvVar('STRIPE_SECRET_KEY'),
  STRIPE_WEBHOOK_SECRET: getEnvVar('STRIPE_WEBHOOK_SECRET'),
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: getEnvVar('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'),
  
  // Supabase Configuration
  NEXT_PUBLIC_SUPABASE_URL: getEnvVar('NEXT_PUBLIC_SUPABASE_URL'),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: getEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
  SUPABASE_SERVICE_KEY: getEnvVar('SUPABASE_SERVICE_KEY'),
  
  // Other Configuration
  NEXT_PUBLIC_AZURE_API_URL: getEnvVar('NEXT_PUBLIC_AZURE_API_URL'),
  NEXT_PUBLIC_BASE_URL: getEnvVar('NEXT_PUBLIC_BASE_URL'),
  NODE_ENV: getEnvVar('NODE_ENV', 'development'),
};

// Validation functions
export function validateStripeConfig(): { isValid: boolean; missing: string[] } {
  const required = ['STRIPE_SECRET_KEY', 'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'];
  const missing = required.filter(key => !ENV[key as keyof typeof ENV]);
  
  return {
    isValid: missing.length === 0,
    missing
  };
}

export function validateSupabaseConfig(): { isValid: boolean; missing: string[] } {
  const required = ['NEXT_PUBLIC_SUPABASE_URL', 'NEXT_PUBLIC_SUPABASE_ANON_KEY', 'SUPABASE_SERVICE_KEY'];
  const missing = required.filter(key => !ENV[key as keyof typeof ENV]);
  
  return {
    isValid: missing.length === 0,
    missing
  };
}

// Debug function
export function getEnvironmentDebugInfo() {
  return {
    nodeEnv: ENV.NODE_ENV,
    hasStripeSecret: !!ENV.STRIPE_SECRET_KEY,
    hasStripePublishable: !!ENV.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
    hasSupabaseUrl: !!ENV.NEXT_PUBLIC_SUPABASE_URL,
    hasSupabaseAnon: !!ENV.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    hasSupabaseService: !!ENV.SUPABASE_SERVICE_KEY,
    stripeValidation: validateStripeConfig(),
    supabaseValidation: validateSupabaseConfig(),
    timestamp: new Date().toISOString()
  };
}
