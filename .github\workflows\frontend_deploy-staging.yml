name: Deploy Frontend to Azure App Service (Staging)

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options: [staging, production]
      deploy_reason:
        description: 'Reason for deployment'
        required: false
        default: 'Manual deployment'
        type: string
      skip_build:
        description: 'Skip local build and reuse existing frontend_dist'
        required: false
        default: false
        type: boolean

env:
  AZURE_WEBAPP_NAME: app-quantboost-frontend-staging
  AZURE_WEBAPP_PACKAGE_PATH: ./frontend_dist
  NODE_VERSION: 22
  AZURE_WEBAPP_RESOURCE_GROUP: rg-quantboost-frontend-staging

# OIDC & IP sync removed: relying on /api/health + optional header bypass (X-GHA-Bypass)
permissions:
  contents: read

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deployment Info
        run: |
          echo "🚀 Manual frontend deployment triggered"
          echo "📍 Environment: ${{ github.event.inputs.environment }}"
          echo "📝 Reason: ${{ github.event.inputs.deploy_reason }}"
          echo "👤 Triggered by: ${{ github.actor }}"
          echo "📦 Commit: ${{ github.sha }}"
          echo "⏭️ Skip build: ${{ github.event.inputs.skip_build }}"

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        if: ${{ github.event.inputs.skip_build != 'true' }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm
          cache-dependency-path: QuantBoost_Frontend/package-lock.json

  # Removed Azure OIDC login (no IP sync). Deployment uses publish profile only.


      - name: Install dependencies
        if: ${{ github.event.inputs.skip_build != 'true' }}
        run: |
          cd QuantBoost_Frontend
          npm ci

      - name: Verify required public secrets present
        if: ${{ github.event.inputs.skip_build != 'true' }}
        shell: bash
        env:
          STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY }}
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        run: |
          set -e
          MISSING=0
          [ -z "$STRIPE_PUBLISHABLE_KEY" ] && echo "Missing STRIPE_PUBLISHABLE_KEY (add publishable key secret)" && MISSING=1 || echo "Found STRIPE_PUBLISHABLE_KEY"
          [ -z "$SUPABASE_URL" ] && echo "Missing SUPABASE_URL" && MISSING=1 || echo "Found SUPABASE_URL"
          [ -z "$SUPABASE_ANON_KEY" ] && echo "Missing SUPABASE_ANON_KEY" && MISSING=1 || echo "Found SUPABASE_ANON_KEY"
          if [ "$MISSING" -eq 1 ]; then
            echo "❌ One or more required public (client) config secrets are missing. Aborting before build." >&2
            exit 1
          fi
          echo "✅ All required public config secrets present."

      - name: Generate .env.production (public config)
        if: ${{ github.event.inputs.skip_build != 'true' }}
        shell: bash
        env:
          STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY }}
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        run: |
          cd QuantBoost_Frontend
          : "Ensure a publishable key secret named STRIPE_PUBLISHABLE_KEY exists (never use secret key here)."
          STRIPE_PK="$STRIPE_PUBLISHABLE_KEY"
          SUPA_URL="$SUPABASE_URL"
          SUPA_ANON="$SUPABASE_ANON_KEY"
          printf "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=%s\n" "$STRIPE_PK" > .env.production
          printf "NEXT_PUBLIC_SUPABASE_URL=%s\n" "$SUPA_URL" >> .env.production
          printf "NEXT_PUBLIC_SUPABASE_ANON_KEY=%s\n" "$SUPA_ANON" >> .env.production
          printf "NEXT_PUBLIC_BASE_URL=%s\n" "https://app-quantboost-frontend-staging.azurewebsites.net" >> .env.production
          printf "NEXT_PUBLIC_AZURE_API_URL=%s\n" "https://ca-quantboost-api.greentree-6ebc0a43.westus3.azurecontainerapps.io" >> .env.production
          printf "QUANTBOOST_ENV=%s\n" "${{ github.event.inputs.environment }}" >> .env.production
          echo "Created .env.production for ${{ github.event.inputs.environment }} deployment:" && sed 's/=.*/=**** (value hidden)/' .env.production

      - name: Build Next.js (standalone)
        if: ${{ github.event.inputs.skip_build != 'true' }}
        shell: bash
        env:
          QUANTBOOST_ENV: ${{ github.event.inputs.environment }}
        run: |
          cd QuantBoost_Frontend
          echo "Building with NODE_ENV=production, QUANTBOOST_ENV=$QUANTBOOST_ENV for environment: ${{ github.event.inputs.environment }}"
          npm run build

      - name: Remove build-time env file
        if: ${{ github.event.inputs.skip_build != 'true' }}
        shell: bash
        run: |
          cd QuantBoost_Frontend
          rm -f .env.production || true
          echo "Removed .env.production to prevent accidental packaging."

      - name: Prune to production dependencies
        if: ${{ github.event.inputs.skip_build != 'true' }}
        run: |
          cd QuantBoost_Frontend
          npm prune --production

      - name: Prepare artifact (frontend_dist)
        if: ${{ github.event.inputs.skip_build != 'true' }}
        run: |
          set -e
          rm -rf frontend_dist
          mkdir -p frontend_dist/.next
          # Copy standalone output INCLUDING hidden .next directory.
          # NOTE: Using '/*' omits dotfiles/dirs like .next. We must copy with trailing '/.'
          # to ensure the internal minimal .next (with BUILD_ID, server files) is present.
          cp -R QuantBoost_Frontend/.next/standalone/. frontend_dist/
          # (Retain explicit static copy for clarity; harmless if already present)
          if [ ! -d frontend_dist/.next/static ]; then
            cp -R QuantBoost_Frontend/.next/static frontend_dist/.next/static || true
          fi
          cp -R QuantBoost_Frontend/public frontend_dist/public
          # Copy root package.json then rewrite
          cp QuantBoost_Frontend/package.json frontend_dist/package.json || true
          if [ -f QuantBoost_Frontend/next.config.ts ]; then cp QuantBoost_Frontend/next.config.ts frontend_dist/; fi
          find frontend_dist -type f -name "*.ts" -not -name "next.config.ts" -delete || true
          echo "Listing root of frontend_dist (first 80 files):";
          find frontend_dist -maxdepth 3 -type f | head -n 80
          # Detect entrypoint (common variants)
          ENTRY=""
          [ -f frontend_dist/server.js ] && ENTRY=server.js || true
            if [ -z "$ENTRY" ] && [ -f frontend_dist/server.cjs ]; then ENTRY=server.cjs; fi
            if [ -z "$ENTRY" ] && [ -f frontend_dist/server/index.js ]; then ENTRY=server/index.js; fi
          if [ -z "$ENTRY" ]; then
            echo "❌ No server.js / server.cjs / server/index.js found in frontend_dist" >&2
            exit 1
          fi
          # Validate Next.js build presence (common failure if .next omitted by copy step)
          if [ ! -f frontend_dist/.next/BUILD_ID ]; then
            echo "❌ Missing .next/BUILD_ID in runtime artifact (standalone copy likely skipped hidden .next)." >&2
            echo "Contents of frontend_dist (debug):"; find frontend_dist -maxdepth 3 -type f | head -n 120
            exit 1
          fi
          if [ ! -d frontend_dist/.next/server ]; then
            echo "❌ Missing .next/server directory in runtime artifact." >&2
            exit 1
          fi
          echo "✅ Validated presence of .next build artifacts (BUILD_ID + server)"
          echo "Detected entrypoint: $ENTRY"
          # Rewrite package.json start script safely (or create minimal if missing)
          if [ -f frontend_dist/package.json ]; then
            node -e "const fs=require('fs');const p='frontend_dist/package.json';const pkg=JSON.parse(fs.readFileSync(p,'utf8'));pkg.scripts=pkg.scripts||{};pkg.scripts.start='node $ENTRY';pkg.private=true;delete pkg.devDependencies;delete pkg.devscripts;fs.writeFileSync(p,JSON.stringify(pkg,null,2));console.log('Updated package.json start script -> node $ENTRY');"
          else
            printf '%s\n' '{' \
            '  "name": "quantboost-frontend-standalone",' \
            '  "version": "0.0.0",' \
            '  "private": true,' \
            '  "engines": { "node": ">=22" },' \
            "  \"scripts\": { \"start\": \"node $ENTRY\" }" \
            '}' > frontend_dist/package.json
            echo 'Created minimal runtime package.json'
          fi
          echo '--- package.json (final) ---'
          cat frontend_dist/package.json
          echo "✅ Artifact assembly complete (start= node $ENTRY)"

      - name: Standalone runtime sanity
        if: ${{ github.event.inputs.skip_build != 'true' }}
        run: |
          node -v
          [ -f frontend_dist/server.js ] && echo "✅ server.js present" || (echo "❌ server.js missing" && exit 1)
          [ -d frontend_dist/node_modules/next ] && echo "✅ next runtime present" || echo "⚠️ next runtime directory not found (may be tree-shaken if fully static)"
          # List key runtime dirs
          find frontend_dist -maxdepth 2 -type f -name server.js

      - name: Verify artifact (skip build path)
        if: ${{ github.event.inputs.skip_build == 'true' }}
        run: |
          if [ ! -d frontend_dist/.next/static ]; then
            echo "❌ frontend_dist missing or not built. Run without skip_build first." >&2
            exit 1
          fi
          echo "✅ Reusing existing artifact." && ls -1 frontend_dist

      - name: Deploy to Azure App Service
        uses: azure/webapps-deploy@v2
        with:
          app-name: ${{ env.AZURE_WEBAPP_NAME }}
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE_STAGING }}
          package: ${{ env.AZURE_WEBAPP_PACKAGE_PATH }}
      - name: Dump deployed package.json (debug)
        run: |
          echo "(Debug) Showing local artifact package.json just deployed:" 
          cat frontend_dist/package.json | sed 's/"dependencies".*/"dependencies": { ... },/' || true

      - name: Deployment Summary
        run: |
          echo "🚀 Frontend deployment completed"
          echo "🌍 Environment: ${{ github.event.inputs.environment }}"
          echo "👤 Deployed by: ${{ github.actor }}"
          echo "🏗️ App Service: ${{ env.AZURE_WEBAPP_NAME }}"
          echo "⏭️ Build skipped: ${{ github.event.inputs.skip_build }}"
          echo "🔗 https://app-quantboost-frontend-staging.azurewebsites.net"
