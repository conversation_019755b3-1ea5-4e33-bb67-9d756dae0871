﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="QuantBoost_Powerpoint_Addin.vsto" version="*******" publicKeyToken="c6092c9ef89afb5c" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="QuantBoost_Powerpoint_Addin" asmv2:product="QuantBoost_Powerpoint_Addin" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.8.1" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="QuantBoost_Powerpoint_Addin.dll.manifest" size="48300">
      <assemblyIdentity name="QuantBoost_Powerpoint_Addin.dll" version="*******" publicKeyToken="c6092c9ef89afb5c" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>YJ+n6+RLCrvg06P71dLaOjXmOnPGSSHv37SqqaBjC14=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=M16R2\danep" issuerKeyHash="582f05a61c8fcb2605b6d499994cc4e005b61052" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>RBmuCEXopjEOhEyTKppQuGAbM5OacUZQ6Olk9AryjPQ=</DigestValue></Reference></SignedInfo><SignatureValue>Wipvcps4w6e/5Vu/QLBh/u28msusamGz33tmDrQwf5SAXqqnaxzsCJlmdcUt/lwyaT0BlyqeMb0FiB0wZ2GHI0QRxRJtyO1x+nQTHYLzJ6FNLVtSzntx+Tl/C6NnmDaCvIRxkpYB0DEX7Yh+VTgTZYA4YYogM+IZIgAXh7tvXVQ=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>urkeVljnx8Q4mQoWMIfwWHGhl8gf3Gh6XjHiKtBdOUnFTJQOFQm5FnqJN/ttKHjS0hdureML04AJfyRo+W2TgNFM+6AU2kCB4kiflUg6A9+NRIUE5wDnzGgVAjHXAwwttrJK6W+MP/7TnAJwLUz2EnGRzjLQd92a2J16iGG3Ozk=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="f48cf20af464e9e85046719a93331b60b8509a2a934c840e31a6e84508ae1944" Description="" Url=""><as:assemblyIdentity name="QuantBoost_Powerpoint_Addin.vsto" version="*******" publicKeyToken="c6092c9ef89afb5c" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=M16R2\danep</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>ACVMRxzAJlVRdEmcbgGTs702EddxeQk0G5z+UuOEnJE=</DigestValue></Reference></SignedInfo><SignatureValue>o5MLM8cqDko1ONa3lOYXOpJCM04+dmU7vBeNYRx9vNySLZv993FrCdRgmIz9bkI5tExa244gpaCvIBzUHHhJ77vj8OaBuNRPTMwKbuTicLBm6o9QXOJM01XuZEffkqic9YCOuOdtNnYd6xYyuYqT9yvDZbPsaVL9R3sHtq3cjew=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>urkeVljnx8Q4mQoWMIfwWHGhl8gf3Gh6XjHiKtBdOUnFTJQOFQm5FnqJN/ttKHjS0hdureML04AJfyRo+W2TgNFM+6AU2kCB4kiflUg6A9+NRIUE5wDnzGgVAjHXAwwttrJK6W+MP/7TnAJwLUz2EnGRzjLQd92a2J16iGG3Ozk=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIBxTCCAS6gAwIBAgIQFG7X9mMlX4NMhpnYtde0iDANBgkqhkiG9w0BAQsFADAhMR8wHQYDVQQDHhYATQAxADYAUgAyAFwAZABhAG4AZQBwMB4XDTI1MDQxNzE5MjcwNloXDTI2MDQxODAxMjcwNlowITEfMB0GA1UEAx4WAE0AMQA2AFIAMgBcAGQAYQBuAGUAcDCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAurkeVljnx8Q4mQoWMIfwWHGhl8gf3Gh6XjHiKtBdOUnFTJQOFQm5FnqJN/ttKHjS0hdureML04AJfyRo+W2TgNFM+6AU2kCB4kiflUg6A9+NRIUE5wDnzGgVAjHXAwwttrJK6W+MP/7TnAJwLUz2EnGRzjLQd92a2J16iGG3OzkCAwEAATANBgkqhkiG9w0BAQsFAAOBgQCIWebVlWri2hRdROSu/IqnmkTk0lIcZPbSoCdaMyBh3BatDofCGDfd5uXPqEOk4kVQV8zXOCs/ihVcEe8SOLQUjGxktNS3dghBwp/RT8RP6qHhGNN4o+nCvwE2Wr38ad3IUJntBS4pdYDGrqT9Aeb4eojUdAxSCG+Snm3Ka7KHhw==</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>