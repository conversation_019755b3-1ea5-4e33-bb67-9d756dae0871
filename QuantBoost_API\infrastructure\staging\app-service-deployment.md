# Azure App Service Deployment Configuration

## Overview
This document provides the configuration needed to deploy the Next.js frontend to Azure App Service B1 tier using GitHub Actions.

## GitHub Actions Workflow Configuration

Create or update `.github/workflows/deploy-frontend-staging.yml`:

```yaml
name: Deploy Frontend to Azure App Service (Staging)

on:
  push:
    branches: [ main ]
    paths: [ 'QuantBoost_Frontend/**' ]
  workflow_dispatch:

env:
  AZURE_WEBAPP_NAME: app-quantboost-frontend-staging
  AZURE_WEBAPP_PACKAGE_PATH: './QuantBoost_Frontend'
  NODE_VERSION: '18'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'QuantBoost_Frontend/package-lock.json'
    
    - name: Install dependencies
      run: |
        cd QuantBoost_Frontend
        npm ci
    
    - name: Build application
      run: |
        cd QuantBoost_Frontend
        npm run build
      env:
        # Build-time environment variables
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY }}
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        NEXT_PUBLIC_BASE_URL: "https://app-quantboost-frontend-staging.azurewebsites.net"
    
    - name: Deploy to Azure App Service
      uses: azure/webapps-deploy@v2
      with:
        app-name: ${{ env.AZURE_WEBAPP_NAME }}
        publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE_STAGING }}
        package: ${{ env.AZURE_WEBAPP_PACKAGE_PATH }}
```

## Required GitHub Secrets

Add these secrets to your GitHub repository:

### Deployment Secrets
- `AZURE_WEBAPP_PUBLISH_PROFILE_STAGING`: Download from Azure Portal → App Service → Get publish profile

### Environment Variable Secrets
- `STRIPE_PUBLISHABLE_KEY`: Your Stripe publishable key
- `STRIPE_SECRET_KEY`: Your Stripe secret key (configured in App Service)
- `STRIPE_WEBHOOK_SECRET`: Your Stripe webhook secret (configured in App Service)
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `SUPABASE_SERVICE_KEY`: Your Supabase service role key (configured in App Service)

## Next.js Configuration Updates

Update `QuantBoost_Frontend/next.config.js`:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Remove standalone output for App Service
  // output: 'standalone', // Remove this line if present
  
  // Configure for Azure App Service
  trailingSlash: false,
  
  // Environment variable handling
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // Headers for API routes
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

## App Service Configuration

The Terraform configuration automatically sets these environment variables in App Service:

### Runtime Environment Variables
- `NODE_ENV=production`
- `STRIPE_SECRET_KEY` (from Terraform variable)
- `STRIPE_WEBHOOK_SECRET` (from Terraform variable)
- `SUPABASE_SERVICE_KEY` (from Terraform variable)

### Public Environment Variables
- `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` (from Terraform variable)
- `NEXT_PUBLIC_SUPABASE_URL` (from Terraform variable)
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` (from Terraform variable)
- `NEXT_PUBLIC_BASE_URL` (automatically set to App Service URL)

## Custom Domain Configuration

To configure a custom domain (e.g., staging.quantboost.ai):

1. **Add custom domain in Azure Portal:**
   ```bash
   az webapp config hostname add \
     --webapp-name app-quantboost-frontend-staging \
     --resource-group rg-quantboost-staging \
     --hostname staging.quantboost.ai
   ```

2. **Configure SSL certificate:**
   ```bash
   az webapp config ssl bind \
     --certificate-thumbprint <thumbprint> \
     --ssl-type SNI \
     --name app-quantboost-frontend-staging \
     --resource-group rg-quantboost-staging
   ```

3. **Update DNS records:**
   - Create CNAME record: `staging.quantboost.ai` → `app-quantboost-frontend-staging.azurewebsites.net`

## Monitoring and Troubleshooting

### Application Insights Integration
- Automatically configured via Terraform
- Connection string set in App Service environment variables
- Logs available in Azure Portal → Application Insights

### Log Streaming
```bash
# View live logs
az webapp log tail \
  --name app-quantboost-frontend-staging \
  --resource-group rg-quantboost-staging
```

### Environment Variable Verification
Create a diagnostic endpoint in your Next.js app:

```javascript
// pages/api/debug/env.js (for testing only)
export default function handler(req, res) {
  if (process.env.NODE_ENV !== 'production') {
    res.status(200).json({
      hasStripeSecret: !!process.env.STRIPE_SECRET_KEY,
      hasSupabaseService: !!process.env.SUPABASE_SERVICE_KEY,
      nodeEnv: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    });
  } else {
    res.status(404).json({ message: 'Not found' });
  }
}
```

## Migration Steps

1. **Apply Terraform changes:**
   ```bash
   cd QuantBoost_API/infrastructure/staging
   terraform plan
   terraform apply
   ```

2. **Update GitHub Actions workflow**
3. **Update Next.js configuration**
4. **Deploy application**
5. **Test payment processing**
6. **Configure custom domain (optional)**

## Cost Monitoring

- App Service B1: ~$13.14/month
- Application Insights: ~$2.30/month (5GB data)
- Total: ~$15.44/month

Monitor costs in Azure Portal → Cost Management + Billing.
