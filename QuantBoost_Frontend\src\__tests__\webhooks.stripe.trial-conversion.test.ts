import { describe, it, expect, vi, beforeEach, afterAll } from 'vitest';

// Mock Supabase client module first (hoisted)
const selectMaybeSingle = vi.fn();
const insertMock = vi.fn();
const updateMock = vi.fn();

vi.mock('@supabase/supabase-js', () => {
  const fromFn = (table: string) => {
    if (table === 'webhook_events') {
      return {
        select: () => ({ eq: () => ({ maybeSingle: selectMaybeSingle }) }),
        insert: insertMock,
        update: updateMock
      } as any;
    }
    if (table === 'enterprise_trials') {
      return {
        update: () => ({ eq: vi.fn().mockResolvedValue({ error: null }) })
      } as any;
    }
    return { select: () => ({ maybeSingle: vi.fn().mockResolvedValue({ data: null, error: null }) }) } as any;
  };
  return {
    createClient: vi.fn().mockReturnValue({
      from: fromFn,
      auth: { admin: { listUsers: vi.fn().mockResolvedValue({ data: { users: [] }, error: null }) } }
    })
  };
});

// Import the route under test after mocks are in place
import { POST as stripeWebhook } from '../app/api/webhooks/stripe/route';

// Minimal mock for env
const OLD_ENV = { ...process.env } as any;

function makeRequest(body: any, headers: Record<string,string> = {}) {
  return new Request('http://localhost/api/webhooks/stripe', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', 'stripe-signature': 'test-signature-bypass', ...headers },
    body: JSON.stringify(body)
  });
}

describe('Stripe Webhook — Enterprise Trial Conversion', () => {
  beforeEach(() => {
    process.env.STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY || 'sk_test_dummy';
    process.env.STRIPE_WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET || 'whsec_dummy';
    process.env.NEXT_PUBLIC_SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://stub.supabase.local';
    process.env.SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || 'service_key_stub';
  });
  afterAll(() => { process.env = OLD_ENV; });

  it('returns 400 on bad signature (no bypass)', async () => {
    const req = new Request('http://localhost/api/webhooks/stripe', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id: 'evt_1', type: 'checkout.session.completed', data: { object: {} }})
    });
    const res = await stripeWebhook(req);
    expect(res.status).toBe(400);
  });

  it('marks enterprise trial converted when metadata.trialId present', async () => {
    // Default: no existing webhook event
    selectMaybeSingle.mockResolvedValue({ data: null, error: null });
    // Build synthetic event with bypass header
    const event = {
      id: 'evt_trial_123',
      type: 'checkout.session.completed',
      data: { object: { id: 'cs_test', metadata: { trialId: 'trial_abc' }, customer: 'cus_X', subscription: 'sub_Y' } },
      created: Math.floor(Date.now() / 1000),
      livemode: false,
      object: 'event',
      pending_webhooks: 0,
      request: { id: null, idempotency_key: null },
  api_version: '2025-08-27.basil'
    };

    const res = await stripeWebhook(makeRequest(event));
    expect(res.status).toBeGreaterThanOrEqual(200);
    expect(res.status).toBeLessThan(300);

    // Duplicate should return 409
    selectMaybeSingle.mockResolvedValueOnce({ data: { id: '1', status: 'processed' }, error: null });
    const dupRes = await stripeWebhook(makeRequest(event));
    expect(dupRes.status).toBe(409);
  });
});
