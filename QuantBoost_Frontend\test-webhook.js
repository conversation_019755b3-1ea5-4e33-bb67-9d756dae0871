#!/usr/bin/env node

/**
 * Webhook Testing Script for QuantBoost
 * 
 * This script helps test webhook endpoints and diagnose issues.
 * Run with: node test-webhook.js
 */

const https = require('https');

const WEBHOOK_BASE_URL = 'https://app-quantboost-frontend-staging.azurewebsites.net';

async function testWebhookHealth() {
  console.log('🔍 Testing webhook health endpoint...');
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'app-quantboost-frontend-staging.azurewebsites.net',
      port: 443,
      path: '/api/webhooks/stripe/health',
      method: 'GET',
      headers: {
        'User-Agent': 'QuantBoost-Webhook-Tester/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`✅ Health check response (${res.statusCode}):`, data);
        resolve({ status: res.statusCode, data: JSON.parse(data) });
      });
    });

    req.on('error', (error) => {
      console.error('❌ Health check failed:', error);
      reject(error);
    });

    req.setTimeout(10000, () => {
      console.error('❌ Health check timed out');
      req.destroy();
      reject(new Error('Timeout'));
    });

    req.end();
  });
}

async function testWebhookEndpoint() {
  console.log('🔍 Testing webhook POST endpoint...');
  
  return new Promise((resolve, reject) => {
    const testPayload = JSON.stringify({
      test: true,
      timestamp: new Date().toISOString()
    });

    const options = {
      hostname: 'app-quantboost-frontend-staging.azurewebsites.net',
      port: 443,
      path: '/api/webhooks/stripe/health',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': testPayload.length,
        'User-Agent': 'QuantBoost-Webhook-Tester/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`✅ POST test response (${res.statusCode}):`, data);
        resolve({ status: res.statusCode, data: JSON.parse(data) });
      });
    });

    req.on('error', (error) => {
      console.error('❌ POST test failed:', error);
      reject(error);
    });

    req.setTimeout(10000, () => {
      console.error('❌ POST test timed out');
      req.destroy();
      reject(new Error('Timeout'));
    });

    req.write(testPayload);
    req.end();
  });
}

async function runTests() {
  console.log('🚀 Starting QuantBoost Webhook Tests\n');
  
  try {
    // Test 1: Health Check
    await testWebhookHealth();
    console.log('');
    
    // Test 2: POST Endpoint
    await testWebhookEndpoint();
    console.log('');
    
    console.log('✅ All tests completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Check Azure Application Insights for detailed logs');
    console.log('2. Monitor Supabase logs for webhook processing');
    console.log('3. Test with a real Stripe webhook event');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Run the tests
runTests();
