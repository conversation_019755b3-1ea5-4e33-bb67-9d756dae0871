# 🔒 QuantBoost Security Guidelines

## API Key and Secrets Management

### ✅ DO's

1. **Use Environment Variables**
   ```bash
   # .env.local (never commit this file)
   STRIPE_SECRET_KEY=sk_live_your_key_here
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_key_here
   SUPABASE_SERVICE_KEY=your_service_key_here
   ```

2. **Use Azure Key Vault for Production**
   - Store all production secrets in Azure Key Vault
   - Reference secrets by name, not value
   - Use managed identity for access

3. **Separate Keys by Environment**
   - Development: Use test keys only
   - Staging: Use separate staging keys
   - Production: Use production keys with restricted permissions

4. **Regular Key Rotation**
   - Rotate API keys every 90 days
   - Update all environments when rotating
   - Test thoroughly after rotation

### ❌ DON'Ts

1. **Never Hardcode Secrets**
   ```javascript
   // ❌ NEVER DO THIS
   const stripeKey = "sk_live_abc123...";
   
   // ✅ DO THIS INSTEAD
   const stripeKey = process.env.STRIPE_SECRET_KEY;
   ```

2. **Don't Commit Environment Files**
   - Never commit `.env`, `.env.local`, `.env.production`
   - Always check `.gitignore` includes these patterns

3. **Don't Log Secrets**
   ```javascript
   // ❌ NEVER DO THIS
   console.log("API Key:", process.env.STRIPE_SECRET_KEY);
   
   // ✅ DO THIS INSTEAD
   console.log("API Key:", process.env.STRIPE_SECRET_KEY ? "✓ Present" : "✗ Missing");
   ```

4. **Don't Share Keys in Chat/Email**
   - Use secure password managers
   - Share through encrypted channels only

## Git Security Best Practices

### Before Every Commit

1. **Check for Secrets**
   ```bash
   # Search for potential secrets before committing
   git diff --cached | grep -E "(key|secret|token|password)"
   ```

2. **Use Git Hooks** (Optional)
   Create `.git/hooks/pre-commit`:
   ```bash
   #!/bin/sh
   # Check for potential secrets
   if git diff --cached --name-only | xargs grep -l -E "(sk_|pk_|eyJ)" 2>/dev/null; then
     echo "🚨 Potential API keys detected! Commit aborted."
     exit 1
   fi
   ```

### Emergency Response (If Keys Are Accidentally Committed)

1. **Immediate Actions**
   - Revoke the compromised keys immediately
   - Generate new keys
   - Update all environments with new keys

2. **Clean Git History**
   ```bash
   # Use the clean-git-secrets.ps1 script
   .\clean-git-secrets.ps1
   ```

## Environment Setup Checklist

### Development Environment
- [ ] `.env.local` file created
- [ ] Test API keys configured
- [ ] `.env.local` added to `.gitignore`
- [ ] Environment variables loaded in application

### Production Environment
- [ ] Azure Key Vault configured
- [ ] Production keys stored in Key Vault
- [ ] Application configured to read from Key Vault
- [ ] Access policies configured with principle of least privilege

## Code Review Guidelines

### Security Checklist for Reviews
- [ ] No hardcoded secrets or API keys
- [ ] Environment variables used correctly
- [ ] No secrets in log statements
- [ ] Proper error handling (don't expose secrets in errors)
- [ ] Configuration files don't contain secrets

### Common Patterns to Watch For
```javascript
// ❌ Potential issues
const config = {
  stripeKey: "sk_live_...",  // Hardcoded key
  apiUrl: "https://api.service.com/v1?key=abc123"  // Key in URL
};

// ✅ Secure patterns
const config = {
  stripeKey: process.env.STRIPE_SECRET_KEY,
  apiUrl: process.env.API_BASE_URL
};
```

## Monitoring and Alerting

### Set Up Alerts For:
- Failed authentication attempts
- Unusual API usage patterns
- Key rotation reminders
- Environment variable changes

### Regular Security Audits
- Monthly review of active API keys
- Quarterly review of access permissions
- Annual penetration testing

## Tools and Resources

### Recommended Tools
- **Git-secrets**: Prevents committing secrets
- **TruffleHog**: Scans for secrets in Git history
- **Azure Key Vault**: Production secret management
- **GitHub Security Scanning**: Automated secret detection

### Emergency Contacts
- **Security Team**: [Insert contact]
- **DevOps Lead**: [Insert contact]
- **Project Manager**: [Insert contact]

---

## 🚨 If You Discover a Security Issue

1. **Do NOT create a public issue**
2. Contact the security team immediately
3. Provide details about the issue
4. Follow the incident response plan

**Remember: Security is everyone's responsibility!**