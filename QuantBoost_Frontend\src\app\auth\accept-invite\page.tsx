"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';

function AcceptInviteContent() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [message, setMessage] = useState('Verifying your invitation...');
  const supabase = useSupabaseClient();

  useEffect(() => {
    const verifyToken = async () => {
      if (!token) {
        setMessage('No invitation token found.');
        return;
      }

      const { data: invitation, error } = await supabase
        .from('invitations')
        .select('*')
        .eq('token', token)
        .single();

      if (error || !invitation) {
        setMessage('This invitation is invalid or has expired.');
        return;
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        // If the user is not logged in, you might want to redirect them to signup
        // and then bring them back to accept the invite.
        // For simplicity, we'll just show a message here.
        setMessage('Please log in or sign up to accept the invitation.');
        return;
      }

      // Add user to the team
      const { error: teamError } = await supabase.from('team_members').insert({
        team_id: invitation.team_id,
        user_id: user.id,
      });

      if (teamError) {
        setMessage('Failed to accept the invitation. Please try again.');
        return;
      }

      // Delete the invitation so it can't be used again
      await supabase.from('invitations').delete().eq('id', invitation.id);

      setMessage('Invitation accepted! You are now part of the team.');
    };

    verifyToken();
  }, [token]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <h1 className="text-2xl font-bold mb-4">Accept Invitation</h1>
      <p>{message}</p>
      {/* You could add a button to redirect to the dashboard */}
    </div>
  );
}

export default function AcceptInvitePage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
      <AcceptInviteContent />
    </Suspense>
  );
}