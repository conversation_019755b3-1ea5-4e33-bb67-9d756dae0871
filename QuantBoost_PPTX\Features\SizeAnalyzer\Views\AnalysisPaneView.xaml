<UserControl x:Class="QuantBoost_Powerpoint_Addin.Features.SizeAnalyzer.Views.AnalysisPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:viewmodels="clr-namespace:QuantBoost_Powerpoint_Addin.Features.SizeAnalyzer.ViewModels"
             FontFamily="Segoe UI" 
             Background="#F5F7FA"
             TextOptions.TextFormattingMode="Ideal"
             UseLayoutRounding="True">
    
    <UserControl.DataContext>
        <viewmodels:AnalysisPaneViewModel />
    </UserControl.DataContext>
    
    <UserControl.Resources>
        <!-- Define QuantBoost color palette -->
        <SolidColorBrush x:Key="PrimaryBlueBrush" Color="#577BF9"/>
        <SolidColorBrush x:Key="HeaderBackgroundBrush" Color="#F0F2F5"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#DDE3EA"/>
        
        <!-- BooleanToVisibilityConverter -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- DataGrid Styles -->
        <!-- Default column header style (left-aligned for text columns) -->
        <Style x:Key="LeftAlignedColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="{StaticResource HeaderBackgroundBrush}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
        </Style>

        <!-- Right-aligned column header style for numerical columns -->
        <Style x:Key="RightAlignedColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="{StaticResource HeaderBackgroundBrush}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>

        <!-- Default style for all column headers (left-aligned) -->
        <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource LeftAlignedColumnHeaderStyle}"/>

        <!-- Reusable style for right-aligned data cells -->
        <Style x:Key="RightAlignedCellStyle" TargetType="TextBlock">
            <Setter Property="HorizontalAlignment" Value="Right"/>
        </Style>
        
        <Style TargetType="DataGridRow">
            <Setter Property="Background" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F8F9FA"/>
                </Trigger>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="{StaticResource PrimaryBlueBrush}"/>
                    <Setter Property="Foreground" Value="White"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Title & Button -->
            <RowDefinition Height="*"/>    <!-- Results DataGrid -->
            <RowDefinition Height="Auto"/> <!-- Totals -->
            <RowDefinition Height="Auto"/> <!-- Status & Progress -->
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,15">
            <TextBlock Text="Presentation Size Analyzer"
                       FontSize="18"
                       FontWeight="Light"
                       Foreground="#34495E"
                       Margin="0,0,0,5"/>
            <TextBlock Text="Uses proportional allocation for accurate breakdown"
                       FontSize="10"
                       FontWeight="Normal"
                       Foreground="#7F8C8D"
                       Margin="0,0,0,10"/>
            
            <StackPanel Orientation="Horizontal" Margin="0,0,0,0">
                <Button Content="Analyze Now"
                        Command="{Binding AnalyzeCommand}"
                        IsEnabled="{Binding IsNotAnalyzing}"
                        Background="{StaticResource PrimaryBlueBrush}"
                        Foreground="White"
                        Padding="15,8"
                        BorderThickness="0"
                        FontSize="11"
                        Cursor="Hand"
                        Margin="0,0,15,0">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="3"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#4A6CF7"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#3B5BF5"/>
                                            </Trigger>
                                            <Trigger Property="IsEnabled" Value="False">
                                                <Setter Property="Background" Value="#CCCCCC"/>
                                                <Setter Property="Foreground" Value="#888888"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <TextBlock Text="⚠️ Note: This operation may take 1-2 minutes on larger presentations with multiple slides"
                           FontSize="10"
                           Foreground="#E67E22"
                           VerticalAlignment="Center"
                           TextWrapping="Wrap"
                           MaxWidth="300"/>
            </StackPanel>
        </StackPanel>

        <!-- Results Grid -->
        <DataGrid Grid.Row="1" 
                  ItemsSource="{Binding AnalysisResults}" 
                  AutoGenerateColumns="False"
                  IsReadOnly="True" 
                  GridLinesVisibility="Horizontal" 
                  BorderThickness="1"
                  BorderBrush="{StaticResource BorderBrush}"
                  Background="White"
                  RowHeaderWidth="0"
                  CanUserReorderColumns="False"
                  CanUserResizeRows="False"
                  CanUserSortColumns="True"
                  SelectionMode="Single">
            
            <DataGrid.Columns>
                <!-- Left-aligned text column -->
                <DataGridTextColumn Header="Slide"
                                    Binding="{Binding SlideName}"
                                    Width="140"
                                    CanUserSort="True"
                                    HeaderStyle="{StaticResource LeftAlignedColumnHeaderStyle}"/>

                <!-- Right-aligned numerical columns -->
                <DataGridTextColumn Header="Size (KB)"
                                    Binding="{Binding SizeKB, StringFormat={}{0:N1} KB}"
                                    Width="90"
                                    CanUserSort="True"
                                    HeaderStyle="{StaticResource RightAlignedColumnHeaderStyle}"
                                    ElementStyle="{StaticResource RightAlignedCellStyle}"/>
                <DataGridTextColumn Header="%"
                                    Binding="{Binding SizePercentage, StringFormat={}{0:F1}%}"
                                    Width="55"
                                    CanUserSort="True"
                                    HeaderStyle="{StaticResource RightAlignedColumnHeaderStyle}"
                                    ElementStyle="{StaticResource RightAlignedCellStyle}"/>

                <!-- Right-aligned text column -->
                <DataGridTextColumn Header="Content Type"
                                    Binding="{Binding ContentType}"
                                    Width="110"
                                    CanUserSort="True"
                                    HeaderStyle="{StaticResource RightAlignedColumnHeaderStyle}"
                                    ElementStyle="{StaticResource RightAlignedCellStyle}"/>
                <!-- Right-aligned numerical columns -->
                <DataGridTextColumn Header="Images"
                                    Binding="{Binding ImageCount}"
                                    Width="55"
                                    CanUserSort="True"
                                    HeaderStyle="{StaticResource RightAlignedColumnHeaderStyle}"
                                    ElementStyle="{StaticResource RightAlignedCellStyle}"/>
                <DataGridTextColumn Header="Charts"
                                    Binding="{Binding ChartCount}"
                                    Width="55"
                                    CanUserSort="True"
                                    HeaderStyle="{StaticResource RightAlignedColumnHeaderStyle}"
                                    ElementStyle="{StaticResource RightAlignedCellStyle}"/>
                <DataGridTextColumn Header="Media"
                                    Binding="{Binding MediaCount}"
                                    Width="55"
                                    CanUserSort="True"
                                    HeaderStyle="{StaticResource RightAlignedColumnHeaderStyle}"
                                    ElementStyle="{StaticResource RightAlignedCellStyle}"/>
                <DataGridTextColumn Header="Objects"
                                    Binding="{Binding EmbeddedObjectCount}"
                                    Width="55"
                                    CanUserSort="True"
                                    HeaderStyle="{StaticResource RightAlignedColumnHeaderStyle}"
                                    ElementStyle="{StaticResource RightAlignedCellStyle}"/>
                <DataGridTextColumn Header="Links"
                                    Binding="{Binding ExcelLinkCount}"
                                    Width="55"
                                    CanUserSort="True"
                                    HeaderStyle="{StaticResource RightAlignedColumnHeaderStyle}"
                                    ElementStyle="{StaticResource RightAlignedCellStyle}"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Totals Section -->
        <Border Grid.Row="2"
                Background="{StaticResource HeaderBackgroundBrush}"
                Padding="10"
                Margin="0,1,0,0"
                BorderThickness="1,0,1,1"
                BorderBrush="{StaticResource BorderBrush}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="Total" FontWeight="Bold" VerticalAlignment="Center"/>

                <Button Grid.Column="1"
                        Content="📊 Export to CSV"
                        Command="{Binding ExportCommand}"
                        IsEnabled="{Binding HasResults}"
                        Background="Transparent"
                        Foreground="{StaticResource PrimaryBlueBrush}"
                        BorderBrush="{StaticResource PrimaryBlueBrush}"
                        BorderThickness="1"
                        Padding="8,4"
                        FontSize="10"
                        Cursor="Hand"
                        Margin="0,0,10,0"
                        VerticalAlignment="Center">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="3"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F8F9FA"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#E9ECEF"/>
                                            </Trigger>
                                            <Trigger Property="IsEnabled" Value="False">
                                                <Setter Property="Foreground" Value="#CCCCCC"/>
                                                <Setter Property="BorderBrush" Value="#CCCCCC"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <TextBlock Grid.Column="2"
                           Text="{Binding TotalSizeDisplay}"
                           FontWeight="Bold"
                           VerticalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Grid Grid.Row="3" Margin="0,10,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <TextBlock Grid.Row="0"
                       Text="{Binding StatusText}" 
                       Foreground="Gray" 
                       FontSize="10"
                       Margin="0,0,0,5"/>
            
            <ProgressBar Grid.Row="1"
                         Value="{Binding ProgressPercent}" 
                         Height="4" 
                         Minimum="0" 
                         Maximum="100"
                         Background="#E0E0E0"
                         Foreground="{StaticResource PrimaryBlueBrush}"
                         Visibility="{Binding IsAnalyzing, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </Grid>
    </Grid>
</UserControl>
