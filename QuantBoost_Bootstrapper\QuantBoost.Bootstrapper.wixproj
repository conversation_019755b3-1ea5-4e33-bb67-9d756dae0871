<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Release</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <ProductVersion>3.14</ProductVersion>
    <ProjectGuid>{A0E2FE9A-6E2C-41C8-9B3B-0A98E5A9B713}</ProjectGuid>
    <SchemaVersion>2.0</SchemaVersion>
    <OutputName>QuantBoost</OutputName>
    <OutputType>Bundle</OutputType>
    <!-- Pass the MSI path at build time: /p:MsiPath=..\QuantBoost_WixInstaller\bin\Release\QuantBoost.msi -->
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <OutputPath>C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\bin\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <OutputPath>C:\VS projects\QuantBoost\QuantBoost_WixInstaller\bin\x64\Release\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Bundle.wxs" />
    <Content Include="License.rtf" />
    <Content Include="NDP481-Web.exe" />
    <Content Include="vstor_redist.exe" />
  </ItemGroup>
  <ItemGroup>
    <WixExtension Include="WixBalExtension">
      <HintPath>WixBalExtension</HintPath>
      <Name>WixBalExtension</Name>
    </WixExtension>
    <WixExtension Include="WixNetFxExtension">
      <HintPath>WixNetFxExtension</HintPath>
      <Name>WixNetFxExtension</Name>
    </WixExtension>
    <WixExtension Include="WixUtilExtension">
      <HintPath>WixUtilExtension</HintPath>
      <Name>WixUtilExtension</Name>
    </WixExtension>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\QuantBoost_WixInstaller\QuantBoost.WixInstaller.wixproj">
      <Name>QuantBoost.WixInstaller</Name>
      <Project>{3fc1c7a6-2f64-4a40-9e42-6b4b95321b69}</Project>
      <Private>True</Private>
      <DoNotHarvest>True</DoNotHarvest>
      <RefProjectOutputGroups>Binaries;Content;Satellites</RefProjectOutputGroups>
      <RefTargetDir>INSTALLFOLDER</RefTargetDir>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.targets')" />
</Project>