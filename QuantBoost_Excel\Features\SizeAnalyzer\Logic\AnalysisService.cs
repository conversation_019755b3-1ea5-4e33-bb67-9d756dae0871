// --- START OF FILE AnalysisService.cs ---

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Runtime.InteropServices;
using Excel = Microsoft.Office.Interop.Excel;

namespace QuantBoost_Excel.Features.SizeAnalyzer.Logic
{
    /// <summary>
    /// Service class for analyzing Excel workbook file sizes using the "Save & Measure" method.
    /// This approach creates temporary workbooks for each worksheet to get accurate size measurements.
    /// </summary>
    public class AnalysisService
    {
        private readonly string _tempFolderPath;

        /// <summary>
        /// Initializes a new instance of the AnalysisService class.
        /// </summary>
        public AnalysisService()
        {
            _tempFolderPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "QuantBoost", "TempAnalysis");
        }

        /// <summary>
        /// Analyzes the active Excel workbook using the "Save & Measure" method with proportional allocation.
        /// This method creates temporary workbooks for each worksheet to get accurate relative weights,
        /// then applies those weights to the actual total workbook file size for perfect breakdown.
        /// </summary>
        /// <param name="progress">Optional progress reporter.</param>
        /// <param name="cancellationToken">Optional cancellation token.</param>
        /// <returns>A list of worksheet analysis summaries with proportionally allocated sizes.</returns>
        public async Task<List<WorksheetAnalysisSummary>> AnalyzeWorkbookAsync(
            IProgress<(string status, int percentage)> progress = null,
            CancellationToken cancellationToken = default)
        {
            System.Diagnostics.Debug.WriteLine("=== ANALYSIS SERVICE: Starting workbook analysis ===");
            var results = new List<WorksheetAnalysisSummary>();

            try
            {
                progress?.Report(("Initializing analysis...", 0));
                cancellationToken.ThrowIfCancellationRequested();

                // Step 1: Pre-flight Check
                System.Diagnostics.Debug.WriteLine("Step 1: Getting Excel Application instance");
                var excelApp = Globals.ThisAddIn.Application;
                System.Diagnostics.Debug.WriteLine($"Excel Application obtained: {excelApp != null}");

                System.Diagnostics.Debug.WriteLine("Step 1: Getting Active Workbook");
                var activeWorkbook = excelApp.ActiveWorkbook;
                System.Diagnostics.Debug.WriteLine($"Active Workbook obtained: {activeWorkbook != null}");
                
                if (activeWorkbook == null)
                {
                    System.Diagnostics.Debug.WriteLine("ERROR: No active workbook found");
                    throw new InvalidOperationException("Please open a workbook to analyze.");
                }

                System.Diagnostics.Debug.WriteLine($"Workbook Name: {activeWorkbook.Name}");
                System.Diagnostics.Debug.WriteLine($"Workbook Path: {activeWorkbook.FullName}");

                progress?.Report(("Checking workbook status...", 5));
                cancellationToken.ThrowIfCancellationRequested();

                // Ensure workbook is saved
                System.Diagnostics.Debug.WriteLine($"Workbook Saved Status: {activeWorkbook.Saved}");
                if (string.IsNullOrEmpty(activeWorkbook.FullName) || activeWorkbook.Saved == false)
                {
                    System.Diagnostics.Debug.WriteLine("ERROR: Workbook is not saved or has no file path");
                    throw new InvalidOperationException("The workbook must be saved to perform an accurate size analysis. Please save the workbook and try again.");
                }

                // Step 2: Prepare temp directory
                System.Diagnostics.Debug.WriteLine("Step 2: Preparing temporary directory");
                progress?.Report(("Preparing temporary workspace...", 10));
                PrepareTempDirectory();
                System.Diagnostics.Debug.WriteLine($"Temp directory prepared: {_tempFolderPath}");
                cancellationToken.ThrowIfCancellationRequested();

                // Step 3: Disable alerts to prevent save prompts (but keep main Excel visible)
                System.Diagnostics.Debug.WriteLine("Step 3: Configuring Excel settings");
                bool originalDisplayAlerts = excelApp.DisplayAlerts;
                bool originalScreenUpdating = excelApp.ScreenUpdating;
                System.Diagnostics.Debug.WriteLine($"Original DisplayAlerts: {originalDisplayAlerts}, ScreenUpdating: {originalScreenUpdating}");

                excelApp.DisplayAlerts = false;
                excelApp.ScreenUpdating = false; // Improve performance and reduce flicker
                System.Diagnostics.Debug.WriteLine("Excel settings configured successfully");

                try
                {
                    // Step 4: Get actual workbook file size for proportional allocation
                    System.Diagnostics.Debug.WriteLine("Step 4: Getting actual workbook file size");
                    progress?.Report(("Getting workbook file size...", 15));
                    long actualWorkbookSize = GetActualWorkbookSize(activeWorkbook);
                    System.Diagnostics.Debug.WriteLine($"Actual workbook size obtained: {actualWorkbookSize} bytes");

                    // Step 5: Iterate through each worksheet and analyze using Save & Measure
                    System.Diagnostics.Debug.WriteLine("Step 5: Starting worksheet iteration");
                    int sheetCount = activeWorkbook.Worksheets.Count;
                    System.Diagnostics.Debug.WriteLine($"Total worksheets to process: {sheetCount}");
                    int processedCount = 0;
                    var rawResults = new List<WorksheetAnalysisSummary>();

                    progress?.Report(("Starting worksheet analysis...", 20));

                    foreach (Excel.Worksheet sheet in activeWorkbook.Worksheets)
                    {
                        try
                        {
                            processedCount++;
                            int progressPercent = 20 + (processedCount * 60) / sheetCount;
                            progress?.Report(($"Processing '{sheet.Name}'...", progressPercent));
                            cancellationToken.ThrowIfCancellationRequested();

                            // Analyze this worksheet using Save & Measure method to get raw size
                            var analysis = await AnalyzeWorksheetAsync(sheet, excelApp, cancellationToken);
                            rawResults.Add(analysis);

                            // Give Excel a moment to breathe
                            await Task.Delay(50, cancellationToken);
                        }

                        finally
                        {
                            // Always release COM objects
                            if (sheet != null)
                            {
                                Marshal.ReleaseComObject(sheet);
                            }
                        }
                    }

                    // Step 6: Apply proportional allocation based on relative weights
                    progress?.Report(("Calculating proportional allocation...", 85));
                    results = ApplyProportionalAllocation(rawResults, actualWorkbookSize);
                }
                finally
                {
                    // Step 5: Restore Excel settings and clean up
                    excelApp.DisplayAlerts = originalDisplayAlerts;
                    excelApp.ScreenUpdating = originalScreenUpdating;
                    CleanupTempDirectory();
                }

                progress?.Report(("Analysis complete!", 100));
                return results;
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("=== ANALYSIS CANCELLED BY USER ===");
                progress?.Report(("Analysis cancelled", 0));
                CleanupTempDirectory();
                throw;
            }
            catch (System.Runtime.InteropServices.COMException comEx)
            {
                System.Diagnostics.Debug.WriteLine("=== COM EXCEPTION IN MAIN ANALYSIS ===");
                System.Diagnostics.Debug.WriteLine($"HRESULT: 0x{comEx.HResult:X8}");
                System.Diagnostics.Debug.WriteLine($"Message: {comEx.Message}");
                System.Diagnostics.Debug.WriteLine($"Source: {comEx.Source}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {comEx.StackTrace}");

                string friendlyMessage = $"Excel COM error (HRESULT: 0x{comEx.HResult:X8}): {comEx.Message}";
                progress?.Report(($"Analysis failed: {friendlyMessage}", 0));
                CleanupTempDirectory();
                throw new InvalidOperationException(friendlyMessage, comEx);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("=== GENERAL EXCEPTION IN MAIN ANALYSIS ===");
                System.Diagnostics.Debug.WriteLine($"Exception Type: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"Message: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");

                progress?.Report(($"Analysis failed: {ex.Message}", 0));
                CleanupTempDirectory();
                throw;
            }
        }

        /// <summary>
        /// Analyzes a single worksheet using the Save & Measure method.
        /// Creates temporary workbooks without disrupting the user's main Excel window.
        /// </summary>
        /// <param name="sheet">The worksheet to analyze.</param>
        /// <param name="excelApp">The Excel application instance.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>Analysis summary for the worksheet.</returns>
        private async Task<WorksheetAnalysisSummary> AnalyzeWorksheetAsync(
            Excel.Worksheet sheet,
            Excel.Application excelApp,
            CancellationToken cancellationToken)
        {
            System.Diagnostics.Debug.WriteLine($"--- Analyzing worksheet: {sheet?.Name ?? "NULL"} ---");
            Excel.Workbook tempWorkbook = null;
            string tempFilePath = null;

            try
            {
                System.Diagnostics.Debug.WriteLine("Creating temporary workbook...");
                // Create a new temporary workbook in memory
                // The workbook will be created but we'll minimize window disruption
                tempWorkbook = excelApp.Workbooks.Add();
                System.Diagnostics.Debug.WriteLine($"Temporary workbook created: {tempWorkbook?.Name ?? "NULL"}");
                cancellationToken.ThrowIfCancellationRequested();

                // Immediately hide the new workbook window to prevent it from appearing on top
                System.Diagnostics.Debug.WriteLine("Hiding temporary workbook window...");
                try
                {
                    if (tempWorkbook.Windows.Count > 0)
                    {
                        tempWorkbook.Windows[1].Visible = false;
                        System.Diagnostics.Debug.WriteLine("Temporary workbook window hidden successfully");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("No windows found for temporary workbook");
                    }
                }
                catch (Exception windowEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Could not hide temporary workbook window: {windowEx.Message}");
                    // Continue anyway - window hiding is not critical for functionality
                }

                // Copy the current sheet to the new workbook
                System.Diagnostics.Debug.WriteLine($"Copying sheet '{sheet.Name}' to temporary workbook...");
                System.Diagnostics.Debug.WriteLine($"Temporary workbook has {tempWorkbook.Worksheets.Count} worksheets before copy");

                Excel.Workbook actualTempWorkbook = tempWorkbook;

                try
                {
                    // Try the safer approach: copy to the end of the workbook
                    sheet.Copy(After: tempWorkbook.Worksheets[tempWorkbook.Worksheets.Count]);
                    System.Diagnostics.Debug.WriteLine("Sheet copy completed successfully using After parameter");
                }
                catch (System.Runtime.InteropServices.COMException copyEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Copy with After parameter failed: {copyEx.Message}");
                    System.Diagnostics.Debug.WriteLine("Trying alternative copy approach...");

                    // Alternative: Copy without specifying position (creates new workbook)
                    sheet.Copy();
                    System.Diagnostics.Debug.WriteLine("Sheet copy completed successfully using parameterless Copy()");

                    // When using parameterless Copy(), Excel creates a new workbook
                    // We need to get reference to this new workbook and close our original temp workbook
                    actualTempWorkbook = excelApp.ActiveWorkbook;
                    System.Diagnostics.Debug.WriteLine($"Parameterless Copy() created new workbook: {actualTempWorkbook.Name}");

                    // Close the original empty temp workbook
                    try
                    {
                        tempWorkbook.Close(SaveChanges: false);
                        Marshal.ReleaseComObject(tempWorkbook);
                        System.Diagnostics.Debug.WriteLine("Original empty temp workbook closed");
                    }
                    catch (Exception closeEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error closing original temp workbook: {closeEx.Message}");
                    }

                    // Update our reference to point to the new workbook
                    tempWorkbook = actualTempWorkbook;
                }

                cancellationToken.ThrowIfCancellationRequested();

                // The workbook now has our copied sheet. Clean up any extra sheets if needed.
                System.Diagnostics.Debug.WriteLine($"Temporary workbook has {tempWorkbook.Worksheets.Count} worksheets after copy");

                // If we used the positioned copy method, we need to delete default sheets
                // If we used parameterless copy, the workbook should already contain only our sheet
                if (tempWorkbook.Worksheets.Count > 1)
                {
                    System.Diagnostics.Debug.WriteLine("Multiple sheets detected, cleaning up default sheets...");
                    // Delete all sheets except the last one (our copied sheet)
                    while (tempWorkbook.Worksheets.Count > 1)
                    {
                        var sheetToDelete = (Excel.Worksheet)tempWorkbook.Worksheets[1];
                        System.Diagnostics.Debug.WriteLine($"Deleting default sheet: {sheetToDelete.Name}");
                        sheetToDelete.Delete();
                    }
                }
                System.Diagnostics.Debug.WriteLine($"Cleanup complete. Temporary workbook now has {tempWorkbook.Worksheets.Count} worksheet(s)");

                // Save the temporary workbook (containing only our sheet)
                tempFilePath = Path.Combine(_tempFolderPath, $"{Guid.NewGuid()}_{SanitizeFileName(sheet.Name)}.xlsx");
                System.Diagnostics.Debug.WriteLine($"Saving temporary workbook to: {tempFilePath}");
                tempWorkbook.SaveAs(tempFilePath, Excel.XlFileFormat.xlOpenXMLWorkbook);
                System.Diagnostics.Debug.WriteLine("Temporary workbook saved successfully");
                cancellationToken.ThrowIfCancellationRequested();

                // Get the file size and other metadata
                System.Diagnostics.Debug.WriteLine("Getting file size and analyzing worksheet metadata...");
                var fileInfo = new FileInfo(tempFilePath);
                System.Diagnostics.Debug.WriteLine($"Temporary file size: {fileInfo.Length} bytes ({fileInfo.Length / 1024.0:F1} KB)");

                var analysis = AnalyzeWorksheetWithInterop(sheet);
                System.Diagnostics.Debug.WriteLine("Worksheet metadata analysis completed");

                // Set the accurate size from the isolated sheet
                analysis.SizeBytes = fileInfo.Length;
                System.Diagnostics.Debug.WriteLine($"Analysis complete for '{sheet.Name}': {analysis.SizeBytes} bytes");

                return analysis;
            }
            catch (System.Runtime.InteropServices.COMException comEx)
            {
                System.Diagnostics.Debug.WriteLine($"COM Exception in AnalyzeWorksheetAsync for '{sheet?.Name ?? "NULL"}':");
                System.Diagnostics.Debug.WriteLine($"  HRESULT: 0x{comEx.HResult:X8}");
                System.Diagnostics.Debug.WriteLine($"  Message: {comEx.Message}");
                System.Diagnostics.Debug.WriteLine($"  Source: {comEx.Source}");
                System.Diagnostics.Debug.WriteLine($"  Stack Trace: {comEx.StackTrace}");

                // Decode common HRESULT values
                switch ((uint)comEx.HResult)
                {
                    case 0x800A03EC:
                        System.Diagnostics.Debug.WriteLine("  Decoded: Name not found (0x800A03EC) - likely accessing invalid worksheet or range");
                        break;
                    case 0x800A01A8:
                        System.Diagnostics.Debug.WriteLine("  Decoded: Object required (0x800A01A8) - object reference issue");
                        break;
                    case 0x800A0009:
                        System.Diagnostics.Debug.WriteLine("  Decoded: Subscript out of range (0x800A0009) - index out of bounds");
                        break;
                    default:
                        System.Diagnostics.Debug.WriteLine($"  Decoded: Unknown COM error code");
                        break;
                }

                throw new InvalidOperationException($"Excel COM error while analyzing worksheet '{sheet?.Name ?? "NULL"}': {comEx.Message} (HRESULT: 0x{comEx.HResult:X8})", comEx);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"General Exception in AnalyzeWorksheetAsync for '{sheet?.Name ?? "NULL"}':");
                System.Diagnostics.Debug.WriteLine($"  Type: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"  Message: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"  Stack Trace: {ex.StackTrace}");
                throw;
            }
            finally
            {
                System.Diagnostics.Debug.WriteLine($"Cleaning up resources for worksheet '{sheet?.Name ?? "NULL"}'...");

                // Clean up temp workbook - this is critical to prevent workbooks staying open
                if (tempWorkbook != null)
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"Closing temporary workbook: {tempWorkbook.Name}");
                        tempWorkbook.Close(SaveChanges: false);
                        System.Diagnostics.Debug.WriteLine("Temporary workbook closed successfully");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error closing temporary workbook: {ex.Message}");

                        // Try alternative close method
                        try
                        {
                            System.Diagnostics.Debug.WriteLine("Trying alternative close method...");
                            tempWorkbook.Close(false);
                            System.Diagnostics.Debug.WriteLine("Alternative close method succeeded");
                        }
                        catch (Exception altEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"Alternative close method also failed: {altEx.Message}");
                        }
                    }
                    finally
                    {
                        try
                        {
                            Marshal.ReleaseComObject(tempWorkbook);
                            System.Diagnostics.Debug.WriteLine("Temporary workbook COM object released");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error releasing temporary workbook COM object: {ex.Message}");
                        }
                    }
                }

                // Clean up temp file
                if (!string.IsNullOrEmpty(tempFilePath) && File.Exists(tempFilePath))
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"Deleting temporary file: {tempFilePath}");
                        File.Delete(tempFilePath);
                        System.Diagnostics.Debug.WriteLine("Temporary file deleted successfully");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error deleting temporary file: {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"--- Cleanup complete for worksheet: {sheet?.Name ?? "NULL"} ---");
            }
        }

        /// <summary>
        /// Prepares the temporary directory for analysis.
        /// </summary>
        private void PrepareTempDirectory()
        {
            // Ensure the directory exists and is clean
            if (Directory.Exists(_tempFolderPath))
            {
                Directory.Delete(_tempFolderPath, true);
            }
            Directory.CreateDirectory(_tempFolderPath);
        }

        /// <summary>
        /// Cleans up the temporary directory after analysis.
        /// </summary>
        private void CleanupTempDirectory()
        {
            try
            {
                if (Directory.Exists(_tempFolderPath))
                {
                    Directory.Delete(_tempFolderPath, true);
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        /// <summary>
        /// Gets the actual file size of the workbook.
        /// For SharePoint/OneDrive files, saves a temporary copy to get the size.
        /// </summary>
        /// <param name="workbook">The Excel workbook.</param>
        /// <returns>The actual file size in bytes.</returns>
        private static long GetActualWorkbookSize(Excel.Workbook workbook)
        {
            try
            {
                string filePath = workbook.FullName;
                System.Diagnostics.Debug.WriteLine($"Getting actual workbook size for: {filePath}");

                // Check if it's a local file first
                if (!string.IsNullOrEmpty(filePath) && !filePath.StartsWith("http") && File.Exists(filePath))
                {
                    var localFileInfo = new FileInfo(filePath);
                    long localSize = localFileInfo.Length;
                    System.Diagnostics.Debug.WriteLine($"Local file size: {localSize:N0} bytes ({localSize / 1024.0:F1} KB, {localSize / (1024.0 * 1024.0):F1} MB)");
                    return localSize;
                }

                // For SharePoint/OneDrive files OR any case where we can't access the file directly,
                // save a temporary copy using the same approach as individual worksheets
                System.Diagnostics.Debug.WriteLine("File is not locally accessible, creating temporary copy to measure size...");

                string tempPath = Path.Combine(Path.GetTempPath(), $"QuantBoost_WorkbookSizeCheck_{Guid.NewGuid()}.xlsx");
                System.Diagnostics.Debug.WriteLine($"Saving temporary workbook copy to: {tempPath}");

                // Save a copy to get the actual file size (same approach as worksheet analysis)
                workbook.SaveCopyAs(tempPath);
                System.Diagnostics.Debug.WriteLine("Temporary workbook copy saved successfully");

                var tempFileInfo = new FileInfo(tempPath);
                long tempSize = tempFileInfo.Length;
                System.Diagnostics.Debug.WriteLine($"Workbook file size from temporary copy: {tempSize:N0} bytes ({tempSize / 1024.0:F1} KB, {tempSize / (1024.0 * 1024.0):F1} MB)");

                // Clean up the temporary file
                try
                {
                    File.Delete(tempPath);
                    System.Diagnostics.Debug.WriteLine("Temporary workbook size check file deleted");
                }
                catch (Exception cleanupEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Could not delete temporary workbook size check file: {cleanupEx.Message}");
                }

                return tempSize;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Could not get actual workbook size: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Exception details: {ex}");
            }

            // Fallback: return 0 if we can't determine the size
            // The proportional allocation will handle this gracefully
            System.Diagnostics.Debug.WriteLine("Returning 0 for actual workbook size - proportional allocation will be skipped");
            return 0;
        }

        /// <summary>
        /// Applies proportional allocation to worksheet sizes based on their relative weights.
        /// </summary>
        /// <param name="rawResults">Raw analysis results with measured sizes.</param>
        /// <param name="actualWorkbookSize">The actual total workbook file size.</param>
        /// <returns>Results with proportionally allocated sizes.</returns>
        private static List<WorksheetAnalysisSummary> ApplyProportionalAllocation(
            List<WorksheetAnalysisSummary> rawResults,
            long actualWorkbookSize)
        {
            if (rawResults == null || rawResults.Count == 0)
                return rawResults;

            // Calculate total of raw measured sizes to determine relative weights
            long totalRawSize = rawResults.Sum(r => r.SizeBytes);

            // Debug logging
            System.Diagnostics.Debug.WriteLine($"Proportional Allocation: Actual workbook size = {actualWorkbookSize:N0} bytes ({actualWorkbookSize / 1024.0:F1} KB)");
            System.Diagnostics.Debug.WriteLine($"Proportional Allocation: Total raw temp files = {totalRawSize:N0} bytes ({totalRawSize / 1024.0:F1} KB)");

            if (totalRawSize == 0 || actualWorkbookSize == 0)
            {
                // If we can't calculate proportions, return raw results
                System.Diagnostics.Debug.WriteLine("Proportional Allocation: Cannot calculate proportions, returning raw results");
                return rawResults;
            }

            // Apply proportional allocation
            var allocatedResults = new List<WorksheetAnalysisSummary>();
            long allocatedSoFar = 0;

            for (int i = 0; i < rawResults.Count; i++)
            {
                var result = rawResults[i];
                long allocatedSize;

                if (i == rawResults.Count - 1)
                {
                    // For the last worksheet, allocate remaining size to ensure perfect total
                    allocatedSize = actualWorkbookSize - allocatedSoFar;
                }
                else
                {
                    // Calculate proportional size based on relative weight
                    double weight = (double)result.SizeBytes / totalRawSize;
                    allocatedSize = (long)(actualWorkbookSize * weight);
                    allocatedSoFar += allocatedSize;
                }

                // Create new result with allocated size
                var allocatedResult = new WorksheetAnalysisSummary
                {
                    Name = result.Name,
                    SizeBytes = Math.Max(0, allocatedSize), // Ensure non-negative
                    HasImages = result.HasImages,
                    ImageCount = result.ImageCount,
                    ImageSizeBytes = result.ImageSizeBytes,
                    CellCount = result.CellCount,
                    RowCount = result.RowCount,
                    FormulaCount = result.FormulaCount,
                    FormulaCharTotal = result.FormulaCharTotal,
                    LargeFormulas = result.LargeFormulas,
                    UsedRange = result.UsedRange,
                    UnusedRangePercentage = result.UnusedRangePercentage,
                    HasCharts = result.HasCharts,
                    ChartCount = result.ChartCount,
                    HasEmbeddedObjects = result.HasEmbeddedObjects,
                    EmbeddedObjectCount = result.EmbeddedObjectCount
                };

                // Debug logging for each worksheet
                double debugWeight = totalRawSize > 0 ? (double)result.SizeBytes / totalRawSize : 0;
                System.Diagnostics.Debug.WriteLine($"  {result.Name}: Raw={result.SizeBytes:N0} bytes ({result.SizeBytes / 1024.0:F1} KB), Weight={debugWeight:P1}, Allocated={allocatedSize:N0} bytes ({allocatedSize / 1024.0:F1} KB)");

                allocatedResults.Add(allocatedResult);
            }

            // Final verification
            long totalAllocated = allocatedResults.Sum(r => r.SizeBytes);
            System.Diagnostics.Debug.WriteLine($"Proportional Allocation Complete: Total allocated = {totalAllocated:N0} bytes ({totalAllocated / 1024.0:F1} KB)");
            System.Diagnostics.Debug.WriteLine($"Difference from actual workbook size: {Math.Abs(actualWorkbookSize - totalAllocated)} bytes");

            return allocatedResults;
        }

        /// <summary>
        /// Sanitizes a filename by removing invalid characters.
        /// </summary>
        /// <param name="fileName">The filename to sanitize.</param>
        /// <returns>A sanitized filename.</returns>
        private static string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "Sheet";

            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
            return string.IsNullOrEmpty(sanitized) ? "Sheet" : sanitized;
        }

        /// <summary>
        /// Analyzes a single worksheet using Excel Interop for live metadata.
        /// This method is kept from the original implementation for gathering worksheet details.
        /// </summary>
        private static WorksheetAnalysisSummary AnalyzeWorksheetWithInterop(Excel.Worksheet sheet)
        {
            var analysis = new WorksheetAnalysisSummary
            {
                Name = sheet.Name,
                UsedRange = "N/A",
                CellCount = 0,
                RowCount = 0,
                FormulaCount = 0,
                FormulaCharTotal = 0,
                LargeFormulas = new List<LargeFormula>(),
                UnusedRangePercentage = 0,
                HasCharts = false,
                ChartCount = 0,
                HasEmbeddedObjects = false,
                EmbeddedObjectCount = 0
            };

            try
            {
                // Get used range information
                var usedRange = sheet.UsedRange;
                if (usedRange != null)
                {
                    analysis.UsedRange = usedRange.Address;
                    analysis.CellCount = usedRange.Cells.Count;
                    analysis.RowCount = usedRange.Rows.Count;

                    // Count formulas
                    try
                    {
                        var formulaCells = usedRange.SpecialCells(Excel.XlCellType.xlCellTypeFormulas);
                        if (formulaCells != null)
                        {
                            analysis.FormulaCount = formulaCells.Count;
                        }
                    }
                    catch
                    {
                        // SpecialCells throws exception if no cells of that type exist
                        analysis.FormulaCount = 0;
                    }

                    Marshal.ReleaseComObject(usedRange);
                }

                // Count charts
                var chartObjects = sheet.ChartObjects() as Excel.ChartObjects;
                analysis.ChartCount = chartObjects?.Count ?? 0;
                analysis.HasCharts = analysis.ChartCount > 0;

                // Count embedded objects (OLE objects)
                var oleObjects = sheet.OLEObjects() as Excel.OLEObjects;
                analysis.EmbeddedObjectCount = oleObjects?.Count ?? 0;
                analysis.HasEmbeddedObjects = analysis.EmbeddedObjectCount > 0;

                // Count images (shapes that are pictures)
                try
                {
                    var shapes = sheet.Shapes;
                    if (shapes != null)
                    {
                        int imageCount = 0;
                        foreach (Excel.Shape shape in shapes)
                        {
                            if (shape.Type == Microsoft.Office.Core.MsoShapeType.msoPicture)
                            {
                                imageCount++;
                            }
                        }
                        analysis.ImageCount = imageCount;
                        analysis.HasImages = imageCount > 0;
                    }
                }
                catch
                {
                    // If we can't access shapes, set defaults
                    analysis.ImageCount = 0;
                    analysis.HasImages = false;
                }
            }
            catch (Exception ex)
            {
                // Log error but continue with partial data
                System.Diagnostics.Debug.WriteLine($"Error analyzing worksheet {sheet.Name}: {ex.Message}");
            }

            return analysis;
        }
    }
}

// --- END OF FILE AnalysisService.cs ---
