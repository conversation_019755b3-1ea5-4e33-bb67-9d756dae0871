// Debug endpoint to fix subscription data issues
// This helps fix missing current_period_end, plan_id, and other subscription data

import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

export async function POST(req: NextRequest) {
  // Only allow in development/staging
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
  }

  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-08-27.basil' as any,
  });

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  );

  try {
    const { subscriptionId } = await req.json();
    
    if (!subscriptionId) {
      return NextResponse.json({ error: 'Missing subscriptionId' }, { status: 400 });
    }

    // Get the subscription from database
    const { data: dbSub } = await supabase
      .from('subscriptions')
      .select('id, stripe_subscription_id, user_id')
      .eq('stripe_subscription_id', subscriptionId)
      .single();

    if (!dbSub) {
      return NextResponse.json({ error: 'Subscription not found in database' }, { status: 404 });
    }

    // Get the subscription from Stripe
    const stripeSub = await stripe.subscriptions.retrieve(subscriptionId);
    
    // Get price details
    const firstItem = stripeSub.items.data[0];
    const priceDetails = await stripe.prices.retrieve(firstItem.price.id);
    
    // Update subscription with all missing data
    const updateData = {
      status: stripeSub.status,
      quantity: firstItem.quantity || 1,
      plan_id: `Basic-${(firstItem.quantity || 1) > 1 ? 'Team' : 'Individual'}-${priceDetails.recurring?.interval || 'unknown'}`,
      current_period_start: new Date((stripeSub as any).current_period_start * 1000).toISOString(),
      current_period_end: new Date((stripeSub as any).current_period_end * 1000).toISOString(),
      trial_start: (stripeSub as any).trial_start ? new Date((stripeSub as any).trial_start * 1000).toISOString() : null,
      trial_end: (stripeSub as any).trial_end ? new Date((stripeSub as any).trial_end * 1000).toISOString() : null,
      cancel_at_period_end: (stripeSub as any).cancel_at_period_end,
    };

    const { error: updateError } = await supabase
      .from('subscriptions')
      .update(updateData)
      .eq('id', dbSub.id);

    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    // Also update licenses with correct expiry date
    await supabase
      .from('licenses')
      .update({
        expiry_date: new Date((stripeSub as any).current_period_end * 1000).toISOString(),
      })
      .eq('subscription_id', dbSub.id);

    return NextResponse.json({ 
      success: true,
      subscription: {
        id: dbSub.id,
        stripe_subscription_id: subscriptionId,
        updated_data: updateData
      }
    });

  } catch (error) {
    console.error('Error fixing subscription data:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
