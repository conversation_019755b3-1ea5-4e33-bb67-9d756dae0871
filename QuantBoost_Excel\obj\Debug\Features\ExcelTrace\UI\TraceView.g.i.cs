﻿#pragma checksum "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "23235BC474256D36313D32B031C09BCB1D7CDF1112392E51E6527951008419F3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using QuantBoost_Excel.Features.ExcelTrace.Model;
using QuantBoost_Excel.Features.ExcelTrace.UI;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace QuantBoost_Excel.Features.ExcelTrace.UI {
    
    
    /// <summary>
    /// TraceView
    /// </summary>
    public partial class TraceView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 425 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ColumnDefinition PrecedentColumnDef;
        
        #line default
        #line hidden
        
        
        #line 427 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ColumnDefinition ValueColumnDef;
        
        #line default
        #line hidden
        
        
        #line 437 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RichTextBox FormulaRichTextBox;
        
        #line default
        #line hidden
        
        
        #line 468 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TreeView PrecedentTreeView;
        
        #line default
        #line hidden
        
        
        #line 491 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem ExpandArrayMenuItem;
        
        #line default
        #line hidden
        
        
        #line 511 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 512 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 516 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/QuantBoost_Excel;component/features/exceltrace/ui/traceview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PrecedentColumnDef = ((System.Windows.Controls.ColumnDefinition)(target));
            return;
            case 2:
            this.ValueColumnDef = ((System.Windows.Controls.ColumnDefinition)(target));
            return;
            case 3:
            this.FormulaRichTextBox = ((System.Windows.Controls.RichTextBox)(target));
            return;
            case 4:
            this.PrecedentTreeView = ((System.Windows.Controls.TreeView)(target));
            
            #line 470 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            this.PrecedentTreeView.SelectedItemChanged += new System.Windows.RoutedPropertyChangedEventHandler<object>(this.PrecedentTreeView_SelectedItemChanged);
            
            #line default
            #line hidden
            
            #line 471 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            this.PrecedentTreeView.PreviewKeyDown += new System.Windows.Input.KeyEventHandler(this.PrecedentTreeView_PreviewKeyDown);
            
            #line default
            #line hidden
            
            #line 472 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            this.PrecedentTreeView.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.PrecedentTreeView_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 490 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigateToCell_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ExpandArrayMenuItem = ((System.Windows.Controls.MenuItem)(target));
            
            #line 491 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            this.ExpandArrayMenuItem.Click += new System.Windows.RoutedEventHandler(this.ExpandArray_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 493 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyCellAddress_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 494 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyCellValue_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 496 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ExpandAll_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 497 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.CollapseAll_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 511 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 518 "..\..\..\..\..\Features\ExcelTrace\UI\TraceView.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

