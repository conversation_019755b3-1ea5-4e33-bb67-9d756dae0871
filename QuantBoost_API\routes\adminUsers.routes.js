// adminUsers.routes.js
// Routes for super admin user viewing operations

const express = require('express');
const router = express.Router();
const { supabase } = require('../supabaseClient'); // Adjusted path
const { sendSuccess, sendError } = require('../utils/responseHelpers'); // Adjusted path
const { authenticateApiKey } = require('../middleware/authMiddleware'); // Adjusted path
const { logLicenseEvent } = require('../utils/eventLogger'); // Adjusted path

// Apply API key authentication to all routes in this file
router.use(authenticateApiKey);

// GET /v1/admin/users - List users with license/subscription summary
router.get('/users', async (req, res) => {
    const {
        page = 1,
        pageSize = 10,
        email,
        user_id,
        full_name,
        sortBy = 'created_at',
        sortOrder = 'desc'
    } = req.query;

    const pageInt = parseInt(page, 10);
    let pageSizeInt = parseInt(pageSize, 10);

    // Validate pageSize
    if (isNaN(pageSizeInt) || pageSizeInt <= 0 || pageSizeInt > 100) {
        pageSizeInt = 10; // Default to 10 if invalid, or could send error
    }
    // Validate page
    if (isNaN(pageInt) || pageInt <= 0) {
        return sendError(res, 400, 'page must be a positive integer.');
    }

    const offset = (pageInt - 1) * pageSizeInt;

    // Validate sortOrder
    const validSortOrder = sortOrder.toLowerCase() === 'asc' ? 'asc' : 'desc';

    // Validate sortBy
    const allowedSortByFields = ['created_at', 'email', 'full_name', 'updated_at', 'id'];
    if (!allowedSortByFields.includes(sortBy)) {
        return sendError(res, 400, `Invalid sortBy field. Allowed fields: ${allowedSortByFields.join(', ')}.`);
    }

    try {
        const selectString = `
            id,
            email,
            full_name,
            created_at,
            updated_at,
            avatar_url,
            raw_app_meta_data,
            licenses_count_total:licenses!user_id(count),
            licenses_count_active:licenses!user_id(count).eq(status,active),
            subscriptions_count_active:subscriptions!user_id(count).in(status,("active","trialing"))
        `;

        let query = supabase
            .from('profiles')
            .select(selectString, { count: 'exact' });

        // Filtering
        if (email) {
            query = query.ilike('email', `%${email}%`);
        }
        if (user_id) {
            // Basic UUID validation
            if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(user_id)) {
                return sendError(res, 400, 'Invalid user_id format. Must be a UUID.');
            }
            query = query.eq('id', user_id);
        }
        if (full_name) {
            query = query.ilike('full_name', `%${full_name}%`);
        }

        // Sorting
        query = query.order(sortBy, { ascending: validSortOrder === 'asc' });

        // Pagination
        query = query.range(offset, offset + pageSizeInt - 1);

        const { data: usersData, error, count: totalCount } = await query;

        if (error) {
            console.error('Error fetching users:', error);
            return sendError(res, 500, 'Failed to fetch users.', error.message);
        }

        const formattedUsers = usersData.map(u => {
            const userResult = { ...u };
            userResult.total_licenses = u.licenses_count_total && u.licenses_count_total.length > 0 ? u.licenses_count_total[0].count : 0;
            userResult.active_licenses = u.licenses_count_active && u.licenses_count_active.length > 0 ? u.licenses_count_active[0].count : 0;
            userResult.active_subscriptions = u.subscriptions_count_active && u.subscriptions_count_active.length > 0 ? u.subscriptions_count_active[0].count : 0;

            delete userResult.licenses_count_total;
            delete userResult.licenses_count_active;
            delete userResult.subscriptions_count_active;
            return userResult;
        });

        sendSuccess(res, {
            users: formattedUsers,
            total_count: totalCount,
            page: pageInt,
            pageSize: pageSizeInt
        });
    } catch (error) {
        console.error('Unexpected error fetching users:', error);
        sendError(res, 500, 'An unexpected error occurred.', error.message);
    }
});

// GET /v1/admin/users/:userId/licenses - List all licenses for a specific user
router.get('/users/:userId/licenses', async (req, res) => {
    const { userId } = req.params;
    const {
        page = 1,
        pageSize = 10,
        status, // Filter by license status (e.g., 'active', 'expired', 'revoked')
        sortBy = 'created_at',
        sortOrder = 'desc'
    } = req.query;

    const pageInt = parseInt(page, 10);
    let pageSizeInt = parseInt(pageSize, 10);

    // Validate pageSize
    if (isNaN(pageSizeInt) || pageSizeInt <= 0 || pageSizeInt > 100) {
        pageSizeInt = 10;
    }
    // Validate page
    if (isNaN(pageInt) || pageInt <= 0) {
        return sendError(res, 400, 'page must be a positive integer.');
    }

    const offset = (pageInt - 1) * pageSizeInt;

    // Validate sortOrder
    const validSortOrder = sortOrder.toLowerCase() === 'asc' ? 'asc' : 'desc';

    // Validate sortBy
    const allowedSortByFields = ['created_at', 'expires_at', 'status', 'license_key', 'id', 'updated_at'];
    if (!allowedSortByFields.includes(sortBy)) {
        return sendError(res, 400, `Invalid sortBy field. Allowed fields: ${allowedSortByFields.join(', ')}.`);
    }

    // Basic UUID validation for userId
    if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(userId)) {
        return sendError(res, 400, 'Invalid user_id format. Must be a UUID.');
    }

    try {
        // First, check if the user exists
        const { data: userData, error: userError } = await supabase
            .from('profiles')
            .select('id')
            .eq('id', userId)
            .single();

        if (userError || !userData) {
            return sendError(res, 404, 'User not found.');
        }

        let query = supabase
            .from('licenses')
            .select('*', { count: 'exact' })
            .eq('user_id', userId);

        // Filtering by status
        if (status) {
            const validStatuses = ['active', 'expired', 'revoked', 'pending_assignment', 'unassigned'];
            if (!validStatuses.includes(status.toLowerCase())) {
                return sendError(res, 400, `Invalid status filter. Allowed statuses: ${validStatuses.join(', ')}.`);
            }
            query = query.eq('status', status.toLowerCase());
        }

        // Sorting
        query = query.order(sortBy, { ascending: validSortOrder === 'asc' });

        // Pagination
        query = query.range(offset, offset + pageSizeInt - 1);

        const { data: licensesData, error: licensesError, count: totalCount } = await query;

        if (licensesError) {
            console.error(`Error fetching licenses for user ${userId}:`, licensesError);
            return sendError(res, 500, 'Failed to fetch licenses.', licensesError.message);
        }

        sendSuccess(res, {
            licenses: licensesData,
            total_count: totalCount,
            page: pageInt,
            pageSize: pageSizeInt
        });
    } catch (error) {
        console.error(`Unexpected error fetching licenses for user ${userId}:`, error);
        sendError(res, 500, 'An unexpected error occurred.', error.message);
    }
});

// GET /v1/admin/users/:userId/subscriptions - List all subscriptions for a specific user
router.get('/users/:userId/subscriptions', async (req, res) => {
    const { userId } = req.params;
    const {
        page = 1,
        pageSize = 10,
        status, // Filter by subscription status (e.g., 'active', 'trialing', 'past_due', 'canceled')
        sortBy = 'created_at',
        sortOrder = 'desc'
    } = req.query;

    const pageInt = parseInt(page, 10);
    let pageSizeInt = parseInt(pageSize, 10);

    // Validate pageSize
    if (isNaN(pageSizeInt) || pageSizeInt <= 0 || pageSizeInt > 100) {
        pageSizeInt = 10;
    }
    // Validate page
    if (isNaN(pageInt) || pageInt <= 0) {
        return sendError(res, 400, 'page must be a positive integer.');
    }

    const offset = (pageInt - 1) * pageSizeInt;

    // Validate sortOrder
    const validSortOrder = sortOrder.toLowerCase() === 'asc' ? 'asc' : 'desc';

    // Validate sortBy
    const allowedSortByFields = [
        'created_at',
        'updated_at',
        'status',
        'current_period_start',
        'current_period_end',
        'trial_start',
        'trial_end',
        'id'
    ];
    if (!allowedSortByFields.includes(sortBy)) {
        return sendError(res, 400, `Invalid sortBy field. Allowed fields: ${allowedSortByFields.join(', ')}.`);
    }

    // Basic UUID validation for userId
    if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(userId)) {
        return sendError(res, 400, 'Invalid user_id format. Must be a UUID.');
    }

    try {
        // First, check if the user exists
        const { data: userData, error: userError } = await supabase
            .from('profiles')
            .select('id')
            .eq('id', userId)
            .single();

        if (userError || !userData) {
            return sendError(res, 404, 'User not found.');
        }

        let query = supabase
            .from('subscriptions')
            .select('*', { count: 'exact' })
            .eq('user_id', userId);

        // Filtering by status
        if (status) {
            // Assuming subscription_status_enum includes these values
            const validStatuses = ['active', 'trialing', 'past_due', 'canceled', 'incomplete', 'incomplete_expired', 'unpaid', 'paused'];
            if (!validStatuses.includes(status.toLowerCase())) {
                return sendError(res, 400, `Invalid status filter. Allowed statuses: ${validStatuses.join(', ')}.`);
            }
            query = query.eq('status', status.toLowerCase());
        }

        // Sorting
        query = query.order(sortBy, { ascending: validSortOrder === 'asc' });

        // Pagination
        query = query.range(offset, offset + pageSizeInt - 1);

        const { data: subscriptionsData, error: subscriptionsError, count: totalCount } = await query;

        if (subscriptionsError) {
            console.error(`Error fetching subscriptions for user ${userId}:`, subscriptionsError);
            return sendError(res, 500, 'Failed to fetch subscriptions.', subscriptionsError.message);
        }

        sendSuccess(res, {
            subscriptions: subscriptionsData,
            total_count: totalCount,
            page: pageInt,
            pageSize: pageSizeInt
        });
    } catch (error) {
        console.error(`Unexpected error fetching subscriptions for user ${userId}:`, error);
        sendError(res, 500, 'An unexpected error occurred.', error.message);
    }
});

module.exports = router;
