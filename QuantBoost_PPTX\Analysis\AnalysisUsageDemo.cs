using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using QuantBoost_Powerpoint_Addin.Utilities; // For project-specific utilities
using QuantBoost_Shared.Utilities; // For ToastNotifier, ErrorHandlingService

namespace QuantBoost_Powerpoint_Addin.Analysis
{
    /// <summary>
    /// Provides example methods showing different ways to use the async analysis functionality.
    /// This class serves as both documentation and a quick reference for developers.
    /// </summary>
    public static class AnalysisUsageDemo
    {
        /// <summary>
        /// Shows how to perform analysis with a simple progress bar in a WinForms control.
        /// </summary>
        public static async Task SimpleProgressBarDemoAsync(string presentationPath, ProgressBar progressBar, Label statusLabel)
        {
            try
            {
                var results = await AnalysisExtensions.AnalyzeWithProgressCallbackAsync(
                    presentationPath,
                    percent =>
                    {
                        if (progressBar.InvokeRequired)
                        {
                            progressBar.Invoke(new Action(() => progressBar.Value = percent));
                        }
                        else
                        {
                            progressBar.Value = percent;
                        }
                        if (statusLabel.InvokeRequired)
                        {
                            statusLabel.Invoke(new Action(() => statusLabel.Text = $"Progress: {percent}%"));
                        }
                        else
                        {
                            statusLabel.Text = $"Progress: {percent}%";
                        }
                    },
                    CancellationToken.None);
                MessageBox.Show($"Analysis complete! Found {results.overallSummary.TotalImages} images " +
                                $"and {results.overallSummary.TotalCharts} charts across " +
                                $"{results.overallSummary.SlideCount} slides.",
                                "Analysis Complete");
            }
            catch (OperationCanceledException)
            {
                statusLabel.Text = "Analysis cancelled";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during analysis: {ex.Message}", "Error");
                statusLabel.Text = "Analysis failed";
            }
        }

        /// <summary>
        /// Shows how to perform analysis with a toast notification for progress.
        /// </summary>
        public static async Task ToastNotificationDemoAsync(string presentationPath)
        {
            try
            {
                // Use the extension method that provides toast notifications
                var results = await AnalysisExtensions.AnalyzeWithToastAsync(
                    presentationPath, 
                    "Analyzing your presentation");

                // Process results
                Console.WriteLine($"Analysis complete! Found {results.slideSummaries.Count} slides.");
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Analysis was cancelled");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during analysis: {ex.Message}", "Error");
            }
        }

        /// <summary>
        /// Shows how to perform analysis with a modal progress dialog.
        /// </summary>
        public static void ProgressDialogDemo(string presentationPath, Form owner = null)
        {
            try
            {
                // Use the extension method that shows a progress dialog
                var results = AnalysisExtensions.AnalyzeWithDialog(
                    presentationPath, 
                    owner,
                    "Analyzing Presentation");

                // Check if the operation was cancelled
                if (results == null)
                {
                    MessageBox.Show("Operation was cancelled.", "Analysis Cancelled");
                    return;
                }

                // Process results
                MessageBox.Show($"Analysis complete! Found {results.Value.overallSummary.TotalImages} images " +
                                $"and {results.Value.overallSummary.TotalCharts} charts.",
                                "Analysis Complete");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during analysis: {ex.Message}", "Error");
            }
        }

        /// <summary>
        /// Shows how to perform analysis with cancellation support.
        /// </summary>
        public static async Task CancellableDemoAsync(string presentationPath, Button cancelButton)
        {
            using (var cts = new CancellationTokenSource())
            {
                try
                {
                    string originalButtonText = cancelButton.Text;
                    cancelButton.Text = "Cancel Analysis";
                    cancelButton.Enabled = true;
                    EventHandler cancelHandler = (s, e) => cts.Cancel();
                    cancelButton.Click += cancelHandler;
                    var results = await AnalysisExtensions.AnalyzeWithProgressCallbackAsync(
                        presentationPath,
                        percent => ToastNotifier.ShowToast($"Progress: {percent}%"),
                        cts.Token);
                    ToastNotifier.ShowToast("Analysis complete!", 3000);
                }
                catch (OperationCanceledException)
                {
                    MessageBox.Show("Analysis was cancelled.", "Operation Cancelled");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error during analysis: {ex.Message}", "Error");
                }
                finally
                {
                    cancelButton.Text = "Analyze";
                    cancelButton.Enabled = true;
                    cancelButton.Click -= (s, e) => cts.Cancel();
                }
            }
        }

        /// <summary>
        /// Shows how to perform background analysis.
        /// </summary>
        public static void BackgroundAnalysisDemo(string presentationPath)
        {
            // Run the analysis in the background
            AnalysisExtensions.AnalyzeInBackground(
                presentationPath,
                // On completion callback
                (slideSummaries, overallSummary) =>
                {
                    // This will be called when analysis completes successfully
                    MessageBox.Show($"Background analysis complete! Found {overallSummary.TotalImages} images " +
                                    $"across {overallSummary.SlideCount} slides.",
                                    "Analysis Complete");
                },
                // On error callback
                (ex) =>
                {
                    // This will be called if an error occurs
                    MessageBox.Show($"Background analysis error: {ex.Message}", "Error");
                },
                // Show progress notifications
                true);

            // Immediately return, analysis continues in background
            MessageBox.Show("Analysis has started in the background. You'll be notified when it's complete.", 
                           "Background Analysis");
        }

        /// <summary>
        /// Shows how to properly register a background task with the add-in.
        /// </summary>
        public static void RegisterWithAddInDemo()
        {
            // TODO: Implement or remove references to GlobalCancellationToken and RegisterBackgroundTask if not available
            // For now, comment out or provide a stub
            /*
            var linkedTokenSource = CancellationTokenSource.CreateLinkedTokenSource(
                Globals.ThisAddIn.GlobalCancellationToken);

            var analysisTask = AnalysisService.AnalyzePresentationAsync(
                presentationPath,
                new Progress<ProgressState>(state => ToastNotifier.ShowToast(state.CurrentStep)),
                linkedTokenSource.Token);

            Globals.ThisAddIn.RegisterBackgroundTask(analysisTask);

            analysisTask.ContinueWith(task =>
            {
                try
                {
                    if (task.IsCanceled)
                    {
                        // Handle cancellation
                    }
                    else if (task.IsFaulted && task.Exception != null)
                    {
                        ErrorHandlingService.HandleException(
                            task.Exception.InnerException ?? task.Exception,
                            "Analysis error");
                    }
                    else
                    {
                        var results = task.Result;
                        // Show results or update UI...
                    }
                }
                finally
                {
                    linkedTokenSource.Dispose();
                }
            });
            */
            MessageBox.Show("RegisterWithAddInDemo is not fully implemented. Please implement GlobalCancellationToken and RegisterBackgroundTask in ThisAddIn if needed.", "Not Implemented");
        }
    }
}