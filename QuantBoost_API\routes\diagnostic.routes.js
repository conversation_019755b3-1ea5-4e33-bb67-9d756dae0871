const express = require('express');
const router = express.Router();
const { supabase, supabaseAdmin } = require('../supabaseClient');
const { sendSuccess, sendError } = require('../utils/responseHelpers');
const { authenticateJWT } = require('../middleware/authMiddleware');

// Apply JWT authentication to all routes in this file
router.use(authenticateJWT);

// GET /me/diagnostic - Comprehensive diagnostic for the authenticated user
router.get('/diagnostic', async (req, res) => {
    const userId = req.user.id;
    const userEmail = req.user.email;

    if (!userId) {
        return sendError(res, 401, 'User not authenticated.');
    }

    console.log(`🔧 [DIAGNOSTIC] Starting diagnostic for user: ${userId} (${userEmail})`);

    try {
        const diagnostic = {
            user: {
                id: userId,
                email: userEmail
            },
            queries: {}
        };

        // 1. Check profile table
        const { data: profile, error: profileError } = await supabaseAdmin
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();

        diagnostic.queries.profile = {
            error: profileError,
            data: profile,
            found: !!profile
        };

        // 2. Check subscriptions table
        const { data: subscriptions, error: subscriptionsError } = await supabaseAdmin
            .from('subscriptions')
            .select('*')
            .eq('user_id', userId);

        diagnostic.queries.subscriptions = {
            error: subscriptionsError,
            data: subscriptions,
            count: subscriptions?.length || 0
        };

        // 3. Check licenses table
        const { data: licenses, error: licensesError } = await supabaseAdmin
            .from('licenses')
            .select('*')
            .eq('user_id', userId);

        diagnostic.queries.licenses = {
            error: licensesError,
            data: licenses,
            count: licenses?.length || 0
        };

        // 4. Check if there are any licenses with the profile's email
        const { data: licensesByEmail, error: licensesByEmailError } = await supabaseAdmin
            .from('licenses')
            .select('*')
            .or(`user_email.eq.${userEmail},assigned_to_email.eq.${userEmail}`);

        diagnostic.queries.licensesByEmail = {
            error: licensesByEmailError,
            data: licensesByEmail,
            count: licensesByEmail?.length || 0
        };

        // 5. Check if there's a profile with matching email
        const { data: profileByEmail, error: profileByEmailError } = await supabaseAdmin
            .from('profiles')
            .select('*')
            .eq('email', userEmail);

        diagnostic.queries.profilesByEmail = {
            error: profileByEmailError,
            data: profileByEmail,
            count: profileByEmail?.length || 0
        };

        console.log(`🔧 [DIAGNOSTIC] Complete diagnostic for user ${userId}:`, JSON.stringify(diagnostic, null, 2));

        sendSuccess(res, diagnostic);
    } catch (err) {
        console.error('Unexpected error in diagnostic:', err);
        sendError(res, 500, 'An unexpected error occurred during diagnostic.');
    }
});

module.exports = router;
