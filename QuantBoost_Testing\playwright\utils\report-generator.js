/* Simple report aggregator producing summary markdown & HTML */
const fs = require('fs');
const path = require('path');

function generate() {
  const reportDir = path.join(process.cwd(), 'playwright', 'reports');
  if (!fs.existsSync(reportDir)) fs.mkdirSync(reportDir, { recursive: true });
  const summaryPath = path.join(reportDir, 'summary.md');
  const htmlPath = path.join(reportDir, 'dashboard.html');
  const timestamp = new Date().toISOString();

  const markdown = `# QuantBoost E2E Test Summary\n\nGenerated: ${timestamp}\n\n> Detailed HTML report located in ./html/index.html (if generated).\n\n`;
  fs.writeFileSync(summaryPath, markdown, 'utf-8');

  const html = `<!DOCTYPE html><html><head><meta charset='utf-8'/><title>QuantBoost E2E Dashboard</title><style>body{font-family:system-ui,Arial;margin:2rem;}h1{margin-top:0;}code{background:#f4f4f4;padding:2px 4px;border-radius:4px;}</style></head><body><h1>QuantBoost E2E Dashboard</h1><p>Generated: ${timestamp}</p><p>See <code>playwright/reports/html/index.html</code> for Playwright's full HTML report.</p></body></html>`;
  fs.writeFileSync(htmlPath, html, 'utf-8');
  console.log('[report-generator] summary written');
}

if (require.main === module) generate();

module.exports = { generate };
