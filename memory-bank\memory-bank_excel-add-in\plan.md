---
title: Plan for QuantBoost Excel Add-in
purpose: Outlines the development tasks for the QuantBoost Excel Add-in.
projects: ["excel-add-in"]
source_analysis: 
status: development
last_updated: 2025-06-15 22:17 PT
tags: ["excel-add-in", "plan", "development", "features"]
---



### Phase 1: The New Analysis Engine (The "Save & Measure" Method)

We'll refactor the `AnalysisService` to implement this new logic. This process is resource-intensive, so making it asynchronous with progress reporting is critical.

**1. Prepare the Temp Directory:**
The service will manage a temporary folder within your application's `AppData` folder. This ensures a clean, dedicated space for the operation.

**2. Implement the `AnalyzeAsync` Method:**
This will be the heart of the new engine.

```csharp
// In a new or refactored AnalysisService.cs
using Excel = Microsoft.Office.Interop.Excel;
using System.IO;
using System.Threading.Tasks;
using System.Runtime.InteropServices;

public class AnalysisService
{
    // The main analysis method
    public async Task<List<WorksheetAnalysis>> AnalyzeWorkbookAsync(IProgress<(string status, int percentage)> progress)
    {
        var results = new List<WorksheetAnalysis>();
        Excel.Application excelApp = Globals.ThisAddIn.Application;
        Excel.Workbook activeWorkbook = excelApp.ActiveWorkbook;

        if (activeWorkbook == null) return results;

        // 1. Prepare temp directory
        string tempFolderPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
            "QuantBoost", "TempAnalysis");
        
        // Ensure the directory exists and is clean
        if (Directory.Exists(tempFolderPath))
        {
            Directory.Delete(tempFolderPath, true);
        }
        Directory.CreateDirectory(tempFolderPath);

        int sheetCount = activeWorkbook.Worksheets.Count;
        int processedCount = 0;
        
        // Disable alerts to prevent save prompts
        excelApp.DisplayAlerts = false;

        try
        {
            // 2. Iterate through each worksheet
            foreach (Excel.Worksheet sheet in activeWorkbook.Worksheets)
            {
                processedCount++;
                progress?.Report(($"Processing '{sheet.Name}'...", (processedCount * 100) / sheetCount));

                // 3. Create a new temporary workbook in memory
                Excel.Workbook tempWorkbook = excelApp.Workbooks.Add();
                
                // 4. Copy the current sheet to the new workbook
                sheet.Copy(tempWorkbook.Worksheets[1]);
                
                // 5. The new workbook now has our sheet and the default "Sheet1". Delete the default.
                if (tempWorkbook.Worksheets.Count > 1) {
                    ((Excel.Worksheet)tempWorkbook.Worksheets["Sheet1"]).Delete();
                }

                // 6. Save the temporary workbook (containing only our sheet)
                string tempFilePath = Path.Combine(tempFolderPath, $"{Guid.NewGuid()}_{sheet.Name}.xlsx");
                tempWorkbook.SaveAs(tempFilePath, Excel.XlFileFormat.xlOpenXMLWorkbook);

                // 7. Close the temp workbook without saving changes again
                tempWorkbook.Close(SaveChanges: false);
                Marshal.ReleaseComObject(tempWorkbook);
                
                // 8. Get the file size and other metadata
                var fileInfo = new FileInfo(tempFilePath);
                var analysis = new WorksheetAnalysis
                {
                    Name = sheet.Name,
                    SizeBytes = fileInfo.Length, // The ACCURATE size of the isolated sheet
                    UsedRange = sheet.UsedRange.Address,
                    RowCount = sheet.UsedRange.Rows.Count,
                    CellCount = sheet.UsedRange.Cells.Count,
                    FormulaCount = sheet.UsedRange.Cells.SpecialCells(Excel.XlCellType.xlCellTypeFormulas).Count,
                    ImageCount = sheet.Shapes.Cast<Excel.Shape>().Count(s => s.Type == Microsoft.Office.Core.MsoShapeType.msoPicture)
                };
                results.Add(analysis);

                Marshal.ReleaseComObject(sheet);
                await Task.Delay(50); // Give Excel a moment to breathe
            }
        }
        finally
        {
            // 9. Clean up
            excelApp.DisplayAlerts = true;
            if (Directory.Exists(tempFolderPath))
            {
                Directory.Delete(tempFolderPath, true);
            }
        }

        return results;
    }
}
```

### Phase 2: Refactoring the UI to WPF with MVVM

This is where we achieve the modern, consistent look and feel.

**1. Project Setup:**
*   Add references to the required WPF assemblies (`PresentationCore`, `PresentationFramework`, `WindowsBase`).
*   Add a new `WPF UserControl` to your project. Let's call it `AnalysisPaneView.xaml`.
*   Add a new class for the ViewModel: `AnalysisPaneViewModel.cs`.

**2. The View (`AnalysisPaneView.xaml`):**
This XAML defines the UI structure and binds to the ViewModel. We'll use a `DataGrid` for the results.

```xml
<UserControl x:Class="ExcelAnalyzerPro.AnalysisPaneView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FontFamily="Segoe UI" Background="#F5F7FA">
    <UserControl.Resources>
        <!-- Define your QuantBoost color palette -->
        <SolidColorBrush x:Key="PrimaryBlueBrush" Color="#577BF9"/>
        <SolidColorBrush x:Key="HeaderBackgroundBrush" Color="#F0F2F5"/>
        <Style TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="{StaticResource HeaderBackgroundBrush}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="BorderBrush" Value="#DDE3EA"/>
        </Style>
    </UserControl.Resources>
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Title & Button -->
            <RowDefinition Height="*"/>    <!-- Results DataGrid -->
            <RowDefinition Height="Auto"/> <!-- Totals -->
            <RowDefinition Height="Auto"/> <!-- Status & Progress -->
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,15">
            <TextBlock Text="Workbook Size Analyzer" FontSize="18" FontWeight="Light" Foreground="#34495E"/>
            <Button Content="Analyze Now" Command="{Binding AnalyzeCommand}" IsEnabled="{Binding IsNotAnalyzing}"
                    Background="{StaticResource PrimaryBlueBrush}" Foreground="White" Padding="15,8" Margin="0,10,0,0"
                    HorizontalAlignment="Left" BorderThickness="0"/>
        </StackPanel>

        <!-- Results Grid -->
        <DataGrid Grid.Row="1" ItemsSource="{Binding AnalysisResults}" AutoGenerateColumns="False"
                  IsReadOnly="True" GridLinesVisibility="Horizontal" BorderThickness="0"
                  RowStyle="{StaticResource {x:Type DataGridRow}}">
            <DataGrid.Columns>
                <DataGridTextColumn Header="Worksheet" Binding="{Binding Name}" Width="*"/>
                <DataGridTextColumn Header="Size" Binding="{Binding SizeBytes, StringFormat={}{0:N0} KB}" Width="100"/>
                <DataGridTextColumn Header="%" Binding="{Binding SizePercentage, StringFormat={}{0:F2}%}" Width="60"/>
                <DataGridTextColumn Header="Rows" Binding="{Binding RowCount, StringFormat=N0}" Width="80"/>
                <DataGridTextColumn Header="Formulas" Binding="{Binding FormulaCount, StringFormat=N0}" Width="80"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Totals Section -->
        <Border Grid.Row="2" Background="{StaticResource HeaderBackgroundBrush}" Padding="10" Margin="0,1,0,0">
            <Grid>
                <TextBlock Text="Total" FontWeight="Bold"/>
                <TextBlock Text="{Binding TotalSize, StringFormat={}{0:N0} KB}" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,0,220,0"/>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Grid Grid.Row="3" Margin="0,10,0,0">
            <ProgressBar Value="{Binding ProgressPercent}" Height="5" VerticalAlignment="Bottom" Visibility="{Binding IsAnalyzing, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            <TextBlock Text="{Binding StatusText}" Foreground="Gray" VerticalAlignment="Center"/>
        </Grid>
    </Grid>
</UserControl>
```
*Note: You will need to implement a simple `IValueConverter` for `BooleanToVisibilityConverter`.*

**3. The ViewModel (`AnalysisPaneViewModel.cs`):**
This class contains the UI logic and data. It will use the `AnalysisService`.

```csharp
// You will need a simple RelayCommand implementation and a BaseViewModel for INotifyPropertyChanged
public class AnalysisPaneViewModel : BaseViewModel // Implements INotifyPropertyChanged
{
    private readonly AnalysisService _analysisService;
    private ObservableCollection<WorksheetAnalysis> _analysisResults;
    private string _statusText;
    private bool _isAnalyzing;
    // ... other properties for TotalSize, ProgressPercent etc.

    public ObservableCollection<WorksheetAnalysis> AnalysisResults
    {
        get => _analysisResults;
        set => SetProperty(ref _analysisResults, value);
    }
    
    // Bind IsEnabled on button to this
    public bool IsNotAnalyzing => !_isAnalyzing; 

    public ICommand AnalyzeCommand { get; }

    public AnalysisPaneViewModel()
    {
        _analysisService = new AnalysisService();
        AnalysisResults = new ObservableCollection<WorksheetAnalysis>();
        AnalyzeCommand = new RelayCommand(async () => await ExecuteAnalyzeAsync(), () => !_isAnalyzing);
        _statusText = "Ready to analyze.";
    }

    private async Task ExecuteAnalyzeAsync()
    {
        if (_isAnalyzing) return;

        IsAnalyzing = true;
        AnalysisResults.Clear();
        TotalSize = 0;

        try
        {
            var progress = new Progress<(string status, int percentage)>(p => {
                StatusText = p.status;
                ProgressPercent = p.percentage;
            });

            var results = await _analysisService.AnalyzeWorkbookAsync(progress);

            // Post-process to calculate percentages
            long total = results.Sum(r => r.SizeBytes);
            TotalSize = total / 1024; // Show in KB
            foreach (var result in results.OrderByDescending(r => r.SizeBytes))
            {
                result.SizePercentage = (total > 0) ? ((double)result.SizeBytes / total * 100) : 0;
                result.SizeBytes /= 1024; // Convert to KB for display
                AnalysisResults.Add(result);
            }
            StatusText = $"Analysis complete. Found {results.Count} worksheets.";
        }
        catch (Exception ex)
        {
            StatusText = $"An error occurred: {ex.Message}";
        }
        finally
        {
            IsAnalyzing = false;
        }
    }
}
```

### Phase 3: Stitching It All Together in VSTO

Now, we host our new WPF control inside the add-in.

**1. Create a Host UserControl:**
*   Add a standard `User Control (Windows Forms)` to your project. Call it `WpfHostControl.cs`.
*   From the Toolbox, drag an `ElementHost` control onto the `WpfHostControl`'s design surface and set its `Dock` property to `Fill`.
*   In the `WpfHostControl`'s constructor, create an instance of your WPF view and assign it to the `ElementHost`.

```csharp
// In WpfHostControl.cs
public partial class WpfHostControl : UserControl
{
    public WpfHostControl()
    {
        InitializeComponent();
        
        var wpfView = new AnalysisPaneView();
        // The ViewModel is automatically created by the View's constructor
        elementHost1.Child = wpfView;
    }
}
```

**2. Update `ThisAddIn.cs`:**
Modify the code that creates the task pane to use your new `WpfHostControl`.

```csharp
// In ThisAddIn.cs
private Microsoft.Office.Tools.CustomTaskPane analysisTaskPane;

public void ToggleAnalysisPane()
{
    if (analysisTaskPane == null)
    {
        // Use the host control now
        var hostControl = new WpfHostControl(); 
        analysisTaskPane = this.CustomTaskPanes.Add(hostControl, "Workbook Size Analyzer");
        analysisTaskPane.Visible = true;
        analysisTaskPane.Width = 450;
    }
    else
    {
        analysisTaskPane.Visible = !analysisTaskPane.Visible;
    }
}
```