// --- START OF FILE ExcelAnalysisModels.cs ---

using System.Collections.Generic;

namespace QuantBoost_Excel.Features.SizeAnalyzer.Logic
{
    /// <summary>
    /// Represents the overall analysis summary for an Excel workbook, similar to OverallAnalysisSummary for PowerPoint.
    /// </summary>
    public class WorkbookAnalysisSummary
    {
        public int WorksheetCount { get; set; }
        public int TotalCells { get; set; }
        public int TotalFormulas { get; set; }
        public int TotalImages { get; set; }
        public int TotalCharts { get; set; }
        public int TotalEmbeddedObjects { get; set; }
        public long TotalWorkbookSizeBytes { get; set; }
        public string LargestWorksheetName { get; set; }
        public long LargestWorksheetSizeBytes { get; set; }
        public string LargestWorksheetDescription { get; set; }
    }

    /// <summary>
    /// Represents the analysis summary for a single worksheet, similar to SlideAnalysisSummary for PowerPoint.
    /// </summary>
    public class WorksheetAnalysisSummary
    {
        public string Name { get; set; }
        public long SizeBytes { get; set; }
        public bool HasImages { get; set; }
        public int ImageCount { get; set; }
        public long ImageSizeBytes { get; set; }
        public int CellCount { get; set; }
        public int RowCount { get; set; }
        public int FormulaCount { get; set; }
        public int FormulaCharTotal { get; set; }
        public List<LargeFormula> LargeFormulas { get; set; } = new List<LargeFormula>();
        public string UsedRange { get; set; }
        public double UnusedRangePercentage { get; set; }
        public bool HasCharts { get; set; }
        public int ChartCount { get; set; }
        public bool HasEmbeddedObjects { get; set; }
        public int EmbeddedObjectCount { get; set; }
    }

    /// <summary>
    /// Represents a large formula found during analysis.
    /// </summary>
    public class LargeFormula
    {
        public string WorksheetName { get; set; }
        public string CellReference { get; set; }
        public int FormulaLength { get; set; }
        public string Formula { get; set; }
    }

    /// <summary>
    /// Represents a worksheet component for size analysis (from the original MVP).
    /// Used internally by the analysis engine.
    /// </summary>
    public class WorksheetComponent
    {
        public string Name { get; set; }
        public long Size { get; set; }
        public long DataSize { get; set; }
        public long FormattingSize { get; set; }
        public long OtherSize { get; set; }
        public bool HasDrawings { get; set; }
        public bool HasImages { get; set; }
        public int ImageCount { get; set; }
        public long ImageSizeBytes { get; set; }
    }

    /// <summary>
    /// Display item for the results grid, similar to AnalysisResultDisplayItem for PowerPoint.
    /// </summary>
    public class WorksheetAnalysisDisplayItem
    {
        public string WorksheetName { get; set; }
        public string ContentType { get; set; }
        public long SizeBytes { get; set; }
        public double SizeKB => SizeBytes / 1024.0;
        public double SizePercentage { get; set; }
        public string ContentDetails { get; set; }

        // Individual count properties for separate columns
        public int CellCount { get; set; }
        public int FormulaCount { get; set; }
        public int ImageCount { get; set; }
        public int ChartCount { get; set; }
        public int EmbeddedObjectCount { get; set; }
    }
}

// --- END OF FILE ExcelAnalysisModels.cs ---