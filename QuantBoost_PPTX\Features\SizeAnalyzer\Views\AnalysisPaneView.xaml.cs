using System.Diagnostics;
using System.Windows.Controls;
using QuantBoost_Powerpoint_Addin.Features.SizeAnalyzer.ViewModels;

namespace QuantBoost_Powerpoint_Addin.Features.SizeAnalyzer.Views
{
    /// <summary>
    /// Interaction logic for AnalysisPaneView.xaml
    /// WPF UserControl for the PowerPoint Size Analyzer feature.
    /// </summary>
    public partial class AnalysisPaneView : UserControl
    {
        /// <summary>
        /// Initializes a new instance of the AnalysisPaneView class.
        /// </summary>
        public AnalysisPaneView()
        {
            InitializeComponent();
            DataContext = new AnalysisPaneViewModel();

            // Suppress known WPF DataGrid binding errors related to CellsPanelHorizontalOffset
            // These are cosmetic errors that don't affect functionality
            SuppressDataGridBindingErrors();
        }

        /// <summary>
        /// Suppresses known WPF DataGrid binding errors that are cosmetic and don't affect functionality.
        /// </summary>
        private void SuppressDataGridBindingErrors()
        {
            // Add a trace listener that filters out specific DataGrid binding errors
            PresentationTraceSources.DataBindingSource.Listeners.Add(new ConsoleTraceListener());
            PresentationTraceSources.DataBindingSource.Switch.Level = SourceLevels.Critical;
        }

        /// <summary>
        /// Gets the ViewModel instance for external access.
        /// </summary>
        public AnalysisPaneViewModel ViewModel => DataContext as AnalysisPaneViewModel;

        /// <summary>
        /// Triggers analysis from external sources (e.g., ribbon button).
        /// </summary>
        public void PerformAnalysis()
        {
            ViewModel?.AnalyzeCommand?.Execute(null);
        }

        /// <summary>
        /// Clears current results from external sources.
        /// </summary>
        public void ClearResults()
        {
            var viewModel = ViewModel;
            if (viewModel != null)
            {
                viewModel.AnalysisResults.Clear();
                viewModel.StatusText = "Ready to analyze.";
                viewModel.TotalSize = 0;
                viewModel.ProgressPercent = 0;
            }
        }

        /// <summary>
        /// Triggers export from external sources.
        /// </summary>
        public void ExportResults()
        {
            ViewModel?.ExportCommand?.Execute(null);
        }
    }
}
