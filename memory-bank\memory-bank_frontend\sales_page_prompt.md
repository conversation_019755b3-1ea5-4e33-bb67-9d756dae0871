
## Coding Agent Prompt — Sales MVP (2 weeks)

You are working in the QuantBoost monorepo to ship a minimal enterprise trials dashboard. Deliver the core flow end‑to‑end: create 30‑day trial → bulk invite → accept invite → convert via Stripe Checkout. Keep scope lean; avoid analytics, invoices UI, complex contacts, or audit logs.

- Primary goal: Strategic 30‑day trial creation with up to 50 seats, bulk invites, secure token acceptance, and conversion via Stripe Checkout.
- Constraints: Next.js 15 (React 19/TS 5), Supabase (auth/DB), Stripe (Checkout). No secrets in client code. Strong validation and safe token handling.

### Repo, stack, and files

- Monorepo root: c:\VS projects\QuantBoost
- Frontend app: QuantBoost_Frontend (Next.js 15, React 19, TS 5, shadcn UI)
  - Client Supabase: SupabaseClient.ts
  - Server Supabase (add if missing): SupabaseServerClient.ts
- Current Sales UI to extend (single page):
  - `src/app/dashboard/sales/page.tsx`
  - `src/app/dashboard/sales/CreateEnterpriseCustomerForm.tsx` (reuse minimal)
  - Ignore `CreateEnterpriseInvoiceForm.tsx` for MVP
- API pattern: Next.js Route Handlers under `src/app/api/.../route.ts`
- Supabase schema reference: `memory-bank/memory-bank_api/supabase_tables.md`

### Scope and acceptance criteria

Must have
- Trials
  - Create 30‑day trial for a company (default seat_limit 50)
  - Bulk invite emails (paste list or CSV), tokenized links emailed
  - Track seats_used and days remaining
  - Convert to paid via Stripe Checkout (annual/quarterly price, returns URL)
- Invite acceptance
  - `/invite/[token]` validates token; creates or links Supabase Auth user; marks accepted; increments seats atomically; blocks on expiry or over‑capacity
- Sales dashboard
  - List active trials with company, seats (X/50), days left; actions: Invite More, Convert to Paid, View Invites; search by company

Out of scope (MVP)
- Contacts management, invoices UI, analytics dashboards, audit logs, multi‑role RBAC

Definition of Done
- E2E: create trial → invite → accept → convert gets Checkout URL
- Client/server validation with clear errors; secure token usage; basic RLS preventing public reads
- README updates for local run and Stripe test mode

### Supabase data model (3 tables)

Reuse existing: `public.enterprise_customers` (already present). Add only:

```
CREATE TABLE IF NOT EXISTS public.enterprise_trials (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id uuid REFERENCES public.enterprise_customers(id) ON DELETE CASCADE,
  status text NOT NULL CHECK (status IN ('active','expired','converted')) DEFAULT 'active',
  seat_limit int NOT NULL DEFAULT 50,
  seats_used int NOT NULL DEFAULT 0,
  expires_at timestamptz NOT NULL,
  converted_at timestamptz,
  created_at timestamptz NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.trial_invites (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  trial_id uuid REFERENCES public.enterprise_trials(id) ON DELETE CASCADE,
  email text NOT NULL,
  invite_token text UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
  accepted_at timestamptz,
  user_id uuid,
  created_at timestamptz NOT NULL DEFAULT now()
);
```

RLS: enable on both new tables; for MVP, access via server routes (service role). Do not expose invite_token in general list responses.

Indexes: `customer_id` on trials; `trial_id` and `email` on invites.

### API routes to implement

- `GET /api/sales/trials` — list trials joined with enterprise_customers (search by company), compute days_left
- `POST /api/sales/trials` — create/find customer; create trial with expires_at=now()+30d; return { trial, customer }
- `POST /api/sales/trials/[trialId]/invite` — accept emails[] or csv; create invites with tokens; send emails; return summary
- `GET /api/sales/trials/[trialId]` — trial detail with invites (redact tokens)
- `POST /api/invite/accept` — validate token; ensure active, not expired, capacity available; create/link user; mark accepted; increment seats in a single transaction
- `POST /api/sales/trials/[trialId]/convert` — ensure/create Stripe Customer; create Checkout session (annual/quarterly); return { checkoutUrl }

Shared helpers: `lib/supabaseServer.ts`, `lib/validation.ts` (zod), `lib/stripe.ts`, `lib/email.ts`.

### UI updates (single page)

- Add dialogs/components:
  - CreateTrialDialog (company, email, optional domain)
  - TrialsTable (company, seats, days left, actions)
  - BulkInviteDialog (textarea for emails/CSV)
  - ViewInvitesDialog (list with accepted statuses)
- Add `/invite/[token]/page.tsx` to call accept endpoint and show success/failure
- Use react-hook-form + zod; add toasts and loading states

### Email (simple)

- Provider: Resend/Postmark (text email)
- Subject: “You’re invited to a QuantBoost strategic trial”
- Link: `${NEXT_PUBLIC_APP_URL}/invite/[token]`

### Stripe Checkout

- Prices: Annual `price_1RC3HTE6FvhUKV1bE9D6zf6e`, Quarterly `price_1RCQeBE6FvhUKV1bUN94Oihf` (make configurable)
- Quantity: default to `seats_used` (or seat_limit if you prefer)

### Env vars (server secrets only)

```
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
STRIPE_SECRET_KEY=
RESEND_API_KEY=
NEXT_PUBLIC_APP_URL=
```

### Delivery plan (2 weeks)

Week 1
1) DB: create enterprise_trials + trial_invites; RLS; indexes
2) API: list/create trials; bulk invites; accept invite
3) Email: send minimal token emails
4) UI: CreateTrialDialog + TrialsTable

Week 2
5) UI: BulkInviteDialog + ViewInvitesDialog; days/seats chips
6) Stripe: ensure customer + create Checkout session (convert)
7) `/invite/[token]` page polish
8) Minimal tests (unit for validation + one Playwright happy path) and README

Keep PRs tight, focus on the core flow, and document assumptions in the PR description.