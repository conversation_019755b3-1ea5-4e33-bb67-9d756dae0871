# QuantBoost Staging Environment Security Script
# Run this script to immediately secure your staging environment

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$true)]
    [string]$AppServiceName = "app-quantboost-frontend-staging",
    
    [Parameter(Mandatory=$false)]
    [string]$YourPublicIP
)

Write-Host "🔒 Securing QuantBoost Staging Environment..." -ForegroundColor Yellow

# Get your current public IP if not provided
if (-not $YourPublicIP) {
    Write-Host "🔍 Detecting your public IP address..." -ForegroundColor Cyan
    try {
        $YourPublicIP = (Invoke-RestMethod -Uri "https://api.ipify.org").Trim()
        Write-Host "✅ Detected IP: $YourPublicIP" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Could not detect IP. Please provide it manually." -ForegroundColor Red
        exit 1
    }
}

# 1. Add IP restriction to allow only your IP
Write-Host "🛡️ Adding IP access restriction..." -ForegroundColor Cyan
try {
    az webapp config access-restriction add `
        --resource-group $ResourceGroupName `
        --name $AppServiceName `
        --rule-name "Developer-Only-Access" `
        --action Allow `
        --ip-address "$YourPublicIP/32" `
        --priority 100
    
    Write-Host "✅ IP restriction added for $YourPublicIP" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to add IP restriction: $_" -ForegroundColor Red
}

# 2. Deny all other traffic
Write-Host "🚫 Adding deny-all rule for other IPs..." -ForegroundColor Cyan
try {
    az webapp config access-restriction add `
        --resource-group $ResourceGroupName `
        --name $AppServiceName `
        --rule-name "Deny-All-Others" `
        --action Deny `
        --ip-address "0.0.0.0/0" `
        --priority 200
    
    Write-Host "✅ Deny-all rule added" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to add deny-all rule: $_" -ForegroundColor Red
}

# 3. Add robots.txt to prevent search engine indexing
Write-Host "🤖 Creating robots.txt to prevent indexing..." -ForegroundColor Cyan
$robotsTxt = @"
User-agent: *
Disallow: /
"@

# You'll need to add this to your public folder manually
Write-Host "📝 Please add the following robots.txt to your public folder:" -ForegroundColor Yellow
Write-Host $robotsTxt -ForegroundColor White

# 4. Check current access restrictions
Write-Host "🔍 Current access restrictions:" -ForegroundColor Cyan
try {
    az webapp config access-restriction show `
        --resource-group $ResourceGroupName `
        --name $AppServiceName
}
catch {
    Write-Host "❌ Could not retrieve access restrictions: $_" -ForegroundColor Red
}

# 5. Security recommendations
Write-Host "`n🔐 Additional Security Recommendations:" -ForegroundColor Yellow
Write-Host "1. Enable Azure App Service Authentication" -ForegroundColor White
Write-Host "2. Use environment-specific Stripe test keys" -ForegroundColor White
Write-Host "3. Monitor webhook events for suspicious activity" -ForegroundColor White
Write-Host "4. Consider using a VPN for staging access" -ForegroundColor White
Write-Host "5. Implement proper logging and alerting" -ForegroundColor White

Write-Host "`n✅ Staging environment security script completed!" -ForegroundColor Green
Write-Host "🔗 Test access: https://$AppServiceName.azurewebsites.net" -ForegroundColor Cyan
Write-Host "⚠️  Only your IP ($YourPublicIP) should now have access" -ForegroundColor Yellow
