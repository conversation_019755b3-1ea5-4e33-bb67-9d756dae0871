// JWKS-based JWT verification for Supabase Auth tokens
// Uses public signing keys so we don't depend on legacy shared secrets

const { createRemoteJWKSet, jwtVerify } = require('jose');

const SUPABASE_URL = process.env.SUPABASE_URL;
if (!SUPABASE_URL) {
  console.warn('[jwtVerifier] SUPABASE_URL is not set; JWKS verification will not work.');
}

// Issuer follows this format per Supabase docs
const ISSUER = SUPABASE_URL ? `${SUPABASE_URL.replace(/\/$/, '')}/auth/v1` : undefined;

// Lazily create J<PERSON><PERSON> to avoid constructing with undefined URL in misconfig
let JWKS = null;
function getJWKS() {
  if (!JWKS) {
    if (!ISSUER) throw new Error('Supabase issuer is undefined; set SUPABASE_URL');
    const jwksUrl = new URL(`${ISSUER}/.well-known/jwks.json`);
    JWKS = createRemoteJWKSet(jwksUrl);
  }
  return JWKS;
}

/**
 * Verify a Supabase JWT and return its claims payload.
 * Throws on invalid signature/claims.
 */
async function verifySupabaseJWT(token) {
  const jwks = getJWKS();
  const { payload } = await jwtVerify(token, jwks, {
    issuer: ISSUER,
    // We deliberately do not enforce audience strictly here to remain compatible
    // with changes to aud. Add audience: 'authenticated' if you require it.
  });
  return payload;
}

module.exports = { verifySupabaseJWT, ISSUER };
