#!/usr/bin/env node
/**
 * Generate a multi-resolution favicon.ico from existing PNG assets.
 * Uses png-to-ico to merge 16x16 and 32x32 QuantBoost logo variants.
 */
const fs = require('fs');
const path = require('path');
const pngToIco = require('png-to-ico');

async function run() {
  const projectRoot = path.resolve(__dirname, '..');
  const publicDir = path.join(projectRoot, 'public');
  const sources = [
    path.join(publicDir, 'QuantBoost_LogoOnly_16x16.png'),
    path.join(publicDir, 'QuantBoost_LogoOnly_32x32.png')
  ];

  for (const src of sources) {
    if (!fs.existsSync(src)) {
      console.error(`Missing required PNG: ${src}`);
      process.exit(1);
    }
  }

  try {
    const ico = await pngToIco(sources);
    const outPath = path.join(publicDir, 'favicon.ico');
    fs.writeFileSync(outPath, ico);
    const bytes = fs.statSync(outPath).size;
    console.log(`favicon.ico generated (${bytes} bytes) from: \n - ${sources.join('\n - ')}`);
  } catch (err) {
    console.error('Failed to generate favicon.ico:', err);
    process.exit(1);
  }
}

run();
