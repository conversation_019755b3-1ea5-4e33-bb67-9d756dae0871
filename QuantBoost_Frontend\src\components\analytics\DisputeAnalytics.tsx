'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, TrendingDown, Clock, DollarSign, Percent, Users } from 'lucide-react';
import { DisputeMetrics } from '@/types/analytics';

interface DisputeAnalyticsProps {
  metrics: DisputeMetrics | null;
  isLoading: boolean;
}

export function DisputeAnalytics({ metrics, isLoading }: DisputeAnalyticsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div className="h-3 bg-gray-200 rounded animate-pulse w-2/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!metrics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Dispute Analytics
          </CardTitle>
          <CardDescription>No dispute data available</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const getDisputeRateColor = (rate: number) => {
    if (rate >= 1.0) return 'text-red-600';
    if (rate >= 0.75) return 'text-orange-500';
    if (rate >= 0.5) return 'text-yellow-500';
    return 'text-green-600';
  };

  const getDisputeRateBadge = (rate: number) => {
    if (rate >= 1.0) return 'destructive';
    if (rate >= 0.75) return 'secondary';
    if (rate >= 0.5) return 'outline';
    return 'default';
  };

  const winRate = metrics.total_disputes > 0 ? 
    (metrics.disputes_won / (metrics.disputes_won + metrics.disputes_lost)) * 100 : 0;

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Disputes</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.total_disputes}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.open_disputes} currently open
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dispute Rate</CardTitle>
            <Percent className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getDisputeRateColor(metrics.dispute_rate_percentage)}`}>
              {metrics.dispute_rate_percentage.toFixed(2)}%
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={getDisputeRateBadge(metrics.dispute_rate_percentage)}>
                {metrics.dispute_rate_percentage >= 1.0 ? 'Critical' : 
                 metrics.dispute_rate_percentage >= 0.75 ? 'High' :
                 metrics.dispute_rate_percentage >= 0.5 ? 'Medium' : 'Low'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Amount at Risk</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${metrics.total_disputed_amount.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg: ${metrics.total_disputes > 0 ? (metrics.total_disputed_amount / metrics.total_disputes).toFixed(0) : '0'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Win Rate</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {winRate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.disputes_won}/{metrics.disputes_won + metrics.disputes_lost} resolved
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Response Times
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Average Response</span>
              <span className="font-medium">
                {metrics.avg_response_time_hours ? `${metrics.avg_response_time_hours.toFixed(1)}h` : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Overdue Disputes</span>
              <Badge variant={metrics.overdue_disputes > 0 ? 'destructive' : 'default'}>
                {metrics.overdue_disputes}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Customer Impact
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Total Customers</span>
              <span className="font-medium">{metrics.total_disputes}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">High Value Disputes</span>
              <Badge variant="secondary">
                {/* Calculate high value disputes (>$500) - would need backend data */}
                -
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
