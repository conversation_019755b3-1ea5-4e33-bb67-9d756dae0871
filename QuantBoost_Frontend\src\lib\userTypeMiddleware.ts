import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

/**
 * User types for access control
 */
export enum UserType {
  TEAM_ADMIN = 'team_admin',
  INDIVIDUAL_SUBSCRIBER = 'individual_subscriber', 
  TEAM_LICENSEE = 'team_licensee',
  NO_LICENSE = 'no_license'
}

export interface UserTypeInfo {
  type: UserType;
  subscription?: any;
  license?: any;
  canAccessBilling: boolean;
  canManageSubscription: boolean;
  canManageTeam: boolean;
}

/**
 * Get authenticated user from Supabase session
 */
async function getAuthenticatedUser(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7);
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      console.error('Error getting user:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('Error in getAuthenticatedUser:', error);
    return null;
  }
}

/**
 * Determines user type and access level based on their subscriptions and licenses
 */
export async function determineUserType(userId: string): Promise<UserTypeInfo> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Check if user owns any subscriptions
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId);

    if (subscriptionsError) {
      console.error('Error fetching subscriptions in userType check:', subscriptionsError);
      throw subscriptionsError;
    }

    if (subscriptions && subscriptions.length > 0) {
      // User owns subscription(s)
      
      // Check if user owns a team subscription (quantity > 1)
      const teamSubscription = subscriptions.find(s => s.quantity > 1);
      if (teamSubscription) {
        return {
          type: UserType.TEAM_ADMIN,
          subscription: teamSubscription,
          canAccessBilling: true,
          canManageSubscription: true,
          canManageTeam: true
        };
      }

      // User has individual subscription(s)
      return {
        type: UserType.INDIVIDUAL_SUBSCRIBER,
        subscription: subscriptions[0],
        canAccessBilling: true,
        canManageSubscription: true,
        canManageTeam: false
      };
    }

    // User doesn't own subscriptions, check if they have assigned license
    const { data: license, error: licenseError } = await supabase
      .from('licenses')
      .select(`
        *,
        subscriptions (*)
      `)
      .eq('user_id', userId)
      .single();

    if (licenseError && licenseError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching license in userType check:', licenseError);
      throw licenseError;
    }

    if (license) {
      return {
        type: UserType.TEAM_LICENSEE,
        license,
        canAccessBilling: false,
        canManageSubscription: false,
        canManageTeam: false
      };
    }

    // User has no subscription or license
    return {
      type: UserType.NO_LICENSE,
      canAccessBilling: false,
      canManageSubscription: false,
      canManageTeam: false
    };

  } catch (error) {
    console.error('Error in determineUserType:', error);
    throw error;
  }
}

/**
 * Middleware to require billing access for Next.js API routes
 * Returns an error response if user doesn't have billing access
 */
export async function requireBillingAccess(req: NextRequest): Promise<NextResponse | null> {
  try {
    const user = await getAuthenticatedUser(req);
    
    if (!user) {
      return NextResponse.json({ 
        error: 'Authentication required' 
      }, { status: 401 });
    }

    const userTypeInfo = await determineUserType(user.id);
    
    if (!userTypeInfo.canAccessBilling) {
      return NextResponse.json({ 
        error: 'Access denied. Billing features are not available for your account type. Contact your team administrator for billing questions.' 
      }, { status: 403 });
    }

    // If we get here, user has access - return null to continue processing
    return null;
  } catch (error) {
    console.error('Error in requireBillingAccess:', error);
    return NextResponse.json({ 
      error: 'Failed to verify billing access' 
    }, { status: 500 });
  }
}

/**
 * Middleware to require subscription management access for Next.js API routes
 */
export async function requireSubscriptionAccess(req: NextRequest): Promise<NextResponse | null> {
  try {
    const user = await getAuthenticatedUser(req);
    
    if (!user) {
      return NextResponse.json({ 
        error: 'Authentication required' 
      }, { status: 401 });
    }

    const userTypeInfo = await determineUserType(user.id);
    
    if (!userTypeInfo.canManageSubscription) {
      return NextResponse.json({ 
        error: 'Access denied. Subscription management is not available for your account type. Contact your team administrator for subscription changes.' 
      }, { status: 403 });
    }

    return null;
  } catch (error) {
    console.error('Error in requireSubscriptionAccess:', error);
    return NextResponse.json({ 
      error: 'Failed to verify subscription access' 
    }, { status: 500 });
  }
}

/**
 * Middleware to require team management access for Next.js API routes
 */
export async function requireTeamAccess(req: NextRequest): Promise<NextResponse | null> {
  try {
    const user = await getAuthenticatedUser(req);
    
    if (!user) {
      return NextResponse.json({ 
        error: 'Authentication required' 
      }, { status: 401 });
    }

    const userTypeInfo = await determineUserType(user.id);
    
    if (!userTypeInfo.canManageTeam) {
      return NextResponse.json({ 
        error: 'Access denied. Team management is only available for team administrators.' 
      }, { status: 403 });
    }

    return null;
  } catch (error) {
    console.error('Error in requireTeamAccess:', error);
    return NextResponse.json({ 
      error: 'Failed to verify team access' 
    }, { status: 500 });
  }
}

/**
 * Helper function to get user type info for authenticated user
 */
export async function getUserTypeInfo(req: NextRequest): Promise<{ user: any, userType: UserTypeInfo } | null> {
  try {
    const user = await getAuthenticatedUser(req);
    
    if (!user) {
      return null;
    }

    const userTypeInfo = await determineUserType(user.id);
    
    return { user, userType: userTypeInfo };
  } catch (error) {
    console.error('Error in getUserTypeInfo:', error);
    return null;
  }
}