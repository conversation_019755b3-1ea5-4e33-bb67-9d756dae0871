import Header from '@/components/Header';

export default function WorksheetSizeAnalyzerPage() {
  return (
    <>
      <Header />
      <main className="max-w-4xl mx-auto p-6 pt-20">
        <h1 className="text-3xl font-bold mb-4">Excel Size Analyzer</h1>
        <p className="mb-6">Identify and fix bloated Excel files to improve performance.</p>
        <div className="aspect-video mb-6">
          <iframe
            width="100%"
            height="100%"
            src="https://www.youtube.com/embed/your_worksheet_size_analyzer_demo"
            title="Worksheet Size Analyzer Demo"
            frameBorder="0"
            allowFullScreen
          ></iframe>
        </div>
        <h2 className="text-xl font-semibold mb-2">Interactive Demo</h2>
        <iframe src="https://macabacus.com/features/audit-excel" className="w-full h-[600px] border" title="Interactive Demo"></iframe>
      </main>
    </>
  );
}
