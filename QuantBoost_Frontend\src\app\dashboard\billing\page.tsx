"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';

import { useEffect, useState } from 'react';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui";
import { apiClient, isApiSuccess, handleApiError } from '@/lib/api';
import Stripe from 'stripe';

interface ChargeReceipt {
  id: string;
  stripe_charge_id: string;
  subscription_id: string;
  amount: number;
  currency: string;
  receipt_url: string;
  receipt_number: string;
  status: string;
  created_at: string;
}

interface BillingOverview {
  nextBillingDate?: string;
  billingInterval?: string;
  totalSpent: number;
  receiptCount: number;
  lastPaymentDate?: string;
  lastPaymentAmount?: number;
}

export default function BillingPage() {
  const [loading, setLoading] = useState(true);
  const [receipts, setReceipts] = useState<ChargeReceipt[]>([]);
  const [overview, setOverview] = useState<BillingOverview | null>(null);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabaseClient();

  useEffect(() => {
    fetchBillingData();
  }, []);

  const fetchBillingData = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setError('User not authenticated');
        return;
      }

      // Get user's Stripe customer ID
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('stripe_customer_id')
        .eq('id', user.id)
        .single();

      if (profileError || !profileData?.stripe_customer_id) {
        setError('No billing information found. Please ensure you have an active subscription.');
        return;
      }

      // Fetch receipts for the user
      const receiptsResponse = await fetch('/api/billing/receipts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user.id }),
      });

      if (!receiptsResponse.ok) {
        throw new Error('Failed to fetch receipts');
      }

      const receiptData = await receiptsResponse.json();
      setReceipts(receiptData);

      // Calculate overview from receipts
      const totalSpent = receiptData.reduce((sum: number, receipt: ChargeReceipt) => 
        sum + (receipt.amount || 0), 0
      );

      const lastPaidReceipt = receiptData
        .filter((receipt: ChargeReceipt) => receipt.status === 'succeeded')
        .sort((a: ChargeReceipt, b: ChargeReceipt) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )[0];

      // Get next billing date from subscriptions
      const subscriptionsResponse = await apiClient.getUserSubscriptions();
      let nextBillingDate: string | undefined;
      let billingInterval: string | undefined;

      if (isApiSuccess(subscriptionsResponse)) {
        const activeSubscription = subscriptionsResponse.data.find(sub => 
          sub.status === 'active' || sub.status === 'trialing'
        );
        if (activeSubscription) {
          nextBillingDate = activeSubscription.current_period_end;
          // Extract billing interval from plan_id if available - with null safety
          if (activeSubscription.plan_id) {
            billingInterval = activeSubscription.plan_id.includes('month') ? 'monthly' : 
                             activeSubscription.plan_id.includes('year') ? 'yearly' : 'unknown';
          } else {
            billingInterval = 'unknown';
          }
        }
      }

      setOverview({
        nextBillingDate,
        billingInterval,
        totalSpent: totalSpent / 100, // Convert from cents
        receiptCount: receiptData.length,
        lastPaymentDate: lastPaidReceipt?.created_at ? new Date(lastPaidReceipt.created_at).toISOString() : undefined,
        lastPaymentAmount: lastPaidReceipt?.amount ? lastPaidReceipt.amount / 100 : undefined,
      });

    } catch (error) {
      console.error('Error fetching billing data:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleManageSubscription = async () => {
    try {
      setLoading(true);
      const response = await apiClient.createBillingPortalSession(window.location.href);
      if (isApiSuccess(response)) {
        window.location.href = response.data.url;
      } else {
        setError(handleApiError(response, 'Failed to create billing portal session'));
      }
    } catch (error) {
      console.error('Error managing subscription:', error);
      setError('Failed to redirect to billing portal');
    } finally {
      setLoading(false);
    }
  };

  const getReceiptStatusColor = (status: string) => {
    switch (status) {
      case 'succeeded':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'open':
        return 'bg-yellow-100 text-yellow-800';
      case 'void':
        return 'bg-gray-100 text-gray-800';
      case 'uncollectible':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Billing History</h3>
          <p className="text-sm text-muted-foreground">Loading billing information...</p>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="h-6 bg-gray-200 rounded animate-pulse" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Billing & Payment History</h3>
          <p className="text-sm text-muted-foreground">
            View your billing information and payment receipts.
          </p>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error Loading Billing Data</CardTitle>
            <CardDescription className="text-red-600">{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={fetchBillingData} 
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Billing & Payment History</h3>
        <p className="text-sm text-muted-foreground">
          View your billing information and payment receipts.
        </p>
      </div>

      {/* Billing Overview */}
      {overview && (
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Next Billing Date</CardTitle>
              <span className="text-2xl">📅</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {overview.nextBillingDate && overview.nextBillingDate !== 'null'
                  ? new Date(Number(overview.nextBillingDate) * 1000).toLocaleDateString()
                  : 'N/A'
                }
              </div>
              <p className="text-xs text-muted-foreground">
                {overview.billingInterval || 'Unknown interval'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Manage Billing</CardTitle>
              <span className="text-2xl">⚙️</span>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={handleManageSubscription}
                className="w-full"
                size="sm"
              >
                Billing Portal
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Receipts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Payment History</CardTitle>
          <CardDescription>
            View and download your payment receipts.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {receipts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">No payment receipts found.</p>
              <p className="text-xs text-muted-foreground mt-1">
                Payment receipts will appear here once you have made payments.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Receipt</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {receipts.map((receipt) => (
                  <TableRow key={receipt.id}>
                    <TableCell className="font-medium">
                      {receipt.receipt_number || `Receipt ${receipt.id?.slice(-8) || 'unknown'}`}
                    </TableCell>
                    <TableCell>
                      {new Date(receipt.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      ${(receipt.amount / 100).toFixed(2)}
                    </TableCell>
                    <TableCell>
                      <span className={`text-xs px-2 py-1 rounded-full ${getReceiptStatusColor(receipt.status)}`}>
                        {receipt.status === 'succeeded' ? 'Paid' : receipt.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      {receipt.receipt_url ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(receipt.receipt_url, '_blank')}
                        >
                          View Receipt
                        </Button>
                      ) : (
                        <span className="text-gray-400 text-sm">Not available</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}