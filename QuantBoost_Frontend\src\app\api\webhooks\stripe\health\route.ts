// Health check endpoint for webhook testing
import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    endpoint: '/api/webhooks/stripe',
    environment: {
      hasStripeSecret: !!process.env.STRIPE_SECRET_KEY,
      hasWebhookSecret: !!process.env.STRIPE_WEBHOOK_SECRET,
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_KEY
    }
  });
}

export async function POST() {
  return NextResponse.json({
    message: 'Webhook endpoint is accessible',
    method: 'POST',
    timestamp: new Date().toISOString()
  });
}
