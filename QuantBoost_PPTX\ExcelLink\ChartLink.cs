using System;

namespace QuantBoost_Powerpoint_Addin.ExcelLink
{
    /// <summary>
    /// Represents the metadata for a single linked Excel chart stored in Custom XML.
    /// Follows the structure defined in the technical specification.
    /// </summary>
    public class ChartLink
    {
        public string Version { get; set; } = "1.0"; // Schema version
        public string Product { get; set; } = "excel-link";
        public string LinkId { get; set; } // Unique ID for this link
        public string SourceFilePath { get; set; } // Full path to the source Excel file
        public string SourceFileHash { get; set; } // Hash of the source file for change detection
        public DateTime? SourceFileLastModifiedUtc { get; set; }
        public string ChartNameOrId { get; set; } // e.g., "Sheet1!Chart 2" or chart object name
        public string WorksheetName { get; set; }
        public bool PreserveFormatting { get; set; } = true;
        public DateTime? LastRefreshedUtc { get; set; }
        public bool FallbackUsedLastRefresh { get; set; } = false;
        public string ErrorState { get; set; }
        public string Notes { get; set; }
        public string PowerPointShapeId { get; set; } // ID of the shape in PowerPoint holding the chart
        public int SlideIndex { get; set; } // 1-based index of the slide containing the shape
        public bool IsActive { get; set; } = true; // Default to active when created
        public string LinkType { get; set; } = "Chart"; // Default type, could be enum: Chart, Range, Table
        public string ModifiedBy { get; set; } // User who last modified/refreshed? Needs population logic.
        public string SourceRange { get; set; } // Specific range if applicable, otherwise use ChartNameOrId?

        // Future: Add fields for format preservation details, etc.

        /// <summary>
        /// Creates a shallow copy of the ChartLink object.
        /// </summary>
        public ChartLink Clone()
        {
            return (ChartLink)this.MemberwiseClone();
        }
    }
}
