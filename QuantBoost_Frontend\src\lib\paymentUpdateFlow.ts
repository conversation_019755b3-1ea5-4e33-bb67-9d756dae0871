import { recordPartialSuccess } from './telemetry';

export interface SetDefaultResult {
  ok: boolean;
  body?: any;
}

export interface AttemptAttachOutcome {
  attached: boolean; // fully attached
  partial: boolean; // setup confirmed but attach failed
  error?: string;
}

export type SetDefaultCaller = (subscriptionId: string, paymentMethodId: string) => Promise<SetDefaultResult>;

/**
 * Orchestrates setting default payment method after a confirmed SetupIntent.
 * Returns partial=true when Stripe confirmation succeeded but backend failed.
 */
export async function attemptAttachPaymentMethod(
  subscriptionId: string,
  paymentMethodId: string,
  caller: SetDefaultCaller,
  telemetry: typeof recordPartialSuccess = recordPartialSuccess
): Promise<AttemptAttachOutcome> {
  try {
    const resp = await caller(subscriptionId, paymentMethodId);
    if (!resp.ok) {
      telemetry({ subscriptionId, paymentMethodId });
      return { attached: false, partial: true, error: resp.body?.error || 'Failed to set default payment method' };
    }
    return { attached: true, partial: false };
  } catch (e: any) {
    telemetry({ subscriptionId, paymentMethodId });
    return { attached: false, partial: true, error: e?.message || 'Failed to set default payment method' };
  }
}
