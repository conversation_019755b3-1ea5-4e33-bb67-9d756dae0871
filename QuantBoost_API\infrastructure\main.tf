terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }

  backend "azurerm" {
    resource_group_name  = "rg-quantboost-api-prod"
    storage_account_name = "stquantboostterraform"
    container_name       = "tfstate"
    key                  = "production.terraform.tfstate"
  }
}

provider "azurerm" {
  features {}
}

data "azurerm_client_config" "current" {}

# Random string for unique resource naming
resource "random_string" "suffix" {
  length  = 6
  special = false
  upper   = false
}

resource "azurerm_resource_group" "main" {
  name     = "rg-quantboost-${var.environment}"
  location = var.location
}

# Key Vault for secrets
resource "azurerm_key_vault" "main" {
  name                = "kv-quantboost-${random_string.suffix.result}"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  tenant_id           = data.azurerm_client_config.current.tenant_id
  sku_name            = "standard"

  access_policy {
    tenant_id = data.azurerm_client_config.current.tenant_id
    object_id = data.azurerm_client_config.current.object_id
    secret_permissions = [ "Get", "List", "Set", "Delete", "Purge" ]
  }
}

# Container Registry
resource "azurerm_container_registry" "main" {
  name                = "acrquantboost${random_string.suffix.result}"
  resource_group_name = azurerm_resource_group.main.name
  location            = azurerm_resource_group.main.location
  sku                 = "Basic"
  admin_enabled       = true
}

# Application Insights
resource "azurerm_log_analytics_workspace" "main" {
  name                = "law-quantboost-prod"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  sku                 = "PerGB2018"
  retention_in_days   = 30
}

resource "azurerm_application_insights" "main" {
  name                = "ai-quantboost-api-prod"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  workspace_id        = azurerm_log_analytics_workspace.main.id
  application_type    = "Node.JS"
}

# Container Apps Environment
resource "azurerm_container_app_environment" "main" {
  name                       = "cae-quantboost-prod"
  location                   = azurerm_resource_group.main.location
  resource_group_name        = azurerm_resource_group.main.name
  log_analytics_workspace_id = azurerm_log_analytics_workspace.main.id
}



# Key Vault Secrets
resource "azurerm_key_vault_secret" "supabase_url" {
  name         = "supabase-url"
  value        = var.supabase_url
  key_vault_id = azurerm_key_vault.main.id
}

resource "azurerm_key_vault_secret" "supabase_key" {
  name         = "supabase-anon-key"
  value        = var.supabase_anon_key
  key_vault_id = azurerm_key_vault.main.id
}

resource "azurerm_key_vault_secret" "supabase_service_key" {
  name         = "supabase-service-role-key"
  value        = var.supabase_service_role_key
  key_vault_id = azurerm_key_vault.main.id
}

resource "azurerm_key_vault_secret" "jwt_secret" {
  name         = "jwt-secret"
  value        = var.jwt_secret
  key_vault_id = azurerm_key_vault.main.id
}

resource "azurerm_key_vault_secret" "stripe_secret_key" {
  name         = "stripe-secret-key"
  value        = var.stripe_secret_key
  key_vault_id = azurerm_key_vault.main.id
}

resource "azurerm_key_vault_secret" "stripe_webhook_secret" {
  name         = "stripe-webhook-signing-secret"
  value        = var.stripe_webhook_signing_secret
  key_vault_id = azurerm_key_vault.main.id
}

// Corrected Container App Resource
resource "azurerm_container_app" "api" {
  name                         = "ca-quantboost-api"
  container_app_environment_id = azurerm_container_app_environment.main.id
  resource_group_name          = azurerm_resource_group.main.name
  revision_mode                = "Single"
  
  identity {
    type = "SystemAssigned"
  }

  registry {
    server               = azurerm_container_registry.main.login_server
    username             = azurerm_container_registry.main.admin_username
    password_secret_name = "acr-password"
  }

  // --- TOP-LEVEL SECRET DEFINITIONS ---
  secret {
    name  = "acr-password"
    value = azurerm_container_registry.main.admin_password
  }
  secret {
    name  = "supabase-url"
    value = var.supabase_url
  }
  secret {
    name  = "supabase-anon-key"
    value = var.supabase_anon_key
  }
  secret {
    name  = "supabase-service-role-key"
    value = var.supabase_service_role_key
  }
  secret {
    name  = "jwt-secret"
    value = var.jwt_secret
  }
  # --- NEW: ADDED STRIPE SECRET DEFINITIONS ---
  secret {
    name  = "stripe-secret-key"
    value = var.stripe_secret_key
  }
  secret {
    name  = "stripe-webhook-signing-secret"
    value = var.stripe_webhook_signing_secret
  }
  # --- END OF NEW SECRET DEFINITIONS ---
  
  template {
    container {
      name   = "quantboost-api"
      image  = "${azurerm_container_registry.main.login_server}/quantboost-api:latest" // This will be updated by the 'az' command
      cpu    = 0.5
      memory = "1Gi"

      # --- ENVIRONMENT VARIABLE DEFINITIONS ---
      env {
        name  = "NODE_ENV"
        value = "production"
      }
      env {
        name  = "PORT"
        value = "3000"
      }
      env {
        name        = "SUPABASE_URL"
        secret_name = "supabase-url"
      }
      env {
        name        = "SUPABASE_ANON_KEY"
        secret_name = "supabase-anon-key"
      }
      env {
        name        = "SUPABASE_SERVICE_ROLE_KEY"
        secret_name = "supabase-service-role-key"
      }
      env {
        name        = "JWT_SECRET"
        secret_name = "jwt-secret"
      }
      env {
        name  = "APPLICATIONINSIGHTS_CONNECTION_STRING"
        value = azurerm_application_insights.main.connection_string
      }
      # --- NEW: ADDED STRIPE ENVIRONMENT VARIABLES ---
      env {
        name        = "STRIPE_SECRET_KEY"
        secret_name = "stripe-secret-key"
      }
      env {
        name        = "STRIPE_WEBHOOK_SIGNING_SECRET"
        secret_name = "stripe-webhook-signing-secret"
      }
      # --- END OF NEW ENVIRONMENT VARIABLES ---
    }

    min_replicas = 1
    max_replicas = 3

    http_scale_rule {
      name                = "http-concurrent-requests"
      concurrent_requests = 50
    }
  }
 
  ingress {
    external_enabled = true
    target_port      = 3000
    
    traffic_weight {
      percentage      = 100
      latest_revision = true
    }
  }
}

# Outputs
output "container_registry_name" {
  description = "Container Registry name"
  value       = azurerm_container_registry.main.name
}

output "container_registry_server" {
  description = "Container Registry server URL"
  value       = azurerm_container_registry.main.login_server
}

output "container_app_fqdn" {
  description = "Container App FQDN"
  value       = azurerm_container_app.api.latest_revision_fqdn
}

output "resource_group_name" {
  description = "Resource Group name"
  value       = azurerm_resource_group.main.name
}

output "key_vault_name" {
  description = "Key Vault name"
  value       = azurerm_key_vault.main.name
}

output "application_insights_connection_string" {
  description = "Application Insights connection string"
  value       = azurerm_application_insights.main.connection_string
  sensitive   = true
}

output "acr_username" {
  description = "Container Registry admin username"
  value       = azurerm_container_registry.main.admin_username
  sensitive   = true
}

# Static Web App for Frontend
resource "azurerm_static_web_app" "frontend" {
  name                = "swa-quantboost-frontend-${var.environment}"
  resource_group_name = azurerm_resource_group.main.name
  location            = "East US 2" # Static Web Apps have limited regions
  sku_tier            = "Free"
  sku_size            = "Free"

  app_settings = {
    "NEXT_PUBLIC_SUPABASE_URL"           = var.supabase_url
    "SUPABASE_SERVICE_KEY"               = var.supabase_service_role_key
    "STRIPE_PUBLISHABLE_KEY"             = var.stripe_publishable_key
    "STRIPE_SECRET_KEY"                  = var.stripe_secret_key
    "STRIPE_WEBHOOK_SECRET"              = var.stripe_webhook_signing_secret
    "NEXT_PUBLIC_BASE_URL"               = var.environment == "prod" ? "https://quantboost.ai" : "https://dev.quantboost.ai"
    "NEXT_PUBLIC_AZURE_API_URL"          = "https://${azurerm_container_app.api.latest_revision_fqdn}"
    "APPLICATIONINSIGHTS_CONNECTION_STRING" = azurerm_application_insights.main.connection_string
  }

  tags = {
    Environment = var.environment
    Component   = "frontend"
  }
}

output "acr_password" {
  description = "Container Registry admin password"
  value       = azurerm_container_registry.main.admin_password
  sensitive   = true
}

output "static_web_app_default_hostname" {
  description = "Static Web App default hostname"
  value       = azurerm_static_web_app.frontend.default_host_name
}

output "static_web_app_api_key" {
  description = "Static Web App deployment token"
  value       = azurerm_static_web_app.frontend.api_key
  sensitive   = true
}