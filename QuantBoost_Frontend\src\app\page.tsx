"use client";

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform, useSpring, Variants } from 'motion/react';
import Link from 'next/link';
import AnimatedQuantBoostLogo from '@/components/AnimatedQuantBoostLogo';
import Header from '@/components/Header';

// Professional animation variants for QuantBoost
const fadeInUp: Variants = {
  hidden: { opacity: 0, y: 40 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const fadeInLeft: Variants = {
  hidden: { opacity: 0, x: -40 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const fadeInRight: Variants = {
  hidden: { opacity: 0, x: 40 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const fadeInScale: Variants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

// Enhanced stagger for testimonials
const staggerTestimonials: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.3
    }
  }
};

const testimonialCard: Variants = {
  hidden: { 
    opacity: 0, 
    y: 50,
    rotateX: -15
  },
  visible: { 
    opacity: 1, 
    y: 0,
    rotateX: 0,
    transition: { 
      duration: 0.8, 
      ease: "easeOut",
      type: "spring",
      stiffness: 100
    }
  }
};

// Accessibility-friendly variants that respect prefers-reduced-motion
const reduceMotionVariants: Variants = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1,
    transition: { duration: 0.3 }
  }
};

// Premium micro-interaction variants
const premiumCardHover = {
  scale: 1.02,
  y: -8,
  boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(16, 185, 129, 0.1)",
  transition: { 
    type: "spring" as const, 
    stiffness: 400, 
    damping: 25 
  }
};

const logoBreathing: Variants = {
  idle: { 
    scale: 1,
    transition: { 
      duration: 4,
      ease: "easeInOut",
      repeat: Infinity,
      repeatType: "reverse" as const
    }
  },
  hover: { 
    scale: 1.05,
    transition: { duration: 0.3 }
  }
};

const premiumButtonHover = {
  scale: 1.05,
  y: -2,
  boxShadow: "0 20px 25px -5px rgba(16, 185, 129, 0.4)",
  transition: { 
    type: "spring" as const, 
    stiffness: 400, 
    damping: 17 
  }
};

const pricingCardHover = {
  scale: 1.02,
  y: -4,
  boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
  transition: { 
    type: "spring" as const, 
    stiffness: 300, 
    damping: 20 
  }
};

export default function HomePage() {
  const heroRef = useRef<HTMLDivElement>(null);
  const mainRef = useRef<HTMLDivElement>(null);
  
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Global scroll progress for grid background
  const { scrollYProgress: globalScrollProgress } = useScroll({
    target: mainRef,
    offset: ["start start", "end end"]
  });

  // Parallax transforms with performance optimization
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const textY = useTransform(scrollYProgress, [0, 1], ["0%", "30%"]);
  const smoothBackgroundY = useSpring(backgroundY, { stiffness: 100, damping: 30, restDelta: 0.001 });

  // Excel grid opacity based on scroll progress
  const gridOpacity = useTransform(globalScrollProgress, [0, 0.3, 0.7, 1], [0.03, 0.08, 0.15, 0.2]);
  const gridScale = useTransform(globalScrollProgress, [0, 1], [1, 1.05]);
  const smoothGridOpacity = useSpring(gridOpacity, { stiffness: 50, damping: 25 });

  // Performance optimization for reduced motion
  const prefersReducedMotion = useRef(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    prefersReducedMotion.current = mediaQuery.matches;
    
    const handleChange = () => {
      prefersReducedMotion.current = mediaQuery.matches;
    };
    
    mediaQuery.addListener(handleChange);
    return () => mediaQuery.removeListener(handleChange);
  }, []);

  // Enhanced animation variants with reduced motion support
  const getAnimationProps = (baseVariant: any) => {
    if (prefersReducedMotion.current) {
      return {
        initial: "visible",
        animate: "visible",
        variants: { visible: { opacity: 1, x: 0, y: 0, scale: 1 } }
      };
    }
    return {
      initial: "hidden",
      whileInView: "visible",
      viewport: { once: true, amount: 0.1, margin: "-10% 0px -10% 0px" },
      variants: baseVariant
    };
  };

  return (
    <>
      <Header showDrawAnimation={false} />

      <main ref={mainRef} className="flex flex-col items-center justify-center min-h-screen p-6 pt-16 overflow-hidden relative">
        
        {/* Excel Grid Background - Brand Authentic */}
        <motion.div 
          className="fixed inset-0 -z-20 overflow-hidden"
          style={{ 
            opacity: smoothGridOpacity,
            scale: gridScale
          }}
        >
          <svg
            className="absolute inset-0 w-full h-full"
            xmlns="http://www.w3.org/2000/svg"
            style={{ 
              width: '100%', 
              height: '100%',
              minHeight: '100vh'
            }}
          >
            <defs>
              <pattern
                id="excel-grid"
                x="0"
                y="0"
                width="40"
                height="24"
                patternUnits="userSpaceOnUse"
              >
                <rect
                  x="0"
                  y="0"
                  width="40"
                  height="24"
                  fill="none"
                  stroke="#10B981"
                  strokeWidth="0.5"
                  opacity="0.6"
                />
                {/* Subtle accent lines every 5th row/column */}
                <rect
                  x="0"
                  y="0"
                  width="200"
                  height="120"
                  fill="none"
                  stroke="#10B981"
                  strokeWidth="1"
                  opacity="0.8"
                  patternUnits="userSpaceOnUse"
                />
              </pattern>
            </defs>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#excel-grid)"
            />
          </svg>
        </motion.div>

        {/* Hero Section with Parallax */}
        <motion.section 
          ref={heroRef}
          className="text-center py-6 relative z-10"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={staggerContainer}
        >
          {/* Parallax Background Elements */}
          <motion.div
            className="absolute inset-0 -z-10 opacity-20"
            style={{ y: smoothBackgroundY }}
          >
            <div className="absolute top-20 left-10 w-72 h-72 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
            <div className="absolute top-40 right-10 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            <div className="absolute bottom-20 left-20 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
          </motion.div>

          <motion.div style={{ y: textY }}>
            <AnimatedQuantBoostLogo 
              size="12rem"
              className="mx-auto mb-4"
              showDrawAnimation={true}
            />
          <motion.p 
            className="text-lg mb-6 max-w-2xl mx-auto"
            variants={fadeInUp}
          >
            Unlock the full power of Excel and PowerPoint with QuantBoost's suite of productivity plug-ins.
          </motion.p>
          <motion.div 
            className="flex flex-col sm:flex-row gap-4 justify-center"
            variants={fadeInUp}
          >
            <motion.a 
              href="/start-trial" 
              className="px-6 py-3 rounded-lg bg-emerald-500 text-white transition-colors duration-200"
              whileHover={premiumButtonHover}
              whileTap={{ scale: 0.98 }}
            >
              Free Trial
            </motion.a>
            <motion.a 
              href="/pricing" 
              className="px-6 py-3 rounded-lg border border-emerald-500 text-emerald-600 transition-colors duration-200 hover:bg-emerald-50"
              whileHover={{ 
                scale: 1.05, 
                y: -2,
                borderColor: "#10B981",
                transition: { type: "spring" as const, stiffness: 400, damping: 17 }
              }}
              whileTap={{ scale: 0.98 }}
            >
              View Pricing
            </motion.a>
          </motion.div>
          </motion.div>
        </motion.section>

        {/* Core Features Section */}
        <motion.section 
          className="py-12 max-w-4xl mx-auto border-t"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1, margin: "-10% 0px -10% 0px" }}
          variants={staggerContainer}
        >
          <motion.h2 
            className="text-center text-2xl font-bold mb-8"
            variants={fadeInUp}
          >
            Everything You Need — Without the Bloat
          </motion.h2>
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-3xl mx-auto"
            variants={staggerContainer}
          >
            <motion.a 
              href="/features/excel-link" 
              className="p-6 border rounded-lg text-center cursor-pointer bg-white shadow-sm hover:shadow-md transition-shadow"
              variants={fadeInUp}
              whileHover={premiumCardHover}
              whileTap={{ scale: 0.98 }}
            >
              <h3 className="font-semibold mb-2">🔗 Excel Link</h3>
              <p>Link Excel models to PowerPoint slides for instant updates and accuracy.</p>
            </motion.a>
            <motion.a 
              href="/features/excel-trace" 
              className="p-6 border rounded-lg text-center cursor-pointer bg-white shadow-sm hover:shadow-md transition-shadow"
              variants={fadeInUp}
              whileHover={premiumCardHover}
              whileTap={{ scale: 0.98 }}
            >
              <h3 className="font-semibold mb-2">🔍 Excel Trace</h3>
              <p>Trace complex formulas and dependencies to reduce errors fast.</p>
            </motion.a>
            <motion.a 
              href="/features/worksheet-size-analyzer" 
              className="p-6 border rounded-lg text-center cursor-pointer bg-white shadow-sm hover:shadow-md transition-shadow"
              variants={fadeInUp}
              whileHover={premiumCardHover}
              whileTap={{ scale: 0.98 }}
            >
              <h3 className="font-semibold mb-2">📊 Excel Size Analyzer</h3>
              <p>Identify and fix bloated Excel files to improve performance.</p>
            </motion.a>
            <motion.a 
              href="/features/slide-size-analyzer" 
              className="p-6 border rounded-lg text-center cursor-pointer bg-white shadow-sm hover:shadow-md transition-shadow"
              variants={fadeInUp}
              whileHover={premiumCardHover}
              whileTap={{ scale: 0.98 }}
            >
              <h3 className="font-semibold mb-2">📈 PowerPoint Size Analyzer</h3>
              <p>Optimize PowerPoint decks by finding oversized images and content.</p>
            </motion.a>
          </motion.div>
        </motion.section>

        {/* Pricing Comparison Section */}
        <motion.section 
          className="py-12 max-w-7xl mx-auto border-t"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={staggerContainer}
        >
          <motion.h2 
            className="text-center text-2xl font-bold mb-8"
            variants={fadeInUp}
          >
            All the Essentials at a Fraction of the Price of the Competition
          </motion.h2>
          <div className="flex flex-col md:flex-row items-center justify-center gap-6">
            <motion.div 
              className="p-6 border-2 border-emerald-500 rounded-lg text-center flex-1 w-70 flex flex-col justify-between cursor-pointer bg-white shadow-lg"
              variants={fadeInLeft}
              whileHover={{
                ...pricingCardHover,
                boxShadow: "0 25px 50px -12px rgba(16, 185, 129, 0.4), 0 0 0 2px rgba(16, 185, 129, 0.2)",
              }}
              whileTap={{ scale: 0.98 }}
            >
              <h3 className="text-xl font-semibold mb-2">QuantBoost</h3>
              <p className="text-3xl font-bold mb-2">$120<span className="text-base font-normal">/year/license</span></p>
              <p className="whitespace-nowrap overflow-hidden">Key features professionals actually use.</p>
            </motion.div>
            <motion.div 
              className="text-3xl font-bold text-gray-500"
              variants={fadeInUp}
            >
              vs
            </motion.div>
            <motion.div 
              className="p-6 border-2 border-blue-500 rounded-lg text-center flex-1 w-70 flex flex-col justify-between opacity-70 cursor-pointer bg-white shadow-lg"
              variants={fadeInRight}
              whileHover={{
                ...pricingCardHover,
                opacity: 0.9,
                boxShadow: "0 25px 50px -12px rgba(59, 130, 246, 0.3), 0 0 0 2px rgba(59, 130, 246, 0.1)",
              }}
              whileTap={{ scale: 0.98 }}
            >
              <h3 className="text-xl font-semibold mb-2">Macabacus</h3>
              <p className="text-3xl font-bold mb-2">$360+<span className="text-base font-normal">/year/license</span></p>
              <p className="whitespace-nowrap overflow-hidden">Extra features no one actually uses.</p>
            </motion.div>
          </div>
          <motion.p 
            className="mt-6 text-center max-w-3xl mx-auto"
            variants={fadeInUp}
          >
            By focusing on the features you actually care about, QuantBoost delivers incredible value without the clutter.
          </motion.p>
        </motion.section>

        {/* Testimonials Section */}
        <motion.section 
          className="py-12 max-w-4xl mx-auto border-t"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={staggerTestimonials}
        >
          <motion.h2 
            className="text-center text-2xl font-bold mb-8"
            variants={fadeInUp}
          >
            What Our Users Say
          </motion.h2>
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
            variants={staggerTestimonials}
          >
            <motion.div 
              className="p-6 border rounded-lg transform-gpu bg-white shadow-sm"
              variants={testimonialCard}
              whileHover={{ 
                scale: 1.02, 
                y: -5,
                boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1)",
                transition: { duration: 0.3 }
              }}
            >
              <p className="text-center mb-2">"QuantBoost Excel Trace is a game changer - formula auditing is way faster. Can't imagine working without this now. Thank you!"</p>
              <p className="text-center font-semibold">Matt, Financial Analyst</p>
            </motion.div>
            <motion.div 
              className="p-6 border rounded-lg transform-gpu bg-white shadow-sm"
              variants={testimonialCard}
              whileHover={{ 
                scale: 1.02, 
                y: -5,
                boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1)",
                transition: { duration: 0.3 }
              }}
            >
              <p className="text-center mb-2">"Excel Link allows us to quickly refresh our PPTX slides without having to copy and paste tables from Excel each time we make a model change."</p>
              <p className="text-center font-semibold">James, Director - Finance</p>
            </motion.div>
          </motion.div>
        </motion.section>

        {/* Final CTA Section */}
        <motion.section 
          className="py-12 text-center border-t"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={staggerContainer}
        >
          <motion.h2 
            className="text-2xl font-bold mb-4"
            variants={fadeInUp}
          >
            Ready to boost your productivity?
          </motion.h2>
          <motion.a 
            href="/start-trial" 
            className="inline-block px-8 py-4 rounded-lg bg-emerald-500 text-white transition"
            variants={fadeInUp}
            whileHover={{
              ...premiumButtonHover,
              scale: 1.08,
              y: -4,
              boxShadow: "0 25px 50px -12px rgba(16, 185, 129, 0.5), 0 0 0 1px rgba(16, 185, 129, 0.2)",
            }}
            whileTap={{ scale: 0.96 }}
          >
            Start Your Free Trial
          </motion.a>
        </motion.section>

        {/* Footer */}
        <footer className="py-8 text-center text-sm text-gray-500 border-t w-full">
          <p>&copy; 2025 QuantBoost. All rights reserved. Visit us at <a href="https://quantboost.ai" className="underline hover:text-emerald-600">quantboost.ai</a></p>
          <div className="mt-2 flex justify-center gap-4">
            <a href="/pricing" className="hover:underline">Pricing</a>
            <a href="/support" className="hover:underline">Support</a>
            <a href="/terms" className="hover:underline">Terms</a>
            <a href="/privacy" className="hover:underline">Privacy</a>
          </div>
        </footer>
      </main>

      {/* CSS for blob animations */}
      <style jsx>{`
        @keyframes blob {
          0% {
            transform: translate(0px, 0px) scale(1);
          }
          33% {
            transform: translate(30px, -50px) scale(1.1);
          }
          66% {
            transform: translate(-20px, 20px) scale(0.9);
          }
          100% {
            transform: translate(0px, 0px) scale(1);
          }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
    </>
  );
}

