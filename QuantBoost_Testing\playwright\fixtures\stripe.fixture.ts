import { test as base } from '@playwright/test';
import Stripe from 'stripe';

const TEST_CARDS = {
  SUCCESS: '****************',
  DECLINE: '****************',
  INSUFFICIENT_FUNDS: '****************',
  EXPIRED: '****************',
  CVC_FAIL: '****************',
  PROCESSING_ERROR: '****************',
  THREE_D_SECURE: '****************',
  THREE_D_SECURE_2: '****************',
};

export type StripeFixture = {
  stripe: Stripe | null;
  testCards: typeof TEST_CARDS;
  createTestCustomer: () => Promise<Stripe.Customer | null>;
  simulateWebhook: (eventType: string, payload: any) => Promise<{ status: number; body: any } | null>;
};

export const test = base.extend<StripeFixture>({
  stripe: async ({}, use) => {
    const key = process.env.STRIPE_SECRET_KEY_TEST;
    if (!key) {
      console.warn('[stripe.fixture] STRIPE_SECRET_KEY_TEST missing - Stripe-dependent tests will skip.');
      await use(null);
      return;
    }
  const stripe = new Stripe(key, { apiVersion: '2025-08-27.basil' as any });
    await use(stripe);
  },
  testCards: async ({}, use) => { await use(TEST_CARDS); },
  createTestCustomer: async ({ stripe }, use) => {
    const factory = async () => {
      if (!stripe) return null;
      const customer = await stripe.customers.create({
        email: `e2e_${Date.now()}_${Math.random().toString(36).slice(2)}@example.com`,
        description: 'QuantBoost E2E Test Customer'
      });
      return customer;
    };
    await use(factory);
  },
  simulateWebhook: async ({}, use) => {
    const fn = async (eventType: string, payload: any) => {
      const baseUrl = process.env.BASE_URL;
      if (!baseUrl) return null;
      try {
        const res = await fetch(`${baseUrl}/api/webhooks/stripe`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'X-Stripe-Signature': 'test-signature-bypass' },
          body: JSON.stringify({
            id: `evt_test_${Date.now()}`,
            type: eventType,
            data: { object: payload },
            created: Math.floor(Date.now() / 1000),
            livemode: false,
            object: 'event',
            pending_webhooks: 1,
            request: { id: null, idempotency_key: null },
            api_version: '2025-08-27.basil'
          })
        });
        return { status: res.status, body: await res.json().catch(() => ({})) };
      } catch (e) {
        console.warn('[simulateWebhook] error', e);
        return { status: 0, body: { error: String(e) } };
      }
    };
    await use(fn);
  }
});

export const expect = test.expect;
