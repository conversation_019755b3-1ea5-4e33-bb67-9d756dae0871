import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(req: NextRequest) {
  // Create Supabase client inside the function to avoid build-time issues
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  );
  try {
    const { email, paymentIntentId } = await req.json();

    const normalizeEmail = (raw: string) => {
      if (!raw) return raw;
      const lower = raw.toLowerCase();
      const corrections: Record<string,string> = {
        'gmail.co': 'gmail.com',
        'gamil.com': 'gmail.com',
        'gnail.com': 'gmail.com',
        'gmai.com': 'gmail.com',
        'gmail.con': 'gmail.com'
      };
      const parts = lower.split('@');
      if (parts.length === 2) {
        const [local, domain] = parts;
        if (corrections[domain]) return `${local}@${corrections[domain]}`;
      }
      return lower;
    };

    const primaryEmail = normalizeEmail(email);

  if (!primaryEmail) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Verify that this email has a recent successful payment
    // This is a security measure to prevent unauthorized login attempts
    if (paymentIntentId) {
      // You could add additional verification here by checking Stripe
      // For now, we'll trust that the payment was successful since this
      // endpoint should only be called from the checkout success flow
    }

    // Check if user exists in auth.users
    const { data: { users }, error: listError } = await supabase.auth.admin.listUsers();
    if (listError) {
      console.error('Error listing users:', listError);
      return NextResponse.json({ error: 'Authentication service error' }, { status: 500 });
    }

    let existingUser = users.find(u => u.email?.toLowerCase() === primaryEmail);
    if (!existingUser && primaryEmail !== email) {
      existingUser = users.find(u => u.email?.toLowerCase() === email.toLowerCase());
    }
    
    // If user doesn't exist, create them (this handles cases where webhook failed or wasn't called)
    if (!existingUser) {
      console.log(`User ${email} not found, creating new user account for payment success flow`);
      
      try {
        const { data: newUserData, error: createError } = await supabase.auth.admin.createUser({
          email: primaryEmail,
          email_confirm: true, // Auto-confirm since they just completed payment
          user_metadata: {
            source: 'post_payment_creation',
            payment_intent_id: paymentIntentId,
            created_at: new Date().toISOString()
          }
        });

        if (createError) {
          console.error('Error creating user account:', createError);
          return NextResponse.json({ 
            error: 'Failed to create user account',
            details: createError.message 
          }, { status: 500 });
        }

        existingUser = newUserData.user;
        console.log(`✅ Successfully created user account for ${email}`);
        
      } catch (userCreateError) {
        console.error('Failed to create user:', userCreateError);
        return NextResponse.json({ 
          error: 'User account creation failed',
          details: userCreateError instanceof Error ? userCreateError.message : 'Unknown error'
        }, { status: 500 });
      }
    }

    if (!existingUser) {
      return NextResponse.json({ error: 'User account creation failed' }, { status: 500 });
    }

    // Since Supabase Site URL points to API and we need to support both VSTO and frontend,
    // we'll let the magic link go to the API callback which will redirect appropriately
    
    // Generate a magic link that goes to API with payment context and auto-login
    const { data, error } = await supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: primaryEmail,
      options: {
        // Redirect to API callback with payment context and auto-login flag
        redirectTo: `/v1/auth/callback?payment=success&email=${encodeURIComponent(primaryEmail)}&payment_intent=${paymentIntentId}&auto_login=true`
      }
    });

    if (error) {
      console.error('Error generating magic link:', error);
      return NextResponse.json({ error: 'Could not generate login link' }, { status: 500 });
    }

    const actionLink = data.properties?.action_link;
    console.log('Generated magic link for payment context:', actionLink);

    return NextResponse.json({ 
      success: true, 
      loginUrl: actionLink,
      message: 'Login link generated successfully',
      redirectToDashboard: true, // Flag to indicate this should redirect directly
      debugInfo: {
  email: primaryEmail,
        paymentIntentId,
        originalActionLink: data.properties?.action_link,
        context: 'payment-success'
      }
    });

  } catch (error) {
    console.error('Error in post-payment login:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
