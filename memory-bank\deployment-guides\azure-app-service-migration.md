# Azure App Service Migration Guide

## Overview
Migrate from Azure Static Web Apps to Azure App Service to resolve environment variable accessibility issues with Next.js API routes.

## Step 1: Azure App Service Setup

### Create App Service Resource
```bash
# Using Azure CLI
az webapp create \
  --resource-group quantboost-rg \
  --plan quantboost-plan \
  --name quantboost-frontend \
  --runtime "NODE:18-lts" \
  --deployment-source-url https://github.com/danedane/QuantBoost.git \
  --deployment-source-branch main
```

### Configure Application Settings (Environment Variables)
```bash
# Stripe Configuration
az webapp config appsettings set \
  --resource-group quantboost-rg \
  --name quantboost-frontend \
  --settings \
    STRIPE_SECRET_KEY="sk_test_51R..." \
    STRIPE_WEBHOOK_SECRET="whsec_..." \
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51R..."

# Supabase Configuration
az webapp config appsettings set \
  --resource-group quantboost-rg \
  --name quantboost-frontend \
  --settings \
    NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co" \
    NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
    SUPABASE_SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# Other Configuration
az webapp config appsettings set \
  --resource-group quantboost-rg \
  --name quantboost-frontend \
  --settings \
    NEXT_PUBLIC_BASE_URL="https://quantboost-frontend.azurewebsites.net" \
    NODE_ENV="production"
```

## Step 2: Update Next.js Configuration

### Modify next.config.js
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Remove standalone output for App Service
  // output: 'standalone', // Remove this line
  
  // Add App Service specific configuration
  experimental: {
    // Enable if needed for your app
  },
  
  // Ensure proper asset handling
  assetPrefix: process.env.NODE_ENV === 'production' 
    ? 'https://quantboost-frontend.azurewebsites.net' 
    : '',
    
  // Configure for Azure App Service
  trailingSlash: false,
  
  // Headers for security
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

## Step 3: GitHub Actions Workflow Update

### Create .github/workflows/azure-app-service.yml
```yaml
name: Deploy to Azure App Service

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'QuantBoost_Frontend/package-lock.json'
    
    - name: Install dependencies
      run: |
        cd QuantBoost_Frontend
        npm ci
    
    - name: Build application
      run: |
        cd QuantBoost_Frontend
        npm run build
      env:
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${{ secrets.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY }}
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
    
    - name: Deploy to Azure App Service
      uses: azure/webapps-deploy@v2
      with:
        app-name: 'quantboost-frontend'
        publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
        package: './QuantBoost_Frontend'
```

## Step 4: Cost Analysis

### Static Web Apps vs App Service
| Feature | Static Web Apps | App Service (B1) | App Service (S1) |
|---------|----------------|------------------|------------------|
| Monthly Cost | $9/month | ~$13/month | ~$74/month |
| API Routes | Limited/Broken | Full Support | Full Support |
| Environment Variables | Problematic | Native Support | Native Support |
| Custom Domains | Included | Included | Included |
| SSL | Included | Included | Included |
| Scaling | Auto | Manual/Auto | Manual/Auto |

**Recommendation**: Start with B1 Basic tier (~$13/month) for development/testing, upgrade to S1 Standard (~$74/month) for production.

## Step 5: Performance Considerations

### Advantages over Static Web Apps:
- **Reliable API Routes**: No more "Backend call failure" errors
- **Proper Environment Variables**: Full access to process.env in API routes
- **Better Error Handling**: Comprehensive logging and debugging
- **Consistent Performance**: No serverless cold starts for API routes

### Potential Considerations:
- **Always-on**: App Service runs continuously (vs serverless)
- **Regional Deployment**: Choose region closest to users
- **Monitoring**: Built-in Application Insights integration

## Step 6: Migration Timeline

### Phase 1 (Day 1): Setup and Configuration
- Create Azure App Service resource
- Configure environment variables
- Update GitHub Actions workflow

### Phase 2 (Day 2): Testing and Validation
- Deploy to staging slot
- Test payment processing flow
- Validate environment variable access
- Performance testing

### Phase 3 (Day 3): Production Deployment
- Deploy to production
- Update DNS/custom domain
- Monitor for issues
- Cleanup old Static Web Apps resource

## Step 7: Breaking Changes Assessment

### Minimal Breaking Changes Required:
- Remove `output: 'standalone'` from next.config.js
- Update GitHub Actions workflow
- Update environment variable references (if any)

### No Code Changes Needed For:
- API routes (will work as-is)
- Environment variable access (process.env works natively)
- Stripe integration (no changes needed)
- Supabase integration (no changes needed)

## Step 8: Rollback Plan

### If Issues Arise:
1. Keep Static Web Apps resource until migration is confirmed working
2. Use Azure App Service deployment slots for safe testing
3. DNS can be quickly switched back if needed
4. GitHub Actions can be reverted to previous workflow

## Next Steps

1. **Create Azure App Service resource**
2. **Configure environment variables in Azure Portal**
3. **Update GitHub Actions workflow**
4. **Test deployment to staging slot**
5. **Validate payment processing**
6. **Deploy to production**
