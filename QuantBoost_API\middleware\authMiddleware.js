const crypto = require('crypto');
const { sendError } = require('../utils/responseHelpers');
const { supabase } = require('../supabaseClient');
const { verifySupabaseJWT } = require('../utils/jwtVerifier');

function authenticateApiKey(req, res, next) {
    const apiKey = req.headers['x-api-key'];
    const masterKey = process.env.MASTER_API_KEY;

    if (!apiKey || !masterKey) {
        // Log this event, as it's a server misconfiguration.
        console.error('[authMiddleware] MASTER_API_KEY is not set or no API key was provided.');
        return sendError(res, 'API Key Required', 401);
    }

    try {
        // Use crypto.timingSafeEqual for constant-time comparison to prevent timing attacks.
        // Both buffers must be of the same length.
        const keyBuffer = Buffer.from(apiKey, 'utf8');
        const masterKeyBuffer = Buffer.from(masterKey, 'utf8');

        if (keyBuffer.length !== masterKeyBuffer.length || !crypto.timingSafeEqual(keyBuffer, masterKeyBuffer)) {
            return sendError(res, 'Invalid API Key', 401);
        }

        // If the keys match, proceed.
        next();
    } catch (error) {
        console.error('[authMiddleware] Error during API key validation:', error);
        return sendError(res, 'Internal Server Error during authentication', 500);
    }
}

async function authenticateJWT(req, res, next) {
    const authHeader = req.headers.authorization;

    // 1. Check if the Authorization header exists and is correctly formatted.
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return sendError(res, 'Authorization header missing or malformed', 401);
    }

    const token = authHeader.split(' ')[1];

    try {
        // 2. First try JWKS verification for new signing keys
        try {
            const claims = await verifySupabaseJWT(token);
            // Minimal mapping: Supabase user id is in sub; email may be present in claim
            const user = { id: claims.sub, email: claims.email };
            if (!user.id) {
                throw new Error('Missing sub claim in JWT');
            }
            console.log(`[authMiddleware] SUCCESS (JWKS): Validated token for user.id: ${user.id}, email: ${user.email}`);
            req.user = user;
            return next();
        } catch (jwksErr) {
            // Fallback to Supabase validation (works for legacy symmetric secrets or during rotation)
            const { data: { user }, error } = await supabase.auth.getUser(token);
            if (error || !user) {
                console.error('JWT Verification Error:', jwksErr?.message || error?.message || 'No user');
                return sendError(res, 'Invalid or expired token', 403);
            }
            console.log(`[authMiddleware] SUCCESS (fallback getUser): Validated token for user.id: ${user.id}, email: ${user.email}`);
            req.user = user;
            return next();
        }
    } catch (err) {
        console.error('JWT Verification Exception:', err?.message || err);
        return sendError(res, 'Token validation error', 500);
    }
}

module.exports = {
    authenticateApiKey,
    authenticateJWT // Added: export authenticateJWT
};
