---
title: Initial Progress Tracking for QuantBoost Excel Add-in
purpose: Lists features and milestones that are considered "not started" or "planned" for the new Excel Add-in, based on development documents.
projects: ["excel-add-in"]
source_analysis: "Derived from project brief and development guides (CleanExcel, Excel Trace, Sheet Size Analyzer)"
status: bootstrapping
last_updated: 2025-06-08T17:30:00Z
tags: ["excel-add-in", "progress", "development", "planned"]
---

**Completed 2025-06-08:**

#### **Phase 1: Project Setup & Foundation (Revised)**

*   `[x]` **1.1: Initialize `QuantBoost_Excel` Project** - (Completed)
*   `[x]` **1.2: Establish Shared Codebase** - (Completed)

*   `[✅]` **1.3: Port Licensing & Global State from PowerPoint Add-in**
    *   `[✅]` **1.3.1: Adapt `ThisAddIn.cs` from PowerPoint.**
        *   `[✅]` **Action:** Copy the *entire contents* of `QuantBoost_Powerpoint_Addin/ThisAddIn.cs` and paste them into your new, empty `QuantBoost_Excel/ThisAddIn.cs`, overwriting everything.
        *   `[✅]` **Action:** Perform a solution-wide "Find and Replace" for the namespace:
            *   Find: `QuantBoost_Powerpoint_Addin`
            *   Replace with: `QuantBoost_Excel`
        *   `[✅]` **Action:** In the new `QuantBoost_Excel/ThisAddIn.cs`, remove all members (fields, properties, methods) that are specific to PowerPoint features. These include:
            *   `_backgroundTasks` and `_backgroundTaskLock` (Keep these)
            *   `_globalCancellationTokenSource` and `GlobalCancellationToken` (Keep these)
            *   `RegisterBackgroundTask` (Keep this)
            *   `ExecuteAuthenticatedApiCallAsync` (Keep this)
            *   The entire `ExcelLinkService` and related logic (Remove)
            *   The entire `AnalyzePane` and `LinkManagerPane` logic (Remove)
            *   The `TestTokenPersistenceFlow` method (Optional, but good to keep for debugging).
        *   `[✅]` **Action:** In `QuantBoost_Excel/ThisAddIn.Designer.cs`, ensure the `Application` object is of type `Microsoft.Office.Interop.Excel.Application`. Visual Studio should handle this automatically.
        *   `[✅]` **Action:** Update the `Globals` helper class at the bottom of the file to reference the new Ribbon class we will create (`MainRibbon` instead of `QuantBoostRibbon`).
            ```csharp
            // In QuantBoost_Excel/ThisAddIn.cs
            internal static partial class Globals
            {
                public static MainRibbon Ribbon { get; internal set; }
            }
            ```
    *   `[✅]` **1.3.2: Verify `ThisAddIn_Startup` Logic.**
        *   `[✅]` **Action:** Confirm that the copied `ThisAddIn_Startup` method is present and compiles. It should work out-of-the-box because it calls the `QuantBoost_Shared` and `QuantBoost_Licensing` libraries, which are already referenced. The entire silent login and license validation flow is now ported.

*   `[✅]` **1.4: Create Ribbon UI by Adapting PowerPoint Ribbon**
    *   `[✅]` **1.4.1: Create `MainRibbon.xml` for Excel.**
        *   `[✅]` **Action:** Add a new "Ribbon (XML)" item to `QuantBoost_Excel` named `MainRibbon.xml`.
        *   `[✅]` **Action:** Paste the following complete XML content into the file. This combines the reusable "Account" group from PowerPoint with our new "Formula Auditing" group.

            ```xml
            <?xml version="1.0" encoding="UTF-8"?>
            <customUI xmlns="http://schemas.microsoft.com/office/2009/07/customui" onLoad="OnLoad">
              <ribbon>
                <tabs>
                  <tab id="tabQuantBoost" label="QuantBoost">

                    <group id="groupFormulaAuditing" label="Formula Auditing">
                      <button id="btnTracePrecedents"
                              label="Trace Precedents"
                              imageMso="FormulaTracePrecedents"
                              size="large"
                              onAction="OnTracePrecedents_Click"
                              getEnabled="IsPremiumFeatureEnabled" />
                    </group>

                    <group id="groupAccount" label="Account">
                      <button id="btnManageAccount"
                              imageMso="Lock"
                              size="large"
                              getLabel="GetManageAccountLabel"
                              onAction="OnManageAccountClick"
                              getVisible="GetManageAccountVisible" />
                      <button id="btnLogout"
                              label="Logout"
                              imageMso="AccountLogout"
                              size="large"
                              onAction="OnLogoutClick"
                              getVisible="GetLogoutButtonVisible" />
                    </group>

                  </tab>
                </tabs>
              </ribbon>
            </customUI>
            ```

    *   `[✅]` **1.4.2: Create `MainRibbon.cs` by Adapting PowerPoint's Ribbon Logic.**
        *   `[✅]` **Action:** Create a new class file at `QuantBoost_Excel/UI/MainRibbon.cs`.
        *   `[✅]` **Action:** Copy the *entire contents* of `QuantBoost_Powerpoint_Addin/UI/QuantBoostRibbon.cs` and paste them into `MainRibbon.cs`.
        *   `[✅]` **Action:** Change the namespace to `QuantBoost_Excel.UI` and the class name to `MainRibbon`.
        *   `[✅]` **Action:** Adapt the `MainRibbon.cs` class:
            *   `[✅]` **KEEP** these methods exactly as they are. They are 100% reusable:
                *   `_ribbonUI` field
                *   `OnLoad(Office.IRibbonUI ribbonUI)`
                *   `OnManageAccountClick(Office.IRibbonControl control)`
                *   `GetManageAccountLabel(Office.IRibbonControl control)`
                *   `GetManageAccountVisible(Office.IRibbonControl control)`
                *   `GetLogoutButtonVisible(Office.IRibbonControl control)`
                *   `OnLogoutClick(Office.IRibbonControl control)`
                *   `IsPremiumFeatureEnabled(Office.IRibbonControl control)`
                *   `UpdateLicenseUI(LicenseDetailsClient license)`
                *   `InvalidateRibbonControl(string controlId)`
                *   `EnsureThisAddInReady(...)`
            *   `[✅]` **REMOVE** all methods and fields related to PowerPoint-specific features:
                *   `_analyzeTaskPane`, `_analyzePaneControl`, `_linkManagerTaskPane`, `_linkManagerPaneControl`
                *   `_excelLinkService` and its property `ExcelLinkSvc`
                *   All `On...Click` callbacks for Analyze and Excel Link (`OnAnalyzeClick`, `OnInsertExcelContentClick`, etc.)
                *   All Task Pane event handlers (`..._VisibleChanged`)
            *   `[✅]` **ADD** the new callback stub for our Excel feature:
                ```csharp
                public void OnTracePrecedents_Click(Office.IRibbonControl control)
                {
                    try
                    {
                        if (!EnsureThisAddInReady("Trace Precedents") || !IsPremiumFeatureEnabled(control))
                        {
                            // This pattern uses the shared ToastNotifier, assuming it's in QuantBoost_Shared
                            // ToastNotifier.ShowToast("Premium license required.", 3000, Color.OrangeRed);
                            MessageBox.Show("A valid license is required to use this feature.", "QuantBoost");
                            return;
                        }

                        // TODO: Logic for Phase 4.1 will go here.
                        // For now, a placeholder:
                        MessageBox.Show("Trace Precedents feature coming soon!", "QuantBoost");
                    }
                    catch (Exception ex)
                    {
                        // Assumes ErrorHandlingService is in QuantBoost_Shared
                        // ErrorHandlingService.HandleException(ex, "Error in OnTracePrecedents_Click");
                    }
                }
                ```
            *   `[✅]` **UPDATE** the `UpdateLicenseUI` method to invalidate the new button:
                ```csharp
                public void UpdateLicenseUI(LicenseDetailsClient license)
                {
                    // ...
                    try
                    {
                        InvalidateRibbonControl("btnTracePrecedents"); // Add this line
                        // ... keep the other invalidations for account buttons
                        InvalidateRibbonControl("btnManageAccount");
                        InvalidateRibbonControl("btnLogout");
                    }
                    //...
                }
                ```

    *   `[✅]` **1.4.3: Integrate Ribbon in `ThisAddIn.cs`.**
        *   `[✅]` **Action:** In `QuantBoost_Excel/ThisAddIn.cs`, ensure the `CreateRibbonExtensibilityObject` method is present and correct. It should now create an instance of your new `MainRibbon`.
            ```csharp
            // In QuantBoost_Excel/ThisAddIn.cs
            protected override Microsoft.Office.Core.IRibbonExtensibility CreateRibbonExtensibilityObject()
            {
                if (_ribbon == null)
                {
                    _ribbon = new UI.MainRibbon(); // Make sure this uses the new class
                    Globals.Ribbon = _ribbon;
                }
                return _ribbon;
            }
            ```
        *   `[✅]` **Action:** Change the type of the `_ribbon` field in `ThisAddIn.cs` to `private MainRibbon _ribbon;`.

#### **Phase 2: Core Logic - Precedent Parsing Engine**

*   `[✅]` **2.1: Create Feature File Structure**
    *   `[✅]` 2.1.1: In Solution Explorer, create the folder `QuantBoost_Excel/Features/ExcelTrace/`.
    *   `[✅]` 2.1.2: Inside `ExcelTrace/`, create sub-folders: `Model`, `Logic`, `UI`.

*   `[✅]` **2.2: Define the Data Model (`TraceNode`)**
    *   `[✅]` 2.2.1: Create a new class file: `Features/ExcelTrace/Model/TraceNode.cs`.
    *   `[✅]` 2.2.2: Define the `NodeType` enum and the `TraceNode` class with detailed properties.
        ```csharp
        public enum NodeType { Root, SameSheet, DifferentSheet, ExternalWorkbook, NamedRange, Error }

        public class TraceNode
        {
            public string DisplayText { get; set; } // e.g., "'Sheet1'!A1" or "MyNamedRange"
            public string FullAddress { get; set; } // Fully qualified address for cycle detection
            public string DisplayValue { get; set; } // The formatted text from the cell, e.g., "1,586" or "43%"
            public string Formula { get; set; } // The formula, if any
            public NodeType NodeType { get; set; }
            public List<TraceNode> Children { get; set; } = new List<TraceNode>();
            public Excel.Range SourceRange { get; set; } // Live reference to the Excel Range object for navigation
            public bool HasError { get; set; }
        }
        ```

*   `[✅]` **2.3: Implement the `TracerEngine`**
    *   `[✅]` 2.3.1: Create a new class file: `Features/ExcelTrace/Logic/TracerEngine.cs`.
    *   `[✅]` 2.3.2: Define the main public method.
        ```csharp
        public TraceNode BuildPrecedentTree(Excel.Range startCell, int maxDepth = 10)
        ```
    *   `[✅]` 2.3.3: Implement the recursive helper function.
        ```csharp
        private void PopulatePrecedents(TraceNode parentNode, int currentDepth, int maxDepth, HashSet<string> visited)
        ```
    *   `[✅]` 2.3.4: **Implement Cycle Detection:** The `visited` `HashSet<string>` will store the `FullAddress` of each node. Before tracing a new node, check if its address is in the set. If so, do not recurse further.
    *   `[✅]` 2.3.5: **Implement Core Recursion Logic:**
        *   `[✅]` Inside `PopulatePrecedents`, use a `try-catch` block around the `parentNode.SourceRange.DirectPrecedents` call.
        *   `[✅]` Iterate through each `Area` in the returned `Range`.
        *   `[✅]` For each `cell` in the `Area`, create a new `TraceNode`.
        *   `[✅]` Populate the new `TraceNode`'s properties (sheet name, workbook name, address).
        *   `[✅]` Call the `GetFormattedValue` helper to set the `DisplayValue`.
        *   `[✅]` Add the new node to `parentNode.Children`.
        *   `[✅]` Recursively call `PopulatePrecedents` for the new node.

*   `[✅]` **2.4: Implement Value Display Helper**
    *   `[✅]` 2.4.1: In `TracerEngine.cs`, create a static helper method.
        ```csharp
        public static string GetFormattedValue(Excel.Range cell)
        ```
    *   `[✅]` 2.4.2: Inside the method, use a `try-catch` block.
    *   `[✅]` 2.4.3: Check if the `cell.Value2` is an error type. If so, return the error text (e.g., `#REF!`).
    *   `[✅]` 2.4.4: Return `cell.Text.ToString()`. The `.Text` property correctly captures all number formatting.
    *   `[✅]` 2.4.5: In the catch block, return a generic error string like `"[Read Error]"`.

---

#### **Phase 3: UI Development - The "Trace In" Window**

*   `[✅]` **3.1: Create and Configure the Form**
    *   `[✅]` 3.1.1: Add a new "Windows Form" to `Features/ExcelTrace/UI/` named `frmTrace.cs`.
    *   `[✅]` 3.1.2: In the form designer, set properties: `Text = "Trace Precedents"`, `FormBorderStyle = SizableToolWindow`, `ShowIcon = false`, `ShowInTaskbar = false`, `StartPosition = CenterParent`.
    *   `[✅]` 3.1.3: Apply the blue theme. If a shared theming helper exists, call it in the constructor. Otherwise, manually set `BackColor` and other colors.

*   `[✅]` **3.2: Add and Configure UI Controls**
    *   `[✅]` 3.2.1: **Top Panel:** Add a `Panel` docked to the top.
        *   `[✅]` Add a `TextBox` named `txtFormula`, set `ReadOnly = true`, `Dock = Fill`.
    *   `[✅]` 3.2.2: **Bottom Panel:** Add a `Panel` docked to the bottom.
        *   `[✅]` Add `Button` controls: `btnOK` (`DialogResult = OK`), `btnCancel` (`DialogResult = Cancel`), and `btnSettings` (with a gear icon). Align them to the right.
    *   `[✅]` 3.2.3: **Main Panel:** Add a `Panel` docked to fill the remaining space.
        *   `[✅]` Add a `TreeView` control named `tvPrecedents`, set `Dock = Fill`, `FullRowSelect = true`, `HideSelection = false`.
        *   `[✅]` **Note on Columns:** A standard `TreeView` does not support columns. To emulate the screenshot, use `string.PadRight()` and a monospaced font (like Consolas) when setting the `TreeNode.Text`.
            ```csharp
            // Example for setting node text
            node.Text = dataNode.DisplayText.PadRight(40) + dataNode.DisplayValue;
            ```
    *   `[✅]` 3.2.4: Add an `ImageList` control named `ilIcons` and link it to `tvPrecedents.ImageList`. Populate it with icons for home, sheet, workbook, and error.

*   `[✅]` **3.3: Implement UI Population Logic**
    *   `[✅]` 3.3.1: Create a public method `public void InitializeTrace(TraceNode rootNode)`.
    *   `[✅]` 3.3.2: Inside, set `txtFormula.Text = rootNode.Formula`.
    *   `[✅]` 3.3.3: Clear the `TreeView`: `tvPrecedents.Nodes.Clear()`.
    *   `[✅]` 3.3.4: Create the root `TreeNode` and add it to the `TreeView`.
    *   `[✅]` 3.3.5: Call a recursive helper `private void PopulateTreeViewNodes(TreeNode parentWinFormsNode, TraceNode parentDataNode)` to populate the children.
    *   `[✅]` 3.3.6: **Critical:** In the helper, for each `TraceNode` child, create a `System.Windows.Forms.TreeNode`, set its `Text` and `ImageKey`, and store the data object: `winFormsNode.Tag = dataNode;`.
    *   `[✅]` 3.3.7: **Lazy Loading:** For any `dataNode` that has precedents that haven't been traced yet, add a single dummy child node with the text "..." to its corresponding `winFormsNode`.

---

#### **Phase 4, 5, & 6: Integration, Settings, & Polishing (Combined for Action Flow)**

*   `[✅]` **4.1: Connect Ribbon Click to Full Workflow**
    *   `[✅]` 4.1.1: In `MainRibbon.cs`, fully implement `OnTracePrecedents_Click`.
    *   `[✅]` 4.1.2: Get `activeCell = Globals.ThisAddIn.Application.ActiveCell`.
    *   `[✅]` 4.1.3: Validate that `activeCell` is not null, is a single cell, and `activeCell.HasFormula`. Show a `MessageBox` if validation fails.
    *   `[✅]` 4.1.4: Instantiate the engine: `var engine = new QuantBoost_Excel.Features.ExcelTrace.Logic.TracerEngine();`.
    *   `[✅]` 4.1.5: Build the data tree: `var rootNode = engine.BuildPrecedentTree(activeCell);`.
    *   `[✅]` 4.1.6: Show the form:
        ```csharp
        using (var form = new QuantBoost_Excel.Features.ExcelTrace.UI.frmTrace())
        {
            form.InitializeTrace(rootNode);
            form.ShowDialog(); // Use ShowDialog for modal behavior
        }
        ```

*   `[✅]` **4.2: Implement UI Interactivity**
    *   `[✅]` 4.2.1: **Navigation on Double-Click:** Handle the `tvPrecedents.NodeMouseDoubleClick` event.
        *   `[✅]` Get the `TraceNode` from the clicked node's `Tag`.
        *   `[✅]` Call a new helper method `private void NavigateToNode(TraceNode node)`.
        *   `[✅]` Inside `NavigateToNode`, activate the correct workbook, sheet, and select the `node.SourceRange`. Wrap all Excel interactions in `try-catch`.
    *   `[✅]` 4.2.2: **Lazy Loading on Expand:** Handle the `tvPrecedents.BeforeExpand` event.
        *   `[✅]` Check if the expanding node has the single "..." dummy child.
        *   `[✅]` If so, remove the dummy node.
        *   `[✅]` Get the `parentDataNode` from the expanding node's `Tag`.
        *   `[✅]` Call the `TracerEngine` again to populate the children of just this `parentDataNode`.
        *   `[✅]` Call `PopulateTreeViewNodes` to add the newly fetched children to the UI.

*   `[✅]` **5.1: Implement Settings Panel and Logic**
    *   `[✅]` 5.1.1: Add a `ContextMenuStrip` control to `frmTrace` named `cmsSettings`.
    *   `[✅]` 5.1.2: Add `ToolStripMenuItem`s with `CheckOnClick = true` for: "Highlight navigated cells", "Open linked workbooks", "Unhide rows & columns".
    *   `[✅]` 5.1.3: In the `btnSettings_Click` event, show the context menu: `cmsSettings.Show(btnSettings, new Point(0, btnSettings.Height));`.
    *   `[✅]` 5.1.4: Create a static `TraceSettings` class to store the boolean state of these settings. Persist them using `Properties.Settings.Default`.
    *   `[✅]` 5.1.5: Modify the `NavigateToNode` method to check these settings before acting (e.g., `if (TraceSettings.ShouldUnhide) { node.SourceRange.EntireRow.Hidden = false; }`).


#### **Phase 1: Foundational Layout & Theming (Code-First)**

*   `[✓]` **1.1: Establish the Code-First Structure**
    *   `[✓]` 1.1.1: In `frmTrace.cs`, create the `private void InitializeUI()` method.
    *   `[✓]` 1.1.2: Call `InitializeUI()` from the `frmTrace()` constructor. Ensure any existing `InitializeComponent()` call is removed or commented out.
    *   `[✓]` 1.1.3: Declare all required UI controls as private fields at the top of the `frmTrace` class (e.g., `private Panel pnlHeader;`, `private TreeView tvPrecedents;`, `private Button btnOK;`). This makes them accessible throughout the class for event handling and data manipulation.

*   `[✓]` **1.2: Implement the Form's Custom Frame and Core Layout**
    *   `[✓]` 1.2.1: In `InitializeUI()`, set the main form's properties programmatically:
        ```csharp
        this.FormBorderStyle = FormBorderStyle.None;
        this.BackColor = QuantBoostTheme.PrimaryBlue; // Assumes QuantBoostTheme class exists
        this.ClientSize = new Size(600, 450);
        this.Text = "Trace Precedents";
        this.StartPosition = FormStartPosition.CenterParent;
        ```
    *   `[✓]` 1.2.2: Add the P/Invoke code for window dragging to the `frmTrace` class and create the `DraggableControl_MouseDown` event handler method.
    *   `[✓]` 1.2.3: In `InitializeUI()`, instantiate and configure the main layout panels (`pnlMainContainer`, `pnlHeader`, `pnlFooter`, `pnlContent`) with their respective `Dock`, `Padding`, and `BackColor` properties.
    *   `[✓]` 1.2.4: **Build the core control hierarchy:** Add the panels to their parents using the `Controls.Add()` method (e.g., `this.Controls.Add(pnlMainContainer);`, `pnlMainContainer.Controls.Add(pnlHeader);`).
    *   `[✓]` 1.2.5: Programmatically subscribe the `pnlHeader.MouseDown` event to the `DraggableControl_MouseDown` handler: `pnlHeader.MouseDown += DraggableControl_MouseDown;`.

*   `[✓]` **1.3: Create and Style Header Content**
    *   `[✓]` 1.3.1: In `InitializeUI()`, instantiate and configure the header `Label` (`lblTitle`) and the custom close `Button` (`btnClose`).
    *   `[✓]` 1.3.2: Set their properties in code, including `Text`, `ForeColor`, `Font`, `Dock`, `FlatStyle`, etc.
    *   `[✓]` 1.3.3: Add `lblTitle` and `btnClose` to the `pnlHeader.Controls` collection.
    *   `[✓]` 1.3.4: Also subscribe the `lblTitle.MouseDown` event to the `DraggableControl_MouseDown` handler to make the entire header area draggable.

*   `[✓]` **1.4: Create and Style Footer Content**
    *   `[✓]` 1.4.1: In `InitializeUI()`, instantiate and configure the footer buttons (`btnOK`, `btnCancel`, `btnSettings`).
    *   `[✓]` 1.4.2: Set their `FlatStyle`, `BackColor`, `ForeColor`, `Dock`, and `Image` properties in code to match the target design.
    *   `[✓]` 1.4.3: Add the footer buttons to the `pnlFooter.Controls` collection.

---

#### **Phase 2: Control-Specific Implementation (Code-First)**

*   `[✓]` **2.1: Implement the Formula Bar**
    *   `[✓]` 2.1.1: In `InitializeUI()`, instantiate the `pnlFormulaContainer` `Panel`, the `txtFormula` `TextBox`, and the `btnGoToFormula` `Button`.
    *   `[✓]` 2.1.2: Set all required properties for these controls in code (`Dock`, `Padding`, `BorderStyle`, `Text`, `ReadOnly`, etc.).
    *   `[✓]` 2.1.3: Build the hierarchy: Add `txtFormula` and `btnGoToFormula` to `pnlFormulaContainer.Controls`, then add `pnlFormulaContainer` to `pnlContent.Controls`.

*   `[✓]` **2.2: Implement the `TreeView` with Custom Drawing**
    *   `[✓]` 2.2.1: In `InitializeUI()`, instantiate the `tvPrecedents` `TreeView`.
    *   `[✓]` 2.2.2: Set its properties programmatically, including the critical `DrawMode = TreeViewDrawMode.OwnerDrawText;`, `BorderStyle = BorderStyle.None;`, `FullRowSelect = true;`, and `ShowLines = false;`.
    *   `[✓]` 2.2.3: Programmatically subscribe to the `DrawNode` event: `tvPrecedents.DrawNode += tvPrecedents_DrawNode;`.
    *   `[✓]` 2.2.4: Implement the `private void tvPrecedents_DrawNode(...)` event handler method to handle custom selection color using `e.Graphics`.
    *   `[✓]` 2.2.5: Add `tvPrecedents` to the `pnlContent.Controls` collection.

*   `[✓]` **2.3: Implement the Column Headers**
    *   `[✓]` 2.3.1: In `InitializeUI()`, instantiate the `pnlTreeHeader` `Panel` and its child `Label` controls ("Precedents", "Value").
    *   `[✓]` 2.3.2: Set their properties (`Dock`, `Text`, `Location`, `Size`, `TextAlign`) to align with the column widths that will be used in the `TreeView`.
    *   `[✓]` 2.3.3: Add the header `Panel` to `pnlContent.Controls` and ensure it is ordered correctly above the `TreeView`.

---

#### **Phase 3: Interactivity & Data Population (Code-First)**

*   `[✓]` **3.1: Implement Data Population with Column Simulation**
    *   `[✓]` 3.1.1: In the method that populates the `TreeView` (e.g., `PopulateTreeViewNodes`), update the logic to set the `TreeNode.Text` using string formatting with padding.
    *   `[✓]` 3.1.2: Set the `TreeNode.NodeFont` property programmatically to a monospaced font like "Consolas" to ensure column alignment.
        ```csharp
        // Example from your node population logic
        treeNode.NodeFont = new Font("Consolas", 9F);
        treeNode.Text = $"{displayText, -45}{displayValue, 15}";
        ```
    *   `[✓]` 3.1.3: Ensure the public `InitializeTrace` method correctly populates `txtFormula.Text` and initiates the `TreeView` population.

*   `[✓]` **3.2: Implement "Go To Formula" Button Logic**
    *   `[✓]` 3.2.1: Create the `private void BtnGoToFormula_Click(object sender, EventArgs e)` event handler method.
    *   `[✓]` 3.2.2: In `InitializeUI()`, subscribe the `btnGoToFormula.Click` event to this new handler.
    *   `[✓]` 3.2.3: Implement the logic within the handler to navigate the user back to the original cell that the trace was started from. This requires storing the root `TraceNode` or its `SourceRange` in a field accessible by the handler.

*   `[✓]` **3.3: Implement Custom Close Button Logic**
    *   `[✓]` 3.3.1: In `InitializeUI()`, subscribe the `btnClose.Click` event to a handler. A lambda expression is suitable for this one-line action.
        ```csharp
        // In InitializeUI()
        btnClose.Click += (s, e) => this.Close();
        ```
    *   `[✓]` 3.3.2: (Optional) Add hover effects for the close button by programmatically handling the `MouseEnter` and `MouseLeave` events to change its `BackColor`.

*   `[✓]` **3.4: Implement Remaining Event Handlers**
    *   `[✓]` 3.4.1: Programmatically subscribe to all other necessary events in `InitializeUI()`, such as `btnOK.Click`, `btnCancel.Click`, `btnSettings.Click`, and `tvPrecedents.NodeMouseDoubleClick`.
    *   `[✓]` 3.4.2: Create the empty stub methods for these event handlers (e.g., `private void BtnSettings_Click(object sender, EventArgs e)`) to be filled out with logic later.

    #### **Phase 1: Fix Core Functionality**

*   `[✓]` **1.1: Fix the Missing `TreeView` (Critical)**
    *   `[✓]` 1.1.1: In `frmTrace.cs`, locate the `BuildControlHierarchy()` method.
    *   `[✓]` 1.1.2: **The Problem:** You are adding the containers (`pnlFormulaContainer`, `pnlTreeHeader`) to `pnlContent`, but you are not adding the `TreeView` itself in this method. It's being added later in `CreateTreeView()`. The `Dock = Fill` control (`TreeView`) must be added first, or other docked controls will cover it.
    *   `[✓]` 1.1.3: **The Fix:** Modify the `BuildControlHierarchy()` and `CreateTreeView()` methods to ensure the correct order.

        **In `BuildControlHierarchy()`:**
        ```csharp
        private void BuildControlHierarchy()
        {
            // ... existing code ...

            // Add sub-panels to content area
            // CRITICAL: Add the TreeView FIRST so it can fill the remaining space.
            pnlContent.Controls.Add(tvPrecedents); // Add the TreeView here
            pnlContent.Controls.Add(pnlTreeHeader); // Then add the top-docked panels
            pnlContent.Controls.Add(pnlFormulaContainer);
        }
        ```

        **In `CreateTreeView()`:**
        ```csharp
        private void CreateTreeView()
        {
            // ... TreeView creation and configuration ...

            // Task 2.2.5: Add TreeView to content panel (will be positioned after headers)
            // pnlContent.Controls.Add(tvPrecedents); // <-- DELETE THIS LINE. It's now handled in BuildControlHierarchy.
        }
        ```
        *   **Alternative Fix:** A simpler, more robust way is to use `BringToFront()` and `SendToBack()`. In `CreateTreeView()`, after adding the control, call `tvPrecedents.SendToBack();`. This ensures it is at the bottom of the z-order, allowing the other docked panels to sit on top of it correctly. This is often safer than managing the `Controls.Add()` order.
            ```csharp
            // In CreateTreeView()
            pnlContent.Controls.Add(tvPrecedents);
            tvPrecedents.SendToBack(); // Ensure it's behind other docked controls
            ```

*   `[✓]` **1.2: Fix the "Go" Button Navigation**
    *   `[✓]` 1.2.1: In `frmTrace.cs`, locate the `BtnGoToFormula_Click` event handler.
    *   `[✓]` 1.2.2: **The Problem:** The current logic `NavigateToNode(_rootNode)` is incorrect for this button's purpose.
    *   `[✓]` 1.2.3: **The Fix:** Implement new logic to parse the formula text and navigate.

        ```csharp
        private void BtnGoToFormula_Click(object sender, EventArgs e)
        {
            try
            {
                string formulaText = txtFormula.Text;
                if (string.IsNullOrEmpty(formulaText)) return;

                // Simple parsing: find the first valid cell reference.
                // This is a basic implementation. A more robust solution would use regex.
                string address = ParseFirstAddressFromFormula(formulaText);

                if (!string.IsNullOrEmpty(address))
                {
                    Excel.Range targetRange = null;
                    try
                    {
                        // This will resolve references like 'Sheet Name'!A1
                        targetRange = Globals.ThisAddIn.Application.Range[address];
                        
                        // Create a temporary TraceNode for the NavigateToNode method
                        var tempNode = new TraceNode { SourceRange = targetRange };
                        NavigateToNode(tempNode);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Could not navigate to '{address}'. Invalid address or sheet.", "Navigation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        ErrorHandlingService.LogException(ex, $"Failed to resolve range from formula: {address}");
                    }
                    finally
                    {
                        if (targetRange != null) Marshal.ReleaseComObject(targetRange);
                    }
                }
                else
                {
                    MessageBox.Show("Could not find a valid cell reference in the formula to navigate to.", "Navigation Error", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, "Error in Go To Formula button");
            }
        }

        // Add this new helper method to the class
        private string ParseFirstAddressFromFormula(string formula)
        {
            // This is a very basic parser. It looks for a pattern like 'Sheet Name'!A1 or just A1.
            // A production version should use a more robust Regex pattern.
            var match = System.Text.RegularExpressions.Regex.Match(formula, @"('?[\w\s&]+'?!)?\$?[A-Z]{1,3}\$?\d{1,7}");
            if (match.Success)
            {
                return match.Value;
            }
            return null;
        }
        ```
#### **Phase 2: UI Polish**

*   `[✓]` **2.1: Adjust Formula Bar Padding**
    *   `[✓]` In `CreateFormulaBar()`, increase the `Height` of `pnlFormulaContainer` from `60` to `70` or `75` to add more space above the `TextBox`.
    *   `[✓]` Adjust the `Location` of the `lblFormula` and `txtFormula` controls to be lower within the panel.

*   `[✓]` **2.2: Refine UI to Match Target Design**
    *   `[✓]` **Colors:** Update the `CreateFooterContent` method to use the exact colors from the target design: a light gray for "Cancel" and a specific green for "OK".
    *   `[✓]` **Fonts:** Review all `Font` initializations to ensure they match the target (e.g., `Segoe UI`, correct sizes, bold/regular).
    *   `[✓]` **Borders:** The target design has very subtle or no borders on the buttons. In `CreateFooterContent`, ensure `FlatStyle` is set to `FlatStyle.Flat` and `FlatAppearance.BorderSize = 0` for the `OK` and `Cancel` buttons to remove their default borders. The "Settings" button has a thin border which `FlatStyle.Standard` might approximate, or it might require custom drawing.
    *   `[✓]` **Go Button:** The "Go" button in the target has an icon. Update the `btnGoToFormula` initialization to use an `Image` instead of text.

    ### **Task List: Refactor `frmTrace` UI to `TableLayoutPanel`**

**Objective:** Replace the `pnlContent` `Panel` with a `TableLayoutPanel` to resolve control ordering and visibility issues, ensuring the `TreeView` is always visible.

---

#### **Phase 1: Replace Core Layout Panel**

*   `[✓]` **1.1: Update the Class-Level Field Declaration**
    *   `[✓]` 1.1.1: In `frmTrace.cs`, locate the `Private Fields` region.
    *   `[✓]` 1.1.2: Change the declaration of `pnlContent` from `Panel` to `TableLayoutPanel` and rename it for clarity.
        *   **From:** `private Panel pnlContent;`
        *   **To:** `private TableLayoutPanel tlpContent;`

*   `[✓]` **1.2: Re-implement the Content Panel Creation**
    *   `[✓]` 1.2.1: In `frmTrace.cs`, locate the `CreateMainLayoutPanels()` method.
    *   `[✓]` 1.2.2: Find the block of code that instantiates `pnlContent`.
    *   `[✓]` 1.2.3: Replace that entire block with the new `TableLayoutPanel` initialization code. This new code will define the three rows needed for the layout.
        ```csharp
        // In CreateMainLayoutPanels()
        // Replace the existing pnlContent block with this:
        tlpContent = new TableLayoutPanel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.FromArgb(240, 248, 255), // Or QuantBoostTheme.ContentBackground
            Padding = new Padding(8),
            ColumnCount = 1,
            RowCount = 3
        };

        // Define the row styles:
        // Row 0: Formula Bar (fixed height)
        tlpContent.RowStyles.Add(new RowStyle(SizeType.Absolute, 75F)); 
        // Row 1: TreeView Header (fixed height)
        tlpContent.RowStyles.Add(new RowStyle(SizeType.Absolute, 25F));
        // Row 2: TreeView (fills all remaining space)
        tlpContent.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
        ```

#### **Phase 2: Update Control Hierarchy and Placement**

*   `[✓]` **2.1: Update the Main Control Hierarchy**
    *   `[✓]` 2.1.1: In `frmTrace.cs`, locate the `BuildControlHierarchy()` method.
    *   `[✓]` 2.1.2: Modify the `pnlMainContainer` section to add the new `tlpContent` instead of the old `pnlContent`.
        *   **From:** `pnlMainContainer.Controls.Add(pnlContent);`
        *   **To:** `pnlMainContainer.Controls.Add(tlpContent);`
    *   `[✓]` 2.1.3: **Delete** the lines that add child controls to `pnlContent` from this method, as they will now be added directly to the `TableLayoutPanel`.
        *   **Delete:** `pnlContent.Controls.Add(pnlFormulaContainer);`
        *   **Delete:** `pnlContent.Controls.Add(pnlTreeHeader);`

*   `[✓]` **2.2: Re-parent the Formula Bar**
    *   `[✓]` 2.2.1: In `frmTrace.cs`, locate the `CreateFormulaBar()` method.
    *   `[✓]` 2.2.2: Find the line that adds controls to `pnlFormulaContainer`.
    *   `[✓]` 2.2.3: **Delete** the lines that add `lblFormula`, `txtFormula`, and `btnGoToFormula` to `pnlFormulaContainer.Controls`.
    *   `[✓]` 2.2.4: **Add** a new line at the end of the method to place the entire `pnlFormulaContainer` into the first row of the `TableLayoutPanel`.
        ```csharp
        // At the end of CreateFormulaBar()
        tlpContent.Controls.Add(pnlFormulaContainer, 0, 0); // Add to Column 0, Row 0
        ```

*   `[✓]` **2.3: Re-parent the TreeView Headers**
    *   `[✓]` 2.3.1: In `frmTrace.cs`, locate the `CreateColumnHeaders()` method.
    *   `[✓]` 2.3.2: **Delete** the lines that add the header labels to `pnlTreeHeader.Controls`.
    *   `[✓]` 2.3.3: **Delete** the line `pnlTreeHeader.BringToFront();`.
    *   `[✓]` 2.3.4: **Add** a new line at the end of the method to place the `pnlTreeHeader` into the second row of the `TableLayoutPanel`.
        ```csharp
        // At the end of CreateColumnHeaders()
        tlpContent.Controls.Add(pnlTreeHeader, 0, 1); // Add to Column 0, Row 1
        ```

*   `[✓]` **2.4: Re-parent the TreeView**
    *   `[✓]` 2.4.1: In `frmTrace.cs`, locate the `CreateTreeView()` method.
    *   `[✓]` 2.4.2: Find the line `pnlContent.Controls.Add(tvPrecedents);` and modify it to add the `TreeView` to the third row of the `TableLayoutPanel`.
        *   **From:** `pnlContent.Controls.Add(tvPrecedents);`
        *   **To:** `tlpContent.Controls.Add(tvPrecedents, 0, 2); // Add to Column 0, Row 2`
    *   `[✓]` 2.4.3: **Delete** the line `tvPrecedents.SendToBack();` as it is no longer necessary.

### **Production Task List: Implementing Multi-Level Precedent Tracing**

**Objective:** Refactor the `TracerEngine` and supporting UI to recursively trace all precedents of a formula, displaying them in a hierarchical, indented `TreeView` that precisely matches the target design's functionality and appearance.

### **✅ CRITICAL FIXES IMPLEMENTED**

**Fixed Logic Error:** Corrected recursive call placement in `TracerEngine.cs` - moved recursion logic from `ProcessPrecedentRange` to `PopulatePrecedents` for proper multi-level tracing.

**Fixed Display Text:** Updated `BuildDisplayText` method to be depth-aware - root shows simple address, depth 1 shows sheet reference, deeper levels show absolute addresses.

**Fixed Indentation:** Corrected indentation logic to start at depth 2, matching target UI where root and first-level children have no indent.

---

#### **Phase 1: Enhance the `TracerEngine` for Deep Tracing**

This phase focuses on the core logic of recursively finding all precedents.

*   `[✓]` **1.1: Implement Recursive Precedent Population**
    *   `[✓]` 1.1.1: In `TracerEngine.cs`, locate the `PopulatePrecedents` method. This is the core of the recursive logic.
    *   `[✓]` 1.1.2: **The Problem:** The current logic only gets the *direct* precedents of the root node. It does not recursively call itself for each child node to find *their* precedents.
    *   `[✓]` 1.1.3: **The Fix:** Inside the `foreach (Excel.Range cell in area.Cells)` loop within `ProcessPrecedentRange`, after creating a `childNode`, add a recursive call if the child itself has a formula.

        ```csharp
        // In TracerEngine.cs -> ProcessPrecedentRange()
        // Inside the foreach loop...
        try
        {
            var childNode = CreateTraceNode(cell, DetermineNodeType(cell, parentNode), currentDepth + 1);

            // Add the new child to the parent's collection
            parentNode.Children.Add(childNode);
            _totalNodesProcessed++;
            
            // *** NEW RECURSIVE LOGIC ***
            // If the child node has a formula, trace its precedents as well.
            if (!string.IsNullOrEmpty(childNode.Formula))
            {
                // The recursive call that was missing.
                PopulatePrecedents(childNode, currentDepth + 1, maxDepth, visited);
            }
        }
        // ...
        ```
        *Note: The cycle detection logic (`if (visited.Contains(...))`) in `PopulatePrecedents` is now critical and must be robust.*

*   `[✓]` **1.2: Refine Node Creation and Display Logic**
    *   `[✓]` 1.2.1: In `TracerEngine.cs`, locate the `BuildDisplayText` method.
    *   `[✓]` 1.2.2: **The Problem:** The current display text for `SameSheet` nodes is just the cell address (e.g., "D13"), but the target UI shows more detail, especially for nested items. The root node in the target UI shows the cell address (e.g., "AP50"), not the full `'Sheet'!Address`.
    *   `[✓]` 1.2.3: **The Fix:** Modify the `switch` statement to produce the correct text for each context.

        ```csharp
        // In TracerEngine.cs -> BuildDisplayText()
        private string BuildDisplayText(Excel.Range cell, NodeType nodeType)
        {
            // ...
            switch (nodeType)
            {
                case NodeType.Root:
                    return address; // Target UI shows just the address for the root

                case NodeType.SameSheet:
                    // For children, showing the sheet name is clearer, even if it's the same.
                    return $"'{sheetName}'!{address}"; 

                case NodeType.DifferentSheet:
                    return $"'{sheetName}'!{address}";

                // ... other cases remain the same ...
            }
            // ...
        }
        ```

*   `[✓]` **1.3: Implement Proper Icon and Indentation Support**
    *   `[✓]` 1.3.1: In `TracerEngine.cs`, review the `DetermineNodeType` method. Ensure it correctly identifies `Root`, `SameSheet`, `DifferentSheet`, etc. The current implementation looks correct.
    *   `[✓]` 1.3.2: In `frmTrace.cs`, locate the `CreateImageList` method.
    *   `[✓]` 1.3.3: **The Problem:** You are creating icons programmatically with colored circles. The target UI uses specific, professional icons (home, sheet, grid).
    *   `[✓]` 1.3.4: **The Fix:**
        *   `[✓]` Add actual icon files (`.png`) to your project as **Embedded Resources**. These are located in the QuantBoost_Excel.Resources folder. Note they are 64x64 pixels and will likely need to be scaled down to a smaller size (like 16x16 or24x24).
            *   `home_64x64.png` (for Root)
            *   `sheet_64x64x.png` (for DifferentSheet)
            *   `cell_grid_64x64.png` (for SameSheet)
            *   `workbook_64x64.png` (for Workbook orExternalWorkbook)
            *   `error_64x64.png` (for Error)
            *   `loading_64x64.png` (for Loading)
            *   `tag_64x64.png` (for NamedRange)
        *   `[✓]` Modify `CreateImageList` to load these embedded resources instead of creating simple icons.

        ```csharp
        // In frmTrace.cs -> CreateImageList()
        private void CreateImageList()
        {
            try
            {
                ilIcons = new ImageList { /* ... */ };
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();

                // Load icons from embedded resources
                ilIcons.Images.Add("Root", new Bitmap(assembly.GetManifestResourceStream("QuantBoost_Excel.Resources.home_64x64.png")));
                ilIcons.Images.Add("Sheet", new Bitmap(assembly.GetManifestResourceStream("QuantBoost_Excel.Resources.sheet_64x64.png")));
                ilIcons.Images.Add("Cell", new Bitmap(assembly.GetManifestResourceStream("QuantBoost_Excel.Resources.cell_grid_64x64.png")));
                // ... etc. for other icons ...

                // ... rest of the method ...
            }
            // ...
        }
        ```
    *    `[✓]` 1.3.5: **Assign Settings Icon:**
        *   `[✓]` In frmTrace.cs, locate the CreateFooterContent method.
        *   `[✓]` Modify the btnSettings creation to load and assign the gear.png icon to its Image property and clear its Text property.
        
        ```csharp
        // Example for settings button
        btnSettings.Text = ""; // Remove text
        btnSettings.Image = new Bitmap(assembly.GetManifestResourceStream("QuantBoost_Excel.Resources.gear.png"));
        btnSettings.ImageAlign = ContentAlignment.MiddleCenter;
---

#### **Phase 2: Update the UI (`frmTrace.cs`) for Hierarchical Display**

This phase focuses on making the `TreeView` render the deep, indented structure correctly.

*   `[✓]` **2.1: Implement Indented Text Formatting**
    *   `[✓]` 2.1.1: In `frmTrace.cs`, locate the `FormatNodeText` method.
    *   `[✓]` 2.1.2: **The Problem:** The text is not indented based on its depth in the tree.
    *   `[✓]` 2.1.3: **The Fix:** Prepend the `displayText` with spaces based on the `dataNode.Depth` property.

        ```csharp
        // In frmTrace.cs -> FormatNodeText()
        private string FormatNodeText(TraceNode dataNode)
        {
            // ...
            try
            {
                // *** NEW INDENTATION LOGIC ***
                // Add 4 spaces of indentation for each level of depth (after the root).
                string indentation = new string(' ', (dataNode.Depth > 0 ? dataNode.Depth : 0) * 4);
                string displayText = indentation + (dataNode.DisplayText ?? "Unknown");
                // ... rest of the method ...
            }
            // ...
        }
        ```

*   `[✓]` **2.2: Refine `TreeView` Population Logic**
    *   `[✓]` 2.2.1: In `frmTrace.cs`, locate the `InitializeTrace` method.
    *   `[✓]` 2.2.2: **The Problem:** The root node is being expanded by default (`rootTreeNode.Expand()`), but its children might not be. The target UI shows the first level expanded.
    *   `[✓]` 2.2.3: **The Fix:** Ensure the `PopulateTreeViewNodes` method is called correctly and that the first-level children are also processed for expansion if they have children. The current `rootTreeNode.Expand()` should be sufficient if the recursive logic in the engine is working correctly. No code change is likely needed here, but it's a key area to verify during testing.

*   `[✓]` **2.3: Update `ImageKey` Logic**
    *   `[✓]` 2.3.1: In `frmTrace.cs`, locate the `GetImageKeyForNodeType` method.
    *   `[✓]` 2.3.2: **The Fix:** Ensure the keys returned by this method exactly match the keys you used when adding the new icons to the `ImageList` in step 1.3.4 (e.g., "Root", "Sheet", "Cell"). The current implementation looks correct, but it depends on the new icon keys.

---


### **Phase 1: Building the WPF View (`TraceView.xaml`)**

This is where we define the entire user interface using declarative XAML markup. We will not use the designer. Open `TraceView.xaml` and replace its content with the following.

**`TraceView.xaml`**
```xml
<UserControl x:Class="QuantBoost_Excel.Features.ExcelTrace.UI.TraceView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:localModel="clr-namespace:QuantBoost_Excel.Features.ExcelTrace.Model"
             mc:Ignorable="d" 
             d:DesignHeight="480" d:DesignWidth="700"
             FontFamily="Segoe UI" FontSize="14" Background="White">

    <!--
    REASON: Resources are defined here to centralize all styling. This makes theme changes
    trivial and ensures a consistent look and feel across the entire control.
    -->
    <UserControl.Resources>
        <!-- Define our blue theme colors as resources for easy reuse -->
        <SolidColorBrush x:Key="ThemePrimaryBrush" Color="#0078D7"/>
        <SolidColorBrush x:Key="ThemeHoverBrush" Color="#4192D9"/>
        <SolidColorBrush x:Key="ThemeSelectedBackgroundBrush" Color="#C7E0F4"/>
        <SolidColorBrush x:Key="ThemeBorderBrush" Color="#E0E0E0"/>
        <SolidColorBrush x:Key="ThemeHeaderBackgroundBrush" Color="#F0F5FA"/>

        <!--
        STYLE 1: Custom ToggleButton style for the TreeView expand/collapse (+/- icons)
        This replaces the default arrow with our custom path-based icons.
        -->
        <Style x:Key="ExpandCollapseToggleStyle" TargetType="ToggleButton">
            <Setter Property="Focusable" Value="False"/>
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Border Background="Transparent" Width="16" Height="16">
                            <Path x:Name="ExpandPath" Stroke="{StaticResource ThemePrimaryBrush}" StrokeThickness="1.5"
                                  HorizontalAlignment="Center" VerticalAlignment="Center">
                                <Path.Data>
                                    <!-- This defines the '+' symbol -->
                                    <PathGeometry Figures="M0,8 H16 M8,0 V16"/>
                                </Path.Data>
                            </Path>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <!-- When expanded, change the path data to a '-' symbol -->
                                <Setter TargetName="ExpandPath" Property="Data" Value="M0,8 H16" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!--
        STYLE 2: Style for the TreeViewItem itself.
        This handles selection color and applies our custom expand/collapse button.
        -->
        <Style TargetType="TreeViewItem">
            <Setter Property="Padding" Value="3"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TreeViewItem">
                        <StackPanel>
                            <Border Name="Bd" Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Padding="{TemplateBinding Padding}">
                                <Grid Margin="{Binding Converter={StaticResource LengthConverter}, RelativeSource={RelativeSource TemplatedParent}}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="19" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <ToggleButton x:Name="Expander" Style="{StaticResource ExpandCollapseToggleStyle}" 
                                                  IsChecked="{Binding Path=IsExpanded, RelativeSource={RelativeSource TemplatedParent}}" 
                                                  ClickMode="Press"/>
                                    <ContentPresenter Grid.Column="1" x:Name="PART_Header" 
                                                      ContentSource="Header" HorizontalAlignment="Left" />
                                </Grid>
                            </Border>
                            <ItemsPresenter x:Name="ItemsHost" />
                        </StackPanel>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsExpanded" Value="false">
                                <Setter TargetName="ItemsHost" Property="Visibility" Value="Collapsed"/>
                            </Trigger>
                            <Trigger Property="HasItems" Value="false">
                                <Setter TargetName="Expander" Property="Visibility" Value="Hidden"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="true">
                                <Setter TargetName="Bd" Property="Background" Value="{StaticResource ThemeSelectedBackgroundBrush}" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <!-- A converter is needed to calculate the indentation margin for nested items -->
            <Style.Resources>
                <localUI:LeftMarginMultiplier x:Key="LengthConverter" Length="19" />
            </Style.Resources>
        </Style>
        
        <!--
        HIERARCHICAL DATA TEMPLATE: This is the core of the UI.
        It tells the TreeView how to render a single TraceNode and where to find its children.
        -->
        <HierarchicalDataTemplate DataType="{x:Type localModel:TraceNode}" ItemsSource="{Binding Children}">
            <Grid Width="650"> <!-- Set a width to ensure columns align -->
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" /> <!-- Precedent column takes up remaining space -->
                    <ColumnDefinition Width="Auto" MinWidth="120" /> <!-- Value column sizes to content -->
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="{Binding DisplayText}" VerticalAlignment="Center"/>
                <TextBlock Grid.Column="1" Text="{Binding DisplayValue}" VerticalAlignment="Center" HorizontalAlignment="Right"/>
            </Grid>
        </HierarchicalDataTemplate>
        
    </UserControl.Resources>

    <!--
    MAIN LAYOUT: DockPanel is perfect for header/footer/content layouts.
    -->
    <DockPanel>
        <!-- Top: Formula Bar -->
        <Border DockPanel.Dock="Top" Background="{StaticResource ThemeHeaderBackgroundBrush}" Padding="8" BorderBrush="{StaticResource ThemeBorderBrush}" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <TextBox Grid.Column="0" Name="FormulaTextBox" IsReadOnly="true" VerticalContentAlignment="Center" FontSize="13" FontFamily="Consolas"/>
                <Button Grid.Column="1" Name="GoToButton" Content="→" Width="30" Margin="5,0,0,0" ToolTip="Go to first precedent" Background="{StaticResource ThemePrimaryBrush}" Foreground="White" BorderThickness="0" Click="GoToButton_Click"/>
            </Grid>
        </Border>

        <!-- Bottom: Button Bar -->
        <Border DockPanel.Dock="Bottom" Background="{StaticResource ThemeHeaderBackgroundBrush}" Padding="8" BorderBrush="{StaticResource ThemeBorderBrush}" BorderThickness="0,1,0,0">
            <Grid>
                <Button Name="SettingsButton" Content="Settings" Width="80" HorizontalAlignment="Left" Background="Transparent" Foreground="{StaticResource ThemePrimaryBrush}" BorderThickness="0" Click="SettingsButton_Click"/>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Name="OkButton" Content="OK" Width="80" IsDefault="True" Background="{StaticResource ThemePrimaryBrush}" Foreground="White" BorderThickness="0" Click="OkButton_Click"/>
                    <Button Name="CancelButton" Content="Cancel" Width="80" IsCancel="True" Margin="5,0,0,0" Background="#DDDDDD" Foreground="#333333" BorderThickness="0" Click="CancelButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Center: The TreeView fills the remaining space -->
        <TreeView Name="PrecedentTreeView" BorderThickness="0" Padding="5"
                  MouseDoubleClick="PrecedentTreeView_MouseDoubleClick"/>
    </DockPanel>
</UserControl>
```

---

### **Phase 2: The Logic Behind the View (`TraceView.xaml.cs`)**

This C# file will be lean. Its main jobs are to receive data, handle user clicks, and notify the WinForms host that an action occurred.

**`TraceView.xaml.cs`**
```csharp
using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using QuantBoost_Excel.Features.ExcelTrace.Model;

namespace QuantBoost_Excel.Features.ExcelTrace.UI
{
    public partial class TraceView : UserControl
    {
        // REASON: RoutedEvents are the WPF way for a control to notify its parent about an action
        // without creating a hard dependency. This keeps our UI component decoupled and reusable.
        public static readonly RoutedEvent OkEvent = EventManager.RegisterRoutedEvent("Ok", RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(TraceView));
        public static readonly RoutedEvent CancelEvent = EventManager.RegisterRoutedEvent("Cancel", RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(TraceView));
        public static readonly RoutedEvent NavigateToNodeEvent = EventManager.RegisterRoutedEvent("NavigateToNode", RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(TraceView));

        public event RoutedEventHandler Ok { add { AddHandler(OkEvent, value); } remove { RemoveHandler(OkEvent, value); } }
        public event RoutedEventHandler Cancel { add { AddHandler(CancelEvent, value); } remove { RemoveHandler(CancelEvent, value); } }
        public event RoutedEventHandler NavigateToNode { add { AddHandler(NavigateToNodeEvent, value); } remove { RemoveHandler(NavigateToNodeEvent, value); } }
        
        private TraceNode _rootNode;

        public TraceView()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Public method for the host to provide the trace data.
        /// </summary>
        public void SetTraceData(TraceNode rootNode)
        {
            _rootNode = rootNode;
            FormulaTextBox.Text = rootNode.Formula ?? (rootNode.DisplayValue ?? "[No Formula]");
            
            // Set the IsExpanded flag on the root before binding
            if (_rootNode != null) _rootNode.IsExpanded = true;
            
            // This is the magic of WPF data binding. We just provide the data source.
            // The HierarchicalDataTemplate in the XAML handles all rendering and nesting.
            PrecedentTreeView.ItemsSource = new[] { _rootNode };
        }

        // --- Event Handlers for UI elements ---

        private void OkButton_Click(object sender, RoutedEventArgs e) => RaiseEvent(new RoutedEventArgs(OkEvent, this));
        private void CancelButton_Click(object sender, RoutedEventArgs e) => RaiseEvent(new RoutedEventArgs(CancelEvent, this));
        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // Logic to show settings menu would go here. For now, it does nothing.
        }

        private void GoToButton_Click(object sender, RoutedEventArgs e)
        {
            if (_rootNode?.Children.Any() == true)
            {
                // Raise the navigation event with the first child node as the data
                RaiseEvent(new RoutedEventArgs(NavigateToNodeEvent, _rootNode.Children.First()));
            }
        }

        private void PrecedentTreeView_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (PrecedentTreeView.SelectedItem is TraceNode selectedNode && !selectedNode.HasChildren)
            {
                // Raise the navigation event for the double-clicked leaf node
                RaiseEvent(new RoutedEventArgs(NavigateToNodeEvent, selectedNode));
                e.Handled = true;
            }
        }
    }
}
```
**`LeftMarginMultiplier.cs` (Required Converter)**
You will need to add this small helper class to the same UI folder. It's necessary for calculating the indentation of `TreeViewItem`s.
```csharp
using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;

namespace QuantBoost_Excel.Features.ExcelTrace.UI
{
    /// <summary>
    /// Converts the nesting level of a TreeViewItem into a left margin.
    /// </summary>
    public class LeftMarginMultiplier : IValueConverter
    {
        public double Length { get; set; }

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var item = value as TreeViewItem;
            if (item == null)
                return new Thickness(0);

            return new Thickness(Length * GetDepth(item), 0, 0, 0);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new System.NotImplementedException();
        }

        private int GetDepth(TreeViewItem item)
        {
            int depth = 0;
            while ((item = ItemsControl.ItemsControlFromItemContainer(item) as TreeViewItem) != null)
            {
                depth++;
            }
            return depth;
        }
    }
}
```

---

### **Phase 3: Refactoring the WinForms Host (`frmTrace.cs`)**

This class becomes dramatically simpler. It is now just a shell that hosts our powerful WPF control. All the complex drawing, layout, and event handling code is deleted.

**`frmTrace.cs` (The New, Simplified Version)**
```csharp
using System;
using System.Windows.Forms;
using System.Windows.Forms.Integration; // The bridge
using QuantBoost_Excel.Features.ExcelTrace.Model;
using QuantBoost_Excel.Features.ExcelTrace.Logic; // For NavigateToNode

namespace QuantBoost_Excel.Features.ExcelTrace.UI
{
    public partial class frmTrace : Form
    {
        private TraceView _wpfView; // The instance of our WPF control

        public frmTrace()
        {
            // Standard WinForms Form setup
            this.Text = "Excel Trace";
            this.ClientSize = new System.Drawing.Size(700, 480);
            this.MinimumSize = new System.Drawing.Size(500, 300);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.SizableToolWindow; // Use a standard resizable border

            InitializeWpfHost();
        }

        /// <summary>
        /// Creates the ElementHost, instantiates the WPF view, and connects them.
        /// </summary>
        private void InitializeWpfHost()
        {
            // 1. Create the host control that will contain our WPF UI.
            var elementHost = new ElementHost
            {
                Dock = DockStyle.Fill
            };

            // 2. Create an instance of our powerful WPF view.
            _wpfView = new TraceView();

            // 3. Subscribe to the events raised by the WPF view.
            // This allows the host to react to button clicks inside the WPF control.
            _wpfView.Ok += (s, e) => { this.DialogResult = DialogResult.OK; this.Close(); };
            _wpfView.Cancel += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };
            _wpfView.NavigateToNode += (s, e) => 
            {
                if (e.OriginalSource is TraceNode node)
                {
                    // Call your existing, robust navigation logic.
                    // NOTE: You would need to make NavigateToNode public or move it to a helper class.
                    // For this example, we assume it's accessible.
                    // NavigateToNode(node); 
                    MessageBox.Show($"Request to navigate to: {node.DisplayText}"); // Placeholder
                }
            };

            // 4. Place the WPF view inside the host.
            elementHost.Child = _wpfView;
            
            // 5. Add the host to the form's controls.
            this.Controls.Add(elementHost);
        }

        /// <summary>
        /// Public entry point for the tracer engine to pass data to the UI.
        /// </summary>
        public void InitializeTrace(TraceNode rootNode)
        {
            if (rootNode == null)
                throw new ArgumentNullException(nameof(rootNode));

            // Simply pass the data along to the WPF view.
            _wpfView.SetTraceData(rootNode);
        }

        // NOTE: The vast majority of the old frmTrace.cs file is now deleted.
        // No more LvPrecedents_DrawItem, CreateListView, CreateHeaderContent, etc.
        // The NavigateToNode method logic would need to be preserved here or in a separate helper class.
    }
}
```

### **Summary of Benefits**

By following this plan, you will have:

1.  **Separation of Concerns:** A clean split between UI definition (XAML) and application logic (C#).
2.  **Maintainability:** The UI is now incredibly easy to change. Want to add a column? Edit two lines in the XAML `Grid`. Change a color? Edit one line in the `Resources`.
3.  **Leveraged the Framework:** You are no longer fighting WinForms to create a modern UI. You are using WPF's powerful data binding, templating, and layout systems as they were intended.
4.  **AI-Friendly:** This XAML-based structure is perfect for your coding assistant to parse, understand, and generate, vastly accelerating future development.
5.  **Production Ready:** This architecture using `Styles`, `Resources`, and `RoutedEvents` is robust, scalable, and follows professional WPF development patterns.

## 1. **Keyboard Right Arrow (Expansion) Not Working**

The issue is in TraceView.xaml.cs. The `IsExpanded` property is being set but not triggering UI updates because `TraceNode` doesn't implement `INotifyPropertyChanged`.

**Fix in TraceNode.cs:**
```csharp
using System.ComponentModel;
using System.Runtime.CompilerServices;

public class TraceNode : INotifyPropertyChanged
{
    private bool _isExpanded;
    
    public bool IsExpanded 
    { 
        get => _isExpanded;
        set
        {
            if (_isExpanded != value)
            {
                _isExpanded = value;
                OnPropertyChanged();
            }
        }
    }

    public event PropertyChangedEventHandler PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
    
    // Update constructor
    public TraceNode()
    {
        Children = new List<TraceNode>();
        ChildrenLoaded = false;
        HasError = false;
        IsCircularReference = false;
        Depth = 0;
        _isExpanded = false; // Use backing field
        FormulaStartIndex = -1;
        FormulaLength = 0;
        GroupIndex = -1;
    }
}
```

## 2. **Hotkey (Ctrl+Shift+[) Not Working**

The hotkey registration is missing. Add this to ThisAddIn.cs:

```csharp
private void ThisAddIn_Startup(object sender, System.EventArgs e)
{
    // ... existing code ...
    
    // Register the hotkey for Excel Trace
    try
    {
        Application.OnKey("^+[", "QuantBoost_Excel.ThisAddIn.LaunchTraceForActiveCell");
        ErrorHandlingService.LogException(null, "Excel Trace hotkey registered successfully");
    }
    catch (Exception ex)
    {
        ErrorHandlingService.LogException(ex, "Failed to register Excel Trace hotkey");
    }
}

private void ThisAddIn_Shutdown(object sender, System.EventArgs e)
{
    // Unregister the hotkey
    try
    {
        Application.OnKey("^+[");
    }
    catch { }
    
    // ... existing code ...
}
```

## 3. **Font Size Inconsistency**

In TraceView.xaml, the Value column has `FontSize="12"` while the rest uses `FontSize="14"`. Fix:

```xml
<!-- Around line 165 -->
<TextBlock Grid.Column="2" Text="{Binding DisplayValue}"
          HorizontalAlignment="Right" VerticalAlignment="Center" Margin="4,0"
          FontFamily="Consolas" FontSize="14"> <!-- Changed from 12 to 14 -->
```

## 4. **GridSplitter Not Working for Data**

The issue is that the data template has its own Grid with fixed column widths. Update TraceView.xaml:

```xml
<!-- Replace the HierarchicalDataTemplate Grid section (around line 95) -->
<HierarchicalDataTemplate DataType="{x:Type localModel:TraceNode}" ItemsSource="{Binding Children}">
    <Grid>
        <Grid.ColumnDefinitions>
            <!-- Bind to the parent column definitions -->
            <ColumnDefinition Width="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=PrecedentColumnDef.Width}"/>
            <ColumnDefinition Width="5"/>
            <ColumnDefinition Width="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=ValueColumnDef.Width}"/>
        </Grid.ColumnDefinitions>
        <!-- ... rest of template ... -->
    </Grid>
</HierarchicalDataTemplate>
```

## 5. **Remove Wrap Formula Button**

Remove from TraceView.xaml:
```xml
<!-- Remove this ToggleButton and its event handlers -->
<!-- <ToggleButton Name="WrapFormulaButton" Grid.Column="1" Content="↩" Width="30" Margin="5,0,0,0"
              Background="{StaticResource ThemePrimaryBrush}" Foreground="White" BorderThickness="0"
              Checked="WrapFormulaButton_Checked" Unchecked="WrapFormulaButton_Unchecked"/> -->
```

And update the RichTextBox to always wrap:
```xml
<RichTextBox Name="FormulaRichTextBox" IsReadOnly="true" 
             VerticalScrollBarVisibility="Auto"
             HorizontalScrollBarVisibility="Disabled"
             FontFamily="Cascadia Code, Consolas" FontSize="12" 
             BorderThickness="0" Padding="4,3"
             Background="White" SelectionBrush="{StaticResource ThemePrimaryBrush}">
    <RichTextBox.Document>
        <FlowDocument>
            <Paragraph TextAlignment="Left">
                <Paragraph.Style>
                    <Style TargetType="Paragraph">
                        <Setter Property="TextWrapping" Value="Wrap"/>
                        <Setter Property="Margin" Value="0"/>
                    </Style>
                </Paragraph.Style>
            </Paragraph>
        </FlowDocument>
    </RichTextBox.Document>
</RichTextBox>
```

## 6. **Auto-Expand First Level**

In TraceView.xaml.cs, modify `SetTraceData`:
```csharp
public void SetTraceData(TraceNode rootNode)
{
    _rootNode = rootNode ?? throw new ArgumentNullException(nameof(rootNode));
    _rootNode.IsExpanded = true;
    
    // Auto-expand first level children
    foreach (var child in _rootNode.Children)
    {
        child.IsExpanded = true;
    }

    this.DataContext = _rootNode;
    PrecedentTreeView.ItemsSource = new[] { _rootNode };
    // ... rest of method
}
```

## 7. **Prevent Multiple Windows**

In ThisAddIn.cs, add a static field and modify `LaunchTraceForActiveCell`:
```csharp
private static frmTrace _activeTraceForm = null;

public void LaunchTraceForActiveCell()
{
    // ... existing validation code ...
    
    try
    {
        // Check if a trace window is already open
        if (_activeTraceForm != null && !_activeTraceForm.IsDisposed)
        {
            _activeTraceForm.Activate();
            _activeTraceForm.WindowState = FormWindowState.Normal;
            return;
        }
        
        Excel.Range activeCell = this.Application.ActiveCell;
        if (activeCell != null && activeCell.Count == 1)
        {
            var tracer = new TracerEngine();
            var rootNode = tracer.BuildPrecedentTree(activeCell);

            _activeTraceForm = new frmTrace();
            _activeTraceForm.InitializeTrace(rootNode);
            _activeTraceForm.FormClosed += (s, e) => { _activeTraceForm = null; };
            _activeTraceForm.Show();
        }
        // ... rest of method
    }
    // ... exception handling
}
```

## 8. **Custom Window Icon**

In frmTrace.cs constructor, add:
```csharp
public frmTrace()
{
    // ... existing code ...
    
    // Set custom icon
    try
    {
        // Option 1: Use embedded resource
        this.Icon = Properties.Resources.ExcelTraceIcon; // Add icon to Resources
        
        // Option 2: Use Excel icon
        // this.Icon = Icon.ExtractAssociatedIcon(Application.ExecutablePath);
    }
    catch
    {
        // Fallback to default if icon loading fails
    }
    
    // ... rest of constructor
}
```

## 9. **Additional Issues Found**

### Issue: Memory Leak with Event Handlers
The WPF view subscribes to events but never unsubscribes. Add to frmTrace.cs:

```csharp
protected override void Dispose(bool disposing)
{
    try
    {
        if (disposing)
        {
            // Unsubscribe from WPF events
            if (_wpfView != null)
            {
                _wpfView.Ok -= (s, e) => { };
                _wpfView.Cancel -= (s, e) => { };
                _wpfView.NavigateToNode -= (s, e) => { };
                _wpfView.Settings -= (s, e) => { };
            }
            
            // Dispose managed resources
            _settingsMenu?.Dispose();
            _tracerEngine = null;
            _rootNode = null;
        }
    }
    catch (Exception ex)
    {
        ErrorHandlingService.LogException(ex, "Error disposing frmTrace");
    }
    finally
    {
        base.Dispose(disposing);
    }
}
```

### Issue: TreeView Performance
Add virtualization to improve performance with large trees in TraceView.xaml:

```xml
<TreeView Name="PrecedentTreeView" Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3"
          BorderThickness="0" Padding="5"
          VirtualizingStackPanel.IsVirtualizing="True"
          VirtualizingStackPanel.VirtualizationMode="Recycling"
          ScrollViewer.CanContentScroll="True"
          ... >
```

These fixes should resolve all the issues you've identified and improve the overall stability and user experience of the Excel Trace module.

## 1. **Root Cause of Array Expansion Issue**

The array expansion is partially implemented but has several issues:

### **Issue 1: Missing Visual Update After Array Expansion**
In TraceView.xaml.cs, the `ExpandArrayNode` method doesn't properly refresh the TreeView's item containers:

```csharp
// Current implementation in ExpandArrayNode
Dispatcher.BeginInvoke(new Action(() =>
{
    PrecedentTreeView.UpdateLayout();
    System.Diagnostics.Debug.WriteLine($"Successfully expanded array node: {arrayNode.DisplayText} with {arrayNode.Children.Count} elements");
}), System.Windows.Threading.DispatcherPriority.Background);
```

### **Issue 2: Array Detection Logic**
The `TracerEngine` creates range nodes with `<array>` display value but doesn't properly populate the `RawValue` with the actual array data:

```csharp
// In CreateRangeNode - it sets DisplayValue but not RawValue
node.DisplayValue = "<array>"; // But RawValue is never set to the actual array
```

### **Fix for Array Expansion:**

````csharp
// ...existing code...

private TraceNode CreateRangeNode(Excel.Range range, int depth, TraceNode parentNode = null)
{
    var node = new TraceNode
    {
        NodeType = NodeType.Range,
        Depth = depth
    };

    try
    {
        // ...existing code...

        // CRITICAL FIX: Actually get the array values from the range
        if (range.Cells.Count > 1)
        {
            try
            {
                // Get the array of values from the range
                var values = range.Value2;
                if (values != null && values.GetType().IsArray)
                {
                    node.RawValue = values;
                    node.DisplayValue = "<array>";
                }
                else
                {
                    // Fallback for single-area ranges
                    node.DisplayValue = $"[{range.Cells.Count} cells]";
                }
            }
            catch
            {
                node.DisplayValue = "<array>";
            }
        }

        // ...existing code...
    }
    catch (Exception ex)
    {
        // ...existing code...
    }

    return node;
}
````

````csharp
// ...existing code...

private void ExpandArrayNode(TraceNode arrayNode)
{
    try
    {
        // Use the TracerEngine to populate array children
        var tracerEngine = new QuantBoost_Excel.Features.ExcelTrace.Logic.TracerEngine();
        bool success = tracerEngine.PopulateNodeChildren(arrayNode);

        if (success)
        {
            // Expand the node in the UI
            arrayNode.IsExpanded = true;

            // CRITICAL FIX: Force TreeView to regenerate containers
            Dispatcher.BeginInvoke(new Action(() =>
            {
                // Find the TreeViewItem for this node
                var treeViewItem = FindTreeViewItem(PrecedentTreeView, arrayNode);
                if (treeViewItem != null)
                {
                    treeViewItem.IsExpanded = true;
                    
                    // Force regeneration of child containers
                    treeViewItem.UpdateLayout();
                    var generator = treeViewItem.ItemContainerGenerator;
                    generator.StatusChanged += (s, e) =>
                    {
                        if (generator.Status == GeneratorStatus.ContainersGenerated)
                        {
                            System.Diagnostics.Debug.WriteLine($"Child containers generated for array node: {arrayNode.DisplayText}");
                        }
                    };
                }
                
                PrecedentTreeView.UpdateLayout();
                System.Diagnostics.Debug.WriteLine($"Successfully expanded array node: {arrayNode.DisplayText} with {arrayNode.Children.Count} elements");
            }), System.Windows.Threading.DispatcherPriority.Loaded);
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"Failed to expand array node: {arrayNode.DisplayText}");
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"Error expanding array node: {ex.Message}");
    }
}

// Add helper method to find TreeViewItem
private TreeViewItem FindTreeViewItem(ItemsControl container, object item)
{
    if (container == null || item == null)
        return null;

    if (container.DataContext == item)
        return container as TreeViewItem;

    // Search the visual tree
    for (int i = 0; i < container.Items.Count; i++)
    {
        var child = container.ItemContainerGenerator.ContainerFromIndex(i) as TreeViewItem;
        if (child != null)
        {
            var result = FindTreeViewItem(child, item);
            if (result != null)
                return result;
        }
    }
    return null;
}
````

## 2. **UltraThink Analysis - Best Practices and Improvements**

### **Critical Issues:**

#### **1. Memory Leaks from Event Handlers**
The current implementation uses anonymous lambdas that can't be unsubscribed:

````csharp
// ...existing code...

private EventHandler<RoutedEventArgs> _okHandler;
private EventHandler<RoutedEventArgs> _cancelHandler;
private EventHandler<RoutedEventArgs> _navigateHandler;
private EventHandler<RoutedEventArgs> _settingsHandler;

private void InitializeWpfHost()
{
    // ...existing code...
    
    // Store handlers as fields for proper cleanup
    _okHandler = (s, e) => { this.DialogResult = DialogResult.OK; this.Close(); };
    _cancelHandler = (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };
    _navigateHandler = (s, e) =>
    {
        if (e.OriginalSource is TraceNode node)
            NavigateToNode(node);
    };
    _settingsHandler = (s, e) =>
    {
        if (e.OriginalSource is System.Windows.Controls.Button settingsButton)
            ShowSettingsMenu(settingsButton);
    };
    
    _wpfView.Ok += _okHandler;
    _wpfView.Cancel += _cancelHandler;
    _wpfView.NavigateToNode += _navigateHandler;
    _wpfView.Settings += _settingsHandler;
    
    // ...existing code...
}

protected override void Dispose(bool disposing)
{
    try
    {
        if (disposing)
        {
            // Properly unsubscribe events
            if (_wpfView != null)
            {
                _wpfView.Ok -= _okHandler;
                _wpfView.Cancel -= _cancelHandler;
                _wpfView.NavigateToNode -= _navigateHandler;
                _wpfView.Settings -= _settingsHandler;
            }
            
            // ...existing code...
        }
    }
    catch (Exception ex)
    {
        ErrorHandlingService.LogException(ex, "Error disposing frmTrace");
    }
    finally
    {
        base.Dispose(disposing);
    }
}
````

#### **2. Missing TraceSettings Class**
The code references `TraceSettings` but it's not defined. Add this:

````csharp
using System;

namespace QuantBoost_Excel.Features.ExcelTrace.Model
{
    /// <summary>
    /// Global settings for the Excel Trace feature.
    /// </summary>
    public static class TraceSettings
    {
        private static bool _highlightNavigatedCells = true;
        private static bool _openLinkedWorkbooks = true;
        private static bool _unhideRowsAndColumns = true;
        private static int _maxTraceDepth = 10;

        /// <summary>
        /// Gets or sets whether to highlight cells when navigating to them.
        /// </summary>
        public static bool HighlightNavigatedCells
        {
            get => _highlightNavigatedCells;
            set => _highlightNavigatedCells = value;
        }

        /// <summary>
        /// Gets or sets whether to automatically open linked workbooks.
        /// </summary>
        public static bool OpenLinkedWorkbooks
        {
            get => _openLinkedWorkbooks;
            set => _openLinkedWorkbooks = value;
        }

        /// <summary>
        /// Gets or sets whether to unhide rows and columns when navigating.
        /// </summary>
        public static bool UnhideRowsAndColumns
        {
            get => _unhideRowsAndColumns;
            set => _unhideRowsAndColumns = value;
        }

        /// <summary>
        /// Gets or sets the maximum depth for tracing precedents.
        /// </summary>
        public static int MaxTraceDepth
        {
            get => _maxTraceDepth;
            set => _maxTraceDepth = Math.Max(1, Math.Min(value, 20)); // Clamp between 1 and 20
        }
    }
}
````

#### **3. Improve Array Element Creation**
The current implementation doesn't handle 2D arrays properly:

````csharp
// ...existing code...

private bool PopulateArrayChildren(TraceNode arrayNode)
{
    try
    {
        if (!(arrayNode.RawValue is Array array))
        {
            ErrorHandlingService.LogException(null, $"Array node {arrayNode.DisplayText} does not contain a valid array");
            return false;
        }

        ErrorHandlingService.LogException(null, $"Expanding array node {arrayNode.DisplayText} with {array.Length} elements");

        // Clear any existing children
        arrayNode.Children.Clear();

        // Handle 2D arrays (most common in Excel)
        if (array.Rank == 2)
        {
            int rows = array.GetLength(0);
            int cols = array.GetLength(1);
            
            for (int row = 1; row <= rows; row++)
            {
                for (int col = 1; col <= cols; col++)
                {
                    var element = array.GetValue(row, col);
                    var childNode = CreateArrayElementNode(element, row, col, arrayNode);
                    arrayNode.Children.Add(childNode);
                }
            }
        }
        else
        {
            // Handle 1D arrays
            for (int i = 0; i < array.Length; i++)
            {
                var element = array.GetValue(i);
                var childNode = CreateArrayElementNode(element, i + 1, 1, arrayNode);
                arrayNode.Children.Add(childNode);
            }
        }

        arrayNode.ChildrenLoaded = true;
        ErrorHandlingService.LogException(null, $"Successfully expanded array node with {arrayNode.Children.Count} elements");
        return true;
    }
    catch (Exception ex)
    {
        ErrorHandlingService.LogException(ex, $"Error expanding array node: {arrayNode.DisplayText}");
        arrayNode.HasError = true;
        arrayNode.ErrorDetails = $"Failed to expand array: {ex.Message}";
        return false;
    }
}

// Update CreateArrayElementNode to handle row/col
private TraceNode CreateArrayElementNode(object element, int row, int col, TraceNode parentNode)
{
    var elementNode = new TraceNode
    {
        DisplayText = col == 1 ? $"[{row}]" : $"[{row},{col}]",
        FullAddress = $"{parentNode.FullAddress}[{row},{col}]",
        RawValue = element,
        NodeType = NodeType.ArrayElement,
        Depth = parentNode.Depth + 1,
        WorkbookName = parentNode.WorkbookName,
        WorksheetName = parentNode.WorksheetName,
        CellAddress = $"{parentNode.CellAddress}[{row},{col}]",
        ChildrenLoaded = true
    };

    // ...existing code...
    
    return elementNode;
}

### **Project Blueprint: The QuantBoost Shortcut Manager**

This implementation will be divided into seven core tasks, moving from the foundational data layer up to the user interface and final integration. Each task is broken into actionable sub-tasks with complete code examples and detailed commentary.

---

### **Task 1: Project Setup and Foundational MVVM Classes**

**Objective:** Establish the project structure and create the core helper classes required for a clean MVVM architecture. This prevents code duplication and enforces a consistent design pattern.

*   **✅ 1.1: Add Project References**
    *   **Action:** In your VSTO project, ensure you have references to the following WPF assemblies. Right-click **References** -> **Add Reference...** -> **Assemblies** -> **Framework**.
        *   `PresentationCore`
        *   `PresentationFramework`
        *   `WindowsBase`
        *   `System.Xaml`
    *   **Action:** Install the `Newtonsoft.Json` NuGet package. It is the gold standard for JSON serialization in .NET Framework.
        *   **Command:** `Install-Package Newtonsoft.Json`

*   **✅ 1.2: Create Core MVVM Helper Classes**
    *   **Commentary:** These classes are the bedrock of MVVM. `ViewModelBase` provides the `INotifyPropertyChanged` implementation, allowing the UI to automatically update when data changes. `RelayCommand` provides a generic implementation of `ICommand`, connecting UI actions (like button clicks) to methods in our ViewModel.
    *   **Action:** Create a new folder named `Mvvm` in your project. Add the following two classes.

    *   **Code: `ViewModelBase.cs`**
        ```csharp
        using System.ComponentModel;
        using System.Runtime.CompilerServices;

        namespace QuantBoost_Excel.Mvvm
        {
            /// <summary>
            /// A base class for objects that require property change notification.
            /// This is a core component of the MVVM pattern.
            /// </summary>
            public abstract class ViewModelBase : INotifyPropertyChanged
            {
                public event PropertyChangedEventHandler PropertyChanged;

                protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
                {
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
                }
            }
        }
        ```

    *   **Code: `RelayCommand.cs`**
        ```csharp
        using System;
        using System.Windows.Input;

        namespace QuantBoost_Excel.Mvvm
        {
            /// <summary>
            /// A generic ICommand implementation to connect UI actions to ViewModel methods.
            /// </summary>
            public class RelayCommand : ICommand
            {
                private readonly Action<object> _execute;
                private readonly Predicate<object> _canExecute;

                public RelayCommand(Action<object> execute, Predicate<object> canExecute = null)
                {
                    _execute = execute ?? throw new ArgumentNullException(nameof(execute));
                    _canExecute = canExecute;
                }

                public event EventHandler CanExecuteChanged
                {
                    add => CommandManager.RequerySuggested += value;
                    remove => CommandManager.RequerySuggested -= value;
                }

                public bool CanExecute(object parameter) => _canExecute?.Invoke(parameter) ?? true;
                public void Execute(object parameter) => _execute(parameter);
            }
        }
        ```

---

### **Task 2: The Data Layer - Models and Configuration Service**

**Objective:** Define the data structure for a shortcut and create a dedicated service to manage loading and saving the user's configuration. This isolates file I/O from the rest of the application.

*   **✅ 2.1: Create the `ShortcutModel`**
    *   **Commentary:** This class represents a single shortcut (one row in the manager). It inherits from `ViewModelBase` to support data binding. It contains properties for its state (enabled, keys), its definition (description, group), its default values (for reset functionality), and the internal method name it triggers. The `ToOnKeyString()` method is critical for translating the model into the format `HotkeyService` understands.
    *   **Action:** Create a folder `Models`. Add the class `ShortcutModel.cs`.

    *   **Code: `ShortcutModel.cs`**
        ```csharp
        using System.Windows.Input;
        using Newtonsoft.Json;
        using QuantBoost_Excel.Mvvm;

        namespace QuantBoost_Excel.Models
        {
            public class ShortcutModel : ViewModelBase
            {
                private bool _isEnabled;
                private ModifierKeys _modifiers;
                private Key _key;

                // Internal properties
                public string MethodName { get; set; }

                // UI & Definition Properties
                public string Group { get; set; }
                public string ActionDescription { get; set; }
                public int UtilityScore { get; set; }

                // Data-bindable state properties
                public bool IsEnabled { get => _isEnabled; set { _isEnabled = value; OnPropertyChanged(); } }
                public ModifierKeys Modifiers { get => _modifiers; set { _modifiers = value; OnPropertyChanged(); } }
                public Key Key { get => _key; set { _key = value; OnPropertyChanged(); } }

                // Properties for "Reset" functionality (not saved in user config)
                [JsonIgnore] public ModifierKeys DefaultModifiers { get; set; }
                [JsonIgnore] public Key DefaultKey { get; set; }
                [JsonIgnore] public bool DefaultIsEnabled { get; set; }

                /// <summary>
                /// Generates the string representation for Application.OnKey (e.g., "^+%n").
                /// This is a robust implementation handling special keys.
                /// </summary>
                [JsonIgnore]
                public string OnKeyString
                {
                    get
                    {
                        string onKeyString = "";
                        if (Modifiers.HasFlag(ModifierKeys.Control)) onKeyString += "^";
                        if (Modifiers.HasFlag(ModifierKeys.Shift)) onKeyString += "+";
                        if (Modifiers.HasFlag(ModifierKeys.Alt)) onKeyString += "%";

                        switch (Key)
                        {
                            case Key.Left: onKeyString += "{LEFT}"; break;
                            case Key.Right: onKeyString += "{RIGHT}"; break;
                            case Key.Up: onKeyString += "{UP}"; break;
                            case Key.Down: onKeyString += "{DOWN}"; break;
                            case Key.Home: onKeyString += "{HOME}"; break;
                            case Key.End: onKeyString += "{END}"; break;
                            case Key.PageUp: onKeyString += "{PGUP}"; break;
                            case Key.PageDown: onKeyString += "{PGDN}"; break;
                            case Key.Insert: onKeyString += "{INSERT}"; break;
                            case Key.Delete: onKeyString += "{DELETE}"; break;
                            case Key.OemComma: onKeyString += ","; break;
                            case Key.OemPeriod: onKeyString += "."; break;
                            case Key.OemMinus: onKeyString += "-"; break;
                            case Key.OemPlus: onKeyString += "="; break;
                            // Add all F-keys
                            case Key.F1: case Key.F2: case Key.F3: case Key.F4: case Key.F5: case Key.F6:
                            case Key.F7: case Key.F8: case Key.F9: case Key.F10: case Key.F11: case Key.F12:
                                onKeyString += "{" + Key.ToString() + "}"; break;
                            default:
                                onKeyString += Key.ToString().ToLower(); break;
                        }
                        return onKeyString;
                    }
                }
            }
        }
        ```

*   **✅ 2.2: Create the `ShortcutConfigurationService`**
    *   **Commentary:** This service is responsible for all file operations. It knows where to store the user's settings (`%APPDATA%`), how to define the default shortcuts, and how to merge the user's saved settings over the defaults. This keeps the rest of the application clean from file system details.
    *   **Action:** Create a folder `Services`. Add the class `ShortcutConfigurationService.cs`.

    *   **Code: `ShortcutConfigurationService.cs`**
        ```csharp
        using System;
        using System.Collections.Generic;
        using System.IO;
        using System.Linq;
        using System.Windows.Input;
        using Newtonsoft.Json;
        using QuantBoost_Excel.Models;

        namespace QuantBoost_Excel.Services
        {
            public class ShortcutConfigurationService
            {
                private readonly string _configFilePath;

                public ShortcutConfigurationService()
                {
                    string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                    string configFolder = Path.Combine(appDataPath, "QuantBoost");
                    Directory.CreateDirectory(configFolder); // Ensure the folder exists
                    _configFilePath = Path.Combine(configFolder, "shortcuts.json");
                }

                public List<ShortcutModel> GetDefaultConfiguration()
                {
                    // This is the master list of all available shortcuts in the add-in.
                    return new List<ShortcutModel>
                    {
                        // Add ALL your shortcuts here. This is the single source of truth for defaults.
                        new ShortcutModel { Group = "General", ActionDescription = "Trace Precedents", MethodName = "LaunchTraceForActiveCell", UtilityScore = 5,
                            IsEnabled = true, Modifiers = ModifierKeys.Control | ModifierKeys.Shift, Key = Key.OemOpenBrackets },

                        new ShortcutModel { Group = "Sheet Navigation", ActionDescription = "Move Sheet Left", MethodName = "MoveSheetLeft", UtilityScore = 3,
                            IsEnabled = true, Modifiers = ModifierKeys.Control | ModifierKeys.Alt, Key = Key.N },
                        
                        new ShortcutModel { Group = "Sheet Navigation", ActionDescription = "Move Sheet Right", MethodName = "MoveSheetRight", UtilityScore = 3,
                            IsEnabled = true, Modifiers = ModifierKeys.Control | ModifierKeys.Alt, Key = Key.M },

                        new ShortcutModel { Group = "Rows & Columns", ActionDescription = "Hide Row", MethodName = "HideRow", UtilityScore = 3,
                            IsEnabled = true, Modifiers = ModifierKeys.Control | ModifierKeys.Alt | ModifierKeys.Shift, Key = Key.Home },
                        
                        // ... etc. Add all shortcuts from your screenshot.
                    };
                }

                public List<ShortcutModel> LoadConfiguration()
                {
                    var defaults = GetDefaultConfiguration();
                    
                    // Set the default values on each model for the "Reset" feature
                    foreach (var d in defaults)
                    {
                        d.DefaultIsEnabled = d.IsEnabled;
                        d.DefaultModifiers = d.Modifiers;
                        d.DefaultKey = d.Key;
                    }

                    if (!File.Exists(_configFilePath))
                    {
                        return defaults; // No user config exists, return defaults.
                    }

                    try
                    {
                        string json = File.ReadAllText(_configFilePath);
                        var userSettings = JsonConvert.DeserializeObject<List<ShortcutModel>>(json);

                        // Merge user settings over defaults. This ensures new shortcuts in updates appear correctly.
                        var finalConfig = new List<ShortcutModel>();
                        foreach (var defaultShortcut in defaults)
                        {
                            var userShortcut = userSettings.FirstOrDefault(u => u.MethodName == defaultShortcut.MethodName);
                            if (userShortcut != null)
                            {
                                // User has a setting for this, apply it but keep the defaults for reset.
                                defaultShortcut.IsEnabled = userShortcut.IsEnabled;
                                defaultShortcut.Modifiers = userShortcut.Modifiers;
                                defaultShortcut.Key = userShortcut.Key;
                            }
                            finalConfig.Add(defaultShortcut);
                        }
                        return finalConfig;
                    }
                    catch (Exception ex)
                    {
                        // ErrorHandlingService.LogError(ex, "Failed to load shortcut configuration. Reverting to defaults.");
                        return defaults; // On error, return defaults to prevent crashing.
                    }
                }

                public void SaveConfiguration(IEnumerable<ShortcutModel> shortcuts)
                {
                    try
                    {
                        // We only save the properties the user can change.
                        var settingsToSave = shortcuts.Select(s => new
                        {
                            s.MethodName,
                            s.IsEnabled,
                            s.Modifiers,
                            s.Key
                        });

                        string json = JsonConvert.SerializeObject(settingsToSave, Formatting.Indented);
                        File.WriteAllText(_configFilePath, json);
                    }
                    catch (Exception ex)
                    {
                        // ErrorHandlingService.LogError(ex, "Failed to save shortcut configuration.");
                        // Optionally, inform the user of the failure.
                    }
                }
            }
        }
        ```

---

### **Task 3: Enhancing the `HotkeyService`**

**Objective:** Adapt the `HotkeyService` to be driven by a configuration list instead of one-off registrations.

*   **✅ 3.1: Add `ApplyConfiguration` Method**
    *   **Commentary:** This new method will be the primary interface for the Shortcut Manager. It takes a list of `ShortcutModel` objects, intelligently unregisters all previously managed keys, and then registers only the currently enabled ones. This is a robust "full refresh" approach.
    *   **Action:** Add the following method to your existing `HotkeyService.cs`.

    *   **Code: Add to `HotkeyService.cs`**
        ```csharp
        // In HotkeyService.cs
        private readonly HashSet<string> _managedKeys = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        public void ApplyConfiguration(IEnumerable<ShortcutModel> shortcuts)
        {
            if (_isDisposed) return;

            lock (_lock)
            {
                // 1. Unregister all keys previously managed by this service.
                foreach (var key in new List<string>(_managedKeys))
                {
                    UnregisterHotkeyInternal(key);
                }
                _managedKeys.Clear();

                // 2. Register only the enabled shortcuts from the new configuration.
                foreach (var shortcut in shortcuts.Where(s => s.IsEnabled))
                {
                    string onKeyString = shortcut.OnKeyString;
                    if (string.IsNullOrEmpty(onKeyString)) continue;

                    // Use the existing RegisterHotkey logic, but simplified.
                    string fullMacroPath = $"{VBA_MODULE_NAME}.{shortcut.MethodName}_Macro"; // Use a consistent macro name
                    
                    // We need a slightly modified registration logic here.
                    // Let's create a private helper.
                    RegisterHotkeyInternal(shortcut);

                    _managedKeys.Add(onKeyString);
                }
            }
        }

        // Create a private helper to avoid duplicating logic from the public RegisterHotkey method.
        private void RegisterHotkeyInternal(ShortcutModel shortcut)
        {
            var registration = new HotkeyRegistration
            {
                Keys = shortcut.OnKeyString,
                MethodName = shortcut.MethodName,
                Description = shortcut.ActionDescription,
                MacroName = $"{shortcut.MethodName}_Macro"
            };

            // Ensure the VBA bridge exists in all currently open workbooks.
            foreach (Excel.Workbook wb in _excelApp.Workbooks)
            {
                EnsureVbaMacroExists(wb, registration);
            }

            // Set the application-level hotkey.
            _excelApp.OnKey(registration.Keys, $"{VBA_MODULE_NAME}.{registration.MacroName}");
        }
        ```

---

### **Task 4: The Brain - `ShortcutManagerViewModel`**

**Objective:** Create the ViewModel that will power the UI. It will manage the list of shortcuts and handle all user actions (applying, resetting, etc.).

*   **✅ 4.1: Implement the `ShortcutManagerViewModel`**
    *   **Commentary:** This is the central controller for the UI. It loads the configuration, exposes it to the View via an `ICollectionView` (for grouping), and provides `ICommand` properties for all the buttons. When a command is executed, it orchestrates the necessary actions by calling the `HotkeyService` and `ShortcutConfigurationService`.
    *   **Action:** Create a folder `ViewModels`. Add the class `ShortcutManagerViewModel.cs`.

    *   **Code: `ShortcutManagerViewModel.cs`**
        ```csharp
        using System.Collections.ObjectModel;
        using System.ComponentModel;
        using System.Linq;
        using System.Windows.Data;
        using System.Windows.Input;
        using QuantBoost_Excel.Models;
        using QuantBoost_Excel.Mvvm;
        using QuantBoost_Excel.Services;

        namespace QuantBoost_Excel.ViewModels
        {
            public class ShortcutManagerViewModel : ViewModelBase
            {
                private readonly HotkeyService _hotkeyService;
                private readonly ShortcutConfigurationService _configService;

                public ObservableCollection<ShortcutModel> Shortcuts { get; }
                public ICollectionView ShortcutsView { get; }

                public ICommand ApplyCommand { get; }
                public ICommand ResetAllCommand { get; }
                public ICommand DisableAllCommand { get; }
                public ICommand ResetSingleCommand { get; }

                public ShortcutManagerViewModel(HotkeyService hotkeyService, ShortcutConfigurationService configService)
                {
                    _hotkeyService = hotkeyService;
                    _configService = configService;

                    Shortcuts = new ObservableCollection<ShortcutModel>(_configService.LoadConfiguration());
                    
                    ShortcutsView = CollectionViewSource.GetDefaultView(Shortcuts);
                    ShortcutsView.GroupDescriptions.Add(new PropertyGroupDescription("Group"));

                    ApplyCommand = new RelayCommand(p => ApplyChanges());
                    ResetAllCommand = new RelayCommand(p => ResetAll());
                    DisableAllCommand = new RelayCommand(p => DisableAll());
                    ResetSingleCommand = new RelayCommand(ResetSingle);
                }

                private void ApplyChanges()
                {
                    // 1. Apply the new configuration to the running application
                    _hotkeyService.ApplyConfiguration(Shortcuts);

                    // 2. Save the user's changes for the next session
                    _configService.SaveConfiguration(Shortcuts);
                }

                private void ResetAll()
                {
                    foreach (var shortcut in Shortcuts)
                    {
                        shortcut.IsEnabled = shortcut.DefaultIsEnabled;
                        shortcut.Modifiers = shortcut.DefaultModifiers;
                        shortcut.Key = shortcut.DefaultKey;
                    }
                }

                private void DisableAll()
                {
                    foreach (var shortcut in Shortcuts)
                    {
                        shortcut.IsEnabled = false;
                    }
                }

                private void ResetSingle(object parameter)
                {
                    if (parameter is ShortcutModel shortcut)
                    {
                        shortcut.IsEnabled = shortcut.DefaultIsEnabled;
                        shortcut.Modifiers = shortcut.DefaultModifiers;
                        shortcut.Key = shortcut.DefaultKey;
                    }
                }
            }
        }
        ```

---

### **Task 5: The Face - `ShortcutManagerWindow` (WPF View)**

**Objective:** Build the WPF window and its controls. This task focuses purely on presentation, binding UI elements to the ViewModel's properties and commands.

*   **✅ 5.1: Create the WPF Window and Converters**
    *   **Commentary:** The View is composed of the XAML markup and minimal code-behind. We use a `ListView` with a `GridView` for the main layout. The most complex part is the "Keystroke" editor, which uses a custom `DataTemplate`. We need a value converter to bind the `ModifierKeys` enum (a bitmask) to individual `CheckBox` controls. The `PreviewKeyDown` event on the `TextBox` is a standard MVVM-friendly way to capture raw key presses.
    *   **Action:** Create a folder `View`. Add a new WPF Window named `ShortcutManagerWindow.xaml`. Add a new class `ModifierKeysToBoolConverter.cs` to the same folder.

    *   **Code: `ModifierKeysToBoolConverter.cs`**
        ```csharp
        using System;
        using System.Globalization;
        using System.Windows.Data;
        using System.Windows.Input;

        namespace QuantBoost_Excel.View
        {
            public class ModifierKeysToBoolConverter : IValueConverter
            {
                public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
                {
                    var modifiers = (ModifierKeys)value;
                    var parameterModifier = (ModifierKeys)Enum.Parse(typeof(ModifierKeys), (string)parameter);
                    return modifiers.HasFlag(parameterModifier);
                }

                public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
                {
                    // This is a one-way binding for display, but we implement ConvertBack for completeness.
                    // The actual update logic would be more complex if two-way binding was needed here.
                    // We will handle updates in the ViewModel or via direct property setting.
                    return Binding.DoNothing;
                }
            }
        }
        ```

    *   **Code: `ShortcutManagerWindow.xaml`**
        ```xml
        <Window x:Class="QuantBoost_Excel.View.ShortcutManagerWindow"
                xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                xmlns:local="clr-namespace:QuantBoost_Excel.View"
                mc:Ignorable="d"
                Title="Shortcut Manager" Height="650" Width="550" WindowStartupLocation="CenterScreen">
            <Window.Resources>
                <local:ModifierKeysToBoolConverter x:Key="ModifierKeyConverter" />
            </Window.Resources>
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <ListView ItemsSource="{Binding ShortcutsView}" Grid.Row="0" AlternationCount="2">
                    <ListView.GroupStyle>
                        <GroupStyle>
                            <GroupStyle.HeaderTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="14" Text="{Binding Name}" Margin="0,15,0,5" Foreground="#005A9E"/>
                                </DataTemplate>
                            </GroupStyle.HeaderTemplate>
                        </GroupStyle>
                    </ListView.GroupStyle>
                    
                    <ListView.View>
                        <GridView>
                            <GridViewColumn Header="Action" Width="180">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <CheckBox IsChecked="{Binding IsEnabled}" Content="{Binding ActionDescription}" VerticalAlignment="Center"/>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <GridViewColumn Header="Keystroke" Width="220">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                            <CheckBox IsChecked="{Binding Modifiers, Converter={StaticResource ModifierKeyConverter}, ConverterParameter=Control}" Content="Ctrl" Tag="Control" Click="Modifier_Click"/>
                                            <CheckBox IsChecked="{Binding Modifiers, Converter={StaticResource ModifierKeyConverter}, ConverterParameter=Alt}" Content="Alt" Margin="5,0" Tag="Alt" Click="Modifier_Click"/>
                                            <CheckBox IsChecked="{Binding Modifiers, Converter={StaticResource ModifierKeyConverter}, ConverterParameter=Shift}" Content="Shift" Tag="Shift" Click="Modifier_Click"/>
                                            <TextBox Text="{Binding Key, Mode=OneWay}" Width="60" Margin="5,0" PreviewKeyDown="KeyCapture_OnPreviewKeyDown" GotFocus="KeyCapture_GotFocus" ToolTip="Click here and press the desired key."/>
                                        </StackPanel>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            
                            <GridViewColumn Header="Utility" Width="50">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding UtilityScore}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <GridViewColumn>
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Content="Reset" Command="{Binding DataContext.ResetSingleCommand, RelativeSource={RelativeSource AncestorType=ListView}}" CommandParameter="{Binding}" Padding="5,2"/>
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                        </GridView>
                    </ListView.View>
                </ListView>

                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
                    <Button Content="Reset All" Command="{Binding ResetAllCommand}" Margin="5" Padding="10,5"/>
                    <Button Content="Disable All" Command="{Binding DisableAllCommand}" Margin="5" Padding="10,5"/>
                    <Button Content="Apply" Command="{Binding ApplyCommand}" IsDefault="True" FontWeight="Bold" Margin="5" Padding="10,5" ToolTip="Apply changes and keep this window open."/>
                    <Button Content="OK" Click="OkButton_Click" IsDefault="False" Margin="5" Padding="10,5" ToolTip="Apply changes and close this window."/>
                    <Button Content="Cancel" IsCancel="True" Margin="5" Padding="10,5"/>
                </StackPanel>
            </Grid>
        </Window>
        ```

    *   **Code: `ShortcutManagerWindow.xaml.cs` (Code-behind)**
        ```csharp
        using System.Windows;
        using System.Windows.Controls;
        using System.Windows.Input;
        using QuantBoost_Excel.Models;
        using QuantBoost_Excel.ViewModels;

        namespace QuantBoost_Excel.View
        {
            public partial class ShortcutManagerWindow : Window
            {
                public ShortcutManagerWindow(ShortcutManagerViewModel viewModel)
                {
                    InitializeComponent();
                    DataContext = viewModel;
                }

                private void KeyCapture_OnPreviewKeyDown(object sender, KeyEventArgs e)
                {
                    e.Handled = true; // Prevent the key from being processed further

                    var textBox = (TextBox)sender;
                    var shortcut = (ShortcutModel)textBox.DataContext;

                    // Ignore modifier-only key presses
                    if (e.Key == Key.LeftCtrl || e.Key == Key.RightCtrl ||
                        e.Key == Key.LeftAlt || e.Key == Key.RightAlt ||
                        e.Key == Key.LeftShift || e.Key == Key.RightShift ||
                        e.Key == Key.System || e.Key == Key.Capital)
                    {
                        return;
                    }

                    shortcut.Key = e.Key;
                }

                private void Modifier_Click(object sender, RoutedEventArgs e)
                {
                    var checkBox = (CheckBox)sender;
                    var shortcut = (ShortcutModel)checkBox.DataContext;
                    var modifier = (ModifierKeys)System.Enum.Parse(typeof(ModifierKeys), (string)checkBox.Tag);

                    if (checkBox.IsChecked == true)
                    {
                        shortcut.Modifiers |= modifier; // Add the flag
                    }
                    else
                    {
                        shortcut.Modifiers &= ~modifier; // Remove the flag
                    }
                }

                private void KeyCapture_GotFocus(object sender, RoutedEventArgs e)
                {
                    // Select all text to make it obvious it's ready for input
                    ((TextBox)sender).SelectAll();
                }

                private void OkButton_Click(object sender, RoutedEventArgs e)
                {
                    var vm = (ShortcutManagerViewModel)DataContext;
                    vm.ApplyCommand.Execute(null);
                    this.DialogResult = true;
                    this.Close();
                }
            }
        }
        ```

---

### **Task 6: Final Integration**

**Objective:** Connect all the pieces. Instantiate the services at startup, apply the initial configuration, and provide a Ribbon button to launch the manager.

*   **✅ 6.1: Update `ThisAddIn.cs`**
    *   **Commentary:** The add-in's main class will now orchestrate the startup sequence. It creates instances of our services, loads the configuration, and passes it to the `HotkeyService`. It also provides the public method to show the manager window.
    *   **Action:** Modify `ThisAddIn.cs`.

    *   **Code: `ThisAddIn.cs`**
        ```csharp
        // Add these using statements at the top
        using QuantBoost_Excel.Services;
        using QuantBoost_Excel.ViewModels;
        using QuantBoost_Excel.View;

        public partial class ThisAddIn
        {
            private HotkeyService _hotkeyService;
            private ShortcutConfigurationService _configService;

            protected override object RequestComAddInAutomationService() => this;

            private void ThisAddIn_Startup(object sender, System.EventArgs e)
            {
                try
                {
                    _configService = new ShortcutConfigurationService();
                    _hotkeyService = new HotkeyService(this.Application);

                    // Load the user's configuration (or defaults) and apply them.
                    var currentConfig = _configService.LoadConfiguration();
                    _hotkeyService.ApplyConfiguration(currentConfig);
                }
                catch (Exception ex)
                {
                    // ErrorHandlingService.LogError(ex, "A critical error occurred during add-in startup.");
                }
            }

            private void ThisAddIn_Shutdown(object sender, System.EventArgs e)
            {
                _hotkeyService?.Dispose();
            }

            public void ShowShortcutManager()
            {
                var viewModel = new ShortcutManagerViewModel(_hotkeyService, _configService);
                var window = new ShortcutManagerWindow(viewModel);
                window.ShowDialog(); // Show as a modal dialog to prevent interaction with Excel while open.
            }

            // Ensure all your public methods that can be called by shortcuts exist here.
            public void LaunchTraceForActiveCell() { /* ... your logic ... */ }
            public void MoveSheetLeft() { /* ... your logic ... */ }
            public void MoveSheetRight() { /* ... your logic ... */ }
            public void HideRow() { /* ... your logic ... */ }
            // ... etc.
        }
        ```

*   **✅ 6.2: Add Ribbon Button**
    *   **Commentary:** The final step is to give the user a way to open the manager.
    *   **Action:** Add a button to your `MainRibbon.xml` and its callback to `MainRibbon.cs`.

    *   **Code: `MainRibbon.xml`**
        ```xml
        <button id="btnShortcutManager" label="Shortcut Manager" size="large" onAction="OnShowShortcutManager" imageMso="ComAddInsDialog" />
        ```

    *   **Code: `MainRibbon.cs`**
        ```csharp
        public void OnShowShortcutManager(Office.IRibbonControl control)
        {
            Globals.ThisAddIn.ShowShortcutManager();
        }
        ```

---

### **Task 7: Verification and Final Polish**

**Objective:** Ensure the feature is robust and user-friendly.

*   **✅ 7.1: Testing Checklist**
    *   ✅ **Default Load:** On first launch, do all default shortcuts work?
    *   ✅ **Manager UI:** Does the manager open and display all shortcuts correctly grouped?
    *   ✅ **Disable/Enable:** Disable a shortcut, click Apply. Does it stop working? Enable it again. Does it resume working?
    *   ✅ **Key Change:** Change the key for an action (e.g., from `Ctrl+Alt+N` to `Ctrl+Shift+1`). Click Apply. Does the new key work? Does the old one stop working?
    *   ✅ **Persistence:** Make changes, click OK to close. Close and reopen Excel. Are your changes still active? Reopen the manager. Does it show your last saved state?
    *   ✅ **Reset:** Use the "Reset" button on a single item. Does it revert to its default? Use "Reset All". Do all items revert? Click Apply. Do the defaults become active?
    *   ✅ **VBA Trust:** Test with "Trust access to the VBA project object model" disabled in Excel's Trust Center. The feature should fail gracefully (shortcuts don't work) without crashing the add-in.

*   **✅ 7.2: Final User Documentation Note**
    *   **Action:** Prepare a note for your help file or a first-time use tooltip.
    *   **Content:** "For keyboard shortcuts to function, please ensure 'Trust access to the VBA project object model' is enabled in Excel's Trust Center (File -> Options -> Trust Center -> Trust Center Settings... -> Macro Settings)."

This comprehensive plan provides a complete, production-ready implementation of a best-in-class Shortcut Manager, giving you a powerful and maintainable feature to compete effectively.

---
title: Plan for QuantBoost Excel Add-in
purpose: Outlines the development tasks for the QuantBoost Excel Add-in.
projects: ["excel-add-in"]
source_analysis: 
status: development
last_updated: 2025-06-15 22:17 PT
tags: ["excel-add-in", "plan", "development", "features"]
---

#### Task 1: Fix Core Functionality Issues

*   [x] **1.1. Implement Robust Keyboard Navigation (Arrow Keys)**
    *   [ ] In `TraceView.xaml.cs`, navigate to the `PrecedentTreeView_PreviewKeyDown` event handler.
    *   [ ] Inside the `case Key.Right:` block, before the existing logic, add a check for lazy-loaded nodes: `if (!selectedNode.ChildrenLoaded)`.
    *   [ ] Inside this new `if` block, add logic to trigger the correct expansion method:
        *   [ ] If `selectedNode.DisplayValue == "<array>"` and `selectedNode.RawValue` is an array, call `ExpandArrayNode(selectedNode)`.
        *   [ ] Otherwise, call the generic `ExpandLazyLoadedNode(selectedNode)` to load standard precedents.
    *   [ ] Ensure you set `e.Handled = true;` after triggering the load to prevent the default `TreeView` behavior.
    *   [ ] Review the `case Key.Left:` block to ensure it correctly collapses expanded nodes (`selectedNode.IsExpanded = false;`) or navigates to the parent if already collapsed.

*   [x] **1.2. Enable Tracing for Array Elements with Formulas**
    *   [ ] In `TracerEngine.cs`, locate the `CreateArrayElementNode` method.
    *   [ ] Inside the method, after the `elementNode` is created, check if the `element` value contains a formula.
    *   [ ] Add the following logic:
        ```csharp
        if (element is string formula && formula.StartsWith("="))
        {
            elementNode.Formula = formula;
            elementNode.ChildrenLoaded = false; // CRITICAL: This makes the node expandable
        }
        else
        {
            elementNode.ChildrenLoaded = true; // This is the default for non-formula values
        }
        ```
    *   [ ] Ensure the `DisplayValue` is still set correctly for formula-based elements (e.g., it might show a cached value or the formula itself if no value is present).

#### Task 2: Fix UI/UX Issues

*   [x] **2.1. Widen the Trace Window for Better Readability**
    *   [ ] In `frmTrace.cs`, find the constructor `public frmTrace()`.
    *   [ ] Change the `this.ClientSize` line from `new System.Drawing.Size(700, 600)` to `new System.Drawing.Size(900, 600)`.
    *   [ ] Update the `this.MinimumSize` to `new System.Drawing.Size(700, 400)` to allow resizing while maintaining usability.

*   [ ] **2.2. Standardize Array Element Icon and Formatting**
    *   [ ] Open `TraceView.md` (the XAML file).
    *   [ ] Locate the `<HierarchicalDataTemplate>` for `TraceNode`.
    *   [ ] Find the `<Style TargetType="TextBlock">` that controls the node type icon (it contains `DataTrigger`s for "Root", "SameSheet", etc.).
    *   [ ] Add a new `DataTrigger` for array elements to give them a consistent icon:
        ```xml
        <DataTrigger Binding="{Binding NodeType}" Value="ArrayElement">
            <Setter Property="Text" Value="🔢"/>
        </DataTrigger>
        ```

*   [x] **2.3. Reduce Initial Tree Expansion Depth**
    *   [ ] In `TraceView.xaml.cs`, navigate to the `SetTraceData` method.
    *   [ ] Locate and remove or comment out the `foreach` loop that expands the first-level children:
        ```csharp
        // Auto-expand first level children --- REMOVE THIS BLOCK
        // foreach (var child in _rootNode.Children)
        // {
        //     child.IsExpanded = true;
        // }
        ```
    *   [ ] Ensure the root node itself is still expanded: `_rootNode.IsExpanded = true;`.

#### Task 3: Implement New UI Feature (Dynamic Formula Bar)

*   [x] **3.1. Create a Helper Method to Update the Formula Display**
    *   [ ] In `TraceView.xaml.cs`, create a new private method: `private void UpdateFormulaDisplay(TraceNode node)`.
    *   [ ] Inside this method, add logic to populate `FormulaRichTextBox` based on the provided `node`.
    *   [ ] The logic should be:
        1.  If `node.Formula` is not null or empty, display the formula. You can reuse the logic from `InitializeStaticFormulaDisplay` to do this.
        2.  If `node.Formula` is null, but `node.NodeType == NodeType.Range` or `node.NodeType == NodeType.ArrayElement`, display the parent's formula for context (e.g., `FindParentNode(...)` might be useful here) or a descriptive text like `"<Array Element>"`.
        3.  Otherwise (it's a simple value), display the `node.DisplayValue` in a muted or distinct style (e.g., gray and italic).
        4.  If the node is the root, call `HighlightSelectedPrecedent(node)` to ensure the entire formula is cleared of highlights.

*   [x] **3.2. Update the Formula Bar on Selection Change**
    *   [ ] In `TraceView.xaml.cs`, find the `PrecedentTreeView_SelectedItemChanged` event handler.
    *   [ ] Inside the `if (e.NewValue is TraceNode selectedNode)` block, make a call to your new helper method: `UpdateFormulaDisplay(selectedNode);`.
    *   [ ] **Crucial Change:** Modify the `HighlightSelectedPrecedent` call. Instead of calling it directly, the logic should be inside `UpdateFormulaDisplay` or be called right after, so that the formula bar is first populated with the *correct* formula, and *then* the highlight is applied. The current implementation tries to highlight a precedent in the root formula, which will be incorrect when a sub-formula is displayed.

*   [x] **3.3. Refine Highlighting Logic for Sub-Formulas**
    *   [ ] The `HighlightSelectedPrecedent` method is designed to work on the *root* formula. You'll need to adjust it.
    *   [ ] The simplest approach is to make the formula bar *always* show the root formula, and only the highlighting changes. This simplifies the logic immensely. If you choose this path, ignore Task 3.1 and 3.2.
   
