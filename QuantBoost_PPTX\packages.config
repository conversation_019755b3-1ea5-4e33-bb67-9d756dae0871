﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="DocumentFormat.OpenXml" version="2.18.0" targetFramework="net481" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net481" />
  <package id="Newtonsoft.Json.Bson" version="1.0.3" targetFramework="net481" />
  <package id="System.IO" version="4.3.0" targetFramework="net481" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net481" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net481" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net481" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net481" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net481" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net481" />
</packages>