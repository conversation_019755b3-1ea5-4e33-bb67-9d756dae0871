// Helper functions for license logic, Supabase interactions, and activation checks

// Fetch license info from Supabase
async function fetchLicenseFromSupabase(supabaseClient, licenseKey) {
    const { data, error } = await supabaseClient
        .from('licenses')
        .select('*')
        .eq('license_key', licenseKey)
        .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found, which is not an "error" for a .single() query
        console.error(`fetchLicenseFromSupabase: Supabase error for key ${licenseKey}: ${error.message}`);
    }
    return data || null; // Return null if not found, or the data object
}

// Get user email from profiles table
async function getEmailFromUserProfile(supabaseClient, userId) {
    if (!userId) {
        return null;
    }

    try {
        const { data: profile, error } = await supabaseClient
            .from('profiles') // Assuming your user profiles table is named 'profiles'
            .select('email')
            .eq('id', userId)
            .single();

        if (error) {
            if (error.code === 'PGRST116') { // No rows found
                console.warn(`[getEmailFromUserProfile] No profile found for user ID: ${userId}`);
            } else {
                console.error(`[getEmailFromUserProfile] Error fetching profile for user ${userId}:`, error.message);
            }
            return null;
        }
        
        if (profile && profile.email) {
            return profile.email;
        } else {
            console.warn(`[getEmailFromUserProfile] Profile found for user ID: ${userId}, but no email associated.`);
            return null;
        }

    } catch (e) {
        console.error(`[getEmailFromUserProfile] Exception fetching profile for user ${userId}:`, e.message);
        return null;
    }
}

// Check license activation status and record new activation if necessary
async function checkOrRecordActivation(supabaseClient, license, deviceFingerprint) {
    // 0. Validate essential inputs
    if (!license || !license.id || !license.product_id) {
        // console.error(`[Activation] Invalid license object provided for activation. License: ${JSON.stringify(license)}`);
        return { allowed: false, error: { message: "Invalid license data for activation." }, activationId: null };
    }

    const assignedEmail = license.email || await getEmailFromUserProfile(supabaseClient, license.user_id);
    if (!assignedEmail) {
        // console.warn(`[Activation] License ${license.id} has no email assigned (directly or via user_id). Cannot activate.`);
        return {
            allowed: false,
            error: {
                message: "License is not associated with an email. Please contact support or ensure your account email is set.",
                code: "NO_LICENSE_EMAIL"
            },
            activationId: null
        };
    }

    const licenseId = license.id;
    const maxActivationsAllowed = license.max_activations || 1; // Default to 1 if not set

    try {
        // 1. Check if an activation record for THIS specific device (machine_id) already exists.
        const { data: specificDeviceActivation, error: specificDeviceError } = await supabaseClient
            .from('license_activations')
            .select('id, is_active') // 'is_active' is the boolean column
            .eq('license_id', licenseId)
            .eq('machine_id', deviceFingerprint)
            .maybeSingle(); // We expect 0 or 1 record for a specific device/license pair

        if (specificDeviceError) {
            // console.error(`[Activation] Supabase error checking specific device activation for license ${licenseId}, device ${deviceFingerprint}: ${specificDeviceError.message}`);
            return { allowed: false, error: { message: "Database error checking device activation." }, activationId: null };
        }

        if (specificDeviceActivation) {
            // An activation record for this device already exists.
            if (specificDeviceActivation.is_active) {
                // Already active on this device. Update last_validated_at.
                const { error: updateError } = await supabaseClient
                    .from('license_activations')
                    .update({ last_validated_at: new Date().toISOString() })
                    .eq('id', specificDeviceActivation.id);
                // if (updateError) console.warn(`[Activation] Failed to update last_validated_at for active device ${specificDeviceActivation.id}: ${updateError.message}`);
                // console.log(`[Activation] License ${licenseId} already active on this device ${deviceFingerprint}. Allowed.`);
                return { allowed: true, activationId: specificDeviceActivation.id };
            } else {
                // Exists for this device but is INACTIVE. Try to reactivate it if slots are available.
                const { count: currentActiveCount, error: countError } = await supabaseClient
                    .from('license_activations')
                    .select('id', { count: 'exact', head: false }) // Just need the count
                    .eq('license_id', licenseId)
                    .eq('is_active', true);

                if (countError) {
                    // console.error(`[Activation] Supabase error counting active activations for license ${licenseId} (for reactivation): ${countError.message}`);
                    return { allowed: false, error: { message: "Database error counting activations for reactivation." }, activationId: null };
                }

                if (currentActiveCount < maxActivationsAllowed) {
                    // Slots available to reactivate this device.
                    const { error: updateError } = await supabaseClient
                        .from('license_activations')
                        .update({
                            is_active: true,
                            last_validated_at: new Date().toISOString(),
                        })
                        .eq('id', specificDeviceActivation.id);

                    if (updateError) {
                        // console.error(`[Activation] Supabase error reactivating device ${specificDeviceActivation.id} for license ${licenseId}: ${updateError.message}`);
                        return { allowed: false, error: { message: "Database error reactivating device." }, activationId: null };
                    }
                    // console.log(`[Activation] Reactivated license ${licenseId} on device ${deviceFingerprint}. Allowed.`);
                    return { allowed: true, activationId: specificDeviceActivation.id };
                } else {
                    // No slots to reactivate this device.
                    // console.warn(`[Activation] Cannot reactivate license ${licenseId} on device ${deviceFingerprint}. Max ${maxActivationsAllowed} active devices allowed. Current: ${currentActiveCount}.`);
                    return {
                        allowed: false,
                        error: {
                            message: `Cannot reactivate on this device. Maximum ${maxActivationsAllowed} active devices allowed. Please deactivate another device.`,
                            code: "MAX_ACTIVATIONS_REACHED_REACTIVATE"
                        },
                        activationId: specificDeviceActivation.id // Return existing inactive ID
                    };
                }
            }
        } else {
            // No activation record for this device_fingerprint and license_id exists yet.
            // This is a NEW device attempting activation for this license.
            const { count: currentActiveCount, error: countError } = await supabaseClient
                .from('license_activations')
                .select('id', { count: 'exact', head: false })
                .eq('license_id', licenseId)
                .eq('is_active', true);

            if (countError) {
                // console.error("[Activation] Supabase error counting active activations for new activation.");
                return { 
                    allowed: false, 
                    error: { message: "Database error counting activations for new activation." }, 
                    activationId: null 
                };
            }

            if (currentActiveCount < maxActivationsAllowed) {
                // Slots available for a new activation.
                const { data: newActivation, error: insertError } = await supabaseClient
                    .from('license_activations')
                    .insert({
                        license_id: licenseId,
                        machine_id: deviceFingerprint,
                        is_active: true,
                        activated_at: new Date().toISOString(), // First time this device activates this license
                        last_validated_at: new Date().toISOString(),
                        email: assignedEmail // Store the email associated with the license at time of activation
                    })
                    .select('id')
                    .single();

                if (insertError) {
                    // console.error("[Activation] Supabase error inserting new activation.");
                    return { 
                        allowed: false, 
                        error: { message: "Database error creating new activation." }, 
                        activationId: null 
                    };
                }
                if (!newActivation || !newActivation.id) {
                    // console.error("[Activation] Insert for new activation did not return ID.");
                    return { 
                        allowed: false, 
                        error: { message: "Failed to confirm new activation details." }, 
                        activationId: null 
                    };
                }
                // console.log(`[Activation] New activation created for license ${licenseId} on device ${deviceFingerprint}. ID: ${newActivation.id}. Allowed.`);
                return { allowed: true, activationId: newActivation.id };
            } else {
                // Max activations reached, and this is a new device.
                // console.warn(`[Activation] Activation attempt failed for license ${licenseId} on new device ${deviceFingerprint}. Max ${maxActivationsAllowed} active devices allowed. Current: ${currentActiveCount}.`);
                return {
                    allowed: false,
                    error: {
                        message: `Maximum ${maxActivationsAllowed} active devices allowed. Please deactivate another device to use this one.`,
                        code: "MAX_ACTIVATIONS_REACHED_NEW_DEVICE"
                    },
                    activationId: null
                };
            }
        }
    } catch (catchError) {
        // console.error(`[Activation] CATCH BLOCK ERROR for license ${licenseId || 'unknown'}, device ${deviceFingerprint}: ${catchError.message}`, catchError.stack);
        return { allowed: false, error: { message: "Unexpected error during activation process." }, activationId: null };
    }
}

// Evaluate the status of a license
function evaluateLicenseStatus(license, subscriptionsData = null) {
    // console.log(`[evaluateLicenseStatus] Evaluating license: ${license?.license_key}, Status: ${license?.status}, Expiry: ${license?.expiry_date}`);

    if (!license) {
        return { isValid: false, apiStatus: 'not_found', calculatedExpiryDate: null, trialDaysLeft: null };
    }

    const now = new Date();
    let calculatedExpiryDate = license.expiry_date ? new Date(license.expiry_date) : null;
    let trialDaysLeft = null;
    let effectiveStatus = license.status; // Start with the DB status

    // --- Trial Logic ---
    if (license.status === 'trial_active' || (license.license_tier === 'trial' && license.status !== 'revoked' && license.status !== 'expired')) {
        let trialEndDate = license.trial_expiry_date ? new Date(license.trial_expiry_date) : null;
        
        // If trial_expiry_date is missing but trial_start_date and trial_duration_days are present, calculate it
        if (!trialEndDate && license.trial_start_date && license.trial_duration_days) {
            const startDate = new Date(license.trial_start_date);
            trialEndDate = new Date(startDate.setDate(startDate.getDate() + parseInt(license.trial_duration_days, 10)));
        }

        if (trialEndDate) {
            if (trialEndDate > now) {
                effectiveStatus = 'trial_active'; // Ensure it's marked as trial_active if within period
                trialDaysLeft = Math.ceil((trialEndDate - now) / (1000 * 60 * 60 * 24));
                calculatedExpiryDate = trialEndDate; // For trials, the trial end date is the effective expiry
            } else {
                effectiveStatus = 'trial_expired'; // Trial period has ended
                calculatedExpiryDate = trialEndDate; 
            }
        } else {
            // If it's marked 'trial_active' but has no trial end date, this is ambiguous.
            // Consider it expired or invalid for safety, or rely on main expiry_date if that makes sense.
            // For now, if it was 'trial_active' but no end date, let it fall through to general status checks.
            // If it was just tier 'trial' but status was e.g. 'unassigned', it's not an active trial.
            if (license.status === 'trial_active') {
                 console.warn(`[evaluateLicenseStatus] License ${license.license_key} is 'trial_active' but has no trial_expiry_date. Evaluation might be inaccurate.`);
                 // Fallback: if no trial_expiry_date, but there is a general expiry_date, use that.
                 // If general expiry_date is also missing or past, it will be handled by later checks.
                 if (!calculatedExpiryDate || calculatedExpiryDate < now) {
                    effectiveStatus = 'trial_expired'; // Or just 'expired'
                 }
            }
        }
    }

    // --- Subscription-based Expiry Override (if applicable and not a trial that already set expiry) ---
    // This part is more relevant if subscriptionsData is passed and contains overriding info.
    // For now, we assume license.expiry_date is the primary source after trial logic.
    // If you had `subscriptionsData` from a joined query or separate fetch:
    // if (subscriptionsData && subscriptionsData.current_period_end) {
    //     const subExpiry = new Date(subscriptionsData.current_period_end);
    //     if (subExpiry > now && (!calculatedExpiryDate || subExpiry > calculatedExpiryDate)) {
    //         calculatedExpiryDate = subExpiry;
    //         // Potentially update effectiveStatus if subscription makes it active
    //         if (['active', 'trialing'].includes(subscriptionsData.status) && effectiveStatus !== 'trial_active') {
    //             effectiveStatus = 'active';
    //         }
    //     }
    // }


    // --- Final Status Determination based on effectiveStatus and calculatedExpiryDate ---
    let finalApiStatus;
    let isValid = false;

    switch (effectiveStatus) {
        case 'active':
        case 'trial_active':
            if (calculatedExpiryDate && calculatedExpiryDate > now) {
                finalApiStatus = effectiveStatus; // Could be 'active' or 'trial_active'
                isValid = true;
            } else if (calculatedExpiryDate && calculatedExpiryDate <= now) {
                finalApiStatus = effectiveStatus === 'trial_active' ? 'trial_expired' : 'expired';
            } else if (!calculatedExpiryDate && effectiveStatus === 'active') {
                // Active license with no expiry date - could be perpetual or an error.
                // For this system, we'll assume perpetual if explicitly 'active' and no expiry.
                // However, it's better practice to have an explicit far-future date for perpetual.
                // Let's treat as valid for now, but log a warning.
                console.warn(`[evaluateLicenseStatus] License ${license.license_key} is 'active' but has no expiry_date. Assuming perpetual for now.`);
                finalApiStatus = 'active_perpetual'; // Custom status for this case
                isValid = true;
                calculatedExpiryDate = null; // Indicate no specific expiry
            }
             else { // No expiry date for a trial_active license (should have been caught by trial logic)
                finalApiStatus = 'invalid_data'; // Or 'trial_expired'
            }
            break;
        case 'assigned': // Assigned but not yet activated/claimed fully, or user hasn't used it.
            // This status implies it *could* become active. For validation endpoint, it's not yet valid for use.
            // However, if it has a future expiry, it's "pending" rather than "invalid".
            if (calculatedExpiryDate && calculatedExpiryDate > now) {
                finalApiStatus = 'pending_activation'; // Or 'assigned'
            } else if (calculatedExpiryDate && calculatedExpiryDate <= now) {
                finalApiStatus = 'expired_unclaimed'; // Was assigned but never used and its potential validity window passed
            } else {
                finalApiStatus = 'pending_activation_no_expiry'; // Assigned, no expiry, could be activated
            }
            isValid = false; // Not usable directly via /validate until status is 'active' or 'trial_active'
            break;
        case 'unassigned':
            finalApiStatus = 'unassigned';
            isValid = false;
            break;
        case 'inactive': // Explicitly set to inactive by an admin, not due to expiry
            finalApiStatus = 'inactive';
            isValid = false;
            break;
        case 'revoked':
            finalApiStatus = 'revoked';
            isValid = false;
            break;
        case 'expired':
        case 'trial_expired': // Already determined by date checks above
            finalApiStatus = effectiveStatus; // 'expired' or 'trial_expired'
            isValid = false;
            break;
        case 'graceperiod': // If you implement a grace period
            // Add logic here, potentially checking a grace_period_end_date
            // For now, let's assume graceperiod means it's still treated as valid for a short time
            if (calculatedExpiryDate && calculatedExpiryDate > now) { // Assuming expiry_date is updated to end of grace
                finalApiStatus = 'graceperiod';
                isValid = true;
            } else {
                finalApiStatus = 'expired'; // Grace period itself expired
                isValid = false;
            }
            break;
        default:
            console.warn(`[evaluateLicenseStatus] Unknown license status in DB: ${license.status} for license ${license.license_key}`);
            finalApiStatus = 'unknown_status';
            isValid = false;
    }
    
    // If it became valid, ensure calculatedExpiryDate is set (unless it's perpetual)
    if (isValid && finalApiStatus !== 'active_perpetual' && !calculatedExpiryDate) {
        // This case should ideally be handled by logic above.
        // If a license is valid (e.g. active) it should have a future expiry or be perpetual.
        // If it's active but calculatedExpiryDate is null here, it's an issue.
        console.error(`[evaluateLicenseStatus] CRITICAL: License ${license.license_key} is valid ('${finalApiStatus}') but has no calculatedExpiryDate.`);
        // To be safe, mark as invalid if this state is reached, as it indicates a logic flaw or bad data.
        // isValid = false;
        // finalApiStatus = 'error_missing_expiry';
        // For now, we allow it if 'active_perpetual' was set, otherwise, this is an issue.
    }


    // console.log(`[evaluateLicenseStatus] Result for ${license?.license_key}: isValid=${isValid}, apiStatus=${finalApiStatus}, expiry=${calculatedExpiryDate}, trialDaysLeft=${trialDaysLeft}`);

    return {
        isValid,
        apiStatus: finalApiStatus,
        calculatedExpiryDate: calculatedExpiryDate ? calculatedExpiryDate.toISOString() : null,
        trialDaysLeft,
        dbStatus: license.status, // Original status from DB for reference
        licenseTier: license.license_tier,
        // You can add more transformed data here if needed by the API response
    };
}

module.exports = { fetchLicenseFromSupabase, getEmailFromUserProfile, checkOrRecordActivation, evaluateLicenseStatus };
