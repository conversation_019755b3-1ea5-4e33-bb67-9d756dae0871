// --- START OF FILE ToastNotifier.cs ---
using System;
using System.Drawing;
using System.Windows.Forms;
// ProgressState, AsyncHelper, ErrorHandlingService are in the same namespace

namespace QuantBoost_Shared.Utilities
{
    public static class ToastNotifier
    {
        // Keep track of the currently showing toast to prevent overlap (optional)
        private static Form _currentToast = null;
        private static System.Windows.Forms.Timer _currentTimer = null; // Use full namespace

        /// <summary>
        /// Shows a temporary, non-modal notification window. Ensures execution on UI thread.
        /// </summary>
        /// <param name="message">The message to display.</param>
        /// <param name="durationMs">How long the toast stays visible (milliseconds).</param>
        /// <param name="backColor">Optional background color.</param>
        public static void ShowToast(string message, int durationMs = 2500, Color? backColor = null)
        {
            // Run the entire toast creation and showing logic on the UI thread
            AsyncHelper.RunOnUIThread(() =>
            {
                try
                {
                     // If a toast is already showing, close it quickly before showing the new one
                    _currentToast?.Close();
                    _currentTimer?.Stop();
                    _currentTimer?.Dispose();
                    _currentToast = null;
                    _currentTimer = null;

                    Color bgColor = backColor ?? Color.FromArgb(40, 40, 40);
                    // Simple check for background lightness to choose text color
                    bool isDarkBackground = (bgColor.R * 0.299 + bgColor.G * 0.587 + bgColor.B * 0.114) < 140;
                    Color foreColor = isDarkBackground ? Color.White : Color.Black;


                    // Create and configure the toast form
                     _currentToast = new Form
                    {
                        FormBorderStyle = FormBorderStyle.None,
                        StartPosition = FormStartPosition.Manual,
                        ShowInTaskbar = false,
                        TopMost = true,
                        Width = 320,
                        Height = 48, // Adjusted height slightly
                        BackColor = bgColor,
                        Opacity = 0.92,
                    };

                    var label = new Label
                    {
                        Text = message,
                        ForeColor = foreColor,
                        BackColor = Color.Transparent,
                        Dock = DockStyle.Fill,
                        TextAlign = ContentAlignment.MiddleCenter,
                        Font = new Font("Segoe UI", 9.5f) // Slightly smaller font
                    };
                    _currentToast.Controls.Add(label);

                    // Position bottom right of primary screen
                    var screen = Screen.PrimaryScreen.WorkingArea;
                    _currentToast.Left = screen.Right - _currentToast.Width - 20;
                    _currentToast.Top = screen.Bottom - _currentToast.Height - 40;

                    // Use a timer to close the form
                    _currentTimer = new System.Windows.Forms.Timer { Interval = durationMs }; // Use full namespace
                    _currentTimer.Tick += (s, e) =>
                    {
                        // This tick runs on the UI thread
                        try
                        {
                            _currentToast?.Close(); // Close the form
                        }
                        catch (ObjectDisposedException) { /* Ignore if already disposed */ }
                        finally
                        {
                            // Clean up timer immediately after trying to close
                            if (_currentTimer != null)
                            {
                                _currentTimer.Stop();
                                _currentTimer.Dispose();
                                _currentTimer = null;
                            }
                            // Ensure _currentToast is nulled if this timer closed it
                            if (_currentToast != null && _currentToast.IsDisposed)
                            {
                                 _currentToast = null;
                            }
                        }
                    };

                    // Ensure the form is disposed and timer cleaned up even if closed manually
                    _currentToast.FormClosed += (s, e) =>
                    {
                        if (_currentTimer != null) // Check if timer still exists
                        {
                            _currentTimer.Stop();
                            _currentTimer.Dispose();
                            _currentTimer = null;
                        }
                        _currentToast = null; // Clear reference
                        label?.Dispose(); // Dispose label explicitly
                    };

                    _currentTimer.Start();
                    _currentToast.Show(); // Show non-modally
                }
                catch (Exception ex)
                {
                    // Log error if toast creation fails
                    ErrorHandlingService.LogException(ex, "Failed to show toast notification");
                    // Clean up references if creation failed partially
                    _currentToast?.Dispose();
                    _currentTimer?.Dispose();
                    _currentToast = null;
                    _currentTimer = null;
                }
            });
        }


        /// <summary>
        /// Creates an IProgress<ProgressState> reporter that shows progress via toast notifications.
        /// </summary>
        /// <param name="title">Optional title prefix for the toast message.</param>
        /// <param name="showDetails">Whether to include the detail message in the toast.</param>
        /// <returns>An IProgress<ProgressState> instance.</returns>
        public static IProgress<ProgressState> CreateProgressReporter(string title = "Progress", bool showDetails = false)
        {
            // Using Progress<T> ensures that the action delegate runs in the SynchronizationContext
            // captured when the Progress<T> object was created. If created on UI thread, updates run on UI thread.
            return new Progress<ProgressState>(state =>
            {
                if (state == null) return; // Safety check

                string message;
                int duration = 1500; // Default short duration for progress
                Color? color = null; // Default background

                // Show different messages based on state
                if (state.HasError)
                {
                    message = $"{title}: Error - {state.DetailMessage ?? "Unknown error"}";
                    color = Color.DarkRed;
                    duration = 5000; // Longer duration for errors
                }
                else if (state.IsComplete)
                {
                    message = $"{title}: {state.DetailMessage ?? "Completed"}";
                    duration = 3000; // Medium duration for completion
                }
                else // In progress
                {
                    message = $"{title}: {state.CurrentStep ?? "Working..."} ({state.PercentComplete}%)";
                    if (showDetails && !string.IsNullOrWhiteSpace(state.DetailMessage))
                    {
                        message += $" - {state.DetailMessage}";
                    }
                    // Keep short duration for frequent progress updates
                }
                ShowToast(message, duration, color);
            });
        }
    }
}
// --- END OF FILE ToastNotifier.cs ---