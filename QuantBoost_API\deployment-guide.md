# QuantBoost API - Azure Deployment Guide

## 🚀 Deployment Prerequisites

### 1. Install Azure CLI
```bash
# Windows (PowerShell as Administrator)
Invoke-WebRequest -Uri https://aka.ms/installazurecliwindows -OutFile .\AzureCLI.msi
Start-Process msiexec.exe -Wait -ArgumentList '/I AzureCLI.msi /quiet'

# Verify installation
az --version
```

### 2. Login to Azure
```bash
az login
az account show
```

### 3. Install Terraform (if not already installed)
```bash
# Windows (using Chocolatey)
choco install terraform

# Or download from: https://www.terraform.io/downloads
```

## 🏗 Infrastructure Deployment

### Step 1: Set Required Variables
Create a `terraform.tfvars` file in the `infrastructure/` directory:

```bash
# infrastructure/terraform.tfvars
supabase_url              = "your_supabase_project_url"
supabase_anon_key         = "your_supabase_anon_key"
supabase_service_role_key = "your_supabase_service_role_key"
jwt_secret                = "your_secure_jwt_secret_32_chars"
environment               = "prod"
location                  = "East US"
```

### Step 2: Initialize and Deploy Infrastructure
```bash
cd infrastructure

# Initialize Terraform
terraform init

# Review the plan
terraform plan

# Deploy infrastructure
terraform apply
```

### Step 3: Get Infrastructure Outputs
```bash
# Get Container Registry details
terraform output container_registry_name
terraform output container_registry_server

# Get Container App details
terraform output container_app_fqdn
```

## 🐳 Container Deployment

### Step 1: Build and Push Container
```bash
# Get ACR login server from terraform output
ACR_NAME=$(terraform output -raw container_registry_name)

# Login to Container Registry
az acr login --name $ACR_NAME

# Build and tag the image
docker build -t $ACR_NAME.azurecr.io/quantboost-api:latest .

# Push to registry
docker push $ACR_NAME.azurecr.io/quantboost-api:latest
```

### Step 2: Deploy to Container Apps
```bash
# Update the container app with new image
az containerapp update \
  --name ca-quantboost-api \
  --resource-group rg-quantboost-api-prod \
  --image $ACR_NAME.azurecr.io/quantboost-api:latest
```

## 🔧 Environment Configuration

### Required Environment Variables in Azure Key Vault:
- `supabase-url`: Your Supabase project URL
- `supabase-anon-key`: Supabase anonymous key
- `supabase-service-role-key`: Supabase service role key
- `jwt-secret`: JWT signing secret (32+ characters)

### Optional Configuration:
- `LOG_LEVEL`: Set to `info`, `warn`, or `error`
- Application Insights will be automatically configured

## 🔄 CI/CD Setup (Optional)

### GitHub Secrets Required:
```bash
# Azure Service Principal
AZURE_CREDENTIALS='{"clientId":"xxx","clientSecret":"xxx","subscriptionId":"xxx","tenantId":"xxx"}'

# Container Registry (from terraform outputs)
ACR_USERNAME="your_acr_username"
ACR_PASSWORD="your_acr_password"
```

### Get Service Principal Credentials:
```bash
# Create service principal for GitHub Actions
az ad sp create-for-rbac --name "quantboost-github-actions" \
  --role contributor \
  --scopes /subscriptions/{subscription-id}/resourceGroups/rg-quantboost-api-prod \
  --sdk-auth
```

## 🏥 Health Checks & Monitoring

### Test Endpoints:
```bash
# Get the Container App FQDN
FQDN=$(terraform output -raw container_app_fqdn)

# Test health endpoints
curl https://$FQDN/health/live
curl https://$FQDN/health/ready

# Test API endpoints
curl https://$FQDN/v1/test/hello
```

### Monitor Application:
- **Azure Portal**: Container Apps → ca-quantboost-api → Monitoring
- **Application Insights**: Search logs and metrics
- **Log Stream**: Real-time application logs

## 🔍 Troubleshooting

### View Container Logs:
```bash
az containerapp logs show \
  --name ca-quantboost-api \
  --resource-group rg-quantboost-api-prod \
  --follow
```

### Check Container App Status:
```bash
az containerapp show \
  --name ca-quantboost-api \
  --resource-group rg-quantboost-api-prod \
  --query "properties.provisioningState"
```

### Scale Container App:
```bash
az containerapp update \
  --name ca-quantboost-api \
  --resource-group rg-quantboost-api-prod \
  --min-replicas 1 \
  --max-replicas 10
```

## 🎯 Expected Results

After successful deployment:
- ✅ Container Apps running with 2-5 replicas
- ✅ Health checks passing
- ✅ Application Insights collecting telemetry
- ✅ Structured JSON logs in Azure Monitor
- ✅ Auto-scaling based on HTTP requests
- ✅ SSL certificate automatically managed
- ✅ Custom domain ready for configuration

## 📊 Cost Estimation

**Monthly costs (East US region):**
- Container Apps Environment: ~$0 (consumption-based)
- Container Apps (2 replicas avg): ~$15-30
- Application Insights: ~$5-15
- Container Registry: ~$5
- Key Vault: ~$1
- **Total estimated**: $26-51/month

## 🔒 Security Features Enabled

- ✅ HTTPS enforced with managed certificates
- ✅ Security headers (CSP, HSTS, etc.)
- ✅ Secrets stored in Azure Key Vault
- ✅ Container running as non-root user
- ✅ Input validation on all endpoints
- ✅ Rate limiting enabled
- ✅ Structured audit logging

## 📞 Next Steps

1. **Install Azure CLI and Terraform**
2. **Configure your Supabase credentials**
3. **Run the deployment commands above**
4. **Test the deployed endpoints**
5. **Set up CI/CD pipeline (optional)**
6. **Configure custom domain (optional)**

Your QuantBoost API will be production-ready and enterprise-grade! 🚀