import { describe, it, expect, vi, beforeEach } from 'vitest';
import { recordPartialSuccess } from '../telemetry';

// Reset module state by re-import? Keeping simple; attempts map not exported so rely on incremental increments.

describe('recordPartialSuccess telemetry', () => {
  const originalWarn = console.warn;
  let warns: any[] = [];
  beforeEach(() => {
    warns = [];
    console.warn = (...args: any[]) => { warns.push(args); };
  });

  it('logs and increments attempt counts', () => {
    const first = recordPartialSuccess({ subscriptionId: 'sub_1', paymentMethodId: 'pm_1' });
    const second = recordPartialSuccess({ subscriptionId: 'sub_1', paymentMethodId: 'pm_1' });
    expect(first.attempt).toBe(1);
    expect(second.attempt).toBe(2);
    expect(warns.length).toBeGreaterThanOrEqual(2);
    expect(warns[0][1].subscriptionId).toBe('sub_1');
  });

  afterEach(() => { console.warn = originalWarn; });
});
