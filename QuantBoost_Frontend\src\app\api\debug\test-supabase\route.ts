// Debug endpoint to test Supabase connection
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(req: NextRequest) {
  try {
    console.log('Testing Supabase connection...');
    console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? 'present' : 'missing');
    console.log('SUPABASE_SERVICE_KEY:', process.env.SUPABASE_SERVICE_KEY ? 'present' : 'missing');
    console.log('Service key length:', process.env.SUPABASE_SERVICE_KEY?.length || 0);
    console.log('Service key starts with:', process.env.SUPABASE_SERVICE_KEY?.substring(0, 20) || 'N/A');

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Test a simple query
    const { data, error } = await supabase
      .from('profiles')
      .select('id, email')
      .limit(1);

    if (error) {
      console.error('Supabase query error:', error);
      return NextResponse.json({ 
        success: false, 
        error: error.message,
        hint: error.hint,
        details: error.details
      }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Supabase connection successful',
      profileCount: data?.length || 0,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Supabase connection test failed:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
