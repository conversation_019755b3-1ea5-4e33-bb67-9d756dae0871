"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';

import { useEffect, useState, Suspense, useRef, useMemo, type ChangeEvent } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion, useScroll, useTransform, useSpring } from 'motion/react';
import { useSupabaseClient } from '../../hooks/useSupabaseClient';
import Header from '@/components/Header';
import AuthHeader from '@/components/AuthHeader';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button, Badge, Separator } from "@/components/ui";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { FormField, FormFieldLabel, FormFieldError } from "@/components/ui/form-field";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/toast";
import { TeamAssignmentCard } from '@/components/TeamAssignmentCard';
import { apiClient, Subscription, License, isApiSuccess, handleApiError } from '@/lib/api';
import { UserType, detectUserType, canAccessBilling, canManageSubscription, canManageTeam, getUserTypeDisplayName } from '@/lib/userTypeDetector';
import { TeamLicenseeDashboard } from '@/components/dashboard/TeamLicenseeDashboard';
import Link from 'next/link';
// Stripe Elements for secure card collection
import { loadStripe, type StripeCardElementChangeEvent } from '@stripe/stripe-js';
import { Elements, CardElement, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { baseElementsOptions, sharedPaymentElementOptions, mapStripeError } from '@/lib/stripe/elementsConfig';
import { authJson } from '@/lib/authFetch';
import { recordPartialSuccess } from '@/lib/telemetry';
import { attemptAttachPaymentMethod } from '@/lib/paymentUpdateFlow';
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

// --- Payment Method Update Components ---
interface PaymentMethodUpdateSectionProps { subscriptionId: string; onClose: () => void; addToast: ReturnType<typeof useToast>["addToast"]; }

// Internal form expects to be rendered within an Elements provider
const PaymentMethodUpdateInner: React.FC<{ onClose: () => void; addToast: PaymentMethodUpdateSectionProps['addToast']; clientSecret: string; subscriptionId: string; }> = ({ onClose, addToast, clientSecret, subscriptionId }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [submitting, setSubmitting] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const [partialWarning, setPartialWarning] = useState<string | null>(null);
  const lastConfirmedPaymentMethodRef = useRef<string | null>(null);

  const retrySetDefault = async () => {
    if (!lastConfirmedPaymentMethodRef.current) return;
    setSubmitting(true);
    setLocalError(null);
    try {
      const resp = await authJson<{ success?: boolean; error?: string }>(
        '/api/billing/set-default-payment-method',
        { method: 'POST', body: JSON.stringify({ subscriptionId, paymentMethodId: lastConfirmedPaymentMethodRef.current }) }
      );
      if (!resp.ok) throw new Error(resp.body?.error || 'Failed to attach payment method');
      addToast({ type: 'success', title: 'Payment Method Attached', description: 'Default payment method updated.' });
      setPartialWarning(null);
      onClose();
    } catch (e: any) {
      const msg = e?.message || 'Retry failed';
      setLocalError(msg);
      addToast({ type: 'error', title: 'Retry Failed', description: msg });
    } finally {
      setSubmitting(false);
    }
  };

  const onSubmit = async () => {
    if (!stripe || !elements || submitting) return;
    setSubmitting(true);
    setLocalError(null);
    setPartialWarning(null);
    try {
      const { error: submitError } = await elements.submit();
      if (submitError) throw new Error(submitError.message || 'Payment details incomplete');
      const { error: confirmError, setupIntent } = await stripe.confirmSetup({
        elements,
        clientSecret,
        redirect: 'if_required'
      });
      if (confirmError) {
        const code = (confirmError as any)?.code;
        throw new Error(mapStripeError(code, confirmError.message || 'Stripe confirmation failed'));
      }
      const paymentMethodId = setupIntent?.payment_method as string | undefined;
      if (!paymentMethodId) throw new Error('No payment method returned');
      lastConfirmedPaymentMethodRef.current = paymentMethodId;
      const outcome = await attemptAttachPaymentMethod(
        subscriptionId,
        paymentMethodId,
        async (subId, pmId) => authJson('/api/billing/set-default-payment-method', { method: 'POST', body: JSON.stringify({ subscriptionId: subId, paymentMethodId: pmId }) })
      );
      if (outcome.partial) {
        setPartialWarning('Card saved with Stripe, but attaching as default failed. You can retry now without re-entering details.');
        addToast({ type: 'warning', title: 'Partial Success', description: 'Payment method saved but not set as default. Retry to finish.' });
        return;
      }
      addToast({ type: 'success', title: 'Payment Method Updated', description: 'Your payment method has been updated successfully.' });
      onClose();
    } catch (e: any) {
      const msg = e?.message || 'Failed to update payment method.';
      setLocalError(msg);
      addToast({ type: 'error', title: 'Update Failed', description: msg });
    } finally { setSubmitting(false); }
  };

  return (
  <div className="p-6 bg-white border rounded-lg shadow-sm">
      <div className="space-y-4">
        <h4 className="font-medium text-lg">Update Payment Method</h4>
    <PaymentElement options={{ ...sharedPaymentElementOptions, readOnly: submitting }} />
    {localError && <p className="text-sm text-red-600">{localError}</p>}
    {partialWarning && (
      <div className="text-sm text-amber-600 flex flex-col gap-2">
        <p>{partialWarning}</p>
        <div>
          <Button size="sm" variant="outline" onClick={retrySetDefault} disabled={submitting}>Retry Attach</Button>
        </div>
      </div>
    )}
        <div className="flex gap-2 pt-2">
          <Button onClick={onSubmit} disabled={submitting || !stripe || !elements}>{submitting ? 'Updating…' : 'Update Payment Method'}</Button>
          <Button variant="outline" onClick={onClose} disabled={submitting}>Cancel</Button>
        </div>
      </div>
    </div>
  );
};

const PaymentMethodUpdateSection: React.FC<PaymentMethodUpdateSectionProps> = ({ subscriptionId, onClose, addToast }) => {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const requestedRef = useRef(false);

  useEffect(() => {
    if (requestedRef.current || !subscriptionId) return;
    requestedRef.current = true;
    let mounted = true;
    (async () => {
      try {
        setLoading(true);
        const r = await authJson<{ client_secret?: string; error?: string }>(
          '/api/billing/create-setup-intent',
          { method: 'POST', body: JSON.stringify({ subscriptionId }) }
        );
        if (!r.ok || !r.body?.client_secret) {
          throw new Error(r.body?.error || 'Failed to initialize secure payment form');
        }
        if (mounted) setClientSecret(r.body.client_secret);
      } catch (e: any) { if (mounted) setError(e?.message || 'Failed to create setup intent'); }
      finally { if (mounted) setLoading(false); }
    })();
    return () => { mounted = false; };
  }, [subscriptionId]);

  if (error) return (
    <div className="p-6 bg-red-50 border border-red-200 rounded">
      <p className="text-sm text-red-700">{error}</p>
      <div className="mt-3 flex gap-2">
        <Button size="sm" variant="outline" onClick={onClose}>Close</Button>
      </div>
    </div>
  );
  if (loading || !clientSecret) return (
    <div className="p-6 border rounded bg-white">
      <p className="text-sm text-muted-foreground">Loading secure payment form…</p>
    </div>
  );

  return (
    <Elements stripe={stripePromise} options={baseElementsOptions(clientSecret)}>
      <PaymentMethodUpdateInner subscriptionId={subscriptionId} clientSecret={clientSecret} onClose={onClose} addToast={addToast} />
    </Elements>
  );
};
// --- End Payment Method Update Components ---

interface DashboardStats {
  totalSubscriptions: number;
  activeSubscriptions: number;
  totalLicenses: number;
  activeLicenses: number;
  activatedLicenses: number;
  assignedLicenses: number;
  availableLicenses: number;
}

interface SubscriptionWithLicenses extends Subscription {
  licenses: License[];
}

interface ChargeReceipt {
  id: string;
  amount: number;
  created_at: string;
  status: string;
  receipt_url?: string;
}

interface BillingOverview {
  nextBillingDate?: string;
  billingInterval?: string;
  nextBillingAmount?: number;
}

function DashboardContent() {
  const searchParams = useSearchParams();
  const { addToast } = useToast();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<{ id?: string; email?: string } | null>(null);
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithLicenses[]>([]);
  const [selectedSubscription, setSelectedSubscription] = useState<SubscriptionWithLicenses | null>(null);
  const [receipts, setReceipts] = useState<ChargeReceipt[]>([]);
  const [billingOverview, setBillingOverview] = useState<BillingOverview | null>(null);
  const [stats, setStats] = useState<DashboardStats>({
    totalSubscriptions: 0,
    activeSubscriptions: 0,
    totalLicenses: 0,
    activeLicenses: 0,
    activatedLicenses: 0,
    assignedLicenses: 0,
    availableLicenses: 0,
  });
  const [error, setError] = useState<string | null>(null);
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState("subscription");
  const [cancelLoading, setCancelLoading] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [undoLoading, setUndoLoading] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [paymentFormLoading, setPaymentFormLoading] = useState(false);
  const [currentPaymentMethod, setCurrentPaymentMethod] = useState<any>(null);
  // Simplified payment form state (remove email and raw card fields)
  const [paymentFormData, setPaymentFormData] = useState({
    billingName: '',
    billingAddress: {
      line1: '',
      line2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'US'
    }
  });
  const [paymentUpdateLoading, setPaymentUpdateLoading] = useState(false);
  const [paymentFormErrors, setPaymentFormErrors] = useState<{[key: string]: string}>({});
  const supabase = useSupabaseClient();
  const receiptsRetry = useRef(false);

  // User type detection state
  const [userType, setUserType] = useState<UserType | null>(null);
  const [userTypeLoading, setUserTypeLoading] = useState(true);
  const [userLicense, setUserLicense] = useState<License | null>(null);

  // Ref to track latest subscriptions for polling loop
  const subscriptionsRef = useRef<SubscriptionWithLicenses[] | null>(null);
  
  // Motion setup for Excel grid background - always visible with optional scroll enhancement
  const mainRef = useRef<HTMLDivElement>(null);
  
  // Global scroll progress for grid background (optional enhancement)
  const { scrollYProgress: globalScrollProgress } = useScroll({
    target: mainRef,
    offset: ["start start", "end end"]
  });

  // Excel grid with base opacity that's always visible, plus scroll enhancement
  const gridOpacity = useTransform(globalScrollProgress, [0, 0.5, 1], [0.18, 0.22, 0.25]);
  const gridScale = useTransform(globalScrollProgress, [0, 1], [1, 1.01]);
  const smoothGridOpacity = useSpring(gridOpacity, { stiffness: 50, damping: 25 });
  const smoothGridScale = useSpring(gridScale, { stiffness: 100, damping: 30, restDelta: 0.001 });

  // Combined effect to handle auth tokens and fetch dashboard data with polling for eventual consistency
  useEffect(() => {
    const initializeDashboard = async () => {
      try {
        const paymentSuccess = (searchParams.get('payment') === 'success') || (searchParams.get('payment_success') === 'true');
        if (paymentSuccess) {
          setShowPaymentSuccess(true);
          setTimeout(() => setShowPaymentSuccess(false), 5000);
        }

        const expectedEmail = searchParams.get('expected_email')?.toLowerCase() || null;
        const exchangeCode = searchParams.get('exchange_code') || null;

        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        if (accessToken && refreshToken) {
          try { await supabase.auth.setSession({ access_token: accessToken, refresh_token: refreshToken }); } catch (error) { console.error('Error setting OAuth session:', error); }
        }

        let { data: { user: currentUser } } = await supabase.auth.getUser();

        // Secure path: only attempt silent exchange if we have an exchange_code parameter
        if (exchangeCode && expectedEmail && (!currentUser || currentUser.email?.toLowerCase() !== expectedEmail)) {
          console.log('Attempting secure session exchange with code');
          try {
            const resp = await fetch('/api/auth/exchange-session', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ code: exchangeCode }) });
            const json = await resp.json();
            if (resp.ok && json.success) {
              if (json.access_token && json.refresh_token) {
                const setRes = await supabase.auth.setSession({ access_token: json.access_token, refresh_token: json.refresh_token });
                if (setRes.error) console.error('Error setting exchanged session tokens', setRes.error);
              } else if (json.action_link) {
                // Fallback redirect if tokens not embedded
                window.location.href = json.action_link; return;
              }
              ({ data: { user: currentUser } } = await supabase.auth.getUser());
            } else {
              console.warn('Secure session exchange failed or invalid code', json.error);
            }
          } catch (ex) { console.error('Secure session exchange exception', ex); }
        } else if (expectedEmail && !exchangeCode) {
          console.log('Expected email present but no exchange code; skipping silent session exchange');
        }

        await fetchDashboardData({ paymentContext: paymentSuccess });

        if (paymentSuccess && (!subscriptionsRef.current || subscriptionsRef.current.length === 0)) {
          for (let attempt = 1; attempt <= 5; attempt++) {
            await new Promise(r => setTimeout(r, 2500));
            await fetchDashboardData({ paymentContext: paymentSuccess, silent: true });
            if (subscriptionsRef.current && subscriptionsRef.current.length > 0) break;
          }
        }
      } catch (error) {
        console.error('Dashboard initialization error:', error);
        setError('Failed to initialize dashboard');
      }
    };

    initializeDashboard();
  }, [searchParams, supabase]);

  // Fetch payment method when subscription changes - COMMENTED OUT due to 404 error
  /* 
  useEffect(() => {
    if (selectedSubscription) {
      fetchCurrentPaymentMethod();
    }
  }, [selectedSubscription]);
  */

  const fetchDashboardData = async ({ paymentContext = false, silent = false }: { paymentContext?: boolean; silent?: boolean } = {}) => {
    try {
      if (!silent) setLoading(true);
      
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        if (paymentContext) {
          // Graceful state while waiting for auth session (e.g., after redirect to payment-success)
          setError(null);
          return;
        }
        throw new Error('No authenticated user found');
      }
      setUser({ id: user.id, email: user.email });

      // Detect user type early to determine what data to fetch
      if (!silent) setUserTypeLoading(true);
      const userTypeResult = await detectUserType(user.id, supabase);
      setUserType(userTypeResult.type);
      if (userTypeResult.license) {
        setUserLicense(userTypeResult.license);
      }
      if (!silent) setUserTypeLoading(false);

      // For team licensees, skip expensive subscription/billing data fetching
      if (userTypeResult.type === UserType.TEAM_LICENSEE) {
        if (!silent) setLoading(false);
        return;
      }

      // Fetch subscriptions with licenses
      const { data: subscriptionsData } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id);

      if (subscriptionsData) {
        const subscriptionsWithLicenses = await Promise.all(
          subscriptionsData.map(async (sub: Subscription) => {
            const { data: licensesData } = await supabase
              .from('licenses')
              .select('*')
              .eq('subscription_id', sub.id);
            
            return {
              ...sub,
              licenses: licensesData || []
            };
          })
        );

  setSubscriptions(subscriptionsWithLicenses);
  subscriptionsRef.current = subscriptionsWithLicenses;
        if (subscriptionsWithLicenses.length > 0) {
          setSelectedSubscription(subscriptionsWithLicenses[0]);
        }

        // Calculate stats
        const totalSubscriptions = subscriptionsWithLicenses.length;
        const activeSubscriptions = subscriptionsWithLicenses.filter(s => s.status === 'active').length;
        const allLicenses = subscriptionsWithLicenses.flatMap(s => s.licenses);
        const totalLicenses = allLicenses.length;
        const activeLicenses = allLicenses.filter(l => l.status === 'active').length;
        const activatedLicenses = allLicenses.filter(l => l.email && l.email !== 'not-activated' && l.email !== '').length;
        const assignedLicenses = allLicenses.filter(l => l.email && l.email !== 'not-activated' && l.email !== '').length;
        const availableLicenses = allLicenses.filter(l => !l.email || l.email === 'not-activated' || l.email === '').length;

        setStats({
          totalSubscriptions,
          activeSubscriptions,
          totalLicenses,
          activeLicenses,
          activatedLicenses,
          assignedLicenses,
          availableLicenses,
        });

        // Fetch billing data
        await fetchBillingData(user.id, subscriptionsWithLicenses);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      if (!silent) setLoading(false);
    }
  };

  const fetchBillingData = async (userId: string, subscriptions: Subscription[]) => {
    try {
      // Fetch receipts for the user (auth)
      const receiptsResponse = await authJson<any>(
        '/api/billing/receipts',
        { method: 'POST', body: JSON.stringify({ userId }) }
      );
      if (receiptsResponse.ok) {
        const receiptsData = receiptsResponse.body;
        const parsed = Array.isArray(receiptsData) ? receiptsData : (receiptsData?.receipts || []);
        setReceipts(parsed);
        if (!receiptsRetry.current && parsed.length === 0) {
          receiptsRetry.current = true;
          setTimeout(() => {
            authJson<any>('/api/billing/receipts', { method: 'POST', body: JSON.stringify({ userId }) })
              .then(r => {
                if (r.ok && r.body) {
                  const again = Array.isArray(r.body) ? r.body : (r.body.receipts || []);
                  if (again.length) setReceipts(again);
                }
              }).catch(() => {});
          }, 4000);
        }
      }

  // Fetch billing overview for the selected subscription when available; otherwise, first active
  const targetSubscription = selectedSubscription || subscriptions.find(s => s.status === 'active');
  if (targetSubscription) {
        try {
          // Fetch upcoming invoice for billing amount and interval
          const upcomingResponse = await authJson<any>(
            '/api/billing/upcoming-invoice',
            { method: 'POST', body: JSON.stringify({ subscriptionId: targetSubscription.stripe_subscription_id }) }
          );
          let nextBillingAmount = 0; let actualBillingInterval: any = null;
          if (upcomingResponse.ok && upcomingResponse.body) {
            nextBillingAmount = (upcomingResponse.body.amount_due || 0) / 100;
            actualBillingInterval = upcomingResponse.body.billing_interval;
          }

          // If we couldn't get billing interval, try the subscription details endpoint
          if (!actualBillingInterval) {
            try {
              const detailsResponse = await authJson<any>(
                '/api/billing/subscription-details',
                { method: 'POST', body: JSON.stringify({ subscriptionId: targetSubscription.stripe_subscription_id }) }
              );
              if (detailsResponse.ok && detailsResponse.body) {
                actualBillingInterval = detailsResponse.body.billing_interval;
              }
            } catch (detailsError) {
              console.error('Error fetching subscription details:', detailsError);
            }
          }

          setBillingOverview({
      nextBillingDate: targetSubscription.current_period_end,
            billingInterval: actualBillingInterval || 'unknown',
            // Use the amount from Stripe; do not zero based on local cancel flag, the server route returns 0 if cancel_at_period_end
            nextBillingAmount: nextBillingAmount,
          });
        } catch (error) {
          console.error('Error fetching billing overview:', error);
          
          // Final fallback - try to get at least the billing interval
          let fallbackInterval = null;
          try {
            const detailsResponse = await authJson<any>(
              '/api/billing/subscription-details',
              { method: 'POST', body: JSON.stringify({ subscriptionId: targetSubscription.stripe_subscription_id }) }
            );
            if (detailsResponse.ok && detailsResponse.body) {
              fallbackInterval = detailsResponse.body.billing_interval;
            }
          } catch (fallbackError) {
            console.error('Error fetching fallback subscription details:', fallbackError);
          }

          // Set basic billing overview without amount
          setBillingOverview({
            nextBillingDate: targetSubscription.current_period_end,
            billingInterval: fallbackInterval || 'unknown',
            nextBillingAmount: undefined,
          });
        }
      }
    } catch (error) {
      console.error('Error fetching billing data:', error);
    }
  };

  // Focused fetch just for the current selected subscription's billing overview
  const fetchBillingOverviewFor = async (sub: Subscription) => {
    try {
      // Upcoming invoice has authoritative amount and interval
      const upcomingResponse = await authJson<any>(
        '/api/billing/upcoming-invoice',
        { method: 'POST', body: JSON.stringify({ subscriptionId: sub.stripe_subscription_id }) }
      );
      let nextBillingAmount: number | undefined = undefined; let interval: string | null = null;
      if (upcomingResponse.ok && upcomingResponse.body) {
        nextBillingAmount = (upcomingResponse.body.amount_due ?? 0) / 100;
        interval = upcomingResponse.body.billing_interval ?? null;
      }

      // If interval missing, fetch subscription details
      if (!interval) {
        try {
          const detailsResponse = await authJson<any>(
            '/api/billing/subscription-details',
            { method: 'POST', body: JSON.stringify({ subscriptionId: sub.stripe_subscription_id }) }
          );
          if (detailsResponse.ok && detailsResponse.body) interval = detailsResponse.body.billing_interval ?? null;
        } catch {}
      }

      setBillingOverview({
        nextBillingDate: sub.current_period_end,
        billingInterval: interval || 'unknown',
        nextBillingAmount: nextBillingAmount,
      });
    } catch (e) {
      console.error('Error fetching billing overview for subscription:', e);
    }
  };

  // Refresh receipts when switching subscription (and we have a user)
  useEffect(() => {
    if (user?.id && selectedSubscription) {
      (async () => {
        try {
          const res = await authJson<any>(
            '/api/billing/receipts',
            { method: 'POST', body: JSON.stringify({ userId: user.id }) }
          );
          if (res.ok && res.body) setReceipts(Array.isArray(res.body) ? res.body : (res.body.receipts || []));
        } catch {}
      })();
  // Also refresh billing overview for the newly selected subscription
  fetchBillingOverviewFor(selectedSubscription);
    }
  }, [selectedSubscription?.stripe_subscription_id]);

  const handleManageSubscription = async () => {
    try {
      setLoading(true);
      const response = await apiClient.createBillingPortalSession(window.location.href);
      if (isApiSuccess(response)) {
        window.location.href = response.data.url;
      } else {
        addToast({
          type: "error", 
          title: "Billing Portal Error",
          description: "Failed to redirect to billing portal"
        });
      }
    } catch (error) {
      console.error('Error managing subscription:', error);
      addToast({
        type: "error", 
        title: "Billing Portal Error",
        description: "Failed to redirect to billing portal"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    setShowCancelDialog(true);
  };

  const confirmCancelSubscription = async () => {
    if (!selectedSubscription) return;

    try {
      setCancelLoading(true);
      // Use Stripe API to cancel at period end
      const response = await authJson<any>(
        '/api/billing/cancel-subscription',
        { method: 'POST', body: JSON.stringify({ subscriptionId: selectedSubscription.stripe_subscription_id, cancelAtPeriodEnd: true }) }
      );

      if (response.ok) {
        // Optimistically flip cancel flag locally so UI updates immediately
  setSelectedSubscription((prev: SubscriptionWithLicenses | null) => prev ? { ...prev, cancel_at_period_end: true } as any : prev);
  setSubscriptions((prev: SubscriptionWithLicenses[]) => prev.map((s: SubscriptionWithLicenses) => s.id === selectedSubscription.id ? ({ ...s, cancel_at_period_end: true } as any) : s));
        setShowCancelDialog(false);
  // Immediately refresh billing overview for accurate Next Amount (should now be 0)
  await fetchBillingOverviewFor({ ...selectedSubscription, cancel_at_period_end: true } as any);
  // Avoid immediate full dashboard refresh which may race with Stripe webhook
  // and temporarily revert the local cancel flag. A full refresh will occur naturally
  // on next navigation/load or can be triggered manually if needed.
        addToast({
          type: "success",
          title: "Subscription Scheduled for Cancellation",
          description: "Your subscription will be canceled at the end of the current billing period."
        });
      } else {
        addToast({
          type: "error",
          title: "Cancellation Failed",
          description: "Failed to cancel subscription. Please try again or contact support."
        });
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      addToast({
        type: "error",
        title: "Cancellation Failed", 
        description: "Failed to cancel subscription. Please try again or contact support."
      });
    } finally {
      setCancelLoading(false);
    }
  };

  const handleUndoCancellation = async () => {
    if (!selectedSubscription) return;

    try {
      setUndoLoading(true);
      const response = await authJson<any>(
        '/api/billing/undo-cancellation',
        { method: 'POST', body: JSON.stringify({ subscriptionId: selectedSubscription.stripe_subscription_id }) }
      );

      if (response.ok) {
        // Optimistically clear cancel flag locally
  setSelectedSubscription((prev: SubscriptionWithLicenses | null) => prev ? { ...prev, cancel_at_period_end: false } as any : prev);
  setSubscriptions((prev: SubscriptionWithLicenses[]) => prev.map((s: SubscriptionWithLicenses) => s.id === selectedSubscription.id ? ({ ...s, cancel_at_period_end: false } as any) : s));
  // Immediately refresh billing overview to show real next invoice amount
  await fetchBillingOverviewFor({ ...selectedSubscription, cancel_at_period_end: false } as any);
  // Avoid immediate full dashboard refresh which may race with Stripe webhook
  // and temporarily revert the local flag.
        addToast({
          type: "success",
          title: "Cancellation Undone",
          description: "Your subscription cancellation has been undone successfully!"
        });
      } else {
        addToast({
          type: "error",
          title: "Undo Failed",
          description: "Failed to undo subscription cancellation. Please try again or contact support."
        });
      }
    } catch (error) {
      console.error('Error undoing subscription cancellation:', error);
      addToast({
        type: "error",
        title: "Undo Failed",
        description: "Failed to undo subscription cancellation. Please try again or contact support."
      });
    } finally {
      setUndoLoading(false);
    }
  };

  // Utility function to calculate days until subscription ends
  const getDaysUntilEnd = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Check if subscription is cancelled but still active
  const isSubscriptionCancelled = () => {
    return selectedSubscription?.cancel_at_period_end === true;
  };

  // Format billing interval for display
  const formatBillingInterval = (interval: string | null | undefined) => {
    switch (interval) {
      case 'month':
        return 'monthly';
      case 'year':
        return 'annual';
      case 'quarter':
        return 'quarterly';
      case '3month':
      case '3_month':
      case '3months':
      case '3_months':
        return 'quarterly';
      case 'week':
        return 'weekly';
      case 'day':
        return 'daily';
      default:
        return interval || 'unknown';
    }
  };

  // (Removed inline PaymentMethodUpdater / PaymentMethodFormShell to prevent remount-driven fetch spam.)

  return (
    <div ref={mainRef} className="relative overflow-hidden">
      {/* Excel Grid Background - Brand Authentic - Always Visible */}
      <motion.div 
        className="absolute inset-0 -z-1 overflow-hidden"
        style={{ 
          opacity: smoothGridOpacity,
          scale: smoothGridScale
        }}
      >
        <svg
          className="absolute inset-0 w-full h-full"
          xmlns="http://www.w3.org/2000/svg"
          style={{ 
            width: '100%', 
            height: '100%',
            minHeight: '100vh'
          }}
        >
          <defs>
            <pattern
              id="dashboard-excel-grid"
              x="0"
              y="0"
              width="40"
              height="24"
              patternUnits="userSpaceOnUse"
            >
              <rect
                x="0"
                y="0"
                width="40"
                height="24"
                fill="none"
                stroke="#10B981"
                strokeWidth="0.5"
                opacity="0.8"
              />
              {/* Subtle accent lines every 5th row/column */}
              <rect
                x="0"
                y="0"
                width="200"
                height="120"
                fill="none"
                stroke="#10B981"
                strokeWidth="1.0"
                opacity="0.9"
                patternUnits="userSpaceOnUse"
              />
            </pattern>
          </defs>
          <rect
            x="0"
            y="0"
            width="100%"
            height="100%"
            fill="url(#dashboard-excel-grid)"
          />
        </svg>
      </motion.div>

      {/* Dashboard Content */}
      <div className="space-y-6 relative z-10">
        
        {/* Loading state for user type detection */}
        {userTypeLoading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading your dashboard...</p>
          </div>
        )}

        {/* Team Licensee Dashboard - Simplified view for users with assigned licenses */}
        {!userTypeLoading && userType === UserType.TEAM_LICENSEE && userLicense && (
          <TeamLicenseeDashboard 
            license={userLicense as any} 
            userEmail={user?.email} 
          />
        )}

        {/* No License Message */}
        {!userTypeLoading && userType === UserType.NO_LICENSE && (
          <div className="text-center py-16 space-y-6">
            <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center">
              <span className="text-4xl">🔒</span>
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">No Active License</h2>
              <p className="mt-2 text-gray-600 max-w-md mx-auto">
                You don't currently have an active QuantBoost license. Contact your team administrator or purchase a subscription to get started.
              </p>
            </div>
            <div className="flex gap-4 justify-center">
              <Button asChild>
                <a href="/pricing" target="_blank" rel="noopener noreferrer">
                  View Pricing
                </a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/support" target="_blank" rel="noopener noreferrer">
                  Contact Support
                </a>
              </Button>
            </div>
          </div>
        )}

        {/* Admin & Subscriber Dashboard - Full featured dashboard */}
        {!userTypeLoading && userType !== UserType.TEAM_LICENSEE && (
          <>
            {/* Welcome Header */}
            <div className="flex justify-between items-center">
              <div>
                {/* Removed Dashboard H1 as requested */}
                <p className="text-muted-foreground">
                  {`Welcome back${user?.email ? `, ${user.email}` : ''}!`}
                  {userType && (
                    <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      {getUserTypeDisplayName(userType)}
                    </span>
                  )}
                </p>
              </div>
            </div>

      {showPaymentSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-600 text-xl">✅</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">
                Payment Successful!
              </h3>
              <div className="mt-1 text-sm text-green-700">
                <p>Your subscription is now active. Welcome to QuantBoost!</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Subscriptions</CardTitle>
            <span className="text-2xl">📊</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSubscriptions}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeSubscriptions} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Licenses</CardTitle>
            <span className="text-2xl">🎫</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalLicenses}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeLicenses} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assigned</CardTitle>
            <span className="text-2xl">👤</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.assignedLicenses}</div>
            <p className="text-xs text-muted-foreground">
              Ready for activation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <span className="text-2xl">🆓</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.availableLicenses}</div>
            <p className="text-xs text-muted-foreground">
              Ready to assign
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Team Assignment Section - Show only for team subscriptions */}
      {selectedSubscription && selectedSubscription.quantity > 1 && (
        <TeamAssignmentCard 
          subscription={selectedSubscription}
          onDataRefresh={fetchDashboardData}
        />
      )}

      {/* Main Content */}
      <div className="space-y-6">
        {/* Billing Information */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Billing Information</CardTitle>
            <CardDescription>
              Manage your billing and view payment history
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
      {/* Compact Billing Overview with Actions - always render when we have a selected subscription */}
      {selectedSubscription && (
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <p className="text-xs font-medium text-gray-600">
                      {isSubscriptionCancelled() ? 'Subscription Ends' : 'Next Billing'}
                    </p>
                    <p className="text-sm font-bold text-gray-900">
            {billingOverview?.nextBillingDate ? (
                        isSubscriptionCancelled() ? (
              `in ${getDaysUntilEnd(billingOverview.nextBillingDate)} days`
                        ) : (
              new Date(billingOverview.nextBillingDate).toLocaleDateString()
                        )
                      ) : 'N/A'}
                    </p>
                  </div>
                  <Separator orientation="vertical" className="h-8" />
                  <Badge 
                    variant={isSubscriptionCancelled() ? "destructive" : "secondary"} 
                    className="text-xs"
                  >
                    {isSubscriptionCancelled() 
            ? 'Ending soon'
            : `${formatBillingInterval(billingOverview?.billingInterval)} billing`
                    }
                  </Badge>
          {billingOverview?.nextBillingAmount !== undefined && (
                    // ...existing amount UI...
                    <>
                      <Separator orientation="vertical" className="h-8" />
                      <div className="text-center">
                        <p className="text-xs font-medium text-gray-600">Next Amount</p>
                        <p className="text-sm font-bold text-gray-900">
              ${billingOverview.nextBillingAmount.toFixed(2)}
                        </p>
                      </div>
                    </>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    disabled={loading || !selectedSubscription || (showPaymentForm && paymentFormLoading)}
                    onClick={() => setShowPaymentForm(!showPaymentForm)}
                  >
                    {showPaymentForm ? (paymentFormLoading ? 'Loading…' : 'Cancel Update') : 'Update Payment'}
                  </Button>
                  {isSubscriptionCancelled() ? (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleUndoCancellation}
                      disabled={!selectedSubscription || undoLoading}
                      className="border-amber-300 text-amber-700 hover:bg-amber-100"
                    >
                      {undoLoading ? 'Processing...' : 'Undo Cancellation'}
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelSubscription}
                      disabled={!selectedSubscription || cancelLoading}
                      className="border-red-300 text-red-700 hover:bg-red-100"
                    >
                      {cancelLoading ? 'Canceling...' : 'Cancel Subscription'}
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* Update Payment Method (moved above Payment History) */}
            {showPaymentForm && selectedSubscription && (
              <PaymentMethodUpdateSection
                subscriptionId={selectedSubscription.stripe_subscription_id}
                onClose={() => setShowPaymentForm(false)}
                addToast={addToast}
              />
            )}

            {/* Payment History */}
            <div>
              <h4 className="text-lg font-medium mb-3">Payment History</h4>
              {receipts.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-sm text-muted-foreground">No payment receipts found.</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Payment receipts will appear here once you have made payments.
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Receipt</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {receipts.map((receipt) => (
                      <TableRow key={receipt.id}>
                        <TableCell>{new Date(receipt.created_at).toLocaleDateString()}</TableCell>
                        <TableCell>${(receipt.amount / 100).toFixed(2)}</TableCell>
                        <TableCell>
                          <span className={`text-xs px-2 py-1 rounded-full ${getReceiptStatusColor(receipt.status)}`}>
                            {receipt.status === 'succeeded' ? 'Paid' : receipt.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          {receipt.receipt_url ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(receipt.receipt_url, '_blank')}
                            >
                              View Receipt
                            </Button>
                          ) : (
                            <span className="text-gray-400 text-sm">Not available</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cancellation Confirmation Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel your subscription? You will continue to have access until{' '}
              {billingOverview?.nextBillingDate ? (
                new Date(billingOverview.nextBillingDate).toLocaleDateString()
              ) : (
                'the end of your current billing period'
              )}
              .
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCancelDialog(false)}>
              Keep Subscription
            </Button>
            <Button 
              variant="destructive" 
              onClick={confirmCancelSubscription}
              disabled={cancelLoading}
            >
              {cancelLoading ? 'Canceling...' : 'Yes, Cancel Subscription'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
          </>
        )}
      </div>
    </div>
  );
}

export default function Dashboard() {
  const [user, setUser] = useState<{ email?: string } | null>(null);
  const supabase = useSupabaseClient();

  // Get current user for header
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUser({ email: user.email });
      }
    };
    getUser();
  }, [supabase]);

  return (
    <>
      <AuthHeader userEmail={user?.email} showDownload={true} />
      <div className="max-w-4xl mx-auto p-6 pt-20">
        <Suspense fallback={<div>Loading...</div>}>
          <DashboardContent />
        </Suspense>
      </div>
    </>
  );
}

function getReceiptStatusColor(status: string) {
  switch (status) {
    case 'succeeded':
      return 'bg-green-100 text-green-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'failed':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
