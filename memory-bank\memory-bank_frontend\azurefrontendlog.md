2025-08-20T05:27:30.4286979Z   fullName: 'NO_FULL_NAME',
2025-08-20T05:27:30.4287032Z   customerEmail: '<EMAIL>',
2025-08-20T05:27:30.4287079Z   customerKeys: [
2025-08-20T05:27:30.4287128Z     'id','object',
2025-08-20T05:27:30.4287176Z     'address',               'balance',
2025-08-20T05:27:30.4287317Z     'created',               'currency',
2025-08-20T05:27:30.4287368Z     'default_source',        'delinquent',
2025-08-20T05:27:30.4287416Z     'description',           'discount',
2025-08-20T05:27:30.4287464Z     'email',                 'invoice_prefix',
2025-08-20T05:27:30.4287511Z     'invoice_settings',      'livemode',
2025-08-20T05:27:30.4287560Z     'metadata',              'name',
2025-08-20T05:27:30.4287608Z     'next_invoice_sequence', 'phone',
2025-08-20T05:27:30.4287655Z     'preferred_locales',     'shipping',
2025-08-20T05:27:30.4287703Z     'tax_exempt',            'test_clock'
2025-08-20T05:27:30.4287748Z   ],
2025-08-20T05:27:30.4287794Z   hasName: true,
2025-08-20T05:27:30.4287864Z   nameValue: null
2025-08-20T05:27:30.4287911Z }
2025-08-20T05:27:30.4287966Z 🔍 PARSED NAME DEBUG - Customer: cus_StW2Rh1OobylEO {
2025-08-20T05:27:30.4288014Z   originalFullName: null,
2025-08-20T05:27:30.4288061Z   parsedFirstName: 'EMPTY_FIRST',
2025-08-20T05:27:30.4288109Z   parsedLastName: 'EMPTY_LAST',
2025-08-20T05:27:30.4288158Z   parseResult: { firstName: null, lastName: null }
2025-08-20T05:27:30.4288202Z }
2025-08-20T05:27:30.4288258Z 📧 Customer details - email: <EMAIL>, firstName: null, lastName: null
2025-08-20T05:27:30.4288309Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1Rxj0fE6FvhUKV1br9ThSnUX {
2025-08-20T05:27:30.4288358Z   stripeCustomerId: 'cus_StW2Rh1OobylEO',
2025-08-20T05:27:30.4288406Z   extractedEmail: '<EMAIL>',
2025-08-20T05:27:30.4288477Z   extractedFirstName: 'NO_FIRST_NAME',
2025-08-20T05:27:30.4288529Z   extractedLastName: 'NO_LAST_NAME',
2025-08-20T05:27:30.4288577Z   subscriptionStatus: 'incomplete_expired',
2025-08-20T05:27:30.4288624Z   hasCustomerName: false
2025-08-20T05:27:30.4288668Z }
2025-08-20T05:27:30.4288719Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-20T05:27:30.4288777Z 🔍 ensureUserProfile called with: customer=cus_StW2Rh1OobylEO, email=<EMAIL>, firstName=null, lastName=null
2025-08-20T05:27:30.4288830Z 🔍 Looking for existing profile by stripe_customer_id: cus_StW2Rh1OobylEO
2025-08-20T05:27:30.4288884Z ✅ Payment event logged for payment_intent.canceled: pi_3Rxj0gE6FvhUKV1b0C0boqdu
2025-08-20T05:27:30.4588581Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:27:30.4588964Z ✅ Stripe event verified: customer.subscription.updated ID: evt_1Ry4YWE6FvhUKV1bdNd7u0OO
2025-08-20T05:27:31.1358094Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:27:31.1358567Z ✅ Found existing profile by stripe_customer_id: 0206e83a-fa24-4000-9096-578cf8b92283
2025-08-20T05:27:31.1358635Z ✅ User profile resolved: 0206e83a-fa24-4000-9096-578cf8b92283
2025-08-20T05:27:31.1358690Z 👤 Updating profile team admin status: false
2025-08-20T05:27:31.1794933Z ✅ Webhook event logged: evt_1Ry4YUE6FvhUKV1bnEpWQpMZ with status: processing
2025-08-20T05:27:31.1795374Z 👉 Handling customer.subscription.updated
2025-08-20T05:27:31.1795446Z 🔗 Subscription: sub_1Rxj0lE6FvhUKV1bA0sLept2, Customer: cus_StW2tAGdJDZog7, Status: incomplete_expired
2025-08-20T05:27:31.1795499Z 💰 Product: prod_S6Fn893jGxRhKk, Quantity: 1
2025-08-20T05:27:31.1795549Z 📅 Billing Period Debug: {
2025-08-20T05:27:31.1795694Z   current_period_start: undefined,
2025-08-20T05:27:31.1795745Z   current_period_end: undefined,
2025-08-20T05:27:31.1795794Z   current_period_start_iso: null,
2025-08-20T05:27:31.1795844Z   current_period_end_iso: null,
2025-08-20T05:27:31.1795891Z   created: 1755584831,
2025-08-20T05:27:31.1795940Z   start_date: 1755584831,
2025-08-20T05:27:31.1795988Z   billing_cycle_anchor: 1755584831,
2025-08-20T05:27:31.1796035Z   status: 'incomplete_expired'
2025-08-20T05:27:31.1796080Z }
2025-08-20T05:27:31.2162525Z ✅ Found existing profile by stripe_customer_id: 2457f382-5a32-4cb1-98a2-85ff01fba63d
2025-08-20T05:27:31.2266179Z ✅ User profile resolved: 2457f382-5a32-4cb1-98a2-85ff01fba63d
2025-08-20T05:27:31.2266588Z 👤 Updating profile team admin status: false
2025-08-20T05:27:31.3893881Z ✅ Profile updated - team admin: false
2025-08-20T05:27:31.4000133Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-20T05:27:31.4711096Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:27:31.5115106Z ✅ Stripe event verified: payment_intent.canceled ID: evt_3Rxj0rE6FvhUKV1b0pfutBPr
2025-08-20T05:27:31.6595986Z ✅ Profile updated - team admin: false
2025-08-20T05:27:31.6596492Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-20T05:27:31.6983595Z 📅 Price interval: year, interval_count: 1
2025-08-20T05:27:31.6984001Z 👤 Fetching customer details for: cus_StW2tAGdJDZog7
2025-08-20T05:27:31.6984063Z ✅ Payment event logged for payment_intent.canceled: pi_3Rxj0gE6FvhUKV1b0J2mdmDa
2025-08-20T05:27:31.8176946Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-20T05:27:31.8177650Z 💾 Creating/updating subscription record in database
2025-08-20T05:27:31.8812320Z ✅ Webhook event logged: evt_1Ry4YWE6FvhUKV1bdNd7u0OO with status: processing
2025-08-20T05:27:31.8812796Z 👉 Handling customer.subscription.updated
2025-08-20T05:27:31.8812863Z 🔗 Subscription: sub_1Rxj0rE6FvhUKV1bqwVUt7b7, Customer: cus_StW2i5NFhMNAOw, Status: incomplete_expired
2025-08-20T05:27:31.8812919Z 💰 Product: prod_S6Fn893jGxRhKk, Quantity: 1
2025-08-20T05:27:31.8812971Z 📅 Billing Period Debug: {
2025-08-20T05:27:31.8813114Z   current_period_start: undefined,
2025-08-20T05:27:31.8813167Z   current_period_end: undefined,
2025-08-20T05:27:31.8813215Z   current_period_start_iso: null,
2025-08-20T05:27:31.8813263Z   current_period_end_iso: null,
2025-08-20T05:27:31.8813311Z   created: 1755584837,
2025-08-20T05:27:31.8813361Z   start_date: 1755584837,
2025-08-20T05:27:31.8813409Z   billing_cycle_anchor: 1755584837,
2025-08-20T05:27:31.8813457Z   status: 'incomplete_expired'
2025-08-20T05:27:31.8813502Z }
2025-08-20T05:27:32.1297086Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_StW2tAGdJDZog7 {
2025-08-20T05:27:32.1297521Z   customerName: 'NO_NAME',
2025-08-20T05:27:32.1297661Z   fullName: 'NO_FULL_NAME',
2025-08-20T05:27:32.1297717Z   customerEmail: '<EMAIL>',
2025-08-20T05:27:32.1297766Z   customerKeys: [
2025-08-20T05:27:32.1297815Z     'id',                    'object',
2025-08-20T05:27:32.1297864Z     'address',               'balance',
2025-08-20T05:27:32.1297912Z     'created',               'currency',
2025-08-20T05:27:32.1297961Z     'default_source',        'delinquent',
2025-08-20T05:27:32.1298008Z     'description',           'discount',
2025-08-20T05:27:32.1298057Z     'email',                 'invoice_prefix',
2025-08-20T05:27:32.1298105Z     'invoice_settings',      'livemode',
2025-08-20T05:27:32.1298153Z     'metadata',              'name',
2025-08-20T05:27:32.1298226Z     'next_invoice_sequence', 'phone',
2025-08-20T05:27:32.1298275Z     'preferred_locales',     'shipping',
2025-08-20T05:27:32.1298323Z     'tax_exempt',            'test_clock'
2025-08-20T05:27:32.1298368Z   ],
2025-08-20T05:27:32.1298415Z   hasName: true,
2025-08-20T05:27:32.1298461Z   nameValue: null
2025-08-20T05:27:32.1298506Z }
2025-08-20T05:27:32.1298559Z 🔍 PARSED NAME DEBUG - Customer: cus_StW2tAGdJDZog7 {
2025-08-20T05:27:32.1298607Z   originalFullName: null,
2025-08-20T05:27:32.1298655Z   parsedFirstName: 'EMPTY_FIRST',
2025-08-20T05:27:32.1298704Z   parsedLastName: 'EMPTY_LAST',
2025-08-20T05:27:32.1298754Z   parseResult: { firstName: null, lastName: null }
2025-08-20T05:27:32.1298820Z }
2025-08-20T05:27:32.1298878Z 📧 Customer details - email: <EMAIL>, firstName: null, lastName: null
2025-08-20T05:27:32.1298930Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1Rxj0lE6FvhUKV1bA0sLept2 {
2025-08-20T05:27:32.1298979Z   stripeCustomerId: 'cus_StW2tAGdJDZog7',
2025-08-20T05:27:32.1299028Z   extractedEmail: '<EMAIL>',
2025-08-20T05:27:32.1299076Z   extractedFirstName: 'NO_FIRST_NAME',
2025-08-20T05:27:32.1299124Z   extractedLastName: 'NO_LAST_NAME',
2025-08-20T05:27:32.1299173Z   subscriptionStatus: 'incomplete_expired',
2025-08-20T05:27:32.1299220Z   hasCustomerName: false
2025-08-20T05:27:32.1299265Z }
2025-08-20T05:27:32.1299317Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-20T05:27:32.1299400Z 🔍 ensureUserProfile called with: customer=cus_StW2tAGdJDZog7, email=<EMAIL>, firstName=null, lastName=null
2025-08-20T05:27:32.1299456Z 🔍 Looking for existing profile by stripe_customer_id: cus_StW2tAGdJDZog7
2025-08-20T05:27:32.5174662Z 📅 Price interval: year, interval_count: 1
2025-08-20T05:27:32.5175160Z 👤 Fetching customer details for: cus_StW2i5NFhMNAOw
2025-08-20T05:27:33.1646527Z ❌ Error updating subscription: {
2025-08-20T05:27:33.1646992Z   code: '22P02',
2025-08-20T05:27:33.1648127Z   details: null,
2025-08-20T05:27:33.1648179Z   hint: null,
2025-08-20T05:27:33.1648238Z   message: 'invalid input value for enum subscription_status: "incomplete_expired"'
2025-08-20T05:27:33.1648284Z }
2025-08-20T05:27:33.4268034Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-20T05:27:33.4667509Z 💾 Creating/updating subscription record in database
2025-08-20T05:27:33.6779426Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_StW2i5NFhMNAOw {
2025-08-20T05:27:33.6780157Z   customerName: 'NO_NAME',
2025-08-20T05:27:33.6780218Z   fullName: 'NO_FULL_NAME',
2025-08-20T05:27:33.6780270Z   customerEmail: '<EMAIL>',
2025-08-20T05:27:33.6780318Z   customerKeys: [
2025-08-20T05:27:33.6780368Z     'id',                    'object',
2025-08-20T05:27:33.6780418Z     'address',               'balance',
2025-08-20T05:27:33.6780468Z     'created',               'currency',
2025-08-20T05:27:33.6780518Z     'default_source',        'delinquent',
2025-08-20T05:27:33.6780567Z     'description',           'discount',
2025-08-20T05:27:33.6780615Z     'email','invoice_prefix',
2025-08-20T05:27:33.6780676Z     'invoice_settings',      'livemode',
2025-08-20T05:27:33.6780751Z     'metadata',              'name',
2025-08-20T05:27:33.6780800Z     'next_invoice_sequence', 'phone',
2025-08-20T05:27:33.6780847Z     'preferred_locales',     'shipping',
2025-08-20T05:27:33.6780896Z     'tax_exempt',            'test_clock'
2025-08-20T05:27:33.6780942Z   ],
2025-08-20T05:27:33.6780989Z   hasName: true,
2025-08-20T05:27:33.6781036Z   nameValue: null
2025-08-20T05:27:33.6781082Z }
2025-08-20T05:27:33.6870038Z 🔍 PARSED NAME DEBUG - Customer: cus_StW2i5NFhMNAOw {
2025-08-20T05:27:33.6870338Z   originalFullName: null,
2025-08-20T05:27:33.6870398Z   parsedFirstName: 'EMPTY_FIRST',
2025-08-20T05:27:33.6870502Z   parsedLastName: 'EMPTY_LAST',
2025-08-20T05:27:33.6870567Z   parseResult: { firstName: null, lastName: null }
2025-08-20T05:27:33.6870646Z }
2025-08-20T05:27:33.6870707Z 📧 Customer details - email: <EMAIL>, firstName: null, lastName: null
2025-08-20T05:27:33.6870761Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1Rxj0rE6FvhUKV1bqwVUt7b7 {
2025-08-20T05:27:33.6870812Z   stripeCustomerId: 'cus_StW2i5NFhMNAOw',
2025-08-20T05:27:33.6870863Z   extractedEmail: '<EMAIL>',
2025-08-20T05:27:33.6870912Z   extractedFirstName: 'NO_FIRST_NAME',
2025-08-20T05:27:33.6870961Z   extractedLastName: 'NO_LAST_NAME',
2025-08-20T05:27:33.6871011Z   subscriptionStatus: 'incomplete_expired',
2025-08-20T05:27:33.6871058Z   hasCustomerName: false
2025-08-20T05:27:33.6871104Z }
2025-08-20T05:27:33.6871158Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-20T05:27:33.6871230Z 🔍 ensureUserProfile called with: customer=cus_StW2i5NFhMNAOw, email=<EMAIL>, firstName=null, lastName=null
2025-08-20T05:27:33.6871287Z 🔍 Looking for existing profile by stripe_customer_id: cus_StW2i5NFhMNAOw
2025-08-20T05:27:34.1810160Z ✅ Webhook event logged: evt_3Rxj0rE6FvhUKV1b0pfutBPr with status: processing
2025-08-20T05:27:34.1997103Z 👉 Handling payment_intent.canceled
2025-08-20T05:27:34.1997533Z 🚫 Payment canceled: pi_3Rxj0rE6FvhUKV1b0ghId1mX, Reason: void_invoice
2025-08-20T05:27:34.3777559Z ✅ Found existing profile by stripe_customer_id: f62482f6-3c52-412a-82a2-966b641a7703
2025-08-20T05:27:34.4048816Z ✅ User profile resolved: f62482f6-3c52-412a-82a2-966b641a7703
2025-08-20T05:27:34.4049196Z 👤 Updating profile team admin status: false
2025-08-20T05:27:34.4672592Z ❌ Error updating subscription: {
2025-08-20T05:27:34.4673247Z   code: '22P02',
2025-08-20T05:27:34.4673307Z   details: null,
2025-08-20T05:27:34.4673395Z   hint: null,
2025-08-20T05:27:34.4673454Z   message: 'invalid input value for enum subscription_status: "incomplete_expired"'
2025-08-20T05:27:34.4673499Z }
2025-08-20T05:27:34.5464293Z ✅ Found existing profile by stripe_customer_id: fface8ff-cb9e-4d6a-a491-42ae8372d9b1
2025-08-20T05:27:34.5470358Z ✅ User profile resolved: fface8ff-cb9e-4d6a-a491-42ae8372d9b1
2025-08-20T05:27:34.5470438Z 👤 Updating profile team admin status: false
2025-08-20T05:27:34.5652678Z ✅ Payment event logged for payment_intent.canceled: pi_3Rxj0lE6FvhUKV1b00tPu8x7
2025-08-20T05:27:34.6755146Z ✅ Profile updated - team admin: false
2025-08-20T05:27:34.6755723Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-20T05:27:34.7056552Z ✅ Profile updated - team admin: false
2025-08-20T05:27:34.7056973Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-20T05:27:34.8209362Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-20T05:27:34.8209788Z 💾 Creating/updating subscription record in database
2025-08-20T05:27:34.9203658Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-20T05:27:34.9336193Z 💾 Creating/updating subscription record in database
2025-08-20T05:27:35.2494592Z ❌ Error updating subscription: {
2025-08-20T05:27:35.2495456Z   code: '22P02',
2025-08-20T05:27:35.2495538Z   details: null,
2025-08-20T05:27:35.2495605Z   hint: null,
2025-08-20T05:27:35.2495681Z   message: 'invalid input value for enum subscription_status: "incomplete_expired"'
2025-08-20T05:27:35.2495745Z }
2025-08-20T05:27:35.2807434Z ❌ Error updating subscription: {
2025-08-20T05:27:35.2808169Z   code: '22P02',
2025-08-20T05:27:35.2808310Z   details: null,
2025-08-20T05:27:35.2808363Z   hint: null,
2025-08-20T05:27:35.2808418Z   message: 'invalid input value for enum subscription_status: "incomplete_expired"'
2025-08-20T05:27:35.2808464Z }
2025-08-20T05:27:35.3123270Z ✅ Payment event logged for payment_intent.canceled: pi_3Rxj0rE6FvhUKV1b0ghId1mX
2025-08-20T05:28:38  No new trace in the past 1 min(s).
2025-08-20T05:29:38  No new trace in the past 2 min(s).
2025-08-20T05:30:38  No new trace in the past 3 min(s).
2025-08-20T05:31:38  No new trace in the past 4 min(s).
2025-08-20T05:32:40  No new trace in the past 5 min(s).
2025-08-20T05:32:48.8390338Z Deploy Async
2025-08-20T05:33:58.5992954Z    _____
2025-08-20T05:33:58.6116101Z   /  _  \ __________ _________   ____
2025-08-20T05:33:58.6116425Z  /  /_\  \\___   /  |  \_  __ \_/ __ \
2025-08-20T05:33:58.6116500Z /    |    \/    /|  |  /|  | \/\  ___/
2025-08-20T05:33:58.6116640Z \____|__  /_____ \____/ |__|    \___  >
2025-08-20T05:33:58.6116728Z         \/      \/                  \/
2025-08-20T05:33:58.6116794Z A P P   S E R V I C E   O N   L I N U X
2025-08-20T05:33:58.6116854Z
2025-08-20T05:33:58.6116925Z Documentation: http://aka.ms/webapp-linux
2025-08-20T05:33:58.6116992Z NodeJS quickstart: https://aka.ms/node-qs
2025-08-20T05:33:58.6117056Z NodeJS Version : v22.17.0
2025-08-20T05:33:58.6117146Z Note: Any data outside '/home' is not persisted
2025-08-20T05:33:58.6117206Z
2025-08-20T05:34:11.8364585Z Starting OpenBSD Secure Shell server: sshd.
2025-08-20T05:34:11.8365197Z WEBSITES_INCLUDE_CLOUD_CERTS is not set to true.
2025-08-20T05:34:11.8365278Z Updating certificates in /etc/ssl/certs...
2025-08-20T05:34:48.7395061Z rehash: warning: skipping ca-certificates.crt,it does not contain exactly one certificate or CRL
2025-08-20T05:34:48.8675128Z 4 added, 0 removed; done.
2025-08-20T05:34:48.8691367Z Running hooks in /etc/ca-certificates/update.d...
2025-08-20T05:34:48.9275804Z done.
2025-08-20T05:34:48.9793051Z CA certificates copied and updated successfully.
2025-08-20T05:34:49.5506153Z Starting periodic command scheduler: cron.
2025-08-20T05:34:49.8465079Z Could not find build manifest file at '/home/<USER>/wwwroot/oryx-manifest.toml'
2025-08-20T05:34:49.8502647Z Could not find operation ID in manifest. Generating an operation id...
2025-08-20T05:34:49.8791651Z Build Operation ID: babdb540-ce4e-4709-b3b1-061858ff4983
2025-08-20T05:34:50.7601798Z Environment Variables for Application Insight's IPA Codeless Configuration exists..
2025-08-20T05:34:50.8317482Z Writing output script to '/opt/startup/startup.sh'
2025-08-20T05:34:50.9301698Z Running #!/bin/sh
2025-08-20T05:34:50.9302440Z
2025-08-20T05:34:50.9302524Z # Enter the source directory to make sure the script runs where the user expects
2025-08-20T05:34:50.9302595Z cd "/home/<USER>/wwwroot"
2025-08-20T05:34:50.9302646Z
2025-08-20T05:34:50.9302705Z export NODE_PATH=/usr/local/lib/node_modules:$NODE_PATH
2025-08-20T05:34:50.9302843Z if [ -z "$PORT" ]; then
2025-08-20T05:34:50.9302898Z 		export PORT=8080
2025-08-20T05:34:50.9302950Z fi
2025-08-20T05:34:50.9303001Z
2025-08-20T05:34:50.9597249Z npm start
2025-08-20T05:34:55.5818011Z npm info using npm@10.9.2
2025-08-20T05:34:55.5941444Z npm info using node@v22.17.0
2025-08-20T05:35:08.7372208Z
2025-08-20T05:35:08.7373114Z > quantboostai@0.1.2 start
2025-08-20T05:35:08.7373196Z > node server.js
2025-08-20T05:35:08.7373262Z
2025-08-20T05:35:21.6396361Z    ▲ Next.js 15.2.4
2025-08-20T05:35:21.6581071Z    - Local:        http://71769e35fd28:8080
2025-08-20T05:35:21.6602120Z    - Network:      http://71769e35fd28:8080
2025-08-20T05:35:21.6795764Z
2025-08-20T05:35:21.6796297Z  ✓ Starting...
2025-08-20T05:35:23.6045304Z  ✓ Ready in 2.1s
2025-08-20T05:36:40  No new trace in the past 1 min(s).
2025-08-20T05:37:40  No new trace in the past 2 min(s).
2025-08-20T05:38:40  No new trace in the past 3 min(s).
2025-08-20T05:39:40  No new trace in the past 4 min(s).
2025-08-20T05:40:40  No new trace in the past 5 min(s).
2025-08-20T05:41:40  No new trace in the past 6 min(s).
2025-08-20T05:42:40  No new trace in the past 7 min(s).
2025-08-20T05:43:31.5184624Z 🚀 Webhook received at: 2025-08-20T05:43:31.506Z
2025-08-20T05:43:32.2496917Z 🔧 Environment check: {
2025-08-20T05:43:32.2497304Z   hasStripeSecret: true,
2025-08-20T05:43:32.2497361Z   hasWebhookSecret: true,
2025-08-20T05:43:32.2497409Z   hasSupabaseUrl: true,
2025-08-20T05:43:32.2497457Z   hasServiceKey: true,
2025-08-20T05:43:32.2497506Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:43:32.2497557Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:43:32.2497603Z }
2025-08-20T05:43:32.2497656Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:43:32.2497707Z 🚀 Webhook received at: 2025-08-20T05:43:32.226Z
2025-08-20T05:43:32.2497755Z 🔧 Environment check: {
2025-08-20T05:43:32.2497802Z   hasStripeSecret: true,
2025-08-20T05:43:32.2497888Z   hasWebhookSecret: true,
2025-08-20T05:43:32.2497938Z   hasSupabaseUrl: true,
2025-08-20T05:43:32.2497985Z   hasServiceKey: true,
2025-08-20T05:43:32.2498034Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:43:32.2498083Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:43:32.2782026Z }
2025-08-20T05:43:32.2782174Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:43:32.7579413Z 🚀 Webhook received at: 2025-08-20T05:43:32.755Z
2025-08-20T05:43:32.7579842Z 🔧 Environment check: {
2025-08-20T05:43:32.7579901Z   hasStripeSecret: true,
2025-08-20T05:43:32.7580039Z   hasWebhookSecret: true,
2025-08-20T05:43:32.7580090Z   hasSupabaseUrl: true,
2025-08-20T05:43:32.7580136Z   hasServiceKey: true,
2025-08-20T05:43:32.7580186Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:43:32.7580236Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:43:32.7580280Z }
2025-08-20T05:43:32.7580333Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:43:32.9627372Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:43:33.1330468Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:43:33.1844329Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:43:33.7854448Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:43:33.8232273Z ✅ Stripe event verified: charge.succeeded ID: evt_3Ry4LCE6FvhUKV1b02m5aEv1
2025-08-20T05:43:33.8271446Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:43:33.8371594Z ✅ Stripe event verified: payment_intent.succeeded ID: evt_3Ry4LCE6FvhUKV1b0E2RfhLo
2025-08-20T05:43:33.8464414Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:43:33.8543175Z ✅ Stripe event verified: invoice.paid ID: evt_1Ry4LEE6FvhUKV1bOLzRehQ6
2025-08-20T05:43:34.0109190Z ⚠️ Duplicate event detected: evt_3Ry4LCE6FvhUKV1b02m5aEv1, status: processed
2025-08-20T05:43:34.0699864Z ⚠️ Duplicate event detected: evt_3Ry4LCE6FvhUKV1b0E2RfhLo, status: processed
2025-08-20T05:43:34.1696756Z ⚠️ Duplicate event detected: evt_1Ry4LEE6FvhUKV1bOLzRehQ6, status: processed
2025-08-20T05:43:37.7036874Z 🚀 Webhook received at: 2025-08-20T05:43:37.701Z
2025-08-20T05:43:37.7037570Z 🔧 Environment check: {
2025-08-20T05:43:37.7037630Z   hasStripeSecret: true,
2025-08-20T05:43:37.7037689Z   hasWebhookSecret: true,
2025-08-20T05:43:37.7037737Z   hasSupabaseUrl: true,
2025-08-20T05:43:37.7037789Z   hasServiceKey: true,
2025-08-20T05:43:37.7037841Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:43:37.7037991Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:43:37.7038040Z }
2025-08-20T05:43:37.7145739Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:43:37.9543233Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:43:38.1757818Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:43:38.1758492Z ✅ Stripe event verified: customer.subscription.created ID: evt_1Ry4LDE6FvhUKV1b5ANshgF6
2025-08-20T05:43:38.5202537Z ⚠️ Duplicate event detected: evt_1Ry4LDE6FvhUKV1b5ANshgF6, status: processed
2025-08-20T05:43:42.4293712Z 🚀 Webhook received at: 2025-08-20T05:43:42.421Z
2025-08-20T05:43:42.4294151Z 🔧 Environment check: {
2025-08-20T05:43:42.4294309Z   hasStripeSecret: true,
2025-08-20T05:43:42.4294359Z   hasWebhookSecret: true,
2025-08-20T05:43:42.4294407Z   hasSupabaseUrl: true,
2025-08-20T05:43:42.4294454Z   hasServiceKey: true,
2025-08-20T05:43:42.4294504Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:43:42.4294553Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:43:42.4294599Z }
2025-08-20T05:43:42.4318434Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:43:42.6636927Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:43:42.9314288Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:43:42.9423023Z ✅ Stripe event verified: charge.succeeded ID: evt_3Ry4LBE6FvhUKV1b1UOUbINL
2025-08-20T05:43:43.3195755Z ⚠️ Duplicate event detected: evt_3Ry4LBE6FvhUKV1b1UOUbINL, status: processed
2025-08-20T05:43:47.0304137Z 🚀 Webhook received at: 2025-08-20T05:43:47.029Z
2025-08-20T05:43:47.0306287Z 🔧 Environment check: {
2025-08-20T05:43:47.0306357Z   hasStripeSecret: true,
2025-08-20T05:43:47.0306406Z   hasWebhookSecret: true,
2025-08-20T05:43:47.0306454Z   hasSupabaseUrl: true,
2025-08-20T05:43:47.0306501Z   hasServiceKey: true,
2025-08-20T05:43:47.0306553Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:43:47.0306603Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:43:47.0306766Z }
2025-08-20T05:43:47.0718910Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:43:47.2462230Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:43:47.5410039Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:43:47.5598848Z ✅ Stripe event verified: invoice.payment_succeeded ID: evt_1Ry4LEE6FvhUKV1blySkpu3J
2025-08-20T05:43:47.8075877Z 🚀 Webhook received at: 2025-08-20T05:43:47.797Z
2025-08-20T05:43:47.8076321Z 🔧 Environment check: {
2025-08-20T05:43:47.8076381Z   hasStripeSecret: true,
2025-08-20T05:43:47.8076429Z   hasWebhookSecret: true,
2025-08-20T05:43:47.8076574Z   hasSupabaseUrl: true,
2025-08-20T05:43:47.8076624Z   hasServiceKey: true,
2025-08-20T05:43:47.8076675Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:43:47.8076725Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:43:47.8076771Z }
2025-08-20T05:43:47.8148857Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:43:48.0115413Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:43:48.0292214Z ✅ Webhook event logged: evt_1Ry4LEE6FvhUKV1blySkpu3J with status: processing
2025-08-20T05:43:48.0303277Z 👉 Handling invoice.payment_succeeded
2025-08-20T05:43:48.0312522Z Skipping non-subscription invoice
2025-08-20T05:43:48.2127086Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:43:48.2419777Z ✅ Stripe event verified: customer.updated ID: evt_1Ry4LEE6FvhUKV1bVSzIlN7e
2025-08-20T05:43:48.4672069Z ⚠️ Duplicate event detected: evt_1Ry4LEE6FvhUKV1bVSzIlN7e, status: processed
2025-08-20T05:43:51.2125420Z 🚀 Webhook received at: 2025-08-20T05:43:51.205Z
2025-08-20T05:43:51.2126185Z 🔧 Environment check: {
2025-08-20T05:43:51.2126248Z   hasStripeSecret: true,
2025-08-20T05:43:51.2126299Z   hasWebhookSecret: true,
2025-08-20T05:43:51.2126348Z   hasSupabaseUrl: true,
2025-08-20T05:43:51.2126400Z   hasServiceKey: true,
2025-08-20T05:43:51.2126569Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:43:51.2126624Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:43:51.2126670Z }
2025-08-20T05:43:51.2459878Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:43:51.4294206Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:43:51.6237387Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:43:51.6247199Z ✅ Stripe event verified: payment_intent.succeeded ID: evt_3Ry4LCE6FvhUKV1b1wVnXjXg
2025-08-20T05:43:52.1152106Z ✅ Webhook event logged: evt_3Ry4LCE6FvhUKV1b1wVnXjXg with status: processing
2025-08-20T05:43:52.1224634Z 👉 Handling payment_intent.succeeded
2025-08-20T05:43:52.1224963Z 💳 Payment succeeded: pi_3Ry4LCE6FvhUKV1b1G97eUHB, Amount: 12000, Customer: cus_Sts5FHWtFR7IQs
2025-08-20T05:43:52.6716173Z ✅ Payment event logged for payment_intent.succeeded: pi_3Ry4LCE6FvhUKV1b1G97eUHB
2025-08-20T05:44:02.5316406Z 🚀 Webhook received at: 2025-08-20T05:44:02.530Z
2025-08-20T05:44:02.5316881Z 🔧 Environment check: {
2025-08-20T05:44:02.5316939Z   hasStripeSecret: true,
2025-08-20T05:44:02.5316987Z   hasWebhookSecret: true,
2025-08-20T05:44:02.5317034Z   hasSupabaseUrl: true,
2025-08-20T05:44:02.5317081Z   hasServiceKey: true,
2025-08-20T05:44:02.5317131Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:02.5317180Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:02.5317226Z }
2025-08-20T05:44:03.3035707Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:03.3036073Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:03.3036135Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:03.3036190Z ✅ Stripe event verified: customer.subscription.created ID: evt_1Ry4LEE6FvhUKV1bVBzdV6x0
2025-08-20T05:44:03.6077800Z ✅ Webhook event logged: evt_1Ry4LEE6FvhUKV1bVBzdV6x0 with status: processing
2025-08-20T05:44:03.6080969Z 👉 Handling customer.subscription.created
2025-08-20T05:44:03.6274295Z 🔗 Subscription: sub_1Ry4LBE6FvhUKV1bdAhr9qm5, Customer: cus_Sts5FHWtFR7IQs, Status: active
2025-08-20T05:44:03.6274607Z 💰 Product: prod_S6Fn893jGxRhKk, Quantity: 1
2025-08-20T05:44:03.6274663Z 🔎 QUANTITY DIAGNOSTICS (subscription event) {
2025-08-20T05:44:03.6274715Z   event_id: 'evt_1Ry4LEE6FvhUKV1bVBzdV6x0',
2025-08-20T05:44:03.6274764Z   path: 'customer.subscription.created',
2025-08-20T05:44:03.6274812Z   raw_first_item_quantity: 1,
2025-08-20T05:44:03.6274861Z   subscription_top_level_quantity: 1,
2025-08-20T05:44:03.6274908Z   items_length: 1,
2025-08-20T05:44:03.6274955Z   all_item_quantities: [ 1 ],
2025-08-20T05:44:03.6275074Z   expected_team: false
2025-08-20T05:44:03.6275121Z }
2025-08-20T05:44:03.6275171Z 📅 Billing Period Debug: {
2025-08-20T05:44:03.6275219Z   current_period_start: undefined,
2025-08-20T05:44:03.6275267Z   current_period_end: undefined,
2025-08-20T05:44:03.6275314Z   current_period_start_iso: null,
2025-08-20T05:44:03.6275362Z   current_period_end_iso: null,
2025-08-20T05:44:03.6275408Z   created: 1755666821,
2025-08-20T05:44:03.6275455Z   start_date: 1755666821,
2025-08-20T05:44:03.6275502Z   billing_cycle_anchor: 1755666821,
2025-08-20T05:44:03.6275548Z   status: 'active'
2025-08-20T05:44:03.6275614Z }
2025-08-20T05:44:03.7965838Z 🚀 Webhook received at: 2025-08-20T05:44:03.787Z
2025-08-20T05:44:03.7966249Z 🔧 Environment check: {
2025-08-20T05:44:03.7966305Z   hasStripeSecret: true,
2025-08-20T05:44:03.7966354Z   hasWebhookSecret: true,
2025-08-20T05:44:03.7966400Z   hasSupabaseUrl: true,
2025-08-20T05:44:03.7966449Z   hasServiceKey: true,
2025-08-20T05:44:03.7966499Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:03.7966550Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:03.7966595Z }
2025-08-20T05:44:03.8081179Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:03.9998049Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:04.0397712Z 🚀 Webhook received at: 2025-08-20T05:44:04.038Z
2025-08-20T05:44:04.0398072Z 🔧 Environment check: {
2025-08-20T05:44:04.0398127Z   hasStripeSecret: true,
2025-08-20T05:44:04.0398175Z   hasWebhookSecret: true,
2025-08-20T05:44:04.0398224Z   hasSupabaseUrl: true,
2025-08-20T05:44:04.0398271Z   hasServiceKey: true,
2025-08-20T05:44:04.0398320Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:04.0398370Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:04.0398414Z }
2025-08-20T05:44:04.0577397Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:04.1702974Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:04.1782774Z ✅ Stripe event verified: payment_method.attached ID: evt_1Ry4oZE6FvhUKV1bEreehlx8
2025-08-20T05:44:04.3574412Z 📅 Price interval: year, interval_count: 1
2025-08-20T05:44:04.3574921Z 👤 Fetching customer details for: cus_Sts5FHWtFR7IQs
2025-08-20T05:44:04.3899009Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:04.5602110Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_Sts5FHWtFR7IQs {
2025-08-20T05:44:04.5602530Z   customerName: 'Real Test Customer',
2025-08-20T05:44:04.5602584Z   fullName: 'Real Test Customer',
2025-08-20T05:44:04.5602739Z   customerEmail: '<EMAIL>',
2025-08-20T05:44:04.5602788Z   customerKeys: [2025-08-20T05:44:04.5602836Z     'id',                    'object',
2025-08-20T05:44:04.5602884Z     'address',               'balance',
2025-08-20T05:44:04.5602932Z     'created',               'currency',
2025-08-20T05:44:04.5602981Z     'default_source',        'delinquent',
2025-08-20T05:44:04.5603028Z     'description',           'discount',
2025-08-20T05:44:04.5603076Z     'email',                 'invoice_prefix',
2025-08-20T05:44:04.5603123Z     'invoice_settings',      'livemode',
2025-08-20T05:44:04.5603246Z     'metadata',              'name',
2025-08-20T05:44:04.5603298Z     'next_invoice_sequence', 'phone',
2025-08-20T05:44:04.5603346Z     'preferred_locales',     'shipping',
2025-08-20T05:44:04.5603394Z     'tax_exempt',            'test_clock'
2025-08-20T05:44:04.5603439Z   ],
2025-08-20T05:44:04.5603486Z   hasName: true,
2025-08-20T05:44:04.5603534Z   nameValue: 'Real Test Customer'
2025-08-20T05:44:04.5603579Z }
2025-08-20T05:44:04.5610687Z 🔍 PARSED NAME DEBUG - Customer: cus_Sts5FHWtFR7IQs {
2025-08-20T05:44:04.5610918Z   originalFullName: 'Real Test Customer',
2025-08-20T05:44:04.5611129Z   parsedFirstName: 'Real',
2025-08-20T05:44:04.5611260Z   parsedLastName: 'Test Customer',
2025-08-20T05:44:04.5611312Z   parseResult: { firstName: 'Real', lastName: 'Test Customer' }
2025-08-20T05:44:04.5611357Z }
2025-08-20T05:44:04.5613978Z 📧 Customer details - email: <EMAIL>, firstName: Real, lastName: Test Customer
2025-08-20T05:44:04.5617917Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1Ry4LBE6FvhUKV1bdAhr9qm5 {
2025-08-20T05:44:04.5618060Z   stripeCustomerId: 'cus_Sts5FHWtFR7IQs',
2025-08-20T05:44:04.5618114Z   extractedEmail: '<EMAIL>',
2025-08-20T05:44:04.5618163Z   extractedFirstName: 'Real',
2025-08-20T05:44:04.5618212Z   extractedLastName: 'Test Customer',
2025-08-20T05:44:04.5618261Z   subscriptionStatus: 'active',
2025-08-20T05:44:04.5618376Z   hasCustomerName: true
2025-08-20T05:44:04.5618422Z }
2025-08-20T05:44:04.5620734Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-20T05:44:04.6008692Z 🔍 ensureUserProfile called with: customer=cus_Sts5FHWtFR7IQs, email=<EMAIL>, firstName=Real, lastName=Test Customer
2025-08-20T05:44:04.6009008Z 🔍 Looking for existing profile by stripe_customer_id: cus_Sts5FHWtFR7IQs
2025-08-20T05:44:04.6610968Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:04.6689420Z ✅ Stripe event verified: customer.updated ID: evt_1Ry4oZE6FvhUKV1bYQaGeD07
2025-08-20T05:44:04.7324700Z ✅ Found existing profile by stripe_customer_id: a2c2507b-31a1-456a-8c4d-fa7a9a1b75cb
2025-08-20T05:44:04.7329580Z ✅ User profile resolved: a2c2507b-31a1-456a-8c4d-fa7a9a1b75cb
2025-08-20T05:44:04.7594461Z 👤 Updating profile team admin status: false
2025-08-20T05:44:04.8732094Z ✅ Webhook event logged: evt_1Ry4oZE6FvhUKV1bEreehlx8 with status: processing
2025-08-20T05:44:04.8736912Z 👉 Handling payment_method.attached {
2025-08-20T05:44:04.8737097Z   id: 'pm_1Ry4oYE6FvhUKV1bwjw87pLc',
2025-08-20T05:44:04.8737151Z   customer: 'cus_StsZeEmX7UBZGS',
2025-08-20T05:44:04.8737198Z   type: 'card',
2025-08-20T05:44:04.8737246Z   brand: 'visa',
2025-08-20T05:44:04.8737454Z   last4: '4242',
2025-08-20T05:44:04.8737607Z   exp_month: 8,
2025-08-20T05:44:04.8737656Z   exp_year: 2026
2025-08-20T05:44:04.8737701Z }
2025-08-20T05:44:05.0426888Z ✅ Profile updated - team admin: false
2025-08-20T05:44:05.0602799Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-20T05:44:05.9196034Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-20T05:44:05.9202505Z 💾 Creating/updating subscription record in database
2025-08-20T05:44:06.6341476Z ✅ Subscription created/updated in DB: 662a348f-2139-460c-857e-423bea50219c
2025-08-20T05:44:06.6750781Z ✅ Webhook event logged: evt_1Ry4oZE6FvhUKV1bYQaGeD07 with status: processing
2025-08-20T05:44:06.6759194Z 👉 Handling customer.updated (invoice_settings) {
2025-08-20T05:44:06.6759420Z   id: 'cus_StsZeEmX7UBZGS',
2025-08-20T05:44:06.6759600Z   default_payment_method: 'pm_1Ry4oYE6FvhUKV1bwjw87pLc'
2025-08-20T05:44:06.6759653Z }
2025-08-20T05:44:06.7564330Z 🔎 LICENSE SYNC DIAGNOSTICS {
2025-08-20T05:44:06.7564746Z   event_id: 'evt_1Ry4LEE6FvhUKV1bVBzdV6x0',
2025-08-20T05:44:06.7564805Z   subscription_id: 'sub_1Ry4LBE6FvhUKV1bdAhr9qm5',
2025-08-20T05:44:06.7564854Z   targetCount: 1,
2025-08-20T05:44:06.7564900Z   currentCount: 1,
2025-08-20T05:44:06.7564950Z   isTeamPlan: false,
2025-08-20T05:44:06.7565104Z   action: 'none'
2025-08-20T05:44:06.7565152Z }
2025-08-20T05:44:06.7565207Z 🎫 License sync - target: 1, current: 1
2025-08-20T05:44:06.7565259Z 🔄 Updating individual license statuses to: active
2025-08-20T05:44:07.0943594Z 🚀 Webhook received at: 2025-08-20T05:44:07.093Z
2025-08-20T05:44:07.0951752Z 🔧 Environment check: {
2025-08-20T05:44:07.0951967Z   hasStripeSecret: true,
2025-08-20T05:44:07.0952022Z   hasWebhookSecret: true,
2025-08-20T05:44:07.0952070Z   hasSupabaseUrl: true,
2025-08-20T05:44:07.0952268Z   hasServiceKey: true,
2025-08-20T05:44:07.0952323Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:07.0952621Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:07.0952673Z }
2025-08-20T05:44:07.1756750Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:07.3256559Z ✅ Individual license statuses updated
2025-08-20T05:44:07.3260934Z ✅ Subscription processing completed successfully
2025-08-20T05:44:07.4675473Z 🚀 Webhook received at: 2025-08-20T05:44:07.467Z
2025-08-20T05:44:07.4841726Z 🔧 Environment check: {
2025-08-20T05:44:07.4842248Z   hasStripeSecret: true,
2025-08-20T05:44:07.4842304Z   hasWebhookSecret: true,
2025-08-20T05:44:07.4842350Z   hasSupabaseUrl: true,
2025-08-20T05:44:07.4842397Z   hasServiceKey: true,
2025-08-20T05:44:07.4842447Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:07.4842498Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:07.4842543Z }
2025-08-20T05:44:07.5051008Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:07.5375856Z 🚀 Webhook received at: 2025-08-20T05:44:07.537Z
2025-08-20T05:44:07.5463806Z 🔧 Environment check: {
2025-08-20T05:44:07.5464326Z   hasStripeSecret: true,
2025-08-20T05:44:07.5464385Z   hasWebhookSecret: true,
2025-08-20T05:44:07.5464433Z   hasSupabaseUrl: true,
2025-08-20T05:44:07.5464480Z   hasServiceKey: true,
2025-08-20T05:44:07.5464531Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:07.5464581Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:07.5464625Z }
2025-08-20T05:44:07.5552025Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:07.8061961Z 🚀 Webhook received at: 2025-08-20T05:44:07.805Z
2025-08-20T05:44:07.8075308Z 🔧 Environment check: {
2025-08-20T05:44:07.8075638Z   hasStripeSecret: true,
2025-08-20T05:44:07.8075694Z   hasWebhookSecret: true,
2025-08-20T05:44:07.8075742Z   hasSupabaseUrl: true,
2025-08-20T05:44:07.8075789Z   hasServiceKey: true,
2025-08-20T05:44:07.8075838Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:07.8075888Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:07.8075932Z }
2025-08-20T05:44:07.8088055Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:07.8966423Z 🚀 Webhook received at: 2025-08-20T05:44:07.888Z
2025-08-20T05:44:07.8985301Z 🔧 Environment check: {
2025-08-20T05:44:07.8985824Z   hasStripeSecret: true,
2025-08-20T05:44:07.8985882Z   hasWebhookSecret:true,
2025-08-20T05:44:07.8985930Z   hasSupabaseUrl: true,
2025-08-20T05:44:07.8985977Z   hasServiceKey: true,
2025-08-20T05:44:07.8986026Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:07.8986076Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:07.8986121Z }
2025-08-20T05:44:07.9089524Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:07.9367095Z 🚀 Webhook received at: 2025-08-20T05:44:07.936Z
2025-08-20T05:44:07.9389378Z 🔧 Environment check: {
2025-08-20T05:44:07.9389939Z   hasStripeSecret: true,
2025-08-20T05:44:07.9390001Z   hasWebhookSecret: true,
2025-08-20T05:44:07.9390050Z   hasSupabaseUrl: true,
2025-08-20T05:44:07.9390097Z   hasServiceKey: true,
2025-08-20T05:44:07.9390147Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:07.9390197Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:07.9390241Z }
2025-08-20T05:44:07.9494474Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:07.9894510Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:08.0259829Z 🚀 Webhook received at: 2025-08-20T05:44:08.019Z
2025-08-20T05:44:08.0270297Z 🔧 Environment check: {
2025-08-20T05:44:08.0270692Z   hasStripeSecret: true,
2025-08-20T05:44:08.0270752Z   hasWebhookSecret: true,
2025-08-20T05:44:08.0270800Z   hasSupabaseUrl: true,
2025-08-20T05:44:08.0270847Z   hasServiceKey: true,
2025-08-20T05:44:08.0270897Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:08.0270945Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:08.0270990Z }
2025-08-20T05:44:08.0284249Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:08.0696752Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:08.0994433Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:08.1873315Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:08.2171313Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:08.2800793Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:08.3108381Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:08.3201797Z ✅ Stripe event verified: payment_intent.succeeded ID: evt_3Ry4oaE6FvhUKV1b0BS3rmGn
2025-08-20T05:44:08.3484879Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:08.3500466Z ✅ Stripe event verified: customer.subscription.created ID: evt_1Ry4ocE6FvhUKV1bjk4msDj2
2025-08-20T05:44:08.3718307Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:08.4195505Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:08.4209691Z ✅ Stripe event verified: charge.succeeded ID: evt_3Ry4oaE6FvhUKV1b0SjXy2CW
2025-08-20T05:44:08.5294393Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:08.5480516Z ✅ Stripe event verified: invoice.paid ID: evt_1Ry4ocE6FvhUKV1baS4lJhLZ
2025-08-20T05:44:08.5784316Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:08.5871268Z 🧪 Test webhook signature detected - bypassing validation
2025-08-20T05:44:08.5877945Z Environment check: NODE_ENV=production, QUANTBOOST_ENV=staging, isStaging=true, isTest=false
2025-08-20T05:44:08.5897000Z ✅ Test event parsed: customer.subscription.created ID: evt_test_1755668644530_trxnoi9arfl
2025-08-20T05:44:08.9189603Z ✅ Webhook event logged: evt_3Ry4oaE6FvhUKV1b0BS3rmGn with status: processing
2025-08-20T05:44:08.9256111Z 👉 Handling payment_intent.succeeded
2025-08-20T05:44:08.9259809Z 💳 Payment succeeded: pi_3Ry4oaE6FvhUKV1b0eixxdS9, Amount: 18000, Customer: cus_StsZeEmX7UBZGS
2025-08-20T05:44:08.9282745Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:08.9359683Z ✅ Stripe event verified: invoice.payment_succeeded ID: evt_1Ry4odE6FvhUKV1bDrmZI8Vp
2025-08-20T05:44:08.9667245Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:09.0050298Z ✅ Stripe event verified: customer.updated ID: evt_1Ry4ocE6FvhUKV1bDdHaUWc8
2025-08-20T05:44:09.1078063Z ✅ Webhook event logged: evt_3Ry4oaE6FvhUKV1b0SjXy2CW with status: processing
2025-08-20T05:44:09.1078504Z 👉 Handling charge.succeeded
2025-08-20T05:44:09.1078576Z 💳 Charge succeeded: ch_3Ry4oaE6FvhUKV1b0WFGIHKV, Amount: 18000, Customer: cus_StsZeEmX7UBZGS
2025-08-20T05:44:09.1270689Z ✅ Webhook event logged: evt_1Ry4ocE6FvhUKV1bjk4msDj2 with status: processing
2025-08-20T05:44:09.1271048Z 👉 Handling customer.subscription.created
2025-08-20T05:44:09.1271111Z 🔗 Subscription: sub_1Ry4oZE6FvhUKV1bGfGV9vMV, Customer: cus_StsZeEmX7UBZGS, Status: active
2025-08-20T05:44:09.1271171Z 💰 Product: prod_S6Fn893jGxRhKk, Quantity: 4
2025-08-20T05:44:09.1271223Z 🔎 QUANTITY DIAGNOSTICS (subscription event) {
2025-08-20T05:44:09.1271302Z   event_id: 'evt_1Ry4ocE6FvhUKV1bjk4msDj2',
2025-08-20T05:44:09.1271352Z   path: 'customer.subscription.created',
2025-08-20T05:44:09.1271400Z   raw_first_item_quantity: 4,
2025-08-20T05:44:09.1271450Z   subscription_top_level_quantity: 4,
2025-08-20T05:44:09.1271496Z   items_length: 1,
2025-08-20T05:44:09.1271544Z   all_item_quantities: [ 4 ],
2025-08-20T05:44:09.1271590Z   expected_team: true
2025-08-20T05:44:09.1271639Z }
2025-08-20T05:44:09.1271689Z 📅 Billing Period Debug: {
2025-08-20T05:44:09.1271740Z   current_period_start: undefined,
2025-08-20T05:44:09.1271788Z   current_period_end: undefined,
2025-08-20T05:44:09.1271859Z   current_period_start_iso: null,
2025-08-20T05:44:09.1271907Z   current_period_end_iso: null,
2025-08-20T05:44:09.1271953Z   created: 1755668643,
2025-08-20T05:44:09.1272001Z   start_date: 1755668643,
2025-08-20T05:44:09.1272049Z   billing_cycle_anchor: 1755668643,
2025-08-20T05:44:09.1272095Z   status: 'active'
2025-08-20T05:44:09.1272140Z }
2025-08-20T05:44:09.2987424Z ✅ Webhook event logged: evt_1Ry4ocE6FvhUKV1baS4lJhLZ with status: processing
2025-08-20T05:44:09.2990857Z 👉 Handling invoice.paid
2025-08-20T05:44:09.2993246Z Skipping non-subscription invoice
2025-08-20T05:44:09.3392103Z 📅 Price interval: month, interval_count: 3
2025-08-20T05:44:09.3395677Z 👤 Fetching customer details for: cus_StsZeEmX7UBZGS
2025-08-20T05:44:09.3770821Z ✅ Webhook event logged: evt_test_1755668644530_trxnoi9arfl with status: processing
2025-08-20T05:44:09.3771179Z 👉 Handling customer.subscription.created
2025-08-20T05:44:09.3771242Z 🔗 Subscription: sub_1Ry4oZE6FvhUKV1bGfGV9vMV, Customer: cus_StsZeEmX7UBZGS, Status: active
2025-08-20T05:44:09.3771295Z 💰 Product: undefined, Quantity: undefined
2025-08-20T05:44:09.3771345Z 🔎 QUANTITY DIAGNOSTICS (subscription event) {
2025-08-20T05:44:09.3771400Z   event_id: 'evt_test_1755668644530_trxnoi9arfl',
2025-08-20T05:44:09.3771452Z   path: 'customer.subscription.created',
2025-08-20T05:44:09.3771500Z   raw_first_item_quantity: undefined,
2025-08-20T05:44:09.3771651Z   subscription_top_level_quantity: 4,
2025-08-20T05:44:09.3771702Z   items_length: 1,
2025-08-20T05:44:09.3771748Z   all_item_quantities: [ undefined ],
2025-08-20T05:44:09.3771795Z   expected_team: false
2025-08-20T05:44:09.3771840Z }
2025-08-20T05:44:09.3771890Z 📅 Billing Period Debug: {
2025-08-20T05:44:09.3771938Z   current_period_start: undefined,
2025-08-20T05:44:09.3771985Z   current_period_end: undefined,
2025-08-20T05:44:09.3772033Z   current_period_start_iso: null,
2025-08-20T05:44:09.3772082Z   current_period_end_iso: null,
2025-08-20T05:44:09.3772128Z   created: 1755668643,
2025-08-20T05:44:09.3772176Z   start_date: undefined,
2025-08-20T05:44:09.3772370Z   billing_cycle_anchor: undefined,
2025-08-20T05:44:09.3772419Z   status: 'active'
2025-08-20T05:44:09.3772463Z }
2025-08-20T05:44:09.4084337Z ✅ Webhook event logged: evt_1Ry4ocE6FvhUKV1bDdHaUWc8 with status: processing
2025-08-20T05:44:09.4088997Z 👉 Handling customer.updated (invoice_settings) {
2025-08-20T05:44:09.4089189Z   id: 'cus_StsZeEmX7UBZGS',
2025-08-20T05:44:09.4089249Z   default_payment_method: 'pm_1Ry4oYE6FvhUKV1bwjw87pLc'
2025-08-20T05:44:09.4089295Z }
2025-08-20T05:44:09.4886172Z ✅ Webhook event logged: evt_1Ry4odE6FvhUKV1bDrmZI8Vp with status: processing
2025-08-20T05:44:09.4889328Z 👉 Handling invoice.payment_succeeded
2025-08-20T05:44:09.4891464Z Skipping non-subscription invoice
2025-08-20T05:44:09.5276172Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_StsZeEmX7UBZGS {
2025-08-20T05:44:09.5276669Z   customerName: 'Team Plan Customer',
2025-08-20T05:44:09.5276731Z   fullName: 'Team Plan Customer',
2025-08-20T05:44:09.5276784Z   customerEmail: '<EMAIL>',
2025-08-20T05:44:09.5276831Z   customerKeys: [
2025-08-20T05:44:09.5276879Z     'id',                    'object',
2025-08-20T05:44:09.5276930Z     'address',               'balance',
2025-08-20T05:44:09.5276979Z     'created',               'currency',
2025-08-20T05:44:09.5277026Z     'default_source',        'delinquent',
2025-08-20T05:44:09.5277158Z     'description',           'discount',
2025-08-20T05:44:09.5277208Z     'email',                 'invoice_prefix',
2025-08-20T05:44:09.5277256Z     'invoice_settings',      'livemode',
2025-08-20T05:44:09.5277304Z     'metadata',              'name',
2025-08-20T05:44:09.5277351Z     'next_invoice_sequence', 'phone',
2025-08-20T05:44:09.5277398Z     'preferred_locales',     'shipping',
2025-08-20T05:44:09.5277446Z     'tax_exempt',            'test_clock'
2025-08-20T05:44:09.5277490Z   ],
2025-08-20T05:44:09.5277537Z   hasName: true,
2025-08-20T05:44:09.5277584Z   nameValue: 'Team Plan Customer'
2025-08-20T05:44:09.5277630Z }
2025-08-20T05:44:09.5283487Z 🔍 PARSED NAME DEBUG - Customer: cus_StsZeEmX7UBZGS {
2025-08-20T05:44:09.5283694Z   originalFullName: 'Team Plan Customer',
2025-08-20T05:44:09.5283919Z   parsedFirstName: 'Team',
2025-08-20T05:44:09.5283975Z   parsedLastName: 'Plan Customer',
2025-08-20T05:44:09.5284026Z   parseResult: { firstName: 'Team', lastName: 'Plan Customer' }
2025-08-20T05:44:09.5284072Z }
2025-08-20T05:44:09.5286737Z 📧 Customer details - email: <EMAIL>, firstName: Team, lastName: Plan Customer
2025-08-20T05:44:09.5289729Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1Ry4oZE6FvhUKV1bGfGV9vMV {
2025-08-20T05:44:09.5289860Z   stripeCustomerId: 'cus_StsZeEmX7UBZGS',
2025-08-20T05:44:09.5289915Z   extractedEmail: '<EMAIL>',
2025-08-20T05:44:09.5290032Z   extractedFirstName: 'Team',
2025-08-20T05:44:09.5290083Z   extractedLastName: 'Plan Customer',
2025-08-20T05:44:09.5290132Z   subscriptionStatus: 'active',
2025-08-20T05:44:09.5290180Z   hasCustomerName: true
2025-08-20T05:44:09.5290225Z }
2025-08-20T05:44:09.5292413Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-20T05:44:09.5295772Z 🔍 ensureUserProfile called with: customer=cus_StsZeEmX7UBZGS, email=<EMAIL>, firstName=Team, lastName=Plan Customer
2025-08-20T05:44:09.5298219Z 🔍 Looking for existing profile by stripe_customer_id: cus_StsZeEmX7UBZGS
2025-08-20T05:44:09.5582714Z ✅ Stored receipt for charge: ch_3Ry4oaE6FvhUKV1b0WFGIHKV
2025-08-20T05:44:09.7293223Z 📅 Price interval: month, interval_count: 3
2025-08-20T05:44:09.7293648Z 👤 Fetching customer details for: cus_StsZeEmX7UBZGS
2025-08-20T05:44:09.7804467Z ✅ Payment event logged for payment_intent.succeeded: pi_3Ry4oaE6FvhUKV1b0eixxdS9
2025-08-20T05:44:09.7980818Z ✅ Found existing profile by stripe_customer_id: 1b8e892f-26fd-4357-b2b5-2950edb475a9
2025-08-20T05:44:09.7986862Z ✅ User profile resolved: 1b8e892f-26fd-4357-b2b5-2950edb475a9
2025-08-20T05:44:09.8003634Z 👤 Updating profile team admin status: true
2025-08-20T05:44:09.8896543Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_StsZeEmX7UBZGS {
2025-08-20T05:44:09.8897276Z   customerName: 'Team Plan Customer',
2025-08-20T05:44:09.8897425Z   fullName: 'Team Plan Customer',
2025-08-20T05:44:09.8897480Z   customerEmail: '<EMAIL>',
2025-08-20T05:44:09.8897527Z   customerKeys: [
2025-08-20T05:44:09.8897575Z     'id',                    'object',
2025-08-20T05:44:09.8897625Z     'address',               'balance',
2025-08-20T05:44:09.8897674Z     'created',               'currency',
2025-08-20T05:44:09.8897721Z     'default_source',        'delinquent',
2025-08-20T05:44:09.8897768Z     'description',           'discount',
2025-08-20T05:44:09.8897816Z     'email',                 'invoice_prefix',
2025-08-20T05:44:09.8897863Z     'invoice_settings',      'livemode',
2025-08-20T05:44:09.8897911Z     'metadata',              'name',
2025-08-20T05:44:09.8897958Z     'next_invoice_sequence', 'phone',
2025-08-20T05:44:09.8898026Z     'preferred_locales',     'shipping',
2025-08-20T05:44:09.8898075Z     'tax_exempt',            'test_clock'
2025-08-20T05:44:09.8898120Z   ],
2025-08-20T05:44:09.8898166Z   hasName: true,
2025-08-20T05:44:09.8898214Z   nameValue: 'Team Plan Customer'
2025-08-20T05:44:09.8898259Z }
2025-08-20T05:44:09.8902463Z 🔍 PARSED NAME DEBUG - Customer: cus_StsZeEmX7UBZGS {
2025-08-20T05:44:09.8902610Z   originalFullName: 'Team Plan Customer',
2025-08-20T05:44:09.8902663Z   parsedFirstName: 'Team',
2025-08-20T05:44:09.8902711Z   parsedLastName: 'Plan Customer',
2025-08-20T05:44:09.8902762Z   parseResult: { firstName: 'Team', lastName: 'Plan Customer' }
2025-08-20T05:44:09.8903019Z }
2025-08-20T05:44:09.8905456Z 📧 Customer details - email: <EMAIL>, firstName: Team, lastName: Plan Customer
2025-08-20T05:44:09.8908544Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1Ry4oZE6FvhUKV1bGfGV9vMV {
2025-08-20T05:44:09.8908669Z   stripeCustomerId: 'cus_StsZeEmX7UBZGS',
2025-08-20T05:44:09.8908724Z   extractedEmail: '<EMAIL>',
2025-08-20T05:44:09.8908773Z   extractedFirstName: 'Team',
2025-08-20T05:44:09.8908823Z   extractedLastName: 'Plan Customer',
2025-08-20T05:44:09.8908872Z   subscriptionStatus: 'active',
2025-08-20T05:44:09.8908919Z   hasCustomerName: true
2025-08-20T05:44:09.8908964Z }
2025-08-20T05:44:09.8910801Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-20T05:44:09.8912780Z 🔍 ensureUserProfile called with: customer=cus_StsZeEmX7UBZGS, email=<EMAIL>, firstName=Team, lastName=Plan Customer
2025-08-20T05:44:09.8914960Z 🔍 Looking for existing profile by stripe_customer_id: cus_StsZeEmX7UBZGS
2025-08-20T05:44:09.9238401Z ✅ Profile updated - team admin: true
2025-08-20T05:44:09.9243842Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-20T05:44:10.0039357Z ✅ Found existing profile by stripe_customer_id: 1b8e892f-26fd-4357-b2b5-2950edb475a9
2025-08-20T05:44:10.0039712Z ✅ User profile resolved: 1b8e892f-26fd-4357-b2b5-2950edb475a9
2025-08-20T05:44:10.0039775Z 👤 Updating profile team admin status: false
2025-08-20T05:44:10.1179513Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-20T05:44:10.1357578Z 💾 Creating/updating subscription record in database
2025-08-20T05:44:10.1882555Z ✅ Profile updated - team admin: false
2025-08-20T05:44:10.2162708Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-20T05:44:10.3588915Z ✅ Subscription created/updated in DB: fd8e543e-fb54-421b-bba9-b01a90794c38
2025-08-20T05:44:10.4582466Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-20T05:44:10.4675464Z 💾 Creating/updating subscription record in database
2025-08-20T05:44:10.5184958Z 🔎 LICENSE SYNC DIAGNOSTICS {
2025-08-20T05:44:10.5185299Z   event_id: 'evt_1Ry4ocE6FvhUKV1bjk4msDj2',
2025-08-20T05:44:10.5185358Z   subscription_id: 'sub_1Ry4oZE6FvhUKV1bGfGV9vMV',
2025-08-20T05:44:10.5185406Z   targetCount: 4,
2025-08-20T05:44:10.5185453Z   currentCount: 0,
2025-08-20T05:44:10.5185499Z   isTeamPlan: true,
2025-08-20T05:44:10.5185552Z   action: 'adding_missing_licenses'
2025-08-20T05:44:10.5185597Z }
2025-08-20T05:44:10.5197119Z 🎫 License sync - target: 4, current: 0
2025-08-20T05:44:10.5197575Z 🎫 Creating 4 Basic-Team licenses with status: inactive
2025-08-20T05:44:10.5979010Z ✅ Subscription created/updated in DB: fd8e543e-fb54-421b-bba9-b01a90794c38
2025-08-20T05:44:10.6894922Z ✅ Successfully created 4 licenses
2025-08-20T05:44:10.6996282Z ℹ️ Team subscription is active, preserving individual license assignment statuses
2025-08-20T05:44:10.6996612Z ✅ Subscription processing completed successfully
2025-08-20T05:44:10.8914436Z 🔎 LICENSE SYNC DIAGNOSTICS {
2025-08-20T05:44:10.8915047Z   event_id: 'evt_test_1755668644530_trxnoi9arfl',
2025-08-20T05:44:10.8915111Z   subscription_id: 'sub_1Ry4oZE6FvhUKV1bGfGV9vMV',
2025-08-20T05:44:10.8915159Z   targetCount: 1,
2025-08-20T05:44:10.8915283Z   currentCount: 4,
2025-08-20T05:44:10.8915332Z   isTeamPlan: false,
2025-08-20T05:44:10.8915379Z   action: 'none'
2025-08-20T05:44:10.8915424Z }
2025-08-20T05:44:10.8915478Z 🎫 License sync - target: 1, current: 4
2025-08-20T05:44:10.8915530Z 🔄 Updating individual license statuses to: active
2025-08-20T05:44:11.0428818Z ✅ Individual license statuses updated
2025-08-20T05:44:11.0503283Z ✅ Subscription processing completed successfully
2025-08-20T05:44:15.9566156Z 🚀 Webhook received at: 2025-08-20T05:44:15.954Z
2025-08-20T05:44:15.9566795Z 🔧 Environment check: {
2025-08-20T05:44:15.9566944Z   hasStripeSecret: true,
2025-08-20T05:44:15.9566997Z   hasWebhookSecret: true,
2025-08-20T05:44:15.9567045Z   hasSupabaseUrl: true,
2025-08-20T05:44:15.9567093Z   hasServiceKey: true,
2025-08-20T05:44:15.9567144Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:15.9567194Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:15.9567240Z }
2025-08-20T05:44:15.9839320Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:16.2319964Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:16.4087764Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:16.4108406Z ✅ Stripe event verified: charge.succeeded ID: evt_3Ry4LCE6FvhUKV1b1IqcJqAm
2025-08-20T05:44:16.6220000Z ⚠️ Duplicate event detected: evt_3Ry4LCE6FvhUKV1b1IqcJqAm, status: processed
2025-08-20T05:44:21.4875706Z 🚀 Webhook received at: 2025-08-20T05:44:21.485Z
2025-08-20T05:44:21.4876616Z 🔧 Environment check: {
2025-08-20T05:44:21.4876678Z   hasStripeSecret: true,
2025-08-20T05:44:21.4876721Z   hasWebhookSecret: true,
2025-08-20T05:44:21.4876763Z   hasSupabaseUrl: true,
2025-08-20T05:44:21.4876806Z   hasServiceKey: true,
2025-08-20T05:44:21.4876853Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:21.4876895Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:21.4877029Z }
2025-08-20T05:44:21.5047299Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:21.7105376Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:21.9653480Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:21.9842271Z ✅ Stripe event verified: payment_intent.succeeded ID: evt_3Ry4LCE6FvhUKV1b1lYfaOLV
2025-08-20T05:44:22.0883875Z 🚀 Webhook received at: 2025-08-20T05:44:22.088Z
2025-08-20T05:44:22.1048130Z 🔧 Environment check: {
2025-08-20T05:44:22.1048481Z   hasStripeSecret: true,
2025-08-20T05:44:22.1048645Z   hasWebhookSecret: true,
2025-08-20T05:44:22.1048695Z   hasSupabaseUrl: true,
2025-08-20T05:44:22.1048744Z   hasServiceKey: true,
2025-08-20T05:44:22.1048793Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:22.1048842Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:22.1048887Z }
2025-08-20T05:44:22.1249683Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:22.2068973Z ⚠️ Duplicate event detected: evt_3Ry4LCE6FvhUKV1b1lYfaOLV, status: processed
2025-08-20T05:44:22.2361655Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:22.3398572Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:22.3482747Z ✅ Stripe event verified: charge.succeeded ID: evt_3Ry4LCE6FvhUKV1b1oIARVmM
2025-08-20T05:44:22.5627347Z ⚠️ Duplicate event detected: evt_3Ry4LCE6FvhUKV1b1oIARVmM, status: processed
2025-08-20T05:44:24.7181612Z 🚀 Webhook received at: 2025-08-20T05:44:24.717Z
2025-08-20T05:44:24.7271420Z 🔧 Environment check: {
2025-08-20T05:44:24.7271549Z   hasStripeSecret: true,
2025-08-20T05:44:24.7271601Z   hasWebhookSecret: true,
2025-08-20T05:44:24.7271649Z   hasSupabaseUrl: true,
2025-08-20T05:44:24.7271695Z   hasServiceKey: true,
2025-08-20T05:44:24.7271746Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:24.7271859Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:24.7271909Z }
2025-08-20T05:44:24.7271962Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:24.7975586Z 🚀 Webhook received at: 2025-08-20T05:44:24.797Z
2025-08-20T05:44:24.8193744Z 🔧 Environment check: {
2025-08-20T05:44:24.8491033Z   hasStripeSecret: true,
2025-08-20T05:44:24.8566190Z   hasWebhookSecret: true,
2025-08-20T05:44:24.8575833Z   hasSupabaseUrl: true,
2025-08-20T05:44:24.8660631Z   hasServiceKey: true,
2025-08-20T05:44:24.8691220Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:24.8696116Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:24.8696263Z }
2025-08-20T05:44:24.8696329Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:24.9072780Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:25.0475467Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:25.1695481Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:25.1907231Z ✅ Stripe event verified: payment_intent.canceled ID: evt_3RxjHGE6FvhUKV1b0hPjvP6U
2025-08-20T05:44:25.4103687Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:25.4514818Z ✅ Stripe event verified: customer.subscription.updated ID: evt_1Ry4ouE6FvhUKV1bbD92tjYQ
2025-08-20T05:44:25.7923609Z ✅ Webhook event logged: evt_1Ry4ouE6FvhUKV1bbD92tjYQ with status: processing
2025-08-20T05:44:25.7924615Z 👉 Handling customer.subscription.updated
2025-08-20T05:44:25.7924689Z 🔗 Subscription: sub_1RxjHFE6FvhUKV1bPzpw3UT0, Customer: cus_StWJVOg5LVJyJc, Status: incomplete_expired
2025-08-20T05:44:25.7924741Z 💰 Product: prod_S6Fn893jGxRhKk, Quantity: 1
2025-08-20T05:44:25.7924792Z 🔎 QUANTITY DIAGNOSTICS (subscription event) {
2025-08-20T05:44:25.7924938Z   event_id: 'evt_1Ry4ouE6FvhUKV1bbD92tjYQ',
2025-08-20T05:44:25.7924992Z   path: 'customer.subscription.updated',
2025-08-20T05:44:25.7925040Z   raw_first_item_quantity: 1,
2025-08-20T05:44:25.7925088Z   subscription_top_level_quantity: 1,
2025-08-20T05:44:25.7925135Z   items_length: 1,
2025-08-20T05:44:25.7925181Z   all_item_quantities: [ 1 ],
2025-08-20T05:44:25.7925227Z   expected_team: false
2025-08-20T05:44:25.7925273Z }
2025-08-20T05:44:25.7925322Z 📅 Billing Period Debug: {
2025-08-20T05:44:25.7925372Z   current_period_start: undefined,
2025-08-20T05:44:25.7925418Z   current_period_end: undefined,
2025-08-20T05:44:25.7925500Z   current_period_start_iso: null,
2025-08-20T05:44:25.7925550Z   current_period_end_iso: null,
2025-08-20T05:44:25.7925597Z   created: 1755585853,
2025-08-20T05:44:25.7925644Z   start_date: 1755585853,
2025-08-20T05:44:25.7925692Z   billing_cycle_anchor: 1755585853,
2025-08-20T05:44:25.7925739Z   status: 'incomplete_expired'
2025-08-20T05:44:25.7925784Z }
2025-08-20T05:44:25.9212311Z ✅ Webhook event logged: evt_3RxjHGE6FvhUKV1b0hPjvP6U with status: processing
2025-08-20T05:44:25.9213000Z 👉 Handling payment_intent.canceled
2025-08-20T05:44:25.9437042Z 🚫 Payment canceled: pi_3RxjHGE6FvhUKV1b0twLOZAz, Reason: void_invoice
2025-08-20T05:44:26.0719012Z 📅 Price interval: year, interval_count: 1
2025-08-20T05:44:26.0719716Z 👤 Fetching customer details for: cus_StWJVOg5LVJyJc
2025-08-20T05:44:26.2696128Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_StWJVOg5LVJyJc {
2025-08-20T05:44:26.2696568Z   customerName: 'NO_NAME',
2025-08-20T05:44:26.2696619Z   fullName: 'NO_FULL_NAME',
2025-08-20T05:44:26.2696664Z   customerEmail: '<EMAIL>',
2025-08-20T05:44:26.2696706Z   customerKeys: [
2025-08-20T05:44:26.2696749Z     'id',                    'object',
2025-08-20T05:44:26.2696793Z     'address',               'balance',
2025-08-20T05:44:26.2696836Z     'created',               'currency',
2025-08-20T05:44:26.2696878Z     'default_source',        'delinquent',
2025-08-20T05:44:26.2697016Z     'description',           'discount',
2025-08-20T05:44:26.2697063Z     'email',                 'invoice_prefix',
2025-08-20T05:44:26.2697104Z     'invoice_settings',      'livemode',
2025-08-20T05:44:26.2697146Z     'metadata',              'name',
2025-08-20T05:44:26.2697187Z     'next_invoice_sequence', 'phone',
2025-08-20T05:44:26.2697229Z     'preferred_locales',     'shipping',
2025-08-20T05:44:26.2697270Z     'tax_exempt',            'test_clock'
2025-08-20T05:44:26.2697311Z   ],
2025-08-20T05:44:26.2697352Z   hasName: true,
2025-08-20T05:44:26.2697392Z   nameValue: null
2025-08-20T05:44:26.2697432Z }
2025-08-20T05:44:26.2714458Z 🔍 PARSED NAME DEBUG - Customer: cus_StWJVOg5LVJyJc {
2025-08-20T05:44:26.2714673Z   originalFullName: null,
2025-08-20T05:44:26.2714721Z   parsedFirstName: 'EMPTY_FIRST',
2025-08-20T05:44:26.2714764Z   parsedLastName: 'EMPTY_LAST',
2025-08-20T05:44:26.2714810Z   parseResult: { firstName: null, lastName: null }
2025-08-20T05:44:26.2714849Z }
2025-08-20T05:44:26.2714900Z 📧 Customer details - email: <EMAIL>, firstName: null, lastName: null
2025-08-20T05:44:26.2714945Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1RxjHFE6FvhUKV1bPzpw3UT0 {
2025-08-20T05:44:26.2714988Z   stripeCustomerId: 'cus_StWJVOg5LVJyJc',
2025-08-20T05:44:26.2715031Z   extractedEmail: '<EMAIL>',
2025-08-20T05:44:26.2715073Z   extractedFirstName: 'NO_FIRST_NAME',
2025-08-20T05:44:26.2715114Z   extractedLastName: 'NO_LAST_NAME',
2025-08-20T05:44:26.2715179Z   subscriptionStatus: 'incomplete_expired',
2025-08-20T05:44:26.2715221Z   hasCustomerName: false
2025-08-20T05:44:26.2715260Z }
2025-08-20T05:44:26.2715305Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-20T05:44:26.2715355Z 🔍 ensureUserProfile called with: customer=cus_StWJVOg5LVJyJc, email=<EMAIL>, firstName=null, lastName=null
2025-08-20T05:44:26.2715403Z 🔍 Looking for existing profile by stripe_customer_id: cus_StWJVOg5LVJyJc
2025-08-20T05:44:26.5307525Z ✅ Found existing profile by stripe_customer_id: 3e5b6233-ec8b-4254-ac6e-30a906046988
2025-08-20T05:44:26.5307951Z ✅ User profile resolved: 3e5b6233-ec8b-4254-ac6e-30a906046988
2025-08-20T05:44:26.5308011Z 👤 Updating profile team admin status: false
2025-08-20T05:44:26.5908680Z ✅ Payment event logged for payment_intent.canceled: pi_3RxjHGE6FvhUKV1b0twLOZAz
2025-08-20T05:44:26.6305435Z ✅ Profile updated - team admin: false
2025-08-20T05:44:26.6316528Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-20T05:44:26.8106364Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-20T05:44:26.8110899Z 💾 Creating/updating subscription record in database
2025-08-20T05:44:26.9493344Z ❌ Error updating subscription: {
2025-08-20T05:44:26.9493751Z   code: '22P02',
2025-08-20T05:44:26.9493803Z   details: null,
2025-08-20T05:44:26.9493850Z   hint: null,
2025-08-20T05:44:26.9493905Z   message: 'invalid input value for enum subscription_status: "incomplete_expired"'
2025-08-20T05:44:26.9493951Z }
2025-08-20T05:44:29.7095301Z 🚀 Webhook received at: 2025-08-20T05:44:29.707Z
2025-08-20T05:44:29.7095783Z 🔧 Environment check: {
2025-08-20T05:44:29.7095846Z   hasStripeSecret: true,
2025-08-20T05:44:29.7095895Z   hasWebhookSecret: true,
2025-08-20T05:44:29.7095941Z   hasSupabaseUrl: true,
2025-08-20T05:44:29.7096117Z   hasServiceKey: true,
2025-08-20T05:44:29.7096169Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:29.7096218Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:29.7096264Z }
2025-08-20T05:44:30.5509307Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:30.5509671Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:30.5509737Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:30.5509791Z ✅ Stripe event verified: invoice.paid ID: evt_1Ry4LEE6FvhUKV1bOmxejzG0
2025-08-20T05:44:30.5509844Z ✅ Webhook event logged: evt_1Ry4LEE6FvhUKV1bOmxejzG0 with status: processing
2025-08-20T05:44:30.5509896Z 👉 Handling invoice.paid
2025-08-20T05:44:30.5509976Z Skipping non-subscription invoice
2025-08-20T05:44:33.9596369Z 🚀 Webhook received at: 2025-08-20T05:44:33.958Z
2025-08-20T05:44:33.9597062Z 🔧 Environment check: {
2025-08-20T05:44:33.9597118Z   hasStripeSecret: true,
2025-08-20T05:44:33.9597168Z   hasWebhookSecret: true,
2025-08-20T05:44:33.9597213Z   hasSupabaseUrl: true,
2025-08-20T05:44:33.9597254Z   hasServiceKey: true,
2025-08-20T05:44:33.9597299Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:33.9597341Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:33.9597384Z }
2025-08-20T05:44:33.9757096Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:34.0925168Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:34.2871788Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:34.3040838Z ✅ Stripe event verified: customer.subscription.created ID: evt_1Ry4LEE6FvhUKV1b0wi1hc9S
2025-08-20T05:44:34.4783044Z ⚠️ Duplicate event detected: evt_1Ry4LEE6FvhUKV1b0wi1hc9S, status: processed
2025-08-20T05:44:37.2531924Z 🚀 Webhook received at: 2025-08-20T05:44:37.252Z
2025-08-20T05:44:37.2647209Z 🔧 Environment check: {
2025-08-20T05:44:37.2647758Z   hasStripeSecret: true,
2025-08-20T05:44:37.2647817Z   hasWebhookSecret: true,
2025-08-20T05:44:37.2647960Z   hasSupabaseUrl: true,
2025-08-20T05:44:37.2648011Z   hasServiceKey: true,
2025-08-20T05:44:37.2648063Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-20T05:44:37.2648113Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-20T05:44:37.2648163Z }
2025-08-20T05:44:37.2648218Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-20T05:44:37.4744955Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-20T05:44:37.6745849Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-20T05:44:37.6746479Z ✅ Stripe event verified: customer.updated ID: evt_1Ry4LEE6FvhUKV1bbaW9FcIl
2025-08-20T05:44:37.9827903Z ✅ Webhook event logged: evt_1Ry4LEE6FvhUKV1bbaW9FcIl with status: processing
2025-08-20T05:44:37.9837698Z 👉 Handling customer.updated (invoice_settings) {
2025-08-20T05:44:37.9837992Z   id: 'cus_Sts5FHWtFR7IQs',
2025-08-20T05:44:37.9838053Z   default_payment_method: 'pm_1Ry4LAE6FvhUKV1bKuOxvWCO'
2025-08-20T05:44:37.9838107Z }
2025-08-20T05:46:40  No new trace in the past 1 min(s).