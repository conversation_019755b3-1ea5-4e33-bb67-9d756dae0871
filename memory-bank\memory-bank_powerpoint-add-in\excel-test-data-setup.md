# Excel Link Test Data Creation Script

## Sample Data for Testing

### Chart Data (Sheet1)
```
Month       Sales    Profit   Expenses
January     45000    12000    33000
February    52000    15000    37000
March       48000    13500    34500
April       55000    16000    39000
May         61000    18500    42500
June        58000    17000    41000
```

### Table Data (Sheet2) 
```
Product        Q1      Q2      Q3      Q4
Widget A       1250    1340    1420    1510
Widget B       980     1050    1180    1290
Widget C       1560    1680    1750    1820
Widget D       890     920     970     1040
```

### Instructions for Manual Setup:
1. Open Excel (already running - Process ID: 71208)
2. Create new workbook or use existing
3. Create Sheet1 with sales data above
4. Create charts from Sheet1 data:
   - Bar chart for Sales vs Month
   - Line chart for Profit trend
   - Pie chart for expense breakdown
5. Create Sheet2 with product data
6. Format as table with headers
7. Save workbook as "ExcelLinkTest.xlsx"

### Test Objects to Create:
- [ ] Bar Chart (Sales by Month)
- [ ] Line Chart (Profit Trend) 
- [ ] Pie Chart (Expense Categories)
- [ ] Data Table (Product Quarterly Data)
- [ ] Formatted Range (Sales Summary)
