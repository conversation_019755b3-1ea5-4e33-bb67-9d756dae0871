---
title: QuantBoost Licensing API - Supabase Table Schemas
purpose: Outlines the schemas for the tables in the Supabase `public` schema for the QuantBoost API project.
last_updated: 2025-09-02T00:00:00.000Z # Updated charge_receipts schema + webhook coverage and name capture notes
tags: ["database", "schema", "supabase", "public", "analytics", "disputes", "payments", "webhook-testing"]
project_ref: izoutrnsxaaoueljiimu
---

# Supabase Table Schemas (as of 2025-08-19)

This document outlines the schemas for the tables in the Supabase `public` schema for the QuantBoost API project (`izoutrnsxaaoueljiimu`).
It reflects decisions on `NOT NULL` constraints and default values, including comprehensive analytics infrastructure for dispute tracking, payment monitoring, business intelligence, and complete webhook testing validation.

**Database Summary:**
- **Total Tables**: 16 (7 core + 9 analytics/enterprise)
- **Core Infrastructure**: Licensing, subscriptions, profiles, activations, webhook tracking, license events
- **Analytics Layer**: Disputes, refunds, payment events, customer events, system alerts, fraud reviews, setup failures
- **Enterprise Layer**: Enterprise customers, enterprise invoices, charge receipts
- **Business Intelligence**: 4 analytics views (customer_lifecycle_analytics, dispute_analytics, payment_health_metrics, refund_analytics)
- **Stored Procedures**: 14 functions for data processing and analytics
- **Webhook Testing**: Complete validation framework for all 29 Stripe webhook events with Supabase integration testing

**Key for Default Values:**
- `NOW()`: Current timestamp.
- `uuid_generate_v4()`: Generates a new UUID v4.
- Specific values like `'inactive'::license_status` or `TRUE` are literal defaults.

## Table of Contents

### Core Tables
- [`license_activations`](#license_activations) - Machine activation tracking
- [`licenses`](#licenses) - License management with enhanced enterprise support
- [`profiles`](#profiles) - User profiles with extended customer data
- [`subscriptions`](#subscriptions) - Subscription management with enhanced billing info
- [`license_events`](#license_events) - License activity logging
- [`webhook_events`](#webhook_events) - Webhook processing with status tracking
- [`charge_receipts`](#charge_receipts) - Payment transaction records

### Enterprise Tables
- [`enterprise_customers`](#enterprise_customers) - Enterprise customer management
- [`enterprise_invoices`](#enterprise_invoices) - Enterprise billing and invoicing
- [`enterprise_trials`](#enterprise_trials) - Enterprise trial management (30-day trials)
- [`trial_invites`](#trial_invites) - Tokenized trial invitations and acceptance tracking

### Analytics Tables
- [`disputes`](#disputes) - Stripe dispute tracking and management
- [`refunds`](#refunds) - Refund processing and approval workflows
- [`payment_events`](#payment_events) - Payment attempt history and risk analysis
- [`customer_events`](#customer_events) - Customer lifecycle and behavior tracking
- [`system_alerts`](#system_alerts) - Automated alerting with severity management
- [`fraud_reviews`](#fraud_reviews) - Stripe fraud review tracking
- [`setup_failures`](#setup_failures) - Failed payment method setup tracking

---

## Core Tables

## `license_activations`

- RLS Enabled: true
- RLS Forced: false

| Column Name         | Data Type                | Default Value        | Nullable | Unique | Comment |
|---------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `license_id`        | uuid                     |                      | **false**|        | FK to `licenses.id` |
| `machine_id`        | text                     |                      | **false**|        | Unique per license if `(license_id, machine_id)` is unique constraint |
| `activated_at`      | timestamp with time zone | `NOW()`              | **false**|        | Time of first activation for this record. |
| `last_validated_at` | timestamp with time zone | `NOW()`              | true     |        | Last time this activation was checked/used. |
| `is_active`         | boolean                  | `TRUE`               | **false**|        | Whether this specific machine activation is currently active. |
| `email`             | text                     |                      | true     |        | Email of the user at the time of activation (denormalized). |
| `version_number`    | text                     |                      | true     |        | Version number of the VSTO add-in that performed the activation. |

**Relationships:**
- `license_id` references `licenses(id)` `ON DELETE CASCADE` (Recommended: if a license is deleted, its activations should also be deleted).

**Indexes (Recommended):**
- Index on `license_id`.
- Unique constraint on `(license_id, machine_id)` if a license can only be active on one machine at a time, or if you want to prevent duplicate activation records for the same machine/license pair.

## `licenses`

- RLS Enabled: true
- RLS Forced: false

| Column Name         | Data Type                | Default Value        | Nullable | Unique | Comment |
|---------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `user_id`           | uuid                     |                      | true     |        | FK to `profiles.id`. User this license is assigned to. |
| `subscription_id`   | uuid                     |                      | true     |        | FK to `subscriptions.id`. Subscription providing this license (NULL for system trials). |
| `product_id`        | text                     |                      | **false**|        | Identifier for the product this license is for. |
| `status`            | USER-DEFINED (license_status) | `'inactive'::license_status` | **false**|        | Enum: "active", "inactive", "canceled", "trial_active", "expired", "graceperiod", "unassigned", "assigned", "revoked", "pending". Default 'inactive'. Added 'pending' for webhook processing. |
| `created_at`        | timestamp with time zone | `NOW()`              | **false**|        |         |
| `updated_at`        | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |
| `email`             | text                     | `'not-activated'`    | true     |        | Email assigned to license. Default 'not-activated' for unassigned team licenses. |
| `trial_start_date`  | timestamp with time zone |                      | true     |        |         |
| `trial_expiry_date` | timestamp with time zone |                      | true     |        |         |
| `license_tier`      | text                     |                      | true     |        | e.g., "Basic-Individual", "Basic-Team". Constraints: CHECK (license_tier IN ('Basic-Individual', 'Basic-Team')). |
| `license_key`       | text                     | `uuid_generate_v4()` | **false**| true   | User-facing or system-used license key. |
| `expiry_date`       | timestamp with time zone |                      | true     |        | Expiry for paid licenses. Calculated from billing period or price interval. |
| `max_activations`   | integer                  | `1`                  | **false**|        | Default number of concurrent machine activations allowed. |
| `team_admin`        | text                     |                      | true     |        | Email of team admin (purchaser) for team licenses. NULL for individual licenses. |
| `enterprise_customer_id` | uuid                |                      | true     |        | Enterprise customer identifier for large accounts and analytics linking. |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE SET NULL` (Recommended: if a profile is deleted, unassign the license).
- `subscription_id` references `subscriptions(id)` `ON DELETE SET NULL` (Recommended: if a subscription is deleted, licenses become unlinked but might remain, or `ON DELETE CASCADE` if licenses should be removed too).
- `id` is referenced by `license_activations(license_id)`.
- `id` is referenced by `license_events(license_id)`.

**Indexes (Recommended):**
- Index on `user_id`.
- Index on `subscription_id`.
- Index on `product_id`.
- Index on `status`.
- Index on `email`.
- Index on `team_admin`.
- Unique constraint `idx_licenses_unique_activated_email_per_sub` on `(subscription_id, email)` WHERE email IS NOT NULL AND email != 'not-activated' (allows multiple 'not-activated' per subscription).

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`.

## `profiles`

- RLS Enabled: true
- RLS Forced: false

| Column Name           | Data Type                | Default Value | Nullable | Unique | Comment |
|-----------------------|--------------------------|---------------|----------|--------|---------|
| `id`                  | uuid                     |               | **false**| PK     | FK to `auth.users.id`. Should match `auth.users.id`. |
| `email`               | text                     |               | **false**| true   | Should match `auth.users.email`. |
| `stripe_customer_id`  | text                     |               | true     | true   |         |
| `is_team_admin`       | boolean                  | `FALSE`       | **false**|        | Renamed from `is_enterprise_admin`. Indicates if user is admin of a team plan. |
| `created_at`          | timestamp with time zone | `NOW()`       | **false**|        |         |
| `updated_at`          | timestamp with time zone | `NOW()`       | **false**|        | Auto-updates via trigger. |
| `first_name`          | text                     |               | true     |        | Customer first name for analytics and personalization. |
| `last_name`           | text                     |               | true     |        | Customer last name for analytics and personalization. |
| `role`                | text                     | `'customer'` | true     |        | User role for access control and analytics. Check: ('customer', 'sales', 'admin', 'enterprise_admin'). |

**Relationships:**
- `id` references `auth.users(id)` `ON DELETE CASCADE` (Critical: if auth user is deleted, profile should be too).
- `id` is referenced by `licenses(user_id)`.
- `id` is referenced by `subscriptions(user_id)`.

**Triggers (Recommended):**
- Trigger on `auth.users` `AFTER INSERT` to create a corresponding `profiles` row.
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`.

## `subscriptions`

- RLS Enabled: true
- RLS Forced: false

| Column Name              | Data Type                | Default Value        | Nullable | Unique | Comment |
|--------------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                     | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `user_id`                | uuid                     |                      | **false**|        | FK to `profiles.id`. Owner of the subscription. |
| `stripe_subscription_id` | text                     |                      | true     | true   | Stripe's ID for this subscription. |
| `status`                 | USER-DEFINED (subscription_status) |             | **false**|        | Enum: "trialing", "active", "past_due", "canceled", "incomplete". |
| `quantity`               | integer                  | `1`                  | **false**|        | Number of seats/licenses in this subscription. |
| `current_period_start`   | timestamp with time zone |                      | true     |        | From Stripe. |
| `current_period_end`     | timestamp with time zone |                      | true     |        | From Stripe. Primary expiry driver for associated licenses. |
| `trial_start`            | timestamp with time zone |                      | true     |        | Trial start date from Stripe (kept as trial_start for Stripe compatibility). |
| `trial_end`              | timestamp with time zone |                      | true     |        | Trial end date from Stripe (kept as trial_end for Stripe compatibility). |
| `cancel_at_period_end`   | boolean                  | `FALSE`              | true     |        | From Stripe. Note: This field is nullable in current schema. |
| `canceled_at`            | timestamp with time zone |                      | true     |        | From Stripe. |
| `plan_id`                | text                     |                      | true     |        | Stripe plan/price ID for analytics and billing management. |
| `product_name`           | text                     |                      | true     |        | Human-readable product name for dashboard display. |
| `amount`                 | integer                  |                      | true     |        | Subscription amount in cents for revenue analytics. |
| `currency`               | text                     |                      | true     |        | Subscription currency (e.g., 'usd') for financial reporting. |
| `interval`               | text                     |                      | true     |        | Billing interval ('month', 'year') for subscription management. |
| `created_at`             | timestamp with time zone | `NOW()`              | **false**|        |         |
| `updated_at`             | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |
| `email`                  | text                     |                      | true     |        | Email of subscription owner for easy identification. Added for webhook processing. |
| `plan_id`                | text                     |                      | true     |        | Stripe plan/price ID for analytics and billing management. |
| `enterprise_customer_id` | uuid                     |                      | true     |        | FK to `enterprise_customers.id`. Enterprise customer if applicable. |
| `product_name`           | text                     |                      | true     |        | Human-readable product name for dashboard display. |
| `amount`                 | integer                  |                      | true     |        | Subscription amount in cents for revenue analytics. |
| `currency`               | text                     | `'usd'`              | true     |        | Subscription currency (e.g., 'usd') for financial reporting. |
| `interval`               | text                     |                      | true     |        | Billing interval ('month', 'year') for subscription management. |
| `interval_count`         | integer                  | `1`                  | true     |        | Number of intervals between billings. |
| `subscription_created`   | timestamp with time zone |                      | true     |        | When subscription was created in Stripe. |
| `billing_cycle_anchor`   | timestamp with time zone |                      | true     |        | Stripe billing cycle anchor date. |
| `start_date`             | timestamp with time zone |                      | true     |        | Subscription start date. |
| `cancel_at`              | timestamp with time zone |                      | true     |        | When subscription is scheduled to be canceled. |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE RESTRICT` (Recommended: prevent deleting a profile if they have active subscriptions, or `CASCADE` if subscriptions should be deleted too).
- `id` is referenced by `licenses(subscription_id)`.

**Indexes (Recommended):**
- Index on `user_id`.
- Index on `stripe_subscription_id`.
- Index on `status`.

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`.

## `license_events`

- RLS Enabled: true
- RLS Forced: false

| Column Name    | Data Type                | Default Value        | Nullable | Unique | Comment |
|----------------|--------------------------|----------------------|----------|--------|---------|
| `id`           | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `event_type`   | text                     |                      | **false**|        | e.g., "validation_attempt", "activation_success". |
| `license_id`   | uuid                     |                      | true     |        | FK to `licenses.id`. Event might not always have a license. |
| `machine_id`   | text                     |                      | **false**|        | Device identifier involved in the event. |
| `email`        | text                     |                      | true     |        | Email associated with the event (user or license email). |
| `status`       | text                     |                      | true     |        | Outcome or specific status of the event, e.g., "success_active", "failure_invalid_key". |
| `created_at`   | timestamp with time zone | `NOW()`              | **false**|        | Timestamp of the event. |

**Relationships:**
- `license_id` references `licenses(id)` `ON DELETE SET NULL` (Recommended: if a license is deleted, keep the event but unlink it).

**Indexes (Recommended):**
- Index on `event_type`.
- Index on `license_id`.
- Index on `email`.
- Index on `created_at`.

## `webhook_events`

- RLS Enabled: true
- RLS Forced: false

| Column Name         | Data Type                | Default Value        | Nullable | Unique | Comment |
|---------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `stripe_event_id`   | text                     |                      | **false**| true   | Stripe's unique event ID for idempotency protection. |
| `event_type`        | text                     |                      | **false**|        | Type of Stripe webhook event (e.g., "checkout.session.completed"). |
| `processed_at`      | timestamp with time zone | `NOW()`              | true     |        | When the webhook was processed. |
| `created_at`        | timestamp with time zone | `NOW()`              | true     |        | When the webhook event record was created. |
| `status`            | character varying        | `'processing'`       | true     |        | Processing status: 'processing', 'processed', 'failed'. |
| `error_message`     | text                     |                      | true     |        | Error details if webhook processing failed. |
| `updated_at`        | timestamp with time zone | `NOW()`              | true     |        | Auto-updates via trigger for status tracking. |

**Relationships:**
- No foreign key relationships.

**Indexes (Recommended):**
- Index on `stripe_event_id` (unique constraint already provides this).
- Index on `event_type`.
- Index on `processed_at`.

**Purpose:**
This table provides idempotency protection for Stripe webhook processing, preventing duplicate processing of the same webhook event.

## `charge_receipts`

- RLS Enabled: true
- RLS Forced: false

| Column Name         | Data Type                | Default Value        | Nullable | Unique | Comment |
|---------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `stripe_charge_id`  | text                     |                      | **false**| true   | Stripe charge ID for payment tracking. |
| `stripe_payment_intent_id` | text              |                      | true     |        | Stripe payment intent ID for enhanced tracking. |
| `stripe_subscription_id` | text                |                      | true     |        | Stripe subscription ID associated to this charge (denormalized). |
| `user_id`           | uuid                     |                      | **false**|        | FK to `profiles.id`. User who made the payment. |
| `amount`            | integer                  |                      | **false**|        | Charge amount in cents. |
| `currency`          | text                     | `'usd'`              | true     |        | Payment currency. |
| `receipt_url`       | text                     |                      | true     |        | URL to Stripe receipt. |
| `receipt_number`    | text                     |                      | true     |        | REMOVED 2025-09-02 (no longer populated by Stripe consistently). |
| `status`            | text                     |                      | **false**|        | Charge status: 'succeeded', 'pending', 'failed'. |
| `created_at`        | timestamp with time zone | `NOW()`              | true     |        |         |
| `updated_at`        | timestamp with time zone | `NOW()`              | true     |        | Auto-updates via trigger. |
| `refunded_amount`   | integer                  | `0`                  | true     |        | Amount refunded from this charge in cents. |
| `refund_status`     | text                     | `'none'`             | true     |        | Refund status: 'none', 'partial', 'full'. |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE SET NULL`
- `stripe_subscription_id` links to `subscriptions(stripe_subscription_id)` for analytics joins (no FK)
- `stripe_charge_id` is referenced by `disputes(stripe_charge_id)`
- `stripe_charge_id` is referenced by `refunds(stripe_charge_id)`

**Indexes (Recommended):**
- Index on `user_id`
- Index on `stripe_subscription_id` (added 2025-09-02)
- Index on `stripe_charge_id` (unique constraint already provides this)
- Index on `stripe_payment_intent_id`
- Index on `status`
- Index on `refund_status`
- Index on `created_at`

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`

---

## Recent changes (2025-09-02)

- charge_receipts: Added `stripe_subscription_id` (TEXT), backfilled from invoices; removed `subscription_id` FK and `receipt_number`; added index on `stripe_subscription_id`.
- Webhook coverage expanded: now handles `payment_intent.succeeded`/`payment_intent.payment_failed` and `customer.updated` with robust idempotency and error logging. `checkout.session.completed` also fills profile names when present.

### How we capture first/last name without UI fields

- Stripe Checkout and Payment Element send name in two places we can consume without custom inputs:
  1) `checkout.session.completed`: `session.customer_details.name` is provided by Stripe when available (e.g., Cardholder Name from Checkout). We split and store into `profiles.first_name/last_name` if missing.
  2) `payment_intent.succeeded`: We read `charge.billing_details.name` from the associated charge and backfill `profiles.first_name/last_name` if still missing.
- Because of this, no change to `src/app/checkout/[priceId]/page.tsx` is strictly required to collect names. If product wants guaranteed capture, we can add explicit first/last inputs later and prefer those over Stripe-provided values.

---

## Enterprise Tables

## `enterprise_customers`

- RLS Enabled: true
- RLS Forced: false

| Column Name              | Data Type                | Default Value        | Nullable | Unique | Comment |
|--------------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                     | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `company_name`           | text                     |                      | **false**|        | Enterprise company name. |
| `contact_email`          | text                     |                      | **false**|        | Primary contact email for enterprise account. |
| `contact_name`           | text                     |                      | true     |        | Primary contact person name. |
| `contact_phone`          | text                     |                      | true     |        | Primary contact phone number. |
| `billing_address`        | jsonb                    |                      | true     |        | Complete billing address information. |
| `stripe_customer_id`     | text                     |                      | true     | true   | Associated Stripe customer ID. |
| `payment_terms`          | integer                  | `30`                 | true     |        | Payment terms in days (e.g., Net 30). |
| `license_quantity`       | integer                  |                      | **false**|        | Number of licenses allocated to enterprise. |
| `license_tier`           | text                     |                      | **false**|        | Enterprise license tier. Check: ('Basic-Enterprise', 'Premium-Enterprise'). |
| `annual_contract_value`  | numeric                  |                      | true     |        | Total annual contract value. |
| `contract_start_date`    | timestamp with time zone |                      | true     |        | Enterprise contract start date. |
| `contract_end_date`      | timestamp with time zone |                      | true     |        | Enterprise contract end date. |
| `sales_rep_id`           | uuid                     |                      | true     |        | FK to `auth.users.id`. Assigned sales representative. |
| `status`                 | text                     | `'prospect'`         | true     |        | Enterprise status. Check: ('prospect', 'active', 'suspended', 'cancelled'). |
| `created_at`             | timestamp with time zone | `NOW()`              | true     |        |         |
| `updated_at`             | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |

**Relationships:**
- `sales_rep_id` references `auth.users(id)` `ON DELETE SET NULL`
- `id` is referenced by `licenses(enterprise_customer_id)`
- `id` is referenced by `subscriptions(enterprise_customer_id)`
- `id` is referenced by `enterprise_invoices(enterprise_customer_id)`

**Indexes (Recommended):**
- Index on `stripe_customer_id` (unique constraint already provides this)
- Index on `sales_rep_id`
- Index on `status`
- Index on `license_tier`

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`

## `enterprise_invoices`

- RLS Enabled: true
- RLS Forced: false

| Column Name              | Data Type                | Default Value        | Nullable | Unique | Comment |
|--------------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                     | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `enterprise_customer_id` | uuid                     |                      | true     |        | FK to `enterprise_customers.id`. Related enterprise customer. |
| `stripe_invoice_id`      | text                     |                      | true     | true   | Stripe invoice ID if generated through Stripe. |
| `invoice_number`         | text                     |                      | true     |        | Human-readable invoice number. |
| `amount_due`             | numeric                  |                      | **false**|        | Total invoice amount due. |
| `currency`               | text                     | `'USD'`              | true     |        | Invoice currency. |
| `due_date`               | timestamp with time zone |                      | true     |        | Invoice payment due date. |
| `invoice_date`           | timestamp with time zone | `NOW()`              | true     |        | Invoice issue date. |
| `status`                 | text                     | `'draft'`            | true     |        | Invoice status. Check: ('draft', 'sent', 'paid', 'void', 'uncollectible'). |
| `stripe_hosted_url`      | text                     |                      | true     |        | Stripe-hosted invoice URL. |
| `stripe_pdf_url`         | text                     |                      | true     |        | Stripe PDF invoice URL. |
| `description`            | text                     |                      | true     |        | Invoice description or memo. |
| `line_items`             | jsonb                    |                      | true     |        | Detailed line items and pricing. |
| `created_by`             | uuid                     |                      | true     |        | FK to `auth.users.id`. User who created the invoice. |
| `created_at`             | timestamp with time zone | `NOW()`              | true     |        |         |
| `updated_at`             | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |

**Relationships:**
- `enterprise_customer_id` references `enterprise_customers(id)` `ON DELETE SET NULL`
- `created_by` references `auth.users(id)` `ON DELETE SET NULL`

**Indexes (Recommended):**
- Index on `enterprise_customer_id`
- Index on `stripe_invoice_id` (unique constraint already provides this)
- Index on `status`
- Index on `invoice_date`
- Index on `created_by`

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`

---

## `enterprise_trials`

- RLS Enabled: true
- RLS Forced: false

| Column Name   | Data Type                | Default Value        | Nullable | Unique | Comment |
|---------------|--------------------------|----------------------|----------|--------|---------|
| `id`          | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `customer_id` | uuid                     |                      | **false**|        | FK to `enterprise_customers.id`. |
| `status`      | text                     | `'active'`           | **false**|        | Check: ('active','expired','converted'). |
| `seat_limit`  | integer                  | `50`                 | **false**|        | Max seats for the trial. |
| `seats_used`  | integer                  | `0`                  | **false**|        | Accepted invites count. |
| `expires_at`  | timestamp with time zone |                      | **false**|        | Trial expiry (typically now() + interval '30 days'). |
| `converted_at`| timestamp with time zone |                      | true     |        | When trial converted to paid. |
| `created_at`  | timestamp with time zone | `NOW()`              | **false**|        |         |

**Relationships:**
- `customer_id` references `enterprise_customers(id)` `ON DELETE CASCADE`

**Indexes (Recommended):**
- Index on `customer_id` (see: `idx_trials_customer_id`)
- Consider index on `(status, expires_at)` for listing active/expiring trials

---

## `trial_invites`

- RLS Enabled: true
- RLS Forced: false

| Column Name    | Data Type                | Default Value        | Nullable | Unique | Comment |
|----------------|--------------------------|----------------------|----------|--------|---------|
| `id`           | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `trial_id`     | uuid                     |                      | **false**|        | FK to `enterprise_trials.id`. |
| `email`        | text                     |                      | **false**|        | Invitee email. |
| `invite_token` | text                     | `uuid_generate_v4()` | **false**| true   | Token embedded in invite link; never exposed in list UIs. |
| `accepted_at`  | timestamp with time zone |                      | true     |        | When invite was accepted. |
| `user_id`      | uuid                     |                      | true     |        | Supabase auth user id that accepted. |
| `created_at`   | timestamp with time zone | `NOW()`              | **false**|        |         |

**Relationships:**
- `trial_id` references `enterprise_trials(id)` `ON DELETE CASCADE`
- `user_id` references `auth.users(id)` `ON DELETE SET NULL` (optional linkage)

**Indexes (Recommended):**
- Index on `trial_id` (see: `idx_invites_trial_id`)
- Index on `email` (see: `idx_invites_email`)
- Unique index on `invite_token`

**Notes:**
- Access typically via server (service role) API. For MVP, RLS policies can be deny-all to client roles. A secure RPC `accept_trial_invite(token, user_id)` performs atomic validation and seat increment.

---

## Analytics Tables

## `disputes`

- RLS Enabled: true
- RLS Forced: false

| Column Name              | Data Type                | Default Value        | Nullable | Unique | Comment |
|--------------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                     | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `user_id`                | uuid                     |                      | true     |        | FK to `profiles.id`. Customer involved in dispute. |
| `subscription_id`        | uuid                     |                      | true     |        | FK to `subscriptions.id`. Related subscription if applicable. |
| `stripe_dispute_id`      | text                     |                      | **false**| true   | Stripe's unique dispute identifier. |
| `stripe_charge_id`       | text                     |                      | **false**|        | Stripe charge ID that was disputed. |
| `stripe_payment_intent_id` | text                   |                      | true     |        | Associated payment intent for enhanced tracking. |
| `amount`                 | integer                  |                      | **false**|        | Disputed amount in cents. |
| `currency`               | text                     | `'usd'`              | **false**|        | Dispute currency. |
| `reason`                 | text                     |                      | **false**|        | Dispute reason category from Stripe. |
| `status`                 | USER-DEFINED (dispute_status) | `'warning_needs_response'::dispute_status` | **false**|        | Enum: "warning_needs_response", "warning_under_review", "warning_closed", "needs_response", "under_review", "charge_refunded", "won", "lost". |
| `created`                | timestamp with time zone |                      | **false**|        | When dispute was created in Stripe. |
| `evidence_due_by`        | timestamp with time zone |                      | true     |        | Deadline for submitting evidence. |
| `evidence_details`       | jsonb                    |                      | true     |        | Evidence submission details and status. |
| `closed_at`              | timestamp with time zone |                      | true     |        | When dispute was resolved. |
| `fee_amount`             | integer                  |                      | true     |        | Dispute fee amount in cents. |
| `fee_currency`           | text                     |                      | true     |        | Dispute fee currency. |
| `fee_refunded`           | boolean                  | `FALSE`              | **false**|        | Whether dispute fee was refunded. |
| `priority`               | integer                  | `1`                  | **false**|        | Internal priority level (1=low, 2=medium, 3=high, 4=critical). |
| `internal_notes`         | text                     |                      | true     |        | Internal team notes about dispute handling. |
| `created_at`             | timestamp with time zone | `NOW()`              | **false**|        |         |
| `updated_at`             | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE SET NULL`
- `subscription_id` references `subscriptions(id)` `ON DELETE SET NULL`
- `stripe_charge_id` references `charge_receipts(stripe_charge_id)` `ON DELETE SET NULL`

**Indexes (Recommended):**
- Index on `user_id`
- Index on `subscription_id`
- Index on `stripe_dispute_id` (unique constraint already provides this)
- Index on `stripe_charge_id`
- Index on `status`
- Index on `reason`
- Index on `priority`
- Index on `evidence_due_by`
- Index on `created`

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`

## `refunds`

- RLS Enabled: true
- RLS Forced: false

| Column Name              | Data Type                | Default Value        | Nullable | Unique | Comment |
|--------------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                     | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `user_id`                | uuid                     |                      | true     |        | FK to `profiles.id`. Customer receiving refund. |
| `subscription_id`        | uuid                     |                      | true     |        | FK to `subscriptions.id`. Related subscription if applicable. |
| `stripe_refund_id`       | text                     |                      | **false**| true   | Stripe's unique refund identifier. |
| `stripe_charge_id`       | text                     |                      | **false**|        | Stripe charge ID being refunded. |
| `stripe_payment_intent_id` | text                   |                      | true     |        | Associated payment intent for enhanced tracking. |
| `amount`                 | integer                  |                      | **false**|        | Refund amount in cents. |
| `currency`               | text                     | `'usd'`              | **false**|        | Refund currency. |
| `reason`                 | text                     |                      | true     |        | Stripe refund reason if provided. |
| `status`                 | text                     | `'pending'`          | **false**|        | Refund status: 'pending', 'succeeded', 'failed', 'canceled'. |
| `refund_type`            | text                     | `'requested'`        | **false**|        | Type: 'requested', 'chargeback', 'dispute_resolution'. |
| `failure_reason`         | text                     |                      | true     |        | Reason for refund failure if applicable. |
| `metadata`               | jsonb                    |                      | true     |        | Additional refund metadata from Stripe. |
| `receipt_number`         | text                     |                      | true     |        | Stripe receipt number for refund. |
| `internal_reason`        | text                     |                      | true     |        | Internal business reason for refund. |
| `approved_by`            | uuid                     |                      | true     |        | FK to `profiles.id`. Team member who approved refund. |
| `approval_notes`         | text                     |                      | true     |        | Notes about refund approval decision. |
| `created_at`             | timestamp with time zone | `NOW()`              | **false**|        |         |
| `updated_at`             | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE SET NULL`
- `subscription_id` references `subscriptions(id)` `ON DELETE SET NULL`
- `stripe_charge_id` references `charge_receipts(stripe_charge_id)` `ON DELETE SET NULL`
- `approved_by` references `profiles(id)` `ON DELETE SET NULL`

**Indexes (Recommended):**
- Index on `user_id`
- Index on `subscription_id`
- Index on `stripe_refund_id` (unique constraint already provides this)
- Index on `stripe_charge_id`
- Index on `status`
- Index on `refund_type`
- Index on `approved_by`
- Index on `created_at`

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`

## `payment_events`

- RLS Enabled: true
- RLS Forced: false

| Column Name              | Data Type                | Default Value        | Nullable | Unique | Comment |
|--------------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                     | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `user_id`                | uuid                     |                      | true     |        | FK to `profiles.id`. Customer involved in payment event. |
| `subscription_id`        | uuid                     |                      | true     |        | FK to `subscriptions.id`. Related subscription if applicable. |
| `stripe_event_id`        | text                     |                      | **false**|        | Stripe event ID that triggered this record. |
| `event_type`             | text                     |                      | **false**|        | Type of payment event (e.g., 'payment_intent.succeeded'). |
| `stripe_payment_intent_id` | text                   |                      | true     |        | Stripe payment intent ID. |
| `stripe_charge_id`       | text                     |                      | true     |        | Stripe charge ID if payment succeeded. |
| `amount`                 | integer                  |                      | true     |        | Payment amount in cents. |
| `currency`               | text                     | `'usd'`              | true     |        | Payment currency. |
| `status`                 | text                     |                      | **false**|        | Payment status: 'succeeded', 'failed', 'pending', 'canceled'. |
| `failure_code`           | text                     |                      | true     |        | Stripe failure code if payment failed. |
| `failure_message`        | text                     |                      | true     |        | Human-readable failure reason. |
| `risk_level`             | text                     |                      | true     |        | Stripe Radar risk level: 'normal', 'elevated', 'highest'. |
| `risk_score`             | integer                  |                      | true     |        | Stripe Radar risk score (0-100). |
| `event_data`             | jsonb                    |                      | true     |        | Complete event payload from Stripe. |
| `created_at`             | timestamp with time zone | `NOW()`              | **false**|        |         |
| `updated_at`             | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE SET NULL`
- `subscription_id` references `subscriptions(id)` `ON DELETE SET NULL`
- `stripe_charge_id` references `charge_receipts(stripe_charge_id)` `ON DELETE SET NULL`

**Indexes (Recommended):**
- Index on `user_id`
- Index on `subscription_id`
- Index on `stripe_event_id`
- Index on `event_type`
- Index on `stripe_payment_intent_id`
- Index on `stripe_charge_id`
- Index on `status`
- Index on `risk_level`
- Index on `created_at`

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`

## `customer_events`

- RLS Enabled: true
- RLS Forced: false

| Column Name              | Data Type                | Default Value        | Nullable | Unique | Comment |
|--------------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                     | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `user_id`                | uuid                     |                      | true     |        | FK to `profiles.id`. Customer involved in event. |
| `stripe_event_id`        | text                     |                      | **false**|        | Stripe event ID that triggered this record. |
| `event_type`             | text                     |                      | **false**|        | Type of customer event (e.g., 'customer.updated'). |
| `stripe_customer_id`     | text                     |                      | **false**|        | Stripe customer ID. |
| `previous_data`          | jsonb                    |                      | true     |        | Previous customer data before change. |
| `current_data`           | jsonb                    |                      | true     |        | Current customer data after change. |
| `changes`                | jsonb                    |                      | true     |        | Specific fields that changed. |
| `created_at`             | timestamp with time zone | `NOW()`              | **false**|        |         |
| `updated_at`             | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE SET NULL`

**Indexes (Recommended):**
- Index on `user_id`
- Index on `stripe_event_id`
- Index on `event_type`
- Index on `stripe_customer_id`
- Index on `created_at`

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`

## `system_alerts`

- RLS Enabled: true
- RLS Forced: false

| Column Name              | Data Type                | Default Value        | Nullable | Unique | Comment |
|--------------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                     | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `alert_type`             | text                     |                      | **false**|        | Type: 'dispute_overdue', 'payment_failure', 'refund_request', 'high_risk_transaction'. |
| `severity`               | text                     | `'medium'`           | **false**|        | Severity: 'low', 'medium', 'high', 'critical'. |
| `title`                  | text                     |                      | **false**|        | Brief alert title for dashboard display. |
| `message`                | text                     |                      | **false**|        | Detailed alert message. |
| `user_id`                | uuid                     |                      | true     |        | FK to `profiles.id`. Related customer if applicable. |
| `dispute_id`             | uuid                     |                      | true     |        | FK to `disputes.id`. Related dispute if applicable. |
| `refund_id`              | uuid                     |                      | true     |        | FK to `refunds.id`. Related refund if applicable. |
| `subscription_id`        | uuid                     |                      | true     |        | FK to `subscriptions.id`. Related subscription if applicable. |
| `status`                 | text                     | `'active'`           | **false**|        | Status: 'active', 'acknowledged', 'resolved', 'dismissed'. |
| `acknowledged_by`        | uuid                     |                      | true     |        | FK to `profiles.id`. Team member who acknowledged alert. |
| `acknowledged_at`        | timestamp with time zone |                      | true     |        | When alert was acknowledged. |
| `resolved_by`            | uuid                     |                      | true     |        | FK to `profiles.id`. Team member who resolved alert. |
| `resolved_at`            | timestamp with time zone |                      | true     |        | When alert was resolved. |
| `resolution_notes`       | text                     |                      | true     |        | Notes about alert resolution. |
| `metadata`               | jsonb                    |                      | true     |        | Additional alert context and data. |
| `created_at`             | timestamp with time zone | `NOW()`              | **false**|        |         |
| `updated_at`             | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE SET NULL`
- `dispute_id` references `disputes(id)` `ON DELETE CASCADE`
- `refund_id` references `refunds(id)` `ON DELETE CASCADE`
- `subscription_id` references `subscriptions(id)` `ON DELETE SET NULL`
- `acknowledged_by` references `profiles(id)` `ON DELETE SET NULL`
- `resolved_by` references `profiles(id)` `ON DELETE SET NULL`

**Indexes (Recommended):**
- Index on `alert_type`
- Index on `severity`
- Index on `user_id`
- Index on `dispute_id`
- Index on `refund_id`
- Index on `subscription_id`
- Index on `status`
- Index on `acknowledged_by`
- Index on `resolved_by`
- Index on `created_at`

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`

## `fraud_reviews`

- RLS Enabled: true
- RLS Forced: false

| Column Name              | Data Type                | Default Value        | Nullable | Unique | Comment |
|--------------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                     | uuid                     | `uuid_generate_v4()` | **false**| PK     | Tracks Stripe fraud reviews (review.opened, review.closed events) |
| `stripe_review_id`       | text                     |                      | **false**| true   | Stripe's unique review identifier. |
| `stripe_charge_id`       | text                     |                      | true     |        | Stripe charge ID under review. |
| `stripe_payment_intent_id` | text                   |                      | true     |        | Associated payment intent. |
| `user_id`                | uuid                     |                      | true     |        | FK to `profiles.id`. Customer under review. |
| `reason`                 | text                     |                      | **false**|        | Reason for fraud review. |
| `status`                 | text                     | `'open'`             | **false**|        | Review status: 'open', 'closed'. |
| `closed_reason`          | text                     |                      | true     |        | Reason for review closure if applicable. |
| `opened_at`              | timestamp with time zone |                      | true     |        | When the review was opened. |
| `closed_at`              | timestamp with time zone |                      | true     |        | When the review was closed. |
| `created_at`             | timestamp with time zone | `NOW()`              | **false**|        |         |
| `updated_at`             | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE SET NULL`

**Indexes (Recommended):**
- Index on `user_id`
- Index on `stripe_review_id` (unique constraint already provides this)
- Index on `stripe_charge_id`
- Index on `status`
- Index on `opened_at`

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`

## `setup_failures`

- RLS Enabled: true
- RLS Forced: false

| Column Name              | Data Type                | Default Value        | Nullable | Unique | Comment |
|--------------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                     | uuid                     | `uuid_generate_v4()` | **false**| PK     | Tracks failed setup intents for payment method setup issues |
| `stripe_setup_intent_id` | text                     |                      | **false**| true   | Stripe's unique setup intent identifier. |
| `user_id`                | uuid                     |                      | true     |        | FK to `profiles.id`. Customer attempting setup. |
| `error_message`          | text                     |                      | true     |        | Human-readable error message. |
| `error_code`             | text                     |                      | true     |        | Stripe error code. |
| `error_type`             | text                     |                      | true     |        | Type of setup error. |
| `failed_at`              | timestamp with time zone |                      | **false**|        | When the setup attempt failed. |
| `created_at`             | timestamp with time zone | `NOW()`              | **false**|        |         |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE SET NULL`

**Indexes (Recommended):**
- Index on `user_id`
- Index on `stripe_setup_intent_id` (unique constraint already provides this)
- Index on `error_code`
- Index on `error_type`
- Index on `failed_at`

---

## Business Intelligence Views

The analytics infrastructure includes 4 PostgreSQL views that provide pre-computed business intelligence metrics for the dashboard. These views combine data across multiple tables to deliver comprehensive analytics.

### `customer_lifecycle_analytics`
Comprehensive customer analytics combining profile, subscription, payment, dispute, and refund data with calculated health scores.

**Source Tables:** `profiles`, `subscriptions`, `charge_receipts`, `disputes`, `refunds`

**Key Calculated Fields:**
- `customer_health_score`: Calculated score (0-100) based on payment history, disputes, and refunds
- `total_revenue`: Sum of all successful charges for the customer
- `avg_charge_amount`: Average charge amount per customer
- `active_subscriptions`: Count of currently active subscriptions
- `canceled_subscriptions`: Count of canceled subscriptions
- Aggregated counts for total charges, disputes, and refunds

### `dispute_analytics`
Comprehensive view combining dispute data with customer and subscription information, including calculated fields for overdue status, time until due, resolution status, and days to resolution.

**Source Tables:** `disputes`, `profiles`, `subscriptions`

**Key Calculated Fields:**
- `is_overdue`: Boolean indicating if evidence deadline has passed
- `time_until_due`: Interval showing time remaining until evidence deadline
- `is_closed`: Boolean indicating if dispute is in a final state (won, lost, charge_refunded, warning_closed)
- `days_to_resolution`: Number of days from creation to closure
- Customer details (email, name) and subscription information

### `payment_health_metrics`
Aggregated view providing payment success rates, risk analysis, and event counting per customer.

**Source Tables:** `payment_events`, `profiles`

**Key Metrics:**
- `success_rate_percentage`: Payment success rate per customer (rounded to 2 decimal places)
- `avg_risk_score`: Average Stripe Radar risk score across all payment attempts
- `high_risk_events`: Count of highest-risk payment attempts
- `recent_events`: Count of events in last 30 days
- `total_payment_events`: Total payment attempts for the customer
- `successful_payments` and `failed_payments`: Breakdown of payment outcomes

### `refund_analytics`
Enhanced view of refund data with customer details, subscription information, and calculated refund percentages relative to original charge amounts.

**Source Tables:** `refunds`, `profiles`, `subscriptions`, `charge_receipts`

**Key Calculated Fields:**
- `refund_percentage`: Percentage of original charge amount being refunded (rounded to 2 decimal places)
- Customer details (email, name) and subscription information
- Original charge details including amount and charge date
- Complete refund lifecycle information

---

## Stored Procedures

The system includes 14 stored procedures for data processing, analytics, and automated workflows:

### Core License Management
- **`create_license_for_user`**: Creates new license with validation and linking
- **`handle_new_user_profile_creation`**: Triggers profile creation from auth events
- **`parse_customer_name`**: Utility for parsing customer names from Stripe data

### Webhook Processing
- **`insert_payment_event`**: Processes payment-related webhook events with risk analysis
- **`insert_customer_event`**: Processes customer lifecycle events and change tracking
- **`upsert_dispute`**: Creates or updates dispute records from Stripe webhook data
- **`upsert_refund`**: Creates or updates refund records from Stripe webhook data

### Analytics & Dashboards
- **`get_dispute_dashboard_metrics`**: Returns dispute analytics for specified date range
- **`get_payment_health_dashboard`**: Returns payment health metrics for specified date range

### Automated Alerting
- **`check_overdue_disputes`**: Identifies disputes approaching or past evidence deadlines
- **`create_dispute_alert`**: Creates system alerts for dispute-related events
- **`create_payment_risk_alert`**: Creates alerts for high-risk payment events
- **`create_refund_alert`**: Creates alerts for refund requests and processing

### Utility Functions
- **`update_updated_at_column`**: Generic trigger function for maintaining updated_at timestamps

---

## Row Level Security (RLS) Policies

All tables have RLS enabled with policies ensuring:

### **Core Tables:**
- **Users can only access their own data** (based on `user_id` or related fields)
- **Team admins can access data for their managed customers**  
- **Service role has full access** for webhook processing and analytics

### **Enterprise Tables:**
- **`enterprise_customers`:**
  - Service role: Full access to all enterprise customers
  - Sales representatives: Access to their assigned customers only
  - Enterprise admins: Access to their own company data only

- **`enterprise_invoices`:**
  - Service role: Full access to all enterprise invoices
  - Sales representatives: Access to invoices for their assigned customers
  - Enterprise admins: Access to their company's invoices only

### **Analytics Tables:**
- **Standard RLS policies** apply based on underlying data relationships
- **Analytics views respect underlying table permissions** automatically

### **Analytics Views:**
- **Views use SECURITY INVOKER** (not SECURITY DEFINER) to respect user permissions
- **Data access filtered through underlying table RLS policies**
- **No direct view-level policies needed** - inherits from source tables

## Migration History

Recent migrations implementing the analytics infrastructure:
- `20250806_001_create_dispute_tracking_tables`: Core dispute and refund tables
- `20250806_002_create_webhook_processing_functions`: Enhanced webhook handling
- `20250806_003_create_business_intelligence_views`: Analytics views and calculations
- `20250806_004_create_rls_policies_dispute_tables`: Security policies for analytics tables
- `20250806_005_create_alert_notifications`: System alerting infrastructure

**Security Updates (August 19, 2025):**
- **Fixed RLS on Enterprise Tables**: Enabled RLS on `enterprise_customers` and `enterprise_invoices`
- **Added Enterprise RLS Policies**: Comprehensive access control for sales reps and enterprise admins
- **Fixed Analytics Views Security**: Recreated all analytics views to use SECURITY INVOKER instead of SECURITY DEFINER
- **Resolved "Unrestricted" Flags**: Eliminated security warnings in Supabase dashboard

## Webhook Testing Infrastructure

**Complete Validation Framework (August 2025):**
- **29 Stripe Webhook Events**: Full coverage testing for subscription lifecycle, payment processing, dispute management, refund processing, fraud detection, setup intents, and customer management
- **Database Integration Testing**: Automated verification that all webhook events properly create/update records in Supabase tables
- **Test Factories**: `WebhookTestFactory` for consistent test data generation across all event types
- **Database Verifiers**: `WebhookDatabaseVerifier` for validating proper data insertion and business logic execution
- **Comprehensive Test Suite**: Priority-based testing (HIGH/MEDIUM/LOW) with category-specific validation
- **Command-line Runner**: `webhook-test.js` for quick execution of targeted webhook validation

**Files:**
- `WEBHOOK_TESTING_STRATEGY.md`: 4-phase testing approach documentation
- `WEBHOOK_TESTING_EXECUTION_PLAN.md`: Step-by-step execution guide with troubleshooting
- `tests/webhook-testing.fixture.ts`: Test infrastructure with factories and verifiers
- `tests/webhook-comprehensive.spec.ts`: Complete test suite covering all 29 events

---