using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace QuantBoost_Powerpoint_Addin.Features.ExcelLink.Converters
{
    /// <summary>
    /// Converts error state to visibility for showing error indicators.
    /// </summary>
    public class ErrorStateToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string errorState)
            {
                return string.IsNullOrEmpty(errorState) ? Visibility.Collapsed : Visibility.Visible;
            }
            
            if (value is bool hasError)
            {
                return hasError ? Visibility.Visible : Visibility.Collapsed;
            }

            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}