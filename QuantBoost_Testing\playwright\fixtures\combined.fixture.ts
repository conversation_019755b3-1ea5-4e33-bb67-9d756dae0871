import { test as base } from '@playwright/test';
import Stripe from 'stripe';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

const TEST_CARDS = {
  SUCCESS: '****************',
  DECLINE: '****************',
  INSUFFICIENT_FUNDS: '****************',
  EXPIRED: '****************',
  CVC_FAIL: '****************',
  PROCESSING_ERROR: '****************',
  THREE_D_SECURE: '****************',
  THREE_D_SECURE_2: '****************',
};

export type CombinedFixture = {
  stripe: Stripe | null;
  testCards: typeof TEST_CARDS;
  createTestCustomer: () => Promise<Stripe.Customer | null>;
  stripeCustomer: Stripe.Customer;
  simulateWebhook: (eventType: string, payload: any) => Promise<{ status: number; body: any } | null>;
  supabase: SupabaseClient | null;
  supabaseClient: SupabaseClient;
  findLicensesByEmail: (email: string) => Promise<any[]>;
  findSubscriptionsByUser: (userId: string) => Promise<any[]>;
};

export const test = base.extend<CombinedFixture>({
  stripe: async ({}, use) => {
    const key = process.env.STRIPE_SECRET_KEY_TEST;
    if (!key) { await use(null); return; }
  const stripe = new Stripe(key, { apiVersion: '2025-08-27.basil' as any });
    await use(stripe);
  },
  testCards: async ({}, use) => { await use(TEST_CARDS); },
  createTestCustomer: async ({ stripe }, use) => {
    const factory = async () => {
      if (!stripe) return null;
      return await stripe.customers.create({
        email: `e2e_${Date.now()}_${Math.random().toString(36).slice(2)}@example.com`,
        description: 'QuantBoost E2E Test Customer'
      });
    };
    await use(factory);
  },
  stripeCustomer: async ({ createTestCustomer }, use) => {
    const customer = await createTestCustomer();
    if (!customer) throw new Error('Failed to create Stripe customer');
    await use(customer);
  },
  simulateWebhook: async ({}, use) => {
    const fn = async (eventType: string, payload: any) => {
      const baseUrl = process.env.BASE_URL;
      if (!baseUrl) return null;
      try {
        const res = await fetch(`${baseUrl}/api/webhooks/stripe`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'X-Stripe-Signature': 'test-signature-bypass' },
          body: JSON.stringify({
            id: `evt_test_${Date.now()}`,
            type: eventType,
            data: { object: payload },
            created: Math.floor(Date.now() / 1000),
            livemode: false,
            object: 'event',
            pending_webhooks: 1,
            request: { id: null, idempotency_key: null },
            api_version: '2025-08-27.basil'
          })
        });
        return { status: res.status, body: await res.json().catch(() => ({})) };
      } catch (e) {
        return { status: 0, body: { error: String(e) } };
      }
    };
    await use(fn);
  },
  supabase: async ({}, use) => {
    const url = process.env.SUPABASE_URL;
    const key = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    if (!url || !key) { await use(null); return; }
    const client = createClient(url, key, { auth: { persistSession: false } });
    await use(client);
  },
  supabaseClient: async ({ supabase }, use) => {
    if (!supabase) throw new Error('Supabase client not available');
    await use(supabase);
  },
  findLicensesByEmail: async ({ supabase }, use) => {
    const fn = async (email: string) => {
      if (!supabase) return [];
      const { data } = await supabase.from('licenses').select('*').ilike('email', email);
      return data || [];
    };
    await use(fn);
  },
  findSubscriptionsByUser: async ({ supabase }, use) => {
    const fn = async (userId: string) => {
      if (!supabase) return [];
      const { data } = await supabase.from('subscriptions').select('*').eq('user_id', userId);
      return data || [];
    };
    await use(fn);
  }
});

export const expect = test.expect;
