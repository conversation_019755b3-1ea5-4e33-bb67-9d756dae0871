using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using QuantBoost_Shared.Utilities;

namespace QuantBoost_Powerpoint_Addin.ExcelLink
{
    /// <summary>
    /// Comprehensive clipboard management utility for resolving Excel-to-PowerPoint clipboard conflicts.
    /// Provides Win32 API integration, diagnostic capabilities, and robust error handling.
    /// </summary>
    public static class ClipboardManager
    {
        #region Win32 API Declarations

        [DllImport("user32.dll", SetLastError = true)]
        private static extern bool OpenClipboard(IntPtr hWndNewOwner);

        [DllImport("user32.dll", SetLastError = true)]
        private static extern bool CloseClipboard();

        [DllImport("user32.dll", SetLastError = true)]
        private static extern bool EmptyClipboard();

        [DllImport("user32.dll", SetLastError = true)]
        private static extern IntPtr GetClipboardOwner();

        [DllImport("user32.dll", SetLastError = true)]
        private static extern IntPtr GetOpenClipboardWindow();

        [DllImport("user32.dll", SetLastError = true)]
        private static extern bool IsClipboardFormatAvailable(uint format);

        [DllImport("user32.dll", SetLastError = true)]
        private static extern uint EnumClipboardFormats(uint format);

        [DllImport("user32.dll", SetLastError = true)]
        private static extern int CountClipboardFormats();

        [DllImport("user32.dll", SetLastError = true)]
        private static extern int GetWindowThreadProcessId(IntPtr hWnd, out uint processId);

        [DllImport("user32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern int GetClassName(IntPtr hWnd, StringBuilder lpClassName, int nMaxCount);

        #endregion

        #region Clipboard Format Constants

        private const uint CF_TEXT = 1;
        private const uint CF_BITMAP = 2;
        private const uint CF_METAFILEPICT = 3;
        private const uint CF_SYLK = 4;
        private const uint CF_DIF = 5;
        private const uint CF_TIFF = 6;
        private const uint CF_OEMTEXT = 7;
        private const uint CF_DIB = 8;
        private const uint CF_PALETTE = 9;
        private const uint CF_PENDATA = 10;
        private const uint CF_RIFF = 11;
        private const uint CF_WAVE = 12;
        private const uint CF_UNICODETEXT = 13;
        private const uint CF_ENHMETAFILE = 14;
        private const uint CF_HDROP = 15;
        private const uint CF_LOCALE = 16;
        private const uint CF_DIBV5 = 17;

        #endregion

        #region Public Methods

        /// <summary>
        /// Safely clears the clipboard using both managed and Win32 API fallbacks.
        /// Handles access denied scenarios and provides comprehensive error recovery.
        /// </summary>
        /// <returns>True if clipboard was successfully cleared, false otherwise</returns>
        public static async Task<bool> SafeClearClipboardAsync()
        {
            try
            {
                // Log thread state for debugging
                bool isSTAThread = IsCurrentThreadSTA();
                ErrorHandlingService.LogException(null,
                    $"SafeClearClipboardAsync: Thread state - STA: {isSTAThread}, ThreadId: {System.Threading.Thread.CurrentThread.ManagedThreadId}");

                // Strategy 1: Try managed Clipboard.Clear() first
                if (await TryManagedClipboardClear())
                {
                    return true;
                }

                // Strategy 2: Try Win32 API approach
                if (await TryWin32ClipboardClear())
                {
                    return true;
                }

                // Strategy 3: Force clear with retry logic
                return await ForceClipboardClearWithRetry();
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "SafeClearClipboardAsync: All clipboard clear strategies failed");
                return false;
            }
        }

        /// <summary>
        /// Checks if clipboard contains any content with multiple format detection.
        /// Ensures execution on STA thread for managed clipboard operations.
        /// </summary>
        /// <returns>True if clipboard has content, false otherwise</returns>
        public static bool HasClipboardContent()
        {
            try
            {
                // For synchronous operations, check if we're on UI thread
                if (System.Threading.Thread.CurrentThread.GetApartmentState() == System.Threading.ApartmentState.STA)
                {
                    // We're on STA thread, safe to use managed Clipboard directly
                    if (Clipboard.ContainsData(DataFormats.Bitmap) ||
                        Clipboard.ContainsData(DataFormats.EnhancedMetafile) ||
                        Clipboard.ContainsData(DataFormats.MetafilePict) ||
                        Clipboard.ContainsData(DataFormats.Text) ||
                        Clipboard.ContainsData(DataFormats.UnicodeText))
                    {
                        return true;
                    }
                }

                // Strategy 2: Check using Win32 API for more comprehensive detection
                // Win32 API calls are thread-safe
                return CountClipboardFormats() > 0;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "HasClipboardContent: Error checking clipboard content");
                return false;
            }
        }

        /// <summary>
        /// Validates clipboard accessibility and permissions.
        /// </summary>
        /// <returns>True if clipboard can be accessed, false otherwise</returns>
        public static bool CanAccessClipboard()
        {
            try
            {
                // Try to open and immediately close clipboard to test access
                if (OpenClipboard(IntPtr.Zero))
                {
                    CloseClipboard();
                    return true;
                }

                var error = Marshal.GetLastWin32Error();
                ErrorHandlingService.LogException(new Exception($"Win32 Error: {error}"), 
                    "CanAccessClipboard: Cannot access clipboard");
                return false;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, 
                    "CanAccessClipboard: Exception during clipboard access test");
                return false;
            }
        }

        /// <summary>
        /// Provides comprehensive clipboard state diagnostics for troubleshooting.
        /// </summary>
        /// <returns>ClipboardDiagnostics object with detailed state information</returns>
        public static ClipboardDiagnostics DiagnoseClipboardState()
        {
            var diagnostics = new ClipboardDiagnostics
            {
                Timestamp = DateTime.UtcNow,
                CanAccess = CanAccessClipboard(),
                HasContent = HasClipboardContent(),
                FormatCount = GetFormatCount(),
                AvailableFormats = GetAvailableFormats(),
                OwnerInfo = GetClipboardOwnerInfo(),
                OpenWindowInfo = GetOpenClipboardWindowInfo()
            };

            return diagnostics;
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Checks if the current thread is STA (Single Thread Apartment) which is required for clipboard operations.
        /// </summary>
        /// <returns>True if current thread is STA, false otherwise</returns>
        private static bool IsCurrentThreadSTA()
        {
            return System.Threading.Thread.CurrentThread.GetApartmentState() == System.Threading.ApartmentState.STA;
        }

        /// <summary>
        /// Attempts to clear clipboard using managed Clipboard.Clear() method.
        /// Ensures execution on STA thread (UI thread in Office add-ins).
        /// </summary>
        private static async Task<bool> TryManagedClipboardClear()
        {
            try
            {
                // Check if we're already on the UI thread (STA)
                if (IsCurrentThreadSTA() && AsyncHelper.IsOnUIThread)
                {
                    // We're on the UI thread, execute directly
                    try
                    {
                        Clipboard.Clear();
                        await Task.Delay(50); // Allow time for operation to complete
                        return !HasClipboardContent();
                    }
                    catch (Exception ex)
                    {
                        ErrorHandlingService.LogException(ex,
                            "TryManagedClipboardClear: Clipboard.Clear() failed on UI thread");
                        return false;
                    }
                }
                else
                {
                    // We're not on the UI thread, need to marshal to UI thread
                    // Use a TaskCompletionSource to handle the async operation
                    var tcs = new TaskCompletionSource<bool>();

                    AsyncHelper.RunOnUIThread(() =>
                    {
                        try
                        {
                            Clipboard.Clear();
                            tcs.SetResult(true);
                        }
                        catch (Exception ex)
                        {
                            ErrorHandlingService.LogException(ex,
                                "TryManagedClipboardClear: Clipboard.Clear() failed on UI thread");
                            tcs.SetResult(false);
                        }
                    });

                    bool result = await tcs.Task;
                    if (result)
                    {
                        await Task.Delay(50); // Allow time for operation to complete
                        return !HasClipboardContent();
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "TryManagedClipboardClear: Managed clipboard clear failed");
                return false;
            }
        }

        /// <summary>
        /// Attempts to clear clipboard using Win32 API.
        /// </summary>
        private static async Task<bool> TryWin32ClipboardClear()
        {
            try
            {
                if (OpenClipboard(IntPtr.Zero))
                {
                    bool success = EmptyClipboard();
                    CloseClipboard();
                    
                    if (success)
                    {
                        await Task.Delay(50); // Allow time for operation to complete
                        return !HasClipboardContent();
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, 
                    "TryWin32ClipboardClear: Win32 clipboard clear failed");
                return false;
            }
        }

        /// <summary>
        /// Force clear clipboard with progressive retry logic.
        /// </summary>
        private static async Task<bool> ForceClipboardClearWithRetry()
        {
            const int maxRetries = 3;
            const int baseDelay = 100;

            for (int attempt = 0; attempt < maxRetries; attempt++)
            {
                try
                {
                    // Progressive delay
                    if (attempt > 0)
                    {
                        await Task.Delay(baseDelay * (attempt + 1));
                    }

                    // Try both strategies in sequence
                    if (await TryManagedClipboardClear() || await TryWin32ClipboardClear())
                    {
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    ErrorHandlingService.LogException(ex, 
                        $"ForceClipboardClearWithRetry: Attempt {attempt + 1} failed");
                }
            }

            return false;
        }

        /// <summary>
        /// Gets the count of available clipboard formats.
        /// </summary>
        private static int GetFormatCount()
        {
            try
            {
                return CountClipboardFormats();
            }
            catch
            {
                return -1; // Indicates error
            }
        }

        /// <summary>
        /// Enumerates all available clipboard formats.
        /// </summary>
        private static List<string> GetAvailableFormats()
        {
            var formats = new List<string>();
            
            try
            {
                uint format = 0;
                while ((format = EnumClipboardFormats(format)) != 0)
                {
                    formats.Add(GetFormatName(format));
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, 
                    "GetAvailableFormats: Error enumerating clipboard formats");
            }

            return formats;
        }

        /// <summary>
        /// Gets human-readable name for clipboard format.
        /// </summary>
        private static string GetFormatName(uint format)
        {
            switch (format)
            {
                case CF_TEXT: return "CF_TEXT";
                case CF_BITMAP: return "CF_BITMAP";
                case CF_METAFILEPICT: return "CF_METAFILEPICT";
                case CF_ENHMETAFILE: return "CF_ENHMETAFILE";
                case CF_UNICODETEXT: return "CF_UNICODETEXT";
                case CF_DIB: return "CF_DIB";
                case CF_DIBV5: return "CF_DIBV5";
                default: return $"Format_{format}";
            }
        }

        /// <summary>
        /// Gets information about the current clipboard owner.
        /// </summary>
        private static ProcessInfo GetClipboardOwnerInfo()
        {
            try
            {
                IntPtr ownerWindow = GetClipboardOwner();
                return GetProcessInfoFromWindow(ownerWindow);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "GetClipboardOwnerInfo: Error getting clipboard owner");
                return new ProcessInfo { ProcessName = "Unknown", WindowTitle = "Error" };
            }
        }

        /// <summary>
        /// Gets information about the window that currently has the clipboard open.
        /// </summary>
        private static ProcessInfo GetOpenClipboardWindowInfo()
        {
            try
            {
                IntPtr openWindow = GetOpenClipboardWindow();
                return GetProcessInfoFromWindow(openWindow);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "GetOpenClipboardWindowInfo: Error getting open clipboard window");
                return new ProcessInfo { ProcessName = "Unknown", WindowTitle = "Error" };
            }
        }

        /// <summary>
        /// Gets process information from a window handle.
        /// </summary>
        private static ProcessInfo GetProcessInfoFromWindow(IntPtr windowHandle)
        {
            var processInfo = new ProcessInfo();

            if (windowHandle == IntPtr.Zero)
            {
                processInfo.ProcessName = "None";
                processInfo.WindowTitle = "No Window";
                return processInfo;
            }

            try
            {
                // Get process ID
                uint processId;
                GetWindowThreadProcessId(windowHandle, out processId);
                processInfo.ProcessId = (int)processId;

                // Get process name
                try
                {
                    using (var process = Process.GetProcessById((int)processId))
                    {
                        processInfo.ProcessName = process.ProcessName;
                    }
                }
                catch
                {
                    processInfo.ProcessName = "Unknown";
                }

                // Get window title
                var titleBuilder = new StringBuilder(256);
                GetWindowText(windowHandle, titleBuilder, titleBuilder.Capacity);
                processInfo.WindowTitle = titleBuilder.ToString();

                // Get window class name
                var classBuilder = new StringBuilder(256);
                GetClassName(windowHandle, classBuilder, classBuilder.Capacity);
                processInfo.WindowClass = classBuilder.ToString();
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "GetProcessInfoFromWindow: Error getting process info");
                processInfo.ProcessName = "Error";
                processInfo.WindowTitle = "Error";
            }

            return processInfo;
        }

        #endregion
    }

    /// <summary>
    /// Comprehensive clipboard state diagnostics for troubleshooting clipboard issues.
    /// </summary>
    public class ClipboardDiagnostics
    {
        /// <summary>
        /// Timestamp when diagnostics were captured.
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Whether the clipboard can be accessed.
        /// </summary>
        public bool CanAccess { get; set; }

        /// <summary>
        /// Whether the clipboard contains any content.
        /// </summary>
        public bool HasContent { get; set; }

        /// <summary>
        /// Number of available clipboard formats.
        /// </summary>
        public int FormatCount { get; set; }

        /// <summary>
        /// List of available clipboard formats.
        /// </summary>
        public List<string> AvailableFormats { get; set; } = new List<string>();

        /// <summary>
        /// Information about the process that owns the clipboard.
        /// </summary>
        public ProcessInfo OwnerInfo { get; set; }

        /// <summary>
        /// Information about the window that has the clipboard open.
        /// </summary>
        public ProcessInfo OpenWindowInfo { get; set; }

        /// <summary>
        /// Returns a formatted string representation of the diagnostics.
        /// </summary>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.AppendLine($"Clipboard Diagnostics ({Timestamp:yyyy-MM-dd HH:mm:ss} UTC):");
            sb.AppendLine($"  Can Access: {CanAccess}");
            sb.AppendLine($"  Has Content: {HasContent}");
            sb.AppendLine($"  Format Count: {FormatCount}");
            sb.AppendLine($"  Available Formats: [{string.Join(", ", AvailableFormats)}]");
            sb.AppendLine($"  Owner: {OwnerInfo}");
            sb.AppendLine($"  Open Window: {OpenWindowInfo}");
            return sb.ToString();
        }
    }

    /// <summary>
    /// Information about a process and its window.
    /// </summary>
    public class ProcessInfo
    {
        /// <summary>
        /// Process ID.
        /// </summary>
        public int ProcessId { get; set; }

        /// <summary>
        /// Process name.
        /// </summary>
        public string ProcessName { get; set; } = string.Empty;

        /// <summary>
        /// Window title.
        /// </summary>
        public string WindowTitle { get; set; } = string.Empty;

        /// <summary>
        /// Window class name.
        /// </summary>
        public string WindowClass { get; set; } = string.Empty;

        /// <summary>
        /// Returns a formatted string representation of the process info.
        /// </summary>
        public override string ToString()
        {
            if (ProcessId == 0)
                return ProcessName;

            return $"{ProcessName} (PID: {ProcessId}) - {WindowTitle}";
        }
    }
}
