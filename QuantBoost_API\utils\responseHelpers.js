// Helper function for sending success responses
function sendSuccess(res, data, message = 'Operation successful', statusCode = 200) {
    // It's good practice to also log successful operations, especially if they involve data modification
    // console.log(`SUCCESS: ${message}`, data); // Consider if this level of logging is needed
    res.status(statusCode).json({
        success: true,
        message: message,
        data: data
    });
}

// Helper function for sending error responses
function sendError(res, message, statusCode = 500, errorDetails = null) {
    console.error(`ERROR ${statusCode}: ${message}`, errorDetails || ''); // Log the error server-side
    const responseObject = {
        success: false,
        error: {
            message: message,
        }
    };
    if (errorDetails) {
        responseObject.error.details = errorDetails;
    }
    res.status(statusCode).json(responseObject);
}

module.exports = {
    sendSuccess,
    sendError
};
