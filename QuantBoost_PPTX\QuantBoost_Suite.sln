﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35931.197
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QuantBoost_Powerpoint", "QuantBoost_Powerpoint.csproj", "{DF2FFE8E-0C17-4D53-8A68-92DDA729D54D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QuantBoost_Excel", "..\QuantBoost_Excel\QuantBoost_Excel.csproj", "{F8183AA1-B6D6-4CA2-9EC9-F5A660FBBCFB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QuantBoost_Licensing", "..\QuantBoost_Licensing\QuantBoost_Licensing.csproj", "{EC2D374B-A69B-4214-A1FC-7CEF1AD5931C}"
EndProject
Project("{D954291E-2A0B-460D-934E-DC6B0785DB48}") = "QuantBoost_Shared", "..\QuantBoost_Shared\QuantBoost_Shared.shproj", "{2919934E-8104-43A1-BC64-84A06CB1F8F1}"
EndProject
Project("{930C7802-8A8C-48F9-8165-68863BCCD9DD}") = "QuantBoost.WixInstaller", "..\QuantBoost_WixInstaller\QuantBoost.WixInstaller.wixproj", "{3FC1C7A6-2F64-4A40-9E42-6B4B95321B69}"
EndProject
Project("{930C7802-8A8C-48F9-8165-68863BCCD9DD}") = "QuantBoost.Bootstrapper", "..\QuantBoost_Bootstrapper\QuantBoost.Bootstrapper.wixproj", "{A0E2FE9A-6E2C-41C8-9B3B-0A98E5A9B713}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Installer", "Installer", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Docs", "Docs", "{19A8D165-D466-4803-BDD3-68F534175E2C}"
	ProjectSection(SolutionItems) = preProject
		..\QuantBoost_WixInstaller\BUILD-STEPS.md = ..\QuantBoost_WixInstaller\BUILD-STEPS.md
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DF2FFE8E-0C17-4D53-8A68-92DDA729D54D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF2FFE8E-0C17-4D53-8A68-92DDA729D54D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF2FFE8E-0C17-4D53-8A68-92DDA729D54D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DF2FFE8E-0C17-4D53-8A68-92DDA729D54D}.Debug|x64.Build.0 = Debug|Any CPU
		{DF2FFE8E-0C17-4D53-8A68-92DDA729D54D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF2FFE8E-0C17-4D53-8A68-92DDA729D54D}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF2FFE8E-0C17-4D53-8A68-92DDA729D54D}.Release|x64.ActiveCfg = Release|Any CPU
		{DF2FFE8E-0C17-4D53-8A68-92DDA729D54D}.Release|x64.Build.0 = Release|Any CPU
		{F8183AA1-B6D6-4CA2-9EC9-F5A660FBBCFB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F8183AA1-B6D6-4CA2-9EC9-F5A660FBBCFB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F8183AA1-B6D6-4CA2-9EC9-F5A660FBBCFB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F8183AA1-B6D6-4CA2-9EC9-F5A660FBBCFB}.Debug|x64.Build.0 = Debug|Any CPU
		{F8183AA1-B6D6-4CA2-9EC9-F5A660FBBCFB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F8183AA1-B6D6-4CA2-9EC9-F5A660FBBCFB}.Release|Any CPU.Build.0 = Release|Any CPU
		{F8183AA1-B6D6-4CA2-9EC9-F5A660FBBCFB}.Release|x64.ActiveCfg = Release|Any CPU
		{F8183AA1-B6D6-4CA2-9EC9-F5A660FBBCFB}.Release|x64.Build.0 = Release|Any CPU
		{EC2D374B-A69B-4214-A1FC-7CEF1AD5931C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EC2D374B-A69B-4214-A1FC-7CEF1AD5931C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EC2D374B-A69B-4214-A1FC-7CEF1AD5931C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EC2D374B-A69B-4214-A1FC-7CEF1AD5931C}.Debug|x64.Build.0 = Debug|Any CPU
		{EC2D374B-A69B-4214-A1FC-7CEF1AD5931C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EC2D374B-A69B-4214-A1FC-7CEF1AD5931C}.Release|Any CPU.Build.0 = Release|Any CPU
		{EC2D374B-A69B-4214-A1FC-7CEF1AD5931C}.Release|x64.ActiveCfg = Release|Any CPU
		{EC2D374B-A69B-4214-A1FC-7CEF1AD5931C}.Release|x64.Build.0 = Release|Any CPU
		{3FC1C7A6-2F64-4A40-9E42-6B4B95321B69}.Debug|Any CPU.ActiveCfg = Debug|x64
		{3FC1C7A6-2F64-4A40-9E42-6B4B95321B69}.Debug|Any CPU.Build.0 = Debug|x64
		{3FC1C7A6-2F64-4A40-9E42-6B4B95321B69}.Debug|x64.ActiveCfg = Debug|x64
		{3FC1C7A6-2F64-4A40-9E42-6B4B95321B69}.Debug|x64.Build.0 = Debug|x64
		{3FC1C7A6-2F64-4A40-9E42-6B4B95321B69}.Release|Any CPU.ActiveCfg = Release|x64
		{3FC1C7A6-2F64-4A40-9E42-6B4B95321B69}.Release|Any CPU.Build.0 = Release|x64
		{3FC1C7A6-2F64-4A40-9E42-6B4B95321B69}.Release|x64.ActiveCfg = Release|x64
		{3FC1C7A6-2F64-4A40-9E42-6B4B95321B69}.Release|x64.Build.0 = Release|x64
		{A0E2FE9A-6E2C-41C8-9B3B-0A98E5A9B713}.Debug|Any CPU.ActiveCfg = Debug|x64
		{A0E2FE9A-6E2C-41C8-9B3B-0A98E5A9B713}.Debug|Any CPU.Build.0 = Debug|x64
		{A0E2FE9A-6E2C-41C8-9B3B-0A98E5A9B713}.Debug|x64.ActiveCfg = Debug|x64
		{A0E2FE9A-6E2C-41C8-9B3B-0A98E5A9B713}.Debug|x64.Build.0 = Debug|x64
		{A0E2FE9A-6E2C-41C8-9B3B-0A98E5A9B713}.Release|Any CPU.ActiveCfg = Release|x64
		{A0E2FE9A-6E2C-41C8-9B3B-0A98E5A9B713}.Release|Any CPU.Build.0 = Release|x64
		{A0E2FE9A-6E2C-41C8-9B3B-0A98E5A9B713}.Release|x64.ActiveCfg = Release|x64
		{A0E2FE9A-6E2C-41C8-9B3B-0A98E5A9B713}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{3FC1C7A6-2F64-4A40-9E42-6B4B95321B69} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{A0E2FE9A-6E2C-41C8-9B3B-0A98E5A9B713} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4F5FF4AA-6891-4353-94A4-0D10E6027080}
	EndGlobalSection
	GlobalSection(SharedMSBuildProjectFiles) = preSolution
		..\QuantBoost_Shared\QuantBoost_Shared.projitems*{2919934e-8104-43a1-bc64-84a06cb1f8f1}*SharedItemsImports = 13
		..\QuantBoost_Shared\QuantBoost_Shared.projitems*{df2ffe8e-0c17-4d53-8a68-92dda729d54d}*SharedItemsImports = 4
		..\QuantBoost_Shared\QuantBoost_Shared.projitems*{f8183aa1-b6d6-4ca2-9ec9-f5a660fbbcfb}*SharedItemsImports = 4
	EndGlobalSection
EndGlobal
