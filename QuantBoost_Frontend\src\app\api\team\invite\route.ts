import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';
import { sendTeamInvitationEmail } from '@/lib/email';
import { requireTeamAccess } from '@/lib/userTypeMiddleware';

export async function POST(req: NextRequest) {
  // Check user access before processing
  const accessDenied = await requireTeamAccess(req);
  if (accessDenied) return accessDenied;

  // Create Supabase client inside the function to avoid build-time issues
  const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_KEY!);
  try {
    const { email, teamId, inviterName, teamName } = await req.json();

    if (!email || !teamId) {
      return NextResponse.json({ error: 'Missing email or teamId' }, { status: 400 });
    }

    // Generate invitation token
    const token = randomUUID();
    
    // Store invitation in database
    const { error: dbError } = await supabase.from('invitations').insert({
      team_id: teamId,
      email,
      token,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      created_at: new Date().toISOString(),
    });

    if (dbError) {
      console.error('Database error creating invitation:', dbError);
      return NextResponse.json({ error: 'Failed to create invitation' }, { status: 500 });
    }

    // Create activation URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const activationUrl = `${baseUrl}/dashboard/license/activate?token=${token}&email=${encodeURIComponent(email)}`;

    // Send invitation email
    const emailResult = await sendTeamInvitationEmail(email, activationUrl, {
      inviterName,
      teamName,
      expirationDays: 7
    });

    if (!emailResult.success) {
      // Log the email error but don't fail the request - invitation is still created
      console.warn('Email sending failed:', emailResult.error);
      
      // Return success with a warning
      return NextResponse.json({ 
        success: true, 
        warning: 'Invitation created but email sending failed. Please provide the activation link manually.',
        activationUrl,
        emailError: emailResult.error
      });
    }

    console.log(`Team invitation sent successfully to ${email}`, {
      messageId: emailResult.messageId,
      activationUrl
    });

    return NextResponse.json({ 
      success: true,
      message: `Invitation sent to ${email}`,
      messageId: emailResult.messageId
    });

  } catch (error) {
    console.error('Error creating team invitation:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}