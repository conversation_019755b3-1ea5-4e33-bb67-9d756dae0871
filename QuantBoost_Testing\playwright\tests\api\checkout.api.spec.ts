import { test, expect } from '../../fixtures/combined.fixture';
import crypto from 'crypto';

// These API-level tests bypass browser-based card entry and validate backend + DB side-effects.
// Prereqs: BASE_URL points to running frontend (Next.js) server with API routes, Stripe/Supabase env vars set.
// Focus areas:
// 1. create-payment-intent route returns clientSecret & is idempotent per (customer, price, quantity)
// 2. simulate-webhook debug route can inject subscription + license records for a supplied customerId
// 3. Full flow: create PI -> call simulate-webhook with same customer -> verify subscription/license rows

const ANNUAL_PRICE_ID = 'price_1RC3HTE6FvhUKV1bE9D6zf6e';

const requireEnv = () => {
  if (!process.env.BASE_URL) test.skip(true, 'BASE_URL not set');
  if (!process.env.STRIPE_SECRET_KEY_TEST) test.skip(true, 'STRIPE_SECRET_KEY_TEST not set');
  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) test.info().annotations.push({ type: 'note', description: 'Service role key missing: DB assertions may be skipped due to RLS.' });
};

async function postJSON(path: string, body: any) {
  const res = await fetch(`${process.env.BASE_URL}${path}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body)
  });
  let json: any = null;
  try { json = await res.json(); } catch { json = {}; }
  return { status: res.status, json };
}

test.describe('API Checkout (backend)', () => {
  test('create-payment-intent returns clientSecret + is idempotent', async () => {
    requireEnv();
    const email = `api_pi_${Date.now()}@example.com`;

    // First call
    const first = await postJSON('/api/checkout/create-payment-intent', { priceId: ANNUAL_PRICE_ID, email, quantity: 1 });
    expect(first.status, 'first status').toBe(200);
    expect(first.json.clientSecret).toBeTruthy();
    expect(first.json.subscriptionId).toBeTruthy();
    expect(first.json.paymentIntentId).toBeTruthy();

    // Second call should reuse subscription (reusedSubscription flag true OR same subscription id)
    const second = await postJSON('/api/checkout/create-payment-intent', { priceId: ANNUAL_PRICE_ID, email, quantity: 1 });
    expect(second.status, 'second status').toBe(200);
    expect(second.json.clientSecret).toBeTruthy();
    expect(second.json.subscriptionId).toBeTruthy();
    // Validate reuse behavior
    expect([true, false]).toContain(!!second.json.reusedSubscription); // presence check
    expect(second.json.subscriptionId).toBe(first.json.subscriptionId); // idempotent key logic
  });

  test('simulate-webhook can create subscription + license with override', async ({ supabase }) => {
    requireEnv();
    const customerId = 'cus_test_' + crypto.randomUUID().replace(/-/g, '').slice(0, 12);
    const email = `api_wh_${Date.now()}@example.com`;

    // Precondition: ensure create-payment-intent creates customer so that profile can link by stripe_customer_id OR fallback logic will create profile.
    const createPI = await postJSON('/api/checkout/create-payment-intent', { priceId: ANNUAL_PRICE_ID, email, quantity: 1 });
    expect(createPI.status).toBe(200);
    expect(createPI.json.subscriptionId).toBeTruthy();

    // Now call simulate webhook with our own fabricated customer id (will create profile if missing)
  const sim = await postJSON('/api/debug/simulate-webhook', { customerId, subscription: { status: 'active', quantity: 1 } });
  if (sim.status === 404) test.skip(true, 'simulate-webhook route not deployed in this environment');
  expect(sim.status).toBe(200);
    expect(sim.json.success).toBeTruthy();

    if (supabase && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      // Poll for subscription row (simulate-webhook inserts directly so should exist immediately)
      let foundSub = false;
      for (let i = 0; i < 5; i++) {
        const { data } = await supabase.from('subscriptions').select('id,stripe_subscription_id').like('stripe_subscription_id', 'sub_simulated_%').limit(1);
        if (data && data.length) { foundSub = true; break; }
        await new Promise(r => setTimeout(r, 500));
      }
      expect(foundSub, 'subscription record inserted').toBeTruthy();
    }
  });

  test('end-to-end (API only) flow: create-payment-intent then simulate-webhook for same customer', async ({ supabase }) => {
    requireEnv();
    const email = `api_full_${Date.now()}@example.com`;

    const first = await postJSON('/api/checkout/create-payment-intent', { priceId: ANNUAL_PRICE_ID, email, quantity: 1 });
    expect(first.status).toBe(200);
    const { customerId, subscriptionId } = first.json;
    expect(customerId).toBeTruthy();

    // Simulate webhook using the real customerId (should upsert subscription/license)
  const sim = await postJSON('/api/debug/simulate-webhook', { customerId });
  if (sim.status === 404) test.skip(true, 'simulate-webhook route not deployed in this environment');
  expect(sim.status).toBe(200);

    if (supabase && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      // Verify a license row appears (poll up to 5s)
      let foundLicense = false;
      for (let i = 0; i < 10; i++) {
        const { data } = await supabase.from('licenses').select('id,subscription_id').eq('subscription_id', sim.json.subscription?.id).limit(1);
        if (data && data.length) { foundLicense = true; break; }
        await new Promise(r => setTimeout(r, 500));
      }
      expect(foundLicense, 'license record inserted').toBeTruthy();
    } else {
      test.info().annotations.push({ type: 'note', description: 'DB verification skipped (missing service role)' });
    }
  });
});
