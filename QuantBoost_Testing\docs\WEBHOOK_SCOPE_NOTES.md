# Webhook Test Scope Notes

## Omitted Scenarios (Explicit)
- Subscription upgrade/downgrade proration flows are **not tested** because the product catalog currently contains a single product with two billing intervals (quarterly/annual) treated as distinct prices but we do **not** support mid‑cycle plan switching or prorated refunds.
- Plan change edge cases (e.g., partial invoice proration adjustments, credit balance carryover) are therefore out-of-scope for this phase.

## Rationale
1. Business policy: No prorated refunds; users change plans only at renewal.
2. Technical architecture: Webhook handler records subscription rows keyed by `stripe_subscription_id`; absence of dynamic plan transitions simplifies state model.
3. Risk tradeoff: Implementing mocks for proration introduces complexity without user impact presently.

## Mitigation / Future Trigger Points
Add plan change & proration tests when ANY of the following become true:
- Multiple distinct products (different feature sets) introduced.
- Mid-cycle upgrade/downgrade UX shipped.
- Finance policy updated to allow prorated credits/refunds.
- Revenue recognition requires allocation across partial periods.

## Related Existing Coverage
Current suite validates:
- Initial subscription creation & updates (quantity, status changes)
- Invoice lifecycle (draft → finalized → paid/voided)
- Payment intent success/failure flows
- Charge refunds & disputes
- License provisioning (single & team quantities)
- Signature validation (positive & negative paths)
- Idempotent processing of duplicate events (now including explicit concurrency test)

This document clarifies intentional test gaps to prevent false assumptions about coverage completeness.
