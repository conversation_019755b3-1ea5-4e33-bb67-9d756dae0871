import { createClient } from '@supabase/supabase-js';
import { decodeJwt } from 'jose';

// Service-role admin client (no session persistence)
export function getSupabaseAdmin() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY!;
  return createClient(supabaseUrl, serviceRoleKey, { auth: { persistSession: false } });
}

// Lightweight helper: extract userId from Authorization: Bearer <jwt>
export function getUserIdFromAuthHeader(req: Request): string | null {
  const auth = req.headers.get('authorization') || req.headers.get('Authorization');
  if (!auth) return null;
  const parts = auth.split(' ');
  if (parts.length !== 2 || parts[0].toLowerCase() !== 'bearer') return null;
  try {
    const payload = decodeJwt(parts[1]);
    const sub = (payload as any).sub as string | undefined;
    return sub || null;
  } catch {
    return null;
  }
}

export function getAppBaseUrl(): string {
  return (
    process.env.NEXT_PUBLIC_APP_URL ||
    process.env.NEXT_PUBLIC_BASE_URL ||
    'http://localhost:3000'
  );
}
