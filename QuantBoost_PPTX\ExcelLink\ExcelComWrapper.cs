// --- START OF FINAL REWRITTEN ExcelComWrapper.cs ---
using System;
using System.Runtime.InteropServices;
using QuantBoost_Shared.Utilities; // For ErrorHandlingService
using Excel = Microsoft.Office.Interop.Excel;

namespace QuantBoost_Powerpoint_Addin.ExcelLink // Assuming this namespace is correct
{
    /// <summary>
    /// Exception thrown when an expected running Excel instance cannot be found via Marshal.GetActiveObject.
    /// </summary>
    public class ExcelInstanceNotFoundException : Exception
    {
        /// <summary>
        /// Initializes a new instance of the ExcelInstanceNotFoundException class.
        /// </summary>
        /// <param name="message">The message that describes the error.</param>
        /// <param name="innerException">The exception that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        public ExcelInstanceNotFoundException(string message, Exception innerException = null)
            : base(message, innerException) { }
    }

    /// <summary>
    /// Provides safe wrappers for interacting with the Excel COM object model,
    /// handling instance acquisition (existing or new) and resource cleanup.
    /// Implements IDisposable to ensure proper release of COM objects.
    /// </summary>
    public class ExcelComWrapper : IDisposable
    {
        #region Fields

        private Excel.Application _excelApp;
        private Excel.Workbook _workbook;
        private Excel.Worksheet _worksheet;
        private bool _disposed = false;
        // Readonly field must be assigned in constructor or initializer
        private readonly bool _ownsExcelApp;
        private bool _keepExcelOpenOnDispose = false;

        #endregion

        #region Properties

        /// <summary>
        /// Gets the wrapped Excel Application object.
        /// Throws ObjectDisposedException if the wrapper has been disposed.
        /// Throws InvalidOperationException if the internal reference is null.
        /// </summary>
        public Excel.Application Application
        {
            get
            {
                if (_disposed) throw new ObjectDisposedException(nameof(ExcelComWrapper));
                if (_excelApp == null) throw new InvalidOperationException("Excel Application object is not initialized or has been released.");
                return _excelApp;
            }
        }

        #endregion

        #region Constructors and Instance Acquisition

        /// <summary>
        /// Gets an existing, running Excel Application instance using Marshal.GetActiveObject.
        /// Throws ExcelInstanceNotFoundException if no running instance can be attached to.
        /// Use this for workflows requiring user interaction with an already open Excel instance
        /// (e.g., inserting content based on the user's current selection).
        /// </summary>
        /// <returns>An ExcelComWrapper instance attached to the running Excel application.</returns>
        /// <exception cref="ExcelInstanceNotFoundException">Thrown if no running Excel instance can be found.</exception>
        /// <exception cref="Exception">Rethrows other unexpected exceptions during attachment.</exception>
        public static ExcelComWrapper GetExistingInstance()
        {
            Excel.Application app = null;
            try
            {
                // GetActiveObject retrieves a running instance.
                app = (Excel.Application)Marshal.GetActiveObject("Excel.Application");
                if (app == null)
                {
                    throw new COMException("Marshal.GetActiveObject returned null unexpectedly.");
                }
                // Use the private constructor, indicating we do NOT own this instance.
                return new ExcelComWrapper(app, ownsInstance: false);
            }
            catch (COMException ex)
            {
                ErrorHandlingService.LogException(ex, "Marshal.GetActiveObject failed to find running Excel instance.");
                ReleaseComObjectInternal(app, finalRelease: true); // Clean up potentially partially acquired object
                throw new ExcelInstanceNotFoundException("Could not find a running instance of Excel. Please ensure Excel is open and accessible.", ex);
            }
            catch (Exception ex)
            {
                 ErrorHandlingService.LogException(ex, "Unexpected error getting existing Excel instance.");
                 ReleaseComObjectInternal(app, finalRelease: true); // Clean up potentially partially acquired object
                 throw new ExcelInstanceNotFoundException("An unexpected error occurred while trying to connect to Excel.", ex);
            }
        }

        /// <summary>
        /// Creates an ExcelComWrapper specifically for "Go to Source" scenarios.
        /// Prioritizes using existing Excel instances to avoid creating orphaned processes.
        /// If no existing instance is found, creates a new one with proper visibility settings.
        /// </summary>
        /// <returns>An ExcelComWrapper instance optimized for Go to Source operations.</returns>
        public static ExcelComWrapper CreateForGoToSource()
        {
            try
            {
                // First try to get an existing instance
                return GetExistingInstance();
            }
            catch (ExcelInstanceNotFoundException)
            {
                // No existing instance found, create a new one
                var wrapper = new ExcelComWrapper();
                // Configure for Go to Source usage
                wrapper.SetKeepExcelOpenOnDispose(true);
                return wrapper;
            }
        }

        /// <summary>
        /// Initializes a new instance of the ExcelComWrapper class.
        /// It attempts to get an existing running Excel instance first. If none is found,
        /// it creates a new Excel Application instance.
        /// Use this for background tasks, automated processes, or when creating a new instance
        /// is an acceptable fallback (e.g., Go To Source, Refresh).
        /// </summary>
        /// <exception cref="InvalidOperationException">Thrown if Excel cannot be attached to or created.</exception>
        /// <exception cref="Exception">Rethrows unexpected exceptions during initialization.</exception>
        public ExcelComWrapper() // Default constructor: Get or Create
        {
            Excel.Application app = null;
            bool owns = false; // Local variable to determine ownership
            try
            {
                // Try to get a running instance first
                app = (Excel.Application)Marshal.GetActiveObject("Excel.Application");
                owns = false; // We attached to an existing instance
                try { if (app != null) app.Visible = true; } // Ensure attached instance is visible
                catch (Exception visEx) { ErrorHandlingService.LogException(visEx, "Failed to set visibility on existing Excel instance."); }
            }
            catch (COMException) // Expected if Excel is not running
            {
                // Excel not running, create a new instance
                try
                {
                    app = new Excel.Application();
                    owns = true; // We created this instance
                    app.Visible = true; // Make the new instance visible
                }
                catch (Exception creationEx)
                {
                    ErrorHandlingService.LogException(creationEx, "Failed to create a new Excel Application instance.");
                    ReleaseComObjectInternal(app, finalRelease: true); // Clean up partially created object
                    throw new InvalidOperationException("Failed to create a new Excel instance. Ensure Excel is installed and permissions are correct.", creationEx);
                }
            }
            catch (Exception ex) // Catch other potential errors during GetActiveObject
            {
                 ErrorHandlingService.LogException(ex, "Unexpected error getting or creating Excel instance in constructor.");
                 ReleaseComObjectInternal(app, finalRelease: true); // Clean up potentially partially obtained object
                 throw; // Re-throw the original exception
            }

            // --- Assign to readonly field HERE in the constructor ---
            _ownsExcelApp = owns;
            // --- Call helper for remaining field initialization ---
            InitializeInstanceFields(app);
        }

        /// <summary>
        /// Private constructor used only by the factory method GetExistingInstance.
        /// </summary>
        /// <param name="appInstance">The existing Excel Application instance.</param>
        /// <param name="ownsInstance">Must be false when called from GetExistingInstance.</param>
        private ExcelComWrapper(Excel.Application appInstance, bool ownsInstance)
        {
             // --- Assign to readonly field HERE in the constructor ---
             _ownsExcelApp = ownsInstance; // Should always be false here
             // --- Call helper for remaining field initialization ---
             InitializeInstanceFields(appInstance);
        }

        /// <summary>
        /// Centralized initialization logic for fields OTHER THAN readonly ones,
        /// called by constructors after ownership is determined.
        /// </summary>
        /// <param name="appInstance">The obtained Excel Application instance.</param>
        private void InitializeInstanceFields(Excel.Application appInstance)
        {
             if (appInstance == null)
             {
                 // This should ideally be caught earlier, but acts as a final safeguard.
                 throw new InvalidOperationException("Excel Application object could not be initialized (null passed to InitializeInstanceFields).");
             }

             _excelApp = appInstance; // Assign non-readonly field

             // Common setup: Disable alerts for automation
             try
             {
                 _excelApp.DisplayAlerts = false;
             }
             catch (Exception alertEx)
             {
                 // Log but continue; disabling alerts is non-critical
                 ErrorHandlingService.LogException(alertEx, "Failed to disable Excel display alerts.");
             }
        }

        #endregion

        #region Workbook and Worksheet Operations

        /// <summary>
        /// Opens a workbook in read-only mode. Releases any previously opened workbook first.
        /// </summary>
        /// <param name="filePath">The full path to the Excel file.</param>
        /// <returns>The opened Workbook object, or null if opening fails.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        /// <exception cref="InvalidOperationException">Thrown if the Excel Application is not available.</exception>
        public Excel.Workbook OpenWorkbook(string filePath)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ExcelComWrapper));
            if (_excelApp == null) throw new InvalidOperationException("Excel application is not initialized.");

            ReleaseComObjectInternal(_workbook, finalRelease: true); // Release previous workbook COM object
            _workbook = null; // Reset field

            try
            {
                // Determine if this is a SharePoint URL for better logging
                bool isSharePointUrl = Uri.TryCreate(filePath, UriKind.Absolute, out Uri uri) &&
                                      (uri.Scheme == "http" || uri.Scheme == "https");

                string fileType = isSharePointUrl ? "SharePoint URL" : "local file";
                ErrorHandlingService.LogException(null, $"ExcelComWrapper: Attempting to open {fileType}: {filePath}");

                // Use relevant parameters for automation: Open ReadOnly, don't update links, don't add to MRU list.
                _workbook = _excelApp.Workbooks.Open(
                    Filename: filePath,
                    UpdateLinks: 0, // 0 = Don't update external links
                    ReadOnly: true,
                    AddToMru: false);

                ErrorHandlingService.LogException(null, $"ExcelComWrapper: Successfully opened {fileType}: {filePath}");

                // Ensure the workbook is visible and activated
                try
                {
                    if (_workbook != null)
                    {
                        // Activate the workbook to bring it to the front
                        _workbook.Activate();
                        
                        // Ensure Excel window is visible
                        if (_excelApp != null && !_excelApp.Visible)
                        {
                            _excelApp.Visible = true;
                        }
                    }
                }
                catch (Exception activateEx)
                {
                    ErrorHandlingService.LogException(activateEx, $"ExcelComWrapper: Warning - Failed to activate workbook after opening: {filePath}");
                    // Don't fail the operation for activation issues
                }

                return _workbook;
            }
            catch(Exception ex)
            {
                 bool isSharePointUrl = Uri.TryCreate(filePath, UriKind.Absolute, out Uri uri) &&
                                       (uri.Scheme == "http" || uri.Scheme == "https");
                 string fileType = isSharePointUrl ? "SharePoint URL" : "local file";

                 ErrorHandlingService.LogException(ex, $"ExcelComWrapper: Failed to open {fileType}: {filePath}");
                 ReleaseComObjectInternal(_workbook, finalRelease: true); // Clean up if open failed partially
                 _workbook = null;
                 return null;
            }
        }

        /// <summary>
        /// Gets a worksheet by its name from the currently open workbook.
        /// Releases any previously held worksheet reference first.
        /// </summary>
        /// <param name="sheetName">The name of the worksheet.</param>
        /// <returns>The Worksheet object, or null if not found or workbook not open.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        public Excel.Worksheet GetWorksheet(string sheetName)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ExcelComWrapper));
            if (_workbook == null)
            {
                 ErrorHandlingService.LogException(new InvalidOperationException("Workbook is not open or available."), "GetWorksheet Error: No Workbook");
                 return null;
            }

            ReleaseComObjectInternal(_worksheet, finalRelease: true); // Release previous worksheet COM object
            _worksheet = null; // Reset field

            try
            {
                // Accessing Sheets collection by name throws if not found.
                _worksheet = (Excel.Worksheet)_workbook.Sheets[sheetName];
                return _worksheet;
            }
            catch(Exception ex) // Catch potential COMExceptions or others
            {
                 ErrorHandlingService.LogException(ex, string.Format("Failed to get worksheet: {0}", sheetName));
                 ReleaseComObjectInternal(_worksheet, finalRelease: true); // Clean up if partially obtained
                 _worksheet = null;
                 return null;
            }
        }

        #endregion

        #region Object Retrieval (Chart, Range, Shape)

        /// <summary>
        /// Gets a chart object by its name from a specific worksheet.
        /// </summary>
        /// <param name="sheetName">The name of the worksheet containing the chart.</param>
        /// <param name="chartName">The name of the chart object (e.g., "Chart 1").</param>
        /// <returns>The ChartObject, or null if not found or an error occurs. The caller is responsible for releasing the returned COM object.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        public Excel.ChartObject GetChartObject(string sheetName, string chartName)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ExcelComWrapper));

            Excel.Worksheet sheet = GetWorksheet(sheetName); // Use the wrapper method to get/manage the sheet
            if (sheet == null) return null; // GetWorksheet already logged the error

            Excel.ChartObjects chartObjects = null;
            Excel.ChartObject chart = null;
            try
            {
                chartObjects = (Excel.ChartObjects)sheet.ChartObjects();
                if (chartObjects == null)
                {
                    throw new InvalidOperationException("Could not retrieve ChartObjects collection.");
                }

                // Explicit cast required as Item returns System.Object
                chart = (Excel.ChartObject)chartObjects.Item(chartName);

                return chart; // Caller must release this object
            }
            catch (COMException comEx) when ((uint)comEx.ErrorCode == 0x800A03EC) // NAME_NOT_FOUND HRESULT
            {
                ErrorHandlingService.LogException(null, string.Format("Chart object '{0}' not found on worksheet '{1}'.", chartName, sheetName));
                return null;
            }
            catch (InvalidCastException castEx)
            {
                 ErrorHandlingService.LogException(castEx, string.Format("Object retrieved for '{0}' on worksheet '{1}' was not a ChartObject.", chartName, sheetName));
                 return null;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, string.Format("Error getting chart object '{0}' from worksheet '{1}'.", chartName, sheetName));
                ReleaseComObjectInternal(chart, finalRelease: true); // Clean up if partially obtained
                return null;
            }
            finally
            {
                ReleaseComObjectInternal(chartObjects, finalRelease: true); // Release intermediate collection
            }
        }

        /// <summary>
        /// Gets a Range object by its address (e.g., "A1:C10") from a specific worksheet.
        /// </summary>
        /// <param name="sheetName">The name of the worksheet containing the range.</param>
        /// <param name="rangeAddress">The address of the range.</param>
        /// <returns>The Range object, or null if not found or an error occurs. The caller is responsible for releasing the returned COM object.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        public Excel.Range GetRange(string sheetName, string rangeAddress)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ExcelComWrapper));

            Excel.Worksheet sheet = GetWorksheet(sheetName); // Use the wrapper method
            if (sheet == null) return null; // GetWorksheet already logged the error

            Excel.Range range = null;
            try
            {
                // Accessing Range property throws if address is invalid.
                range = sheet.Range[rangeAddress];
                return range; // Caller must release this object.
            }
            catch (COMException comEx) when ((uint)comEx.ErrorCode == 0x800A03EC) // NAME_NOT_FOUND HRESULT (can happen for invalid range syntax)
            {
                 ErrorHandlingService.LogException(null, string.Format("Range address '{0}' not found or invalid on worksheet '{1}'.", rangeAddress, sheetName));
                 ReleaseComObjectInternal(range, finalRelease: true); // Clean up if partially obtained
                 return null;
            }
            catch(Exception ex)
            {
                 ErrorHandlingService.LogException(ex, string.Format("Error getting range '{0}' from worksheet '{1}'.", rangeAddress, sheetName));
                 ReleaseComObjectInternal(range, finalRelease: true); // Clean up if partially obtained
                 return null;
            }
        }

        /// <summary>
        /// Gets a Shape object by its name from a specific worksheet.
        /// </summary>
        /// <param name="sheetName">The name of the worksheet containing the shape.</param>
        /// <param name="shapeName">The name of the shape.</param>
        /// <returns>The Shape object, or null if not found or an error occurs. The caller is responsible for releasing the returned COM object.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        public Excel.Shape GetShape(string sheetName, string shapeName)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ExcelComWrapper));

            Excel.Worksheet sheet = GetWorksheet(sheetName); // Use the wrapper method
            if (sheet == null) return null; // GetWorksheet already logged the error

            Excel.Shapes shapes = null;
            Excel.Shape shape = null;
            try
            {
                shapes = sheet.Shapes;
                if (shapes == null)
                {
                     throw new InvalidOperationException("Could not retrieve Shapes collection.");
                }
                // Accessing Item by name throws if not found.
                shape = shapes.Item(shapeName);
                return shape; // Caller must release this object.
            }
            catch (COMException comEx) when ((uint)comEx.ErrorCode == 0x800A03EC) // NAME_NOT_FOUND HRESULT
            {
                ErrorHandlingService.LogException(null, string.Format("Shape '{0}' not found on worksheet '{1}'.", shapeName, sheetName));
                return null;
            }
            catch(Exception ex)
            {
                ErrorHandlingService.LogException(ex, string.Format("Error getting shape '{0}' from worksheet '{1}'.", shapeName, sheetName));
                ReleaseComObjectInternal(shape, finalRelease: true); // Clean up if partially obtained
                return null;
            }
            finally
            {
                ReleaseComObjectInternal(shapes, finalRelease: true); // Release intermediate collection
            }
        }

        #endregion

        #region Actions

        /// <summary>
        /// Exports a chart (contained within a ChartObject) to an image file.
        /// </summary>
        /// <param name="chartObject">The ChartObject to export. This object should be released by the caller.</param>
        /// <param name="imagePath">The full path where the image file should be saved.</param>
        /// <returns>True if export was successful, false otherwise.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        public bool ExportChartAsImage(Excel.ChartObject chartObject, string imagePath)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ExcelComWrapper));
            if (chartObject == null)
            {
                 ErrorHandlingService.LogException(new ArgumentNullException("chartObject"), "ExportChartAsImage Error");
                 return false;
            }

            Excel.Chart chart = null;
            string chartObjName = "[Unknown Name]"; // Default name for logging
            try
            {
                 try { chartObjName = chartObject.Name; } catch { /* Ignore errors getting name */ }

                 chart = chartObject.Chart;
                 if (chart == null)
                 {
                     throw new InvalidOperationException("ChartObject does not contain a valid Chart.");
                 }

                 // Ensure directory exists before exporting
                 string directory = System.IO.Path.GetDirectoryName(imagePath);
                 if (!string.IsNullOrEmpty(directory) && !System.IO.Directory.Exists(directory))
                 {
                     System.IO.Directory.CreateDirectory(directory);
                 }

                 chart.Export(imagePath, "PNG"); // Using PNG format
                 return true;
            }
            catch (Exception ex)
            {
                 ErrorHandlingService.LogException(ex, string.Format("Failed to export chart '{0}' to '{1}'.", chartObjName, imagePath));
                 return false;
            }
            finally
            {
                 // Release the Chart COM object obtained within this method
                 ReleaseComObjectInternal(chart, finalRelease: true);
            }
        }

        /// <summary>
        /// Makes the Excel application window visible to the user.
        /// </summary>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        public void MakeVisible()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ExcelComWrapper));
            if (_excelApp != null)
            {
                 try
                 {
                     if (!_excelApp.Visible)
                     {
                         _excelApp.Visible = true;
                     }
                 }
                 catch (Exception ex)
                 {
                     ErrorHandlingService.LogException(ex, "Failed to set Excel visibility to true.");
                 }
            }
            else
            {
                ErrorHandlingService.LogException(new InvalidOperationException("Excel Application object is null."), "MakeVisible Error");
            }
        }

        /// <summary>
        /// Sets whether to keep Excel application open when this wrapper is disposed.
        /// Useful for "Go to Source" scenarios where the user should continue working in Excel.
        /// </summary>
        /// <param name="keepOpen">True to keep Excel open after disposal, false to quit as normal</param>
        public void SetKeepExcelOpenOnDispose(bool keepOpen = true)
        {
            _keepExcelOpenOnDispose = keepOpen;
        }

        /// <summary>
        /// Activates the Excel application window and brings it to the foreground.
        /// Ensures the workbook and worksheet are properly visible to the user.
        /// </summary>
        public void ActivateExcelWindow()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ExcelComWrapper));
            if (_excelApp == null) return;

            try
            {
                // Make Excel visible first
                _excelApp.Visible = true;
                
                // If we have an active workbook, activate it
                if (_workbook != null)
                {
                    try
                    {
                        _workbook.Activate();
                        
                        // If we have an active worksheet, activate it
                        if (_worksheet != null)
                        {
                            _worksheet.Activate();
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorHandlingService.LogException(ex, "Failed to activate workbook/worksheet in ActivateExcelWindow");
                    }
                }

                // Bring Excel window to foreground using Windows API
                try
                {
                    var hwnd = (IntPtr)_excelApp.Hwnd;
                    if (hwnd != IntPtr.Zero)
                    {
                        ShowWindow(hwnd, SW_RESTORE); // Restore if minimized
                        SetForegroundWindow(hwnd);
                    }
                }
                catch (Exception ex)
                {
                    ErrorHandlingService.LogException(ex, "Failed to bring Excel window to foreground");
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error in ActivateExcelWindow");
            }
        }

        // Windows API declarations for window management
        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        private const int SW_RESTORE = 9;

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Releases all COM objects held by this wrapper and quits the Excel application
        /// if this wrapper instance created it.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this); // Prevent finalizer from running
        }

        /// <summary>
        /// Performs the actual cleanup logic.
        /// </summary>
        /// <param name="disposing">True if called from Dispose(), false if called from the finalizer.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;

            if (disposing)
            {
                // Dispose managed state (managed objects) if any were held.
                // Currently none in this class.
            }

            // --- Release COM objects held as instance fields ---
            // Release in reverse order of acquisition (Worksheet -> Workbook -> Application)
            ReleaseComObjectInternal(_worksheet, finalRelease: true);
            _worksheet = null;
            
            if (_workbook != null)
            {
                // Only close the workbook if we own the Excel Application AND we're not keeping Excel open
                // If we attached to an existing instance or keeping Excel open, don't close the user's workbook
                if (_ownsExcelApp && !_keepExcelOpenOnDispose)
                {
                    try { _workbook.Close(SaveChanges: false); } // Close without saving
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Ignoring error during Workbook.Close: {ex.Message}"); }
                }
                ReleaseComObjectInternal(_workbook, finalRelease: true);
                _workbook = null;
            }

            if (_excelApp != null)
            {
                // Only Quit the Excel Application if this wrapper instance created it AND we're not keeping it open
                if (_ownsExcelApp && !_keepExcelOpenOnDispose)
                {
                    try
                    {
                        // Check if there are other workbooks open before quitting
                        bool hasOtherWorkbooks = false;
                        try
                        {
                            hasOtherWorkbooks = _excelApp.Workbooks.Count > 0;
                        }
                        catch { /* Ignore errors checking workbook count */ }

                        // Only quit if no other workbooks are open
                        if (!hasOtherWorkbooks)
                        {
                            _excelApp.Quit();
                        }
                    }
                    catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Ignoring error during Application.Quit: {ex.Message}"); }
                }
                // Always release the COM reference to the Application object held by this wrapper
                ReleaseComObjectInternal(_excelApp, finalRelease: true);
                _excelApp = null;
            }

            _disposed = true;
        }

        /// <summary>
        /// Finalizer to ensure COM objects are released as a fallback if Dispose() is not called.
        /// </summary>
        ~ExcelComWrapper()
        {
            // Call the dispose logic, indicating it's from the finalizer.
            Dispose(false);
        }

        #endregion

        #region COM Object Release Helpers

        /// <summary>
        /// Safely releases a COM object using Marshal.ReleaseComObject.
        /// Use this for objects obtained from wrapper methods but managed by the caller,
        /// or in finally blocks.
        /// </summary>
        /// <param name="obj">The COM object to release.</param>
        public static void ReleaseComObject(object obj) // Made public static
        {
            ReleaseComObjectInternal(obj, finalRelease: false);
        }

        /// <summary>
        /// Internal helper to safely release a COM object.
        /// Can use either ReleaseComObject or FinalReleaseComObject.
        /// </summary>
        /// <param name="obj">The COM object to release.</param>
        /// <param name="finalRelease">If true, use FinalReleaseComObject; otherwise, use ReleaseComObject.</param>
        private static void ReleaseComObjectInternal(object obj, bool finalRelease = false)
        {
            if (obj != null && Marshal.IsComObject(obj))
            {
                try
                {
                    if (finalRelease)
                    {
                        Marshal.FinalReleaseComObject(obj);
                    }
                    else
                    {
                        Marshal.ReleaseComObject(obj);
                    }
                }
                catch (Exception ex)
                {
                    // Log cautiously, especially if called from Dispose/finalizer
                    ErrorHandlingService.LogException(ex, string.Format("Error during {0} for COM object.", finalRelease ? "FinalReleaseComObject" : "ReleaseComObject"));
                    // System.Diagnostics.Debug.WriteLine($"ExcelComWrapper: Ignoring error during COM release: {ex.Message}");
                }
                finally
                {
                    // Set obj to null in the caller if possible.
                }
            }
        }

        #endregion
    }
}
// --- END OF FINAL REWRITTEN ExcelComWrapper.cs ---