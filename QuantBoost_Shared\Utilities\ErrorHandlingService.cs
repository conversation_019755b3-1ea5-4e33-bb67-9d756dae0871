// --- START OF FILE ErrorHandlingService.cs ---
using System;
using System.Diagnostics; // Use Debug for output window logging during development
using System.IO;
using System.Windows.Forms;
using System.Threading; // For SynchronizationContext

namespace QuantBoost_Shared.Utilities
{
    public static class ErrorHandlingService
    {
        // Consider making log path configurable or more dynamic
        private static readonly string LogDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "QuantBoost", "logs");
        private static readonly string LogPath = Path.Combine(LogDirectory, "addin_errors.log");
        private static readonly object _logLock = new object(); // Simple lock for file access

        /// <summary>
        /// Logs the exception and shows a message box to the user.
        /// CAUTION: Avoid using during critical Add-in startup phases (constructors, ThisAddIn_Startup)
        /// as MessageBox can interfere with Office loading. Use LogException instead during startup.
        /// </summary>
        /// <param name="ex">The exception.</param>
        /// <param name="context">Optional context information.</param>
        public static void HandleException(Exception ex, string context = null)
        {
            LogException(ex, context); // Always log

            // Show message box (consider if this should be conditional based on context/severity)
            string message = $"An error occurred";
            if (!string.IsNullOrEmpty(context))
            {
                message += $" during {context}";
            }
            message += $":\n\n{ex.Message}\n\nSee logs for more details.";

            // Ensure MessageBox runs on UI thread if called from background
            Action showMsg = () => MessageBox.Show(message, "Quant Boost Add-in Error", MessageBoxButtons.OK, MessageBoxIcon.Error);

            if (IsUiThread()) // If already on UI thread, show directly
            {
                 showMsg();
            }
            else // Otherwise, marshal using AsyncHelper (Requires AsyncHelper to be initialized!)
            {
                AsyncHelper.RunOnUIThread(showMsg);
            }
        }

        /// <summary>
        /// Logs exception details and context information to a file and Debug output.
        /// Safe to call from any thread and during startup.
        /// </summary>
        /// <param name="ex">The exception (can be null for info messages).</param>
        /// <param name="context">Context information or message.</param>
        public static void LogException(Exception ex, string context = null)
        {
            try
            {
                string messageType = ex == null ? "INFO" : "ERROR";
                string logMessage = $"[{DateTime.UtcNow:u}] [{messageType}]";
                if (!string.IsNullOrEmpty(context))
                {
                    logMessage += $" Context: {context}";
                }
                if (ex != null)
                {
                    // Include full exception details (including inner exceptions)
                    logMessage += $"\nException: {ex.ToString()}";
                }
                logMessage += "\r\n--------------------------------------------------\r\n";

                // Log to Debug Output window
                Debug.WriteLine(logMessage);

                // Log to file with basic locking
                lock (_logLock)
                {
                    Directory.CreateDirectory(LogDirectory); // Ensure directory exists
                    File.AppendAllText(LogPath, logMessage);
                }
            }
            catch (Exception logEx)
            {
                // Log logging errors to Debug output only to avoid infinite loops
                Debug.WriteLine($"!!! FAILED TO WRITE TO LOG FILE: {logEx.Message} !!!");
                Debug.WriteLine($"Original Log Message Attempt: {context} - {ex?.Message}");
            }
        }

        /// <summary>
        /// Helper to check if the current thread is the main UI thread.
        /// Relies on AsyncHelper having captured the UI context.
        /// </summary>
        private static bool IsUiThread()
        {
            // This check is simplified. A more robust check might involve
            // comparing SynchronizationContext.Current with the captured _uiContext
            // or checking Control.InvokeRequired if a control reference is available.
            // For simplicity, assume if SynchronizationContext.Current exists, it's likely UI.
            return SynchronizationContext.Current != null; // Basic check
        }
    }
}
// --- END OF FILE ErrorHandlingService.cs ---