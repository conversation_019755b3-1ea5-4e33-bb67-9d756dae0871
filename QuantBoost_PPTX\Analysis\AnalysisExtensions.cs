using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using QuantBoost_Shared.Utilities; // For ToastNotifier, AsyncHelper, ProgressState
// Ensure QuantBoost_Powerpoint_Addin contains AnalysisService, Globals.ThisAddIn
// Ensure QuantBoost_Powerpoint_Addin contains SlideAnalysisSummary, OverallAnalysisSummary (likely within or referenced by AnalysisService)

namespace QuantBoost_Powerpoint_Addin.Analysis
{
    // Removed duplicate ProgressState class. Use the main definition in ProgressState.cs.

    // TODO: Define AnalysisService class elsewhere with the expected methods.
    // Example placeholder:
    /*
    public static class AnalysisService
    {
        public static async Task<(List<SlideAnalysisSummary> slideSummaries, OverallAnalysisSummary overallSummary)>
            AnalyzePresentationAsync(
                string presentationPath,
                IProgress<ProgressState> progress = null, // Needs to accept IProgress<ProgressState>
                CancellationToken cancellationToken = default) // Needs to accept CancellationToken
        {
            // Implementation goes here...
            // Report progress using progress?.Report(...)
            // Check for cancellation using cancellationToken.ThrowIfCancellationRequested()
            await Task.Delay(1000, cancellationToken); // Simulate work
            return (new List<SlideAnalysisSummary>(), new OverallAnalysisSummary());
        }
    }
    */

    // TODO: Define SlideAnalysisSummary and OverallAnalysisSummary classes elsewhere.
    /*
    public class SlideAnalysisSummary { /* ... properties ... * / }
    public class OverallAnalysisSummary { /* ... properties ... * / }
    */

    /// <summary>
    /// Provides extension methods for convenient analysis execution with various UI feedback options.
    /// </summary>
    public static class AnalysisExtensions
    {
        /// <summary>
        /// Performs analysis with a toast notification for progress reporting.
        /// </summary>
        /// <param name="presentationPath">Path to the presentation file</param>
        /// <param name="toastTitle">Title for the toast notification</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>Analysis result</returns>
        public static async Task<(List<SlideAnalysisSummary> slideSummaries, OverallAnalysisSummary overallSummary)>
            AnalyzeWithToastAsync(
                string presentationPath,
                string toastTitle = "Analyzing Presentation",
                CancellationToken cancellationToken = default)
        {
            // Create a toast notifier for progress reporting
            // NOTE: Using Progress<T> directly which calls ShowToast on each update.
            var toastProgress = new Progress<ProgressState>(state =>
            {
                // Update toast - consider throttling this if updates are too frequent
                ToastNotifier.ShowToast($"{toastTitle} ({state.PercentComplete}%) - {state.CurrentStep}", 2500);
            });

            // Show initial toast
            ToastNotifier.ShowToast($"{toastTitle} (0%)", 2500);

            try
            {
                // Perform the analysis
                // ****** ERROR FIX AREA (Original Error on Line 38) ******
                // ASSUMPTION: AnalysisService.AnalyzePresentationAsync MUST be defined to accept
                // (string, IProgress<ProgressState>, CancellationToken) for this call to work.
                var result = await AnalysisService.AnalyzePresentationWithProgressAsync(
                    presentationPath,
                    toastProgress, // Pass the progress reporter
                    cancellationToken);

                // Show completion toast
                ToastNotifier.ShowToast("Analysis complete!", 3000);

                return result;
            }
            catch (OperationCanceledException)
            {
                // Show cancellation toast
                ToastNotifier.ShowToast("Analysis was cancelled", 3000);
                throw;
            }
            catch (Exception ex)
            {
                // Show error toast
                // TODO: Ensure ToastNotifier is implemented in QuantBoost_Powerpoint_Addin.UI
                ToastNotifier.ShowToast($"Analysis error: {ex.Message}", 5000);
                // TODO: Implement ErrorHandlingService or replace with appropriate error logging/display
                // ErrorHandlingService.HandleException(ex, "Analysis Error");
                Console.WriteLine($"Analysis Error: {ex}"); // Placeholder
                throw;
            }
            // Removed incorrect curly brace placement from original code
        }

        /// <summary>
        /// Performs analysis with a modal progress dialog.
        /// </summary>
        /// <param name="presentationPath">Path to the presentation file</param>
        /// <param name="owner">Owner form for the dialog</param>
        /// <param name="dialogTitle">Title for the dialog</param>
        /// <returns>Analysis result or null if cancelled</returns>
        public static (List<SlideAnalysisSummary> slideSummaries, OverallAnalysisSummary overallSummary)?
            AnalyzeWithDialog(
                string presentationPath,
                Form owner = null,
                string dialogTitle = "Analyzing Presentation")
        {
            // Create a progress dialog
            using (var progressDialog = new ProgressDialog(dialogTitle))
            {
                // Set up cancellation
                using (var cts = new CancellationTokenSource())
                {
                    // Handle cancellation request
                    progressDialog.CancelRequested += (s, e) => cts.Cancel();

                    // Show the dialog modally or non-modally depending on owner
                    // Showing modally is generally better for this type of blocking operation
                    // progressDialog.Show(owner); // Non-modal requires careful handling
                    // progressDialog.Show();      // Non-modal

                    // Task to run the analysis so the UI thread isn't blocked getting the result
                    Task<(List<SlideAnalysisSummary>, OverallAnalysisSummary)> analysisTask = null;

                    // Use Shown event to start the task *after* the dialog is visible
                    progressDialog.Shown += (s, e) =>
                    {
                        analysisTask = Task.Run(async () =>
                        {
                            // ****** ERROR FIX AREA (Original Error on Line 98) ******
                            // ASSUMPTION: AnalysisService.AnalyzePresentationAsync MUST be defined to accept
                            // (string, IProgress<ProgressState>, CancellationToken) for this call to work.
                            return await AnalysisService.AnalyzePresentationWithProgressAsync(
                                presentationPath,
                                progressDialog, // The dialog itself implements IProgress<ProgressState>
                                cts.Token);
                        }, cts.Token);

                        // Optionally, handle task completion here if needed for non-modal dialog updates
                    };

                    // Show the dialog modally - this blocks until the dialog is closed
                    progressDialog.ShowDialog(owner); // Use ShowDialog for modal behavior

                    // After the dialog is closed (either by completion, cancellation, or error)

                    if (cts.IsCancellationRequested)
                    {
                        // Return null to indicate cancellation
                        return null;
                    }

                    if (analysisTask == null)
                    {
                        // Should not happen if Shown event fired correctly, but handle defensively
                        // TODO: Log this unexpected state
                        return null;
                    }

                    if (analysisTask.IsFaulted)
                    {
                        // Extract exception (usually an AggregateException)
                        var ex = analysisTask.Exception.Flatten().InnerException;
                        // Show error and rethrow or handle
                        // TODO: Implement ErrorHandlingService or replace
                        // ErrorHandlingService.HandleException(ex, "Analysis Error");
                        MessageBox.Show($"Analysis Error: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        // Depending on desired behavior, you might throw, return null, or log
                        throw new Exception("Analysis failed.", ex);
                    }

                    if (analysisTask.IsCompleted) // Check IsCompletedSuccessfully implicitly
                    {
                        // Return the result
                        return analysisTask.Result;
                    }

                    // If none of the above, something unexpected happened (e.g., dialog closed before task started?)
                    // TODO: Log this state
                    return null; // Or throw an exception
                }
            }
        }


        /// <summary>
        /// Performs analysis in the background with optional notifications.
        /// </summary>
        /// <param name="presentationPath">Path to the presentation file</param>
        /// <param name="onComplete">Callback when analysis completes (runs on UI thread)</param>
        /// <param name="onError">Callback when an error occurs (runs on UI thread)</param>
        /// <param name="showProgressNotifications">Whether to show progress notifications</param>
        public static void AnalyzeInBackground(
            string presentationPath,
            Action<List<SlideAnalysisSummary>, OverallAnalysisSummary> onComplete,
            Action<Exception> onError = null,
            bool showProgressNotifications = true)
        {
            // ****** ERROR FIX AREA (Original Error on Line 138) ******
            // TODO: Define a global CancellationTokenSource or similar mechanism in Globals.ThisAddIn
            // For now, using CancellationToken.None as a placeholder. Replace with your actual token source.
            // Example: CancellationToken globalToken = Globals.ThisAddIn.GlobalCancellationTokenSource.Token;
            CancellationToken globalToken = CancellationToken.None; // Placeholder

            // Create a cancellation token source specific to this background task, possibly linked
            var linkedTokenSource = CancellationTokenSource.CreateLinkedTokenSource(globalToken);

            // Start the task
            var backgroundTask = Task.Run(async () =>
            {
                try
                {
                    // Create progress reporter if notifications are enabled
                    IProgress<ProgressState> progress = null;
                    if (showProgressNotifications)
                    {
                        // ****** ERROR FIX AREA (Original Error on Line 149) ******
                        // TODO: Implement CreateProgressReporter method in ToastNotifier class.
                        // It should return an IProgress<ProgressState> that calls ToastNotifier.ShowToast.
                        // Example Placeholder Implementation:
                        // progress = new Progress<ProgressState>(state => ToastNotifier.ShowToast($"Analyzing... {state.PercentComplete}%", 1500));
                        progress = new Progress<ProgressState>(state => { /* Call ToastNotifier here */ }); // Placeholder
                        // Alternatively, create the Progress<T> instance directly as in AnalyzeWithToastAsync
                        // progress = new Progress<ProgressState>(state => ToastNotifier.ShowToast($"Analyzing ({state.PercentComplete}%): {state.CurrentStep}", 2000));
                    }

                    // Run the analysis
                    // ****** ERROR FIX AREA (Original Error on Line 153) ******
                    // ASSUMPTION: AnalysisService.AnalyzePresentationAsync MUST be defined to accept
                    // (string, IProgress<ProgressState>, CancellationToken) for this call to work.
                    var result = await AnalysisService.AnalyzePresentationWithProgressAsync(
                        presentationPath,
                        progress,
                        linkedTokenSource.Token);

                    // Show completion notification if enabled
                    if (showProgressNotifications)
                    {
                        // TODO: Ensure ToastNotifier is implemented
                        ToastNotifier.ShowToast("Analysis complete!", 3000);
                    }

                    // Call completion callback on UI thread
                    // ****** ERROR FIX AREA (Original Error on Line 165) ******
                    // TODO: Implement AsyncHelper.RunOnUIThread(Action action)
                    // This typically uses SynchronizationContext.Current.Post() or Control.BeginInvoke()
                    AsyncHelper.RunOnUIThread(() => onComplete?.Invoke(result.slideSummaries, result.overallSummary));
                }
                catch (OperationCanceledException)
                {
                    // Analysis was cancelled (by user or global token)
                    if (showProgressNotifications)
                    {
                        // TODO: Ensure ToastNotifier is implemented
                        ToastNotifier.ShowToast("Analysis was cancelled", 3000);
                    }
                    // Optionally call onError or a specific onCancelled callback on UI thread
                }
                catch (Exception ex)
                {
                    // Show error notification if enabled
                    if (showProgressNotifications)
                    {
                        // TODO: Ensure ToastNotifier is implemented
                        ToastNotifier.ShowToast($"Analysis error: {ex.Message}", 5000);
                    }

                    // Call error callback on UI thread
                    // ****** ERROR FIX AREA (Original Error on Line 184) ******
                    // TODO: Implement AsyncHelper.RunOnUIThread(Action action)
                    AsyncHelper.RunOnUIThread(() => onError?.Invoke(ex));
                }
                finally
                {
                    // Cleanup
                    linkedTokenSource.Dispose();
                }
            }, linkedTokenSource.Token); // Pass token to Task.Run as well

            // Register the task with the add-in
            // ****** ERROR FIX AREA (Original Error on Line 194) ******
            // TODO: Implement RegisterBackgroundTask(Task task) in Globals.ThisAddIn
            // This might add the task to a list for tracking or cancellation.
            // Globals.ThisAddIn.RegisterBackgroundTask(backgroundTask); // Placeholder call
        }

        /// <summary>
        /// Performs analysis with a simple progress callback (percentage only).
        /// </summary>
        /// <param name="presentationPath">Path to the presentation file</param>
        /// <param name="progressCallback">Callback for progress updates (0-100)</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>Analysis result</returns>
        public static async Task<(List<SlideAnalysisSummary> slideSummaries, OverallAnalysisSummary overallSummary)>
            AnalyzeWithProgressCallbackAsync(
                string presentationPath,
                Action<int> progressCallback,
                CancellationToken cancellationToken = default)
        {
            // Create a progress adapter
            var progress = new Progress<ProgressState>(state =>
            {
                progressCallback?.Invoke(state.PercentComplete);
            });

            // Run the analysis
            // ****** ERROR FIX AREA (Original Error on Line 217) ******
            // ASSUMPTION: AnalysisService.AnalyzePresentationAsync MUST be defined to accept
            // (string, IProgress<ProgressState>, CancellationToken) for this call to work.
            return await AnalysisService.AnalyzePresentationWithProgressAsync(
                presentationPath,
                progress,
                cancellationToken);
        }

        /// <summary>
        /// Performs analysis with a detailed progress callback.
        /// </summary>
        /// <param name="presentationPath">Path to the presentation file</param>
        /// <param name="progressCallback">Callback for detailed progress updates (step, percent, detail)</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>Analysis result</returns>
        public static async Task<(List<SlideAnalysisSummary> slideSummaries, OverallAnalysisSummary overallSummary)>
            AnalyzeWithDetailedProgressAsync(
                string presentationPath,
                Action<string, int, string> progressCallback,
                CancellationToken cancellationToken = default)
        {
            // Create a progress adapter
            var progress = new Progress<ProgressState>(state =>
            {
                progressCallback?.Invoke(
                    state.CurrentStep,
                    state.PercentComplete,
                    state.DetailMessage);
            });

            // Run the analysis
            // ****** ERROR FIX AREA (Original Error on Line 246) ******
            // ASSUMPTION: AnalysisService.AnalyzePresentationAsync MUST be defined to accept
            // (string, IProgress<ProgressState>, CancellationToken) for this call to work.
            return await AnalysisService.AnalyzePresentationWithProgressAsync(
                presentationPath,
                progress,
                cancellationToken);
        }
    }

    /// <summary>
    /// Simple progress dialog for displaying progress.
    /// NOTE: Ensure this is thread-safe if accessed from multiple threads,
    /// especially when Report is called from a background thread.
    /// The InvokeRequired pattern handles UI updates safely.
    /// </summary>
    internal class ProgressDialog : Form, IProgress<ProgressState>
    {
        private ProgressBar _progressBar;
        private Label _statusLabel;
        private Button _cancelButton;
        private Label _detailLabel;

        public event EventHandler CancelRequested;

        public ProgressDialog(string title)
        {
            InitializeComponents(title);
        }

        private void InitializeComponents(string title)
        {
            // Configure form
            this.Text = title;
            this.Width = 400;
            this.Height = 180; // Increased height slightly for better spacing
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.StartPosition = FormStartPosition.CenterParent;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ControlBox = false; // Prevents closing via 'X', only Cancel button works
            this.ShowInTaskbar = false; // Optional: Hide from taskbar

            // Create controls
            _statusLabel = new Label
            {
                Text = "Initializing...",
                AutoSize = true,
                Location = new System.Drawing.Point(12, 15) // Adjusted position
            };

            _detailLabel = new Label
            {
                Text = "",
                AutoSize = true, // Allow multi-line wrapping if needed
                MaximumSize = new System.Drawing.Size(this.ClientSize.Width - 24, 0), // Prevent horizontal overflow
                Location = new System.Drawing.Point(12, 40), // Adjusted position
                ForeColor = System.Drawing.Color.DimGray,
                Font = new System.Drawing.Font(this.Font, System.Drawing.FontStyle.Regular)
            };

            _progressBar = new ProgressBar
            {
                Minimum = 0,
                Maximum = 100,
                Value = 0,
                Location = new System.Drawing.Point(12, 75), // Adjusted position
                Width = this.ClientSize.Width - 24 // Use client width
            };

            _cancelButton = new Button
            {
                Text = "Cancel",
                Location = new System.Drawing.Point(this.ClientSize.Width - 87, 110), // Adjusted position
                Width = 75,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right // Anchor to bottom-right
            };
            _cancelButton.Click += (s, e) => CancelRequested?.Invoke(this, EventArgs.Empty);

            // Add controls to form
            this.Controls.Add(_statusLabel);
            this.Controls.Add(_detailLabel);
            this.Controls.Add(_progressBar);
            this.Controls.Add(_cancelButton);

            // Handle resizing if form style changes later
            this.ClientSizeChanged += (s, e) =>
            {
                _progressBar.Width = this.ClientSize.Width - 24;
                _cancelButton.Location = new System.Drawing.Point(this.ClientSize.Width - 87, 110);
                _detailLabel.MaximumSize = new System.Drawing.Size(this.ClientSize.Width - 24, 0);
            };
        }

        /// <summary>
        /// Implements the IProgress<ProgressState> interface.
        /// Safely updates UI controls from any thread.
        /// </summary>
        public void Report(ProgressState value)
        {
            // Check if this control's handle has been created on the UI thread
            // This is crucial for thread safety with WinForms controls.
            if (this.IsHandleCreated) // Added check
            {
                if (InvokeRequired)
                {
                    // We are on a background thread, marshal the call to the UI thread
                    try
                    {
                        // Use BeginInvoke for potentially better responsiveness than Invoke
                        this.BeginInvoke(new Action<ProgressState>(UpdateUI), value);
                    }
                    catch (ObjectDisposedException)
                    {
                        // Ignore if the form is already disposed (e.g., closed quickly)
                    }
                }
                else
                {
                    // We are already on the UI thread
                    UpdateUI(value);
                }
            }
            // If handle not created yet, maybe queue update? Or ignore? Ignoring for now.
        }

        // Helper method to perform the actual UI updates
        private void UpdateUI(ProgressState value)
        {
            if (this.IsDisposed) return; // Prevent updates if disposed

            _statusLabel.Text = value.CurrentStep ?? "Processing..."; // Provide default
            // Clamp value using Math.Min/Math.Max (C# 7.3/.NET 4.8.1 does not support Math.min/Math.max)
            _progressBar.Value = Math.Max(_progressBar.Minimum, Math.Min(_progressBar.Maximum, value.PercentComplete)); // Clamp value
            _detailLabel.Text = value.DetailMessage ?? "";
        }

        // Override Dispose to ensure cleanup if needed
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Dispose managed resources (controls are handled by base)
                _cancelButton.Click -= (s, e) => CancelRequested?.Invoke(this, EventArgs.Empty); // Unsubscribe event
            }
            base.Dispose(disposing);
        }
    }

    // AsyncHelper is now provided by QuantBoost_Shared.Utilities
}