using System;
using System.Windows.Controls;
using System.Windows.Threading;
using QuantBoost_Powerpoint_Addin.Features.ExcelLink.ViewModels;

namespace QuantBoost_Powerpoint_Addin.Features.ExcelLink.Views
{
    /// <summary>
    /// Interaction logic for LinkManagerView.xaml
    /// </summary>
    public partial class LinkManagerView : UserControl
    {
        /// <summary>
        /// Initializes a new instance of the LinkManagerView class.
        /// </summary>
        public LinkManagerView()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Gets the ViewModel instance for external access.
        /// </summary>
        public LinkManagerViewModel ViewModel => DataContext as LinkManagerViewModel;

        /// <summary>
        /// Triggers refresh from external sources (e.g., ribbon button).
        /// Thread-safe method that marshals to UI thread if needed.
        /// </summary>
        public void RefreshLinks()
        {
            if (Dispatcher.CheckAccess())
            {
                // Already on UI thread
                ViewModel?.LoadLinksCommand?.Execute(null);
            }
            else
            {
                // Marshal to UI thread
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    ViewModel?.LoadLinksCommand?.Execute(null);
                }), DispatcherPriority.Normal);
            }
        }

        /// <summary>
        /// Clears current results from external sources.
        /// Thread-safe method that marshals to UI thread if needed.
        /// </summary>
        public void ClearResults()
        {
            if (Dispatcher.CheckAccess())
            {
                // Already on UI thread
                ViewModel?.ClearLinks();
            }
            else
            {
                // Marshal to UI thread
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    ViewModel?.ClearLinks();
                }), DispatcherPriority.Normal);
            }
        }

        /// <summary>
        /// Triggers export from external sources.
        /// Thread-safe method that marshals to UI thread if needed.
        /// </summary>
        public void ExportResults()
        {
            if (Dispatcher.CheckAccess())
            {
                // Already on UI thread
                ViewModel?.ExportCommand?.Execute(null);
            }
            else
            {
                // Marshal to UI thread
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    ViewModel?.ExportCommand?.Execute(null);
                }), DispatcherPriority.Normal);
            }
        }
    }
}