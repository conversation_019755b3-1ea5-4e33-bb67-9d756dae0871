using System;

namespace QuantBoost_Powerpoint_Addin.Utilities
{
    /// <summary>
    /// Utility class for formatting data for display in the UI
    /// </summary>
    public static class FormattingUtils
    {
        /// <summary>
        /// Formats a file size in bytes to a human-readable string
        /// </summary>
        /// <param name="bytes">Size in bytes</param>
        /// <returns>Formatted size string (e.g., "1.5 MB", "234 KB")</returns>
        public static string FormatSize(long bytes)
        {
            if (bytes < 0)
                return "0 bytes";

            string[] sizes = { "bytes", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }

            // Return formatted string with appropriate decimal places
            if (order == 0)
                return $"{len:0} {sizes[order]}";
            else
                return $"{len:0.#} {sizes[order]}";
        }

        /// <summary>
        /// Generates basic optimization notes for a slide based on its content
        /// </summary>
        /// <param name="imageCount">Number of images on the slide</param>
        /// <param name="chartCount">Number of charts on the slide</param>
        /// <param name="mediaCount">Number of media files on the slide</param>
        /// <param name="embeddedObjectCount">Number of embedded objects on the slide</param>
        /// <param name="totalAssetSizeBytes">Total size of all assets on the slide</param>
        /// <returns>Optimization suggestion or empty string if no issues detected</returns>
        public static string GenerateOptimizationNote(int imageCount, int chartCount, int mediaCount, int embeddedObjectCount, long totalAssetSizeBytes)
        {
            var suggestions = new System.Collections.Generic.List<string>();

            // Check for high image count
            if (imageCount > 5)
                suggestions.Add("High image count - consider consolidating");

            // Check for large total asset size
            if (totalAssetSizeBytes > 5 * 1024 * 1024) // > 5 MB
                suggestions.Add("Large asset size - review necessity");
            else if (totalAssetSizeBytes > 1024 * 1024) // > 1 MB
                suggestions.Add("Consider image compression");

            // Check for media files
            if (mediaCount > 0)
                suggestions.Add("Review media files for optimization");

            // Check for multiple embedded objects
            if (embeddedObjectCount > 2)
                suggestions.Add("Multiple embedded objects - review complexity");

            return string.Join("; ", suggestions);
        }
    }
}