import { test, expect } from '../fixtures/combined.fixture';

test.describe('🔄 Comprehensive Webhook Coverage Tests', () => {

  // Setup hook to ensure test profile exists
  test.beforeEach(async ({ stripeCustomer, supabaseClient }) => {
    // Create test profile in Supabase if it doesn't exist
    const { data: existingProfile } = await supabaseClient
      .from('profiles')
      .select('id')
      .eq('stripe_customer_id', stripeCustomer.id)
      .maybeSingle();
    
    if (!existingProfile && stripeCustomer.email) {
      await supabaseClient
        .from('profiles')
        .upsert({
          id: crypto.randomUUID(),
          email: stripeCustomer.email,
          full_name: 'Test Customer',
          stripe_customer_id: stripeCustomer.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    }
  });

  test.describe('💳 Dispute Management Webhooks', () => {
    
    test('🚨 charge.dispute.created - Creates dispute record and sends alert', async ({
      stripeCustomer,
      supabaseClient,
      simulateWebhook
    }) => {
      const chargeId = `ch_test_${Date.now()}`;
      
      // Get test profile
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      expect(profile).toBeTruthy();
      
      // Insert a charge receipt for reference
      await supabaseClient
        .from('charge_receipts')
        .insert({
          user_id: profile!.id,
          stripe_charge_id: chargeId,
          amount: 2000,
          currency: 'usd',
          status: 'succeeded'
        });

      // Simulate dispute.created webhook
      const disputeData = {
        id: `dp_test_${Date.now()}`,
        charge: chargeId,
        amount: 2000,
        currency: 'usd',
        reason: 'fraudulent',
        status: 'warning_needs_response',
        created: Math.floor(Date.now() / 1000),
        evidence_details: {
          due_by: Math.floor((Date.now() + 7 * 24 * 60 * 60 * 1000) / 1000) // 7 days from now
        }
      };

      const webhookResult = await simulateWebhook('charge.dispute.created', disputeData);
      expect(webhookResult?.status).toBe(200);

      // Verify dispute was created in database
      const { data: dispute, error } = await supabaseClient
        .from('disputes')
        .select('*')
        .eq('stripe_dispute_id', disputeData.id)
        .single();

      expect(error).toBeNull();
      expect(dispute).toBeTruthy();
      expect(dispute!.stripe_charge_id).toBe(chargeId);
      expect(dispute!.amount).toBe(2000);
      expect(dispute!.reason).toBe('fraudulent');
      expect(dispute!.status).toBe('warning_needs_response');
    });

    test('💸 charge.refunded - Updates charge with refund information', async ({
      stripeCustomer,
      supabaseClient,
      simulateWebhook
    }) => {
      const chargeId = `ch_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      expect(profile).toBeTruthy();

      // Create initial charge receipt
      await supabaseClient
        .from('charge_receipts')
        .insert({
          user_id: profile!.id,
          stripe_charge_id: chargeId,
          amount: 2000,
          currency: 'usd',
          status: 'succeeded'
        });

      // Simulate charge refunded webhook
      const chargeData = {
        id: chargeId,
        amount: 2000,
        amount_refunded: 1000 // Partial refund
      };

      const webhookResult = await simulateWebhook('charge.refunded', chargeData);
      expect(webhookResult?.status).toBe(200);

      // Verify charge was updated with refund information
      const { data: charge } = await supabaseClient
        .from('charge_receipts')
        .select('refunded_amount, refund_status')
        .eq('stripe_charge_id', chargeId)
        .single();

      expect(charge).toBeTruthy();
      expect(charge!.refunded_amount).toBe(1000);
      expect(charge!.refund_status).toBe('partial');
    });

    test('🔄 refund.created - Creates refund record', async ({
      stripeCustomer,
      supabaseClient,
      simulateWebhook
    }) => {
      const chargeId = `ch_test_${Date.now()}`;
      const refundId = `re_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      expect(profile).toBeTruthy();

      // Create initial charge receipt
      await supabaseClient
        .from('charge_receipts')
        .insert({
          user_id: profile!.id,
          stripe_charge_id: chargeId,
          amount: 2000,
          currency: 'usd',
          status: 'succeeded'
        });

      // Simulate refund created webhook
      const refundData = {
        id: refundId,
        charge: chargeId,
        amount: 2000,
        currency: 'usd',
        status: 'succeeded',
        reason: 'requested_by_customer',
        created: Math.floor(Date.now() / 1000)
      };

      const webhookResult = await simulateWebhook('refund.created', refundData);
      expect(webhookResult?.status).toBe(200);

      // Verify refund record was created
      const { data: refund } = await supabaseClient
        .from('refunds')
        .select('*')
        .eq('stripe_refund_id', refundId)
        .single();

      expect(refund).toBeTruthy();
      expect(refund!.stripe_charge_id).toBe(chargeId);
      expect(refund!.amount).toBe(2000);
      expect(refund!.reason).toBe('requested_by_customer');
    });

    test('❌ payment_intent.payment_failed - Handles payment failure', async ({
      stripeCustomer,
      supabaseClient,
      simulateWebhook
    }) => {
      const paymentIntentId = `pi_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      expect(profile).toBeTruthy();

      // Create incomplete subscription
      const { data: subscription } = await supabaseClient
        .from('subscriptions')
        .insert({
          user_id: profile!.id,
          status: 'incomplete',
          quantity: 1
        })
        .select()
        .single();

      expect(subscription).toBeTruthy();

      // Simulate payment intent failed webhook
      const paymentIntentData = {
        id: paymentIntentId,
        customer: stripeCustomer.id,
        amount: 2000,
        currency: 'usd',
        status: 'requires_payment_method',
        last_payment_error: {
          message: 'Your card was declined.',
          code: 'card_declined'
        },
        created: Math.floor(Date.now() / 1000)
      };

      const webhookResult = await simulateWebhook('payment_intent.payment_failed', paymentIntentData);
      expect(webhookResult?.status).toBe(200);

      // Verify subscription status was updated
      const { data: updatedSubscription } = await supabaseClient
        .from('subscriptions')
        .select('status, last_payment_error')
        .eq('id', subscription!.id)
        .single();

      expect(updatedSubscription).toBeTruthy();
      expect(updatedSubscription!.status).toBe('payment_failed');
      expect(updatedSubscription!.last_payment_error).toBe('Your card was declined.');

      // Verify payment event was created
      const { data: paymentEvent } = await supabaseClient
        .from('payment_events')
        .select('*')
        .eq('stripe_payment_intent_id', paymentIntentId)
        .single();

      expect(paymentEvent).toBeTruthy();
      expect(paymentEvent!.event_type).toBe('payment_failed');
      expect(paymentEvent!.status).toBe('failed');
    });

    test('🔍 review.opened - Creates fraud review record', async ({
      supabaseClient,
      simulateWebhook
    }) => {
      const reviewId = `prv_test_${Date.now()}`;
      const chargeId = `ch_test_${Date.now()}`;

      // Simulate review opened webhook
      const reviewData = {
        id: reviewId,
        charge: chargeId,
        reason: 'rule',
        created: Math.floor(Date.now() / 1000)
      };

      const webhookResult = await simulateWebhook('review.opened', reviewData);
      expect(webhookResult?.status).toBe(200);

      // Verify fraud review was created
      const { data: review } = await supabaseClient
        .from('fraud_reviews')
        .select('*')
        .eq('stripe_review_id', reviewId)
        .single();

      expect(review).toBeTruthy();
      expect(review!.stripe_charge_id).toBe(chargeId);
      expect(review!.reason).toBe('rule');
      expect(review!.status).toBe('open');
    });

    test('❌ setup_intent.setup_failed - Records setup failure', async ({
      stripeCustomer,
      supabaseClient,
      simulateWebhook
    }) => {
      const setupIntentId = `seti_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      expect(profile).toBeTruthy();

      // Simulate setup intent failed webhook
      const setupIntentData = {
        id: setupIntentId,
        customer: stripeCustomer.id,
        status: 'requires_payment_method',
        last_setup_error: {
          message: 'Your card number is invalid.',
          code: 'invalid_number'
        }
      };

      const webhookResult = await simulateWebhook('setup_intent.setup_failed', setupIntentData);
      expect(webhookResult?.status).toBe(200);

      // Verify setup failure was recorded
      const { data: failure } = await supabaseClient
        .from('setup_failures')
        .select('*')
        .eq('stripe_setup_intent_id', setupIntentId)
        .single();

      expect(failure).toBeTruthy();
      expect(failure!.user_id).toBe(profile!.id);
      expect(failure!.error_message).toBe('Your card number is invalid.');
      expect(failure!.error_code).toBe('invalid_number');
      expect(failure!.failed_at).toBeTruthy();
    });

    test('🔄 Unknown webhook type - Graceful handling', async ({ simulateWebhook }) => {
      const webhookResult = await simulateWebhook('unknown.event.type', { id: 'test_123' });
      
      expect(webhookResult?.status).toBe(200);
      // Should not crash, just log and return success
    });

    test('💰 Complete Dispute Lifecycle - From Creation to Resolution', async ({
      stripeCustomer,
      supabaseClient,
      simulateWebhook
    }) => {
      const chargeId = `ch_test_${Date.now()}`;
      const disputeId = `dp_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      expect(profile).toBeTruthy();

      // Create initial charge
      await supabaseClient
        .from('charge_receipts')
        .insert({
          user_id: profile!.id,
          stripe_charge_id: chargeId,
          amount: 2000,
          currency: 'usd',
          status: 'succeeded'
        });

      // Step 1: Dispute created
      await simulateWebhook('charge.dispute.created', {
        id: disputeId,
        charge: chargeId,
        amount: 2000,
        currency: 'usd',
        reason: 'fraudulent',
        status: 'warning_needs_response',
        created: Math.floor(Date.now() / 1000),
        evidence_details: {
          due_by: Math.floor((Date.now() + 7 * 24 * 60 * 60 * 1000) / 1000)
        }
      });

      // Step 2: Funds withdrawn
      await simulateWebhook('charge.dispute.funds_withdrawn', {
        id: disputeId, 
        amount: 2000
      });

      // Step 3: Dispute updated to under review
      await simulateWebhook('charge.dispute.updated', {
        id: disputeId, 
        status: 'under_review'
      });

      // Step 4: Dispute won, funds reinstated
      await simulateWebhook('charge.dispute.funds_reinstated', {
        id: disputeId, 
        amount: 2000
      });

      // Step 5: Dispute closed
      await simulateWebhook('charge.dispute.closed', {
        id: disputeId, 
        status: 'won'
      });

      // Verify final state
      const { data: finalDispute } = await supabaseClient
        .from('disputes')
        .select('*')
        .eq('stripe_dispute_id', disputeId)
        .single();

      expect(finalDispute).toBeTruthy();
      expect(finalDispute!.status).toBe('won');
      expect(finalDispute!.funds_withdrawn).toBe(false);
      expect(finalDispute!.closed_at).toBeTruthy();
      expect(finalDispute!.reinstated_at).toBeTruthy();
      expect(finalDispute!.withdrawn_at).toBeTruthy();
    });
  });
});