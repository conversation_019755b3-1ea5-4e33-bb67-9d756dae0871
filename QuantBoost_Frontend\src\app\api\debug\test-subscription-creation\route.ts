import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

function toIsoDate(secOrMs: number | null | undefined): string | null {
  if (!secOrMs) return null;
  return new Date(secOrMs * 1000).toISOString();
}

function generateLicenseKey(): string {
  return crypto.randomUUID();
}

export async function POST(req: Request) {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );

  try {
    // Test data for subscription creation
    const testEmail = '<EMAIL>';
    
    // Find the user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', testEmail)
      .maybeSingle();

    if (profileError) {
      console.error('Profile lookup error:', profileError);
      return NextResponse.json({ error: 'Profile lookup failed', details: profileError }, { status: 500 });
    }

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found for email: ' + testEmail }, { status: 404 });
    }

    console.log('Found profile:', profile);

    // Test subscription data
    const testSubscriptionData = {
      user_id: profile.id,
      stripe_subscription_id: 'sub_test_debug_' + Date.now(),
      status: 'active',
      quantity: 1,
      current_period_start: toIsoDate(Math.floor(Date.now() / 1000)),
      current_period_end: toIsoDate(Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60)), // 1 year
      trial_start: null,
      trial_end: null,
      cancel_at_period_end: false,
    };

    console.log('Attempting to create subscription with data:', testSubscriptionData);

    // Try to create subscription
    const { data: subData, error: subError } = await supabase
      .from('subscriptions')
      .insert(testSubscriptionData)
      .select('id')
      .single();

    if (subError) {
      console.error('Subscription creation error:', subError);
      return NextResponse.json({ 
        error: 'Subscription creation failed', 
        details: subError,
        testData: testSubscriptionData 
      }, { status: 500 });
    }

    console.log('Successfully created subscription:', subData);

    // Test license creation
    const testLicenseData = {
      user_id: profile.id,
      subscription_id: subData.id,
      license_key: generateLicenseKey(),
      product_id: 'prod_test_debug',
      status: 'active',
      email: null,
    };

    console.log('Attempting to create license with data:', testLicenseData);

    const { data: licenseData, error: licenseError } = await supabase
      .from('licenses')
      .insert(testLicenseData)
      .select('id')
      .single();

    if (licenseError) {
      console.error('License creation error:', licenseError);
      return NextResponse.json({ 
        error: 'License creation failed', 
        details: licenseError,
        testData: testLicenseData,
        subscriptionCreated: subData
      }, { status: 500 });
    }

    console.log('Successfully created license:', licenseData);

    return NextResponse.json({ 
      success: true, 
      subscription: subData, 
      license: licenseData,
      message: 'Test subscription and license created successfully'
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Unexpected error', details: error }, { status: 500 });
  }
}
