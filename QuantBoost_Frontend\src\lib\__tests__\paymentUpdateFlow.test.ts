import { describe, it, expect, vi } from 'vitest';
import { attemptAttachPaymentMethod } from '../paymentUpdateFlow';

describe('attemptAttachPaymentMethod', () => {
  it('returns attached on success', async () => {
    const caller = vi.fn(async () => ({ ok: true, body: {} }));
    const telemetry = vi.fn();
    const res = await attemptAttachPaymentMethod('sub_1', 'pm_1', caller as any, telemetry as any);
    expect(res.attached).toBe(true);
    expect(res.partial).toBe(false);
    expect(telemetry).not.toHaveBeenCalled();
  });

  it('returns partial and logs telemetry on failure', async () => {
    const caller = vi.fn(async () => ({ ok: false, body: { error: 'fail' } }));
    const telemetry = vi.fn();
    const res = await attemptAttachPaymentMethod('sub_1', 'pm_1', caller as any, telemetry as any);
    expect(res.attached).toBe(false);
    expect(res.partial).toBe(true);
    expect(res.error).toBeDefined();
    expect(telemetry).toHaveBeenCalledTimes(1);
  });

  it('catches thrown exception as partial', async () => {
    const caller = vi.fn(async () => { throw new Error('boom'); });
    const telemetry = vi.fn();
    const res = await attemptAttachPaymentMethod('sub_1', 'pm_1', caller as any, telemetry as any);
    expect(res.partial).toBe(true);
    expect(telemetry).toHaveBeenCalled();
  });
});
