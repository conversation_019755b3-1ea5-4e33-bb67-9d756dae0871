import { test as base } from '@playwright/test';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

export interface SupabaseFixture {
  supabase: SupabaseClient | null;
  findLicensesByEmail: (email: string) => Promise<any[]>;
  findSubscriptionsByUser: (userId: string) => Promise<any[]>;
}

export const test = base.extend<SupabaseFixture>({
  supabase: async ({}, use) => {
    const url = process.env.SUPABASE_URL;
    const key = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    if (!url || !key) {
      console.warn('[supabase.fixture] Missing SUPABASE_URL or key - db assertions skipped.');
      await use(null);
      return;
    }
    const client = createClient(url, key, { auth: { persistSession: false } });
    await use(client);
  },
  findLicensesByEmail: async ({ supabase }, use) => {
    const fn = async (email: string) => {
      if (!supabase) return [];
      const { data } = await supabase.from('licenses').select('*').ilike('email', email);
      return data || [];
    };
    await use(fn);
  },
  findSubscriptionsByUser: async ({ supabase }, use) => {
    const fn = async (userId: string) => {
      if (!supabase) return [];
      const { data } = await supabase.from('subscriptions').select('*').eq('user_id', userId);
      return data || [];
    };
    await use(fn);
  }
});

export const expect = test.expect;
