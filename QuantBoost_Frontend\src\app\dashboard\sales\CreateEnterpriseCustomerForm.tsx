"use client";

import { useState } from 'react';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface CreateEnterpriseCustomerFormProps {
  onSuccess: () => void;
}

export default function CreateEnterpriseCustomerForm({ onSuccess }: CreateEnterpriseCustomerFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabaseClient();

  const [formData, setFormData] = useState({
    company_name: '',
    contact_email: '',
    contact_name: '',
    contact_phone: '',
    license_quantity: 20,
    license_tier: 'Basic-Enterprise',
    annual_contract_value: 0,
    contract_start_date: '',
    contract_end_date: '',
    payment_terms: 30,
    billing_address: {
      line1: '',
      line2: '',
      city: '',
      state: '',
      postal_code: '',
      country: 'US',
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError(null);

      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/sales/enterprise-customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create customer');
      }

      onSuccess();
    } catch (error) {
      console.error('Error creating customer:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const updateBillingAddress = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      billing_address: {
        ...prev.billing_address,
        [field]: value,
      },
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md">
          {error}
        </div>
      )}

      {/* Company Information */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">Company Information</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="company_name">Company Name *</Label>
            <Input
              id="company_name"
              value={formData.company_name}
              onChange={(e) => updateFormData('company_name', e.target.value)}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="contact_name">Contact Name</Label>
            <Input
              id="contact_name"
              value={formData.contact_name}
              onChange={(e) => updateFormData('contact_name', e.target.value)}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="contact_email">Contact Email *</Label>
            <Input
              id="contact_email"
              type="email"
              value={formData.contact_email}
              onChange={(e) => updateFormData('contact_email', e.target.value)}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="contact_phone">Contact Phone</Label>
            <Input
              id="contact_phone"
              value={formData.contact_phone}
              onChange={(e) => updateFormData('contact_phone', e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* License Information */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">License Information</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="license_quantity">License Quantity *</Label>
            <Input
              id="license_quantity"
              type="number"
              min="20"
              value={formData.license_quantity}
              onChange={(e) => updateFormData('license_quantity', parseInt(e.target.value))}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="license_tier">License Tier</Label>
            <Select 
              value={formData.license_tier} 
              onValueChange={(value) => updateFormData('license_tier', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Basic-Enterprise">Basic Enterprise</SelectItem>
                <SelectItem value="Premium-Enterprise">Premium Enterprise</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="annual_contract_value">Annual Contract Value</Label>
            <Input
              id="annual_contract_value"
              type="number"
              step="0.01"
              value={formData.annual_contract_value}
              onChange={(e) => updateFormData('annual_contract_value', parseFloat(e.target.value))}
            />
          </div>
        </div>
      </div>

      {/* Contract Information */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">Contract Information</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="contract_start_date">Contract Start Date</Label>
            <Input
              id="contract_start_date"
              type="date"
              value={formData.contract_start_date}
              onChange={(e) => updateFormData('contract_start_date', e.target.value)}
            />
          </div>
          
          <div>
            <Label htmlFor="contract_end_date">Contract End Date</Label>
            <Input
              id="contract_end_date"
              type="date"
              value={formData.contract_end_date}
              onChange={(e) => updateFormData('contract_end_date', e.target.value)}
            />
          </div>
          
          <div>
            <Label htmlFor="payment_terms">Payment Terms (Days)</Label>
            <Input
              id="payment_terms"
              type="number"
              value={formData.payment_terms}
              onChange={(e) => updateFormData('payment_terms', parseInt(e.target.value))}
            />
          </div>
        </div>
      </div>

      {/* Billing Address */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">Billing Address</h4>
        
        <div className="grid grid-cols-1 gap-4">
          <div>
            <Label htmlFor="address_line1">Address Line 1</Label>
            <Input
              id="address_line1"
              value={formData.billing_address.line1}
              onChange={(e) => updateBillingAddress('line1', e.target.value)}
            />
          </div>
          
          <div>
            <Label htmlFor="address_line2">Address Line 2</Label>
            <Input
              id="address_line2"
              value={formData.billing_address.line2}
              onChange={(e) => updateBillingAddress('line2', e.target.value)}
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                value={formData.billing_address.city}
                onChange={(e) => updateBillingAddress('city', e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="state">State</Label>
              <Input
                id="state"
                value={formData.billing_address.state}
                onChange={(e) => updateBillingAddress('state', e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="postal_code">Postal Code</Label>
              <Input
                id="postal_code"
                value={formData.billing_address.postal_code}
                onChange={(e) => updateBillingAddress('postal_code', e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="country">Country</Label>
              <Input
                id="country"
                value={formData.billing_address.country}
                onChange={(e) => updateBillingAddress('country', e.target.value)}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="submit" disabled={loading}>
          {loading ? 'Creating...' : 'Create Customer'}
        </Button>
      </div>
    </form>
  );
}
