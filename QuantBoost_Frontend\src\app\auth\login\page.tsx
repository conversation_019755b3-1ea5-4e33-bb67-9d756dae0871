"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';

const schema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type FormData = z.infer<typeof schema>;

export default function LoginPage() {
  const [message, setMessage] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const supabase = useSupabaseClient();
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<FormData>({
    resolver: zodResolver(schema),
  });

  const onSubmit = async (data: FormData) => {
    setMessage(null);
    setIsLoading(true);

    try {
      // Use Supabase directly for magic link authentication
      const { error } = await supabase.auth.signInWithOtp({
        email: data.email,
        options: {
          shouldCreateUser: true,
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) {
        console.error('Magic link error:', error);
        setMessage(error.message || 'Failed to send magic link. Please try again.');
      } else {
        setIsSuccess(true);
        setMessage('Success! Check your email for a magic link to sign in.');
      }
    } catch (error) {
      console.error('Unexpected magic link error:', error);
      setMessage('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      {/* Main Form Panel */}
      <div className="w-full max-w-md bg-white rounded-2xl shadow-lg p-8 space-y-6">
        {/* Logo */}
        <div className="flex justify-center">
          <Image
            src="/QuantBoost_LeftLogo_v0.png"
            alt="QuantBoost Logo"
            width={200}
            height={60}
            className="h-12 w-auto"
          />
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Email Input */}
          <div className="space-y-2">
            <Input
              type="email"
              placeholder="Email address*"
              {...register('email')}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              disabled={isSuccess}
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email.message}</p>
            )}
          </div>

          {/* Success Message (Conditional) */}
          {isSuccess && (
            <div className="flex items-center space-x-3 px-4 py-3 border border-green-300 rounded-lg bg-green-50">
              <div className="flex-shrink-0">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <span className="text-green-800 font-medium">Success!</span>
            </div>
          )}

          {/* Error Message */}
          {message && !isSuccess && (
            <div className="px-4 py-3 border border-red-300 rounded-lg bg-red-50">
              <p className="text-red-800 text-sm">{message}</p>
            </div>
          )}

          {/* Continue Button */}
          <Button
            type="submit"
            disabled={isSubmitting || isLoading || isSuccess}
            className="w-full py-3 bg-gray-800 hover:bg-gray-900 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Sending...' : isSuccess ? 'Email Sent' : 'Continue'}
          </Button>
        </form>

        {/* Additional Message for Success State */}
        {isSuccess && (
          <div className="text-center space-y-2">
            <p className="text-gray-600 text-sm">
              We've sent a magic link to your email address.
            </p>
            <p className="text-gray-600 text-sm">
              Click the link in your email to sign in to your dashboard.
            </p>
          </div>
        )}
      </div>

      {/* Legal Footer */}
      <div className="fixed bottom-4 left-0 right-0 text-center">
        <p className="text-sm text-gray-500">
          <Link href="/terms" className="text-green-600 hover:underline">
            Enterprise Terms of Service
          </Link>
          {' and '}
          <Link href="/privacy" className="text-green-600 hover:underline">
            Privacy Policy
          </Link>
        </p>
      </div>
    </div>
  );
}
