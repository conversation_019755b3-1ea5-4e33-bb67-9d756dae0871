# Spec-Driven Development Document

**Project:** QuantBoost
**Feature:** Stripe API upgrade to 2025-08-27.basil
**Date:** 2025-09-04
**Status:** Draft

---

## 1. SPECIFICATION (/specify phase)

### 1.1 Overview
**What are we building and why?**
Upgrade our Stripe integration from API version 2025-03-31.basil to 2025-08-27.basil. The goal is to stay current with Stripe’s monthly Basil releases (which are backward-compatible), ensure continued correctness and performance of our billing/checkout flows, and position ourselves to adopt new additive features introduced between March and August 2025.

Key scope:
- Update all Stripe client initializations and tests pinned to 2025-03-31.basil to 2025-08-27.basil.
- Validate critical flows (Checkout Sessions, PaymentIntents, Subscriptions, Invoices, Portal, and Webhooks) still behave as expected under the newer monthly release.
- Ensure webhook endpoint versions in Stripe Dashboard match the new version where we pin endpoint-level versions.
- Document optional, additive features we may adopt later (e.g., excluded_payment_method_types on PaymentIntents, invoice rendering templates, flexible billing mode enhancements).

References:
- Stripe versioning: Monthly releases are backward-compatible within the same major release (Basil). The current version is 2025-08-27.basil.
- <PERSON> changelog (2025-04 → 2025-08): Additive changes to Billing, Checkout, Payment Methods, Terminal, Connect.

### 1.2 User Stories
**Who will use this and how?**

#### Primary User Personas
- **Developer / DevOps:**
  - **Needs:** Keep dependencies and API versions current; ensure stable deployments with minimal code churn.
  - **Context:** Local, CI, staging, and production environments.
  - **Success Criteria:** All builds/tests pass; no production incidents; Stripe metrics unchanged.

- **QA Engineer:**
  - **Needs:** Confirm core payment/subscription flows still work.
  - **Context:** Test mode with Stripe CLI/webhooks and Playwright/Vitest.
  - **Success Criteria:** Test matrix passes; webhook processing consistent; no regressions.

- **Support/Finance Ops (Indirect):**
  - **Needs:** No disruption to billing, invoices, renewals.
  - **Context:** Production monitoring of payments and subscriptions.
  - **Success Criteria:** No increase in payment failures; invoices generated and reconciled normally.

#### User Journeys
**Primary Flow:**
1. Frontend creates a Checkout Session or PaymentIntent.
2. Customer completes payment; Stripe emits webhooks.
3. Webhook handler updates Supabase (profiles, subscriptions, licenses, invoices).
4. Customer manages subscription via Customer Portal (where applicable).

**Alternative Flows:**
- **Flow A:** Subscription changes mid-cycle (proration); validate invoice creation and payment.
- **Flow B:** Payment fails; verify webhook handling and retry paths; ensure no duplicate processing (idempotency).

### 1.3 Functional Requirements
**What must the system do?**

#### Core Functionality
- [ ] **FR-001:** Update all Stripe client initializations to `apiVersion: '2025-08-27.basil'`.
  - Acceptance: No lingering references to `2025-03-31.basil` in code or tests.

- [ ] **FR-002:** Update tests/fixtures and any mocked event payloads that assert `api_version` to `2025-08-27.basil`.
  - Acceptance: All Playwright/Vitest tests compile and pass locally and in CI.

- [ ] **FR-003:** Verify webhook signature verification still works and endpoint-level versions (Dashboard) are aligned to 2025-08-27.basil if pinned at the endpoint.
  - Acceptance: Stripe CLI-triggered events are received and processed without signature or payload mismatch errors.

- [ ] **FR-004:** Validate core flows: Checkout, Subscription lifecycle (create, update, cancel, renew), Invoice creation/collection, PaymentIntents confirm/capture, Customer Portal sessions.
  - Acceptance: Test plan executed with all green; no behavior regressions.

- [ ] **FR-005:** Confirm no reliance on previously removed features (already removed in 2025-03-31.basil): total_count expansion; legacy upcoming invoice APIs; subscription-level current_period_* fields.
  - Acceptance: Code scan confirms compliant endpoints/fields; behavior observed correct under test.

- [ ] **FR-006:** Optional toggles documented (not implemented in this change):
  - PaymentIntents `excluded_payment_method_types` support.
  - Customer Portal configuration `name` field.
  - Add-invoice-items metadata/period; third-party tax with flexible billing mode.

#### Business Rules
- **BR-001:** Maintain idempotency on webhook processing and client requests (existing idempotency patterns must remain).
- **BR-002:** Preserve current product/price IDs and subscription semantics; do not change pricing logic in this upgrade.

#### Data Requirements
- **DR-001:** No schema changes required. Existing tables (e.g., webhook_events, receipts) continue storing event payloads; stored `api_version` values will update to 2025-08-27.basil.
- **DR-002:** Ensure any analytics relying on `api_version` string can handle the new value.

### 1.4 Non-Functional Requirements
**How should the system behave?**

#### Performance
- **Response Time:** No regression beyond ±5% on API routes that interact with Stripe.
- **Throughput:** Maintain current throughput under typical staging load.
- **Scalability:** Unchanged.

#### Security
- **Authentication:** Continue using secret keys via env vars; no change.
- **Authorization:** Unchanged; RBAC remains in-app.
- **Data Protection:** Secrets remain in env; no PII schema changes.

#### Usability
- **Accessibility:** N/A (no UI changes).
- **Browser Support:** Unchanged.
- **User Experience:** Payment and billing flows remain identical for end users.

### 1.5 Success Metrics
- **Metric 1:** 100% pass rate in unit, integration, and E2E tests in CI.
- **Metric 2:** 0 production incidents attributable to Stripe upgrade in 7 days post-release.
- **Metric 3:** No increase in payment failure rate or invoice collection errors compared to prior 14-day baseline.

### 1.6 Assumptions & Dependencies
#### Assumptions
- Already on Basil 2025-03-31.basil and compatible with its breaking changes.
- Stripe Node SDK is compatible with the new monthly version; updating the apiVersion is sufficient for this upgrade.

#### Dependencies
- Stripe API and Dashboard webhook endpoint configuration.
- Supabase database and existing logging/monitoring.

### 1.7 Review & Acceptance Checklist
- [ ] Requirements are clear and unambiguous
- [ ] User stories cover all primary use cases
- [ ] Success criteria are measurable
- [ ] Edge cases and error scenarios are considered
- [ ] Non-functional requirements are specified
- [ ] Dependencies and risks are identified
- [ ] Stakeholders have reviewed and approved

---

## 2. TECHNICAL PLAN (/plan phase)

### 2.1 Technology Stack

#### Frontend/Backend
- **Framework:** Next.js (App Router) for API routes; Azure SWA function for alternate webhook
- **Language:** TypeScript/JavaScript
- **Auth/DB:** Supabase
- **Payments:** Stripe Node SDK

### 2.2 Architecture Overview

#### System Architecture
```
[Client] → [Next.js API routes] → [Stripe API]
                           ↘︎  [Supabase]
[Stripe Webhooks] → [Next.js webhook] & [Azure SWA webhook] → [Supabase]
```

#### Data Flow
1. API route initializes Stripe with pinned apiVersion → calls Stripe APIs.
2. Stripe emits events → webhooks verify signature → persist and act on events in Supabase.
3. Portal/Invoices/Subscriptions continue as before.

#### Component Breakdown
- **Stripe clients:** All new Stripe(secret, { apiVersion }) instances.
- **Webhook handlers:** Next.js route and Azure Function.
- **Billing/Checkout routes:** PaymentIntents, SetupIntents, Checkout Sessions, Portal sessions, subscription ops.

### 2.3 Data Model
No schema changes. Ensure any code reading `event.api_version` continues to work.

### 2.4 API Design
No new endpoints. Existing endpoints maintain behavior.

### 2.5 Security Implementation
- Webhook signature verification remains via `stripe.webhooks.constructEvent`.
- Secrets via environment variables; no rotation required for this change.

### 2.6 Performance Considerations
- No new caching. Monitor latency of Stripe-dependent routes during staging test.

### 2.7 Monitoring & Observability
- Continue logging webhook events and route-level errors.
- Watch for signature verification errors and deserialization issues post-upgrade.

### 2.8 Deployment Strategy
- Use a feature branch; deploy to staging.
- Verify tests and perform smoke tests with Stripe CLI triggers.
- Production release during low-traffic window.
- Rollback plan: revert branch to previous version if any critical regressions observed.

### 2.9 Technical Risks & Mitigation
- **Risk 1:** Payload differences in webhooks if endpoint-level version misaligned. → **Mitigation:** Ensure Dashboard webhook endpoints are set to 2025-08-27.basil (or Latest) to match app expectations.
- **Risk 2:** Hidden reliance on previously removed features (e.g., total_count expansion). → **Mitigation:** Code scan and targeted tests; no such usage identified previously; re-verify.

---

## 3. IMPLEMENTATION TASKS (/tasks phase)

### 3.1 Development Setup
- [ ] **SETUP-001:** Create a feature branch `feat/stripe-2025-08-27`.
- [ ] **SETUP-002:** Optionally bump `stripe` package to latest compatible minor/patch.
- [ ] **SETUP-003:** Confirm env variables exist in staging/prod (no changes expected).

### 3.2 Core Infrastructure
- [ ] **INFRA-001:** Review Stripe Dashboard webhook endpoint config; align endpoint-level version to 2025-08-27.basil if pinned.

### 3.3 Backend Implementation
- [ ] **BACK-001:** Update all `new Stripe(..., { apiVersion: '2025-03-31.basil' })` to `'2025-08-27.basil'`.
- [ ] **BACK-002:** Remove any assumptions about list `total_count` expansion (should already be compliant since 2025-03-31.basil).
- [ ] **BACK-003:** Validate any use of deprecated Upcoming Invoice API; ensure Create Preview Invoice API is used if needed.

### 3.4 Frontend Implementation
- [ ] **FRONT-001:** None (UI unchanged). Ensure no frontend code is tightly coupled to API version string.

### 3.5 Integration & Data Flow
- [ ] **INTEG-001:** Validate Checkout Session flows (subscription and one-time) under test.
- [ ] **INTEG-002:** Validate PaymentIntent create/confirm flows; ensure client secret handling unchanged.
- [ ] **INTEG-003:** Validate Customer Portal session creation.

### 3.6 Testing Implementation
- [ ] **TEST-001:** Update tests/fixtures expecting `api_version: '2025-03-31.basil'` to `'2025-08-27.basil'`.
- [ ] **TEST-002:** Run unit/integration tests; fix any brittle assertions.
- [ ] **TEST-003:** E2E (Playwright) runs for: checkout success/failure, subscription create/update/cancel, invoice renewal, webhook idempotency.
- [ ] **TEST-004:** Webhook smoke tests using Stripe CLI.

### 3.7 Security & Performance
- [ ] **SEC-001:** Re-run webhook signature negative/positive tests.
- [ ] **PERF-001:** Spot-check latency on Stripe-dependent routes in staging.

### 3.8 Documentation & Deployment
- [ ] **DOC-001:** Update internal docs to reflect new apiVersion.
- [ ] **DEPLOY-001:** Deploy to staging; then to production after sign-off.
- [ ] **DEPLOY-002:** Monitor logs/metrics post-release (48–72h).

### 3.9 Task Dependencies
- SETUP → BACK/INTEG → TEST → DEPLOY.
- Dashboard webhook endpoint version check should occur before staging tests.

### 3.10 Definition of Done
- [ ] Code updated and linted
- [ ] Tests (unit/integration/E2E) passing
- [ ] Staging smoke tests green
- [ ] Docs updated
- [ ] No security/perf regressions
- [ ] Production release completed with monitoring in place

---

## 4. IMPLEMENTATION GUIDANCE

### 4.1 Development Workflow
- Make version changes in a single PR.
- Run full test suite locally and in CI.
- Use Stripe CLI to trigger representative webhooks in test mode.

### 4.2 Quality Gates
- **Code Review:** Required
- **Automated Testing:** Must pass
- **Security Scan:** Run as usual
- **Performance Check:** Sanity on route latency

### 4.3 Risk Mitigation During Implementation
- Keep change set small and focused (apiVersion + tests).
- Stage thoroughly before prod.

---

## 5. SIGN-OFF & APPROVAL

### 5.1 Stakeholder Approval
- [ ] Product Owner: Approved
- [ ] Tech Lead: Approved
- [ ] Security: Reviewed
- [ ] DevOps: Deployment plan approved

### 5.2 Implementation Team Understanding
- [ ] Backend Dev: API and webhook impacts understood
- [ ] Frontend Dev: No UI impact
- [ ] QA Engineer: Test plan ready
- [ ] DevOps Engineer: Rollout/rollback understood

---

**Document Version:** 1.0  
**Last Updated:** 2025-09-04  
**Next Review:** 2025-09-11

---

## Appendix A: Impacted files (apiVersion pins and test assertions)

Update apiVersion from 2025-03-31.basil → 2025-08-27.basil in the following files and any duplicates:

- QuantBoost_Frontend/src/app/api/checkout/create-payment-intent/route.ts
- QuantBoost_Frontend/src/app/api/checkout/create-session/route.ts
- QuantBoost_Frontend/src/app/api/checkout/create-setup-intent/route.ts
- QuantBoost_Frontend/src/app/api/checkout/update-payment-intent/route.ts
- QuantBoost_Frontend/src/app/api/billing/cancel-subscription/route.ts
- QuantBoost_Frontend/src/app/api/billing/create-portal-session/route.ts
- QuantBoost_Frontend/src/app/api/billing/create-setup-intent/route.ts
- QuantBoost_Frontend/src/app/api/billing/get-payment-method/route.ts
- QuantBoost_Frontend/src/app/api/billing/invoices/route.ts
- QuantBoost_Frontend/src/app/api/billing/subscription-details/route.ts
- QuantBoost_Frontend/src/app/api/billing/undo-cancellation/route.ts
- QuantBoost_Frontend/src/app/api/billing/upcoming-invoice/route.ts
- QuantBoost_Frontend/src/app/api/billing/update-payment-method/route.ts
- QuantBoost_Frontend/src/app/api/debug/fix-subscription-data/route.ts
- QuantBoost_Frontend/src/app/api/debug/reprocess-webhooks/route.ts
- QuantBoost_Frontend/src/app/api/sales/enterprise-customers/route.ts (two inits)
- QuantBoost_Frontend/src/app/api/sales/enterprise-invoices/route.ts
- QuantBoost_Frontend/src/app/api/test/simulate-stripe-webhook/route.ts (api_version in payloads)
- QuantBoost_Frontend/src/app/api/webhooks/stripe/route.ts
- QuantBoost_Frontend/src/__tests__/webhooks.stripe.trial-conversion.test.ts (api_version in payloads)
- QuantBoost_Frontend/src/lib/stripe/checkout.ts
- QuantBoost_Frontend/api/webhooks/stripe/index.js (Azure SWA webhook)
- QuantBoost_Testing/playwright/fixtures/combined.fixture.ts (init + api_version)
- QuantBoost_Testing/playwright/fixtures/stripe.fixture.ts (init + api_version)
- QuantBoost_Testing/playwright/fixtures/webhook-testing.fixture.ts (api_version)
- QuantBoost_Testing/playwright/tests/api/purchase.full-flow.spec.ts (api_version)
- QuantBoost_Testing/playwright/tests/api/webhook.direct.spec.ts (api_version)
- QuantBoost_Testing/playwright/tests/security/webhook.signature-negative.spec.ts (api_version)
- memory-bank/Done/QuantBoost_Testing_PRP.md (doc reference)
- memory-bank/memory-bank_frontend/WEBHOOK_TESTING_STRATEGY.md (doc reference)
- memory-bank/deployment-guides/azure-functions-hybrid.md (doc reference)
- .github/copilot-instructions.md (doc reference)

Note: Also search for additional occurrences if new files were added since this inventory.

---

## Appendix B: What changed between 2025-03-31.basil and 2025-08-27.basil (high-signal items)

- Monthly Basil releases are backward-compatible. No breaking changes are expected when updating within Basil from 03-31 to 08-27.
- Additive enhancements that could be relevant (optional adoption later):
  - PaymentIntents: `excluded_payment_method_types` parameter support.
  - Checkout/Payment Links: Invoice Rendering Templates; NZ BECS Direct Debit support; app-to-web enhancements.
  - Billing: Flexible billing mode (introduced June as opt-in); thresholds and mixed intervals; portal configuration `name` field; add_invoice_items metadata/period; third-party tax support (when in flexible billing mode).
  - Events: New ping event type (Apr); context field added to event payloads (Apr).

Verification focus areas:
- Checkout Sessions for subscriptions already changed in 2025-03-31 (deferred subscription creation). Validate our flows remain correct.
- Confirm no usage of removed `total_count` expansion or legacy Upcoming Invoice endpoints (deprecated in 03-31).
- Webhook endpoint version alignment in Dashboard.
