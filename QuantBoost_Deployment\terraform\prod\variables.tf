# ==============================================================================
# Global Settings
# ==============================================================================

variable "environment" {
  description = "The deployment environment name (e.g., dev, staging, prod)."
  type        = string
  default     = "prod"
}

variable "api_location" {
  description = "The primary Azure region for the API infrastructure."
  type        = string
  default     = "West US 3"
}

variable "distribution_location" {
  description = "The primary Azure region for distribution assets (Storage, CDN)."
  type        = string
  default     = "East US"
}

# ==============================================================================
# API & Secrets
# ==============================================================================

variable "supabase_url" {
  description = "The Supabase project URL."
  type        = string
  sensitive   = true
  default     = null
}

variable "supabase_anon_key" {
  description = "The Supabase anonymous key."
  type        = string
  sensitive   = true
  default     = null
}

variable "supabase_service_role_key" {
  description = "The Supabase service role key."
  type        = string
  sensitive   = true
  default     = null
}

variable "stripe_secret_key" {
  description = "The Stripe secret API key."
  type        = string
  sensitive   = true
  default     = null
}

variable "stripe_webhook_signing_secret" {
  description = "The signing secret for the Stripe webhook endpoint."
  type        = string
  sensitive   = true
  default     = null
}

variable "jwt_secret" {
  description = "The JWT secret for signing authentication tokens."
  type        = string
  sensitive   = true
  default     = null
}

# ==============================================================================
# Distribution & Signing
# ==============================================================================

variable "custom_domain_host_name" {
  description = "The custom domain for the installer download link (e.g., download.quantboost.ai)."
  type        = string
  default     = "download.quantboost.ai"
}

# Control whether to deploy API-related resources (set to false to reuse existing API infra)
variable "deploy_api" {
  description = "If true, deploy API RG, KV, ACR, LAW, App Insights, Container Apps. If false, skip."
  type        = bool
  default     = false
}
