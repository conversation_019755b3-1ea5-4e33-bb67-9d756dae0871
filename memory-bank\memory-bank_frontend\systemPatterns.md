---
title: System Patterns for QuantBoost_Frontend
purpose: To describe the high-level architecture, components, data flow, and interactions of the QuantBoost_Frontend project.
projects: ["QuantBoost_Frontend"]
source_analysis: Codebase analysis of QuantBoost_Frontend
status: bootstrapped-incomplete
last_updated: 2025-05-13T00:00:00.000Z
tags: ["frontend", "architecture", "nextjs", "supabase"]
---

## QuantBoost_Frontend System Patterns

### High-Level Architecture

*   **Client-Server Architecture (Jamstack-like with Next.js):**
    *   The frontend is built with Next.js, which can serve static pages, server-rendered pages, and API routes.
    *   It heavily relies on client-side rendering and interactions, especially for user authentication and dynamic content.
*   **Backend as a Service (BaaS):** Supabase is used for authentication and likely database storage, abstracting many backend complexities.
*   **Third-Party Integration:** Stripe is integrated for payment processing.

### Main Components/Modules & Responsibilities

*   **`src/app/` (Next.js App Router):**
    *   Defines the routes and views of the application.
    *   Each sub-folder typically represents a URL path (e.g., `/pricing`, `/features/excel-trace`).
    *   `page.tsx` files within these folders are the main entry points for rendering the UI for that route.
    *   `layout.tsx` defines the root layout for the application, including global styles and providers.
*   **`src/components/`:**
    *   **`ui/`:** Contains UI components, likely from Shadcn/ui (e.g., `Button`, `Card`, `Input`). These are reusable building blocks for the interface.
    *   **`SupabaseProvider.tsx`:** A client-side component that wraps the application to provide Supabase session and user context to its children. It listens to authentication state changes.
    *   **`LogoutButton.tsx`:** A client-side component that handles user logout via Supabase.
*   **`src/lib/`:**
    *   **`utils.ts`:** Contains utility functions, such as `cn` for managing CSS classes.
*   **Root Files:**
    *   **`SupabaseClient.ts`:** Initializes the Supabase client for client-side usage (using public anon key).
    *   **`SupabaseServerClient.ts`:** Initializes a Supabase client for server-side usage (using service role key, for admin-level operations). This would be used in Next.js API routes or server components that need privileged access.
    *   **`middleware.ts`:** (File exists but content not reviewed) Likely handles request middleware, potentially for authentication, redirects, or setting headers.
    *   **`next.config.ts` / `next.config.mjs`:** Next.js configuration file.
    *   **`tailwind.config.js`:** Tailwind CSS configuration.
    *   **`tsconfig.json`:** TypeScript configuration.

### Key Data Flow Patterns

1.  **User Authentication:**
    *   User interacts with login/signup forms (not explicitly seen, but implied by `LogoutButton` and `SupabaseProvider`).
    *   `SupabaseClient` handles communication with Supabase Auth.
    *   `SupabaseProvider` listens to auth state changes and updates the session/user context.
    *   Authenticated state likely gates access to certain routes or features (potentially handled by `middleware.ts` or within page components).
2.  **Page Rendering (Next.js App Router):**
    *   User navigates to a URL.
    *   Next.js matches the route to the corresponding `app/.../page.tsx`.
    *   `layout.tsx` provides the overall page structure.
    *   Page components fetch data if needed (client-side or server-side) and render UI elements.
    *   Many pages are static marketing/informational content (e.g., feature pages, homepage).
3.  **Trial Signup (`start-trial/page.tsx`):**
    *   User enters email.
    *   Client-side form submission.
    *   A `TODO` indicates a plan to call an API endpoint (e.g., `/api/auth/start-trial`). This endpoint would:
        *   Validate email.
        *   Interact with Supabase to create a user/trial.
        *   Potentially interact with Stripe to set up a trial subscription.
4.  **Pricing & Checkout (`pricing/page.tsx`):**
    *   User selects a product plan.
    *   Client-side `handleBuyNow` function is triggered.
    *   It fetches the current user's ID from Supabase (client-side).
    *   It makes a POST request to a backend API endpoint (`/api/checkout/create-session`) with the `priceId` and `userId`.
    *   The backend API route (not reviewed, but presumed to exist) would:
        *   Use `SupabaseServerClient` or similar to validate the user and potentially log subscription details.
        *   Use the Stripe SDK (server-side) to create a Stripe Checkout session.
        *   Return the Stripe Checkout session URL to the client.
    *   The client then redirects the user to the Stripe Checkout page.
5.  **Static Content & Feature Pages:**
    *   Pages like `/features/*`, `/help`, `/training` primarily display static content, including embedded videos and iframes.

### Interactions with Other Potential Projects/Services

*   **QuantBoost_API (Presumed):**
    *   The frontend makes a call to `/api/checkout/create-session`. This API route is part of the Next.js frontend project itself (acting as a "Backend For Frontend" - BFF).
    *   If there's a separate `QuantBoost_API` project (as suggested by the overall project structure), this BFF API route might then call the main `QuantBoost_API` for more complex business logic or data operations not directly handled by Supabase. However, based on the reviewed files, the primary backend interaction seems to be with Supabase and Stripe (via the BFF).
*   **Supabase (External BaaS):**
    *   Handles authentication.
    *   Likely handles database storage for users, product information, subscriptions, etc. (though specific table interactions are not detailed in the reviewed frontend code).
*   **Stripe (External Payment Gateway):**
    *   Handles payment processing and subscription management.
*   **External Content:**
    *   YouTube for video demos.
    *   Macabacus.com for embedded interactive demos.

### Recurring Design Patterns

*   **Provider Pattern:** `SupabaseProvider` uses React Context to make session/user data available throughout the component tree.
*   **Utility CSS Classes:** Tailwind CSS is used extensively, promoting a utility-first approach to styling.
*   **Component-Based UI:** The UI is built from reusable React components (Shadcn/ui and custom components).
*   **Client-Side Data Fetching & State Management:** `useState` and `useEffect` are used for managing local component state and triggering side effects like API calls (e.g., in `pricing/page.tsx`, `start-trial/page.tsx`).
*   **API Routes as BFF:** Next.js API routes (e.g., `/api/checkout/create-session`) are used to handle backend logic that shouldn't be exposed directly to the client, such as interacting with Stripe's secret keys.
*   **Environment Variable Configuration:** Key service integrations (Supabase, Stripe) are configured via environment variables.
