"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';


import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { useSupabaseClient } from '../../../../hooks/useSupabaseClient';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { apiClient, isApiSuccess, handleApiError } from '@/lib/api';
import Link from 'next/link';

interface ActivationResult {
  success: boolean;
  message: string;
  licenseId?: string;
  email?: string;
}

function LicenseActivationContent() {
  const [loading, setLoading] = useState(true);
  const [result, setResult] = useState<ActivationResult | null>(null);
  const [user, setUser] = useState<{ email?: string } | null>(null);
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const email = searchParams.get('email');
  const supabase = useSupabaseClient();

  useEffect(() => {
    handleLicenseActivation();
  }, []);

  const handleLicenseActivation = async () => {
    try {
      setLoading(true);

      // Check if user is authenticated
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      setUser(currentUser);

      if (!currentUser) {
        setResult({
          success: false,
          message: 'You must be logged in to activate a license. Please log in and try again.',
        });
        return;
      }

      if (!token) {
        setResult({
          success: false,
          message: 'Invalid activation link. The activation token is missing.',
        });
        return;
      }

      // Look for license assigned to this email
      const targetEmail = email || currentUser.email;
      if (!targetEmail) {
        setResult({
          success: false,
          message: 'No email address found for license activation.',
        });
        return;
      }

      // Get user subscriptions to find assigned licenses
      const subscriptionsResponse = await apiClient.getUserSubscriptions();
      if (!isApiSuccess(subscriptionsResponse)) {
        setResult({
          success: false,
          message: 'Failed to fetch subscription data for license activation.',
        });
        return;
      }

      // Look for assigned licenses across all subscriptions
      let foundLicense = null;
      let foundSubscription = null;

      for (const subscription of subscriptionsResponse.data) {
        const licensesResponse = await apiClient.getTeamLicenses(subscription.id);
        if (isApiSuccess(licensesResponse)) {
          const assignedLicense = licensesResponse.data.find(license => 
            license.email === targetEmail && license.status === 'assigned'
          );
          if (assignedLicense) {
            foundLicense = assignedLicense;
            foundSubscription = subscription;
            break;
          }
        }
      }

      if (!foundLicense || !foundSubscription) {
        setResult({
          success: false,
          message: `No assigned license found for ${targetEmail}. The license may have already been activated or the invitation may have expired.`,
        });
        return;
      }

      // Activate the license by assigning it to the current user
      const activationResponse = await apiClient.assignTeamLicense(foundSubscription.id, {
        license_id_to_assign: foundLicense.id,
        target_user_email: currentUser.email!,
      });

      if (isApiSuccess(activationResponse)) {
        setResult({
          success: true,
          message: 'License activated successfully! You can now use your QuantBoost license.',
          licenseId: foundLicense.id,
          email: targetEmail,
        });
      } else {
        setResult({
          success: false,
          message: handleApiError(activationResponse, 'Failed to activate license') || 'Failed to activate license',
        });
      }

    } catch (error) {
      console.error('Error activating license:', error);
      setResult({
        success: false,
        message: 'An unexpected error occurred during license activation.',
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Activating License</CardTitle>
            <CardDescription>Please wait while we activate your license...</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className={result?.success ? 'text-green-700' : 'text-red-700'}>
            {result?.success ? 'License Activated!' : 'Activation Failed'}
          </CardTitle>
          <CardDescription>
            {result?.success ? 'Your license is now active' : 'There was a problem with activation'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className={`p-4 rounded-lg ${
            result?.success
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 border border-red-200'
          }`}>
            <p className={`text-sm ${result?.success ? 'text-green-700' : 'text-red-700'}`}>
              {result?.message}
            </p>
          </div>

          {result?.success && result.licenseId && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm text-blue-700">
                <strong>License ID:</strong> {result.licenseId.slice(-8)}
              </p>
              {result.email && (
                <p className="text-sm text-blue-700">
                  <strong>Activated for:</strong> {result.email}
                </p>
              )}
            </div>
          )}

          <div className="flex flex-col space-y-2">
            {result?.success ? (
              <>
                <Link href="/dashboard">
                  <Button className="w-full">
                    Go to Dashboard
                  </Button>
                </Link>
                <Link href="/dashboard/licenses">
                  <Button variant="outline" className="w-full">
                    View All Licenses
                  </Button>
                </Link>
              </>
            ) : (
              <>
                {!user ? (
                  <Link href="/auth/login">
                    <Button className="w-full">
                      Log In to Continue
                    </Button>
                  </Link>
                ) : (
                  <Button
                    onClick={handleLicenseActivation}
                    className="w-full"
                  >
                    Try Again
                  </Button>
                )}
                <Link href="/dashboard/team">
                  <Button variant="outline" className="w-full">
                    Team Management
                  </Button>
                </Link>
              </>
            )}
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-500">
              Need help? Contact support at{' '}
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function LicenseActivationPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Loading...</CardTitle>
            <CardDescription>Please wait while we load the page...</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          </CardContent>
        </Card>
      </div>
    }>
      <LicenseActivationContent />
    </Suspense>
  );
}
