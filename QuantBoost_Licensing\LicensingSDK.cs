// --- START OF COMPLETE LicensingSDK.cs FILE ---
// Target: .NET Framework 4.8.1, C# 7.3
// JSON Library: Newtonsoft.Json

using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Net.Mail;



namespace QuantBoost_Licensing
{
    /// <summary>
    /// Unified license states reflecting API responses and client-side conditions.
    /// </summary>
    public enum LicenseStatus
    {
        /// <summary>License state is not yet determined or validation failed unexpectedly.</summary>
        Unknown,
        /// <summary>License is valid, active, and within its term.</summary>
        Active,
        /// <summary>License is valid and currently in an active trial period.</summary>
        TrialActive,
        /// <summary>License has expired but is within the allowed grace period.</summary>
        GracePeriod,
        /// <summary>License term has ended and is outside any grace period.</summary>
        Expired,
        /// <summary>License has been administratively revoked.</summary>
        Revoked,
        /// <summary>License has been cancelled by the user or subscription management.</summary>
        Cancelled, // API sends "Cancelled" (double 'l')
        /// <summary>License is present but not currently active (e.g., pending payment, or explicitly set to inactive).</summary>
        Inactive,
        /// <summary>The provided license key was not found or doesn't match the product. (Used by key-based validation)</summary>
        InvalidKey,
        /// <summary>Activation failed because the license is not assigned to an email. (Used by key-based validation)</summary>
        NotAssigned,
        /// <summary>Activation failed due to rate limits or other server-side activation blocks. (Used by key-based validation)</summary>
        ActivationBlocked,
        /// <summary>A network error occurred trying to contact the validation server.</summary>
        NetworkError,
        /// <summary>User is authenticated, but no licenses are associated with their account for this product. (Used by token-based validation)</summary>
        NoLicenseFoundForUser
    }

    /// <summary>
    /// Interface for the QuantBoost Licensing Manager.
    /// </summary>
    public interface IQuantBoostLicensingManager // Using I prefix for interface convention
    {
        // Properties
        LicenseDetails CurrentLicense { get; } // No '?' - null checked at usage site
        LicenseStatus CurrentStatus { get; } // Reflects the latest validated status enum
        string LicenseTier { get; } // No '?' - null checked at usage site
        int? TrialDaysLeft { get; } // Nullable<int> is fine (value type)
        string ActivationId { get; } // No '?' - null checked at usage site
        bool IsValid { get; } // Simple check if license allows usage (Active, Trial, Grace)
        bool IsFeatureEnabled(string featureKey); // Check specific features if available

        // Methods
        Task<LicenseStatus> ValidateLicenseAsync(string licenseKey); // Validate a specific key
        Task<LicenseStatus> ValidateWithAccessTokenAsync(string accessToken); // Validate using an access token
        Task<LicenseStatus> ValidateUsingStoredKeyAsync(); // Validate cached key
        Task<bool> RequestMagicLinkAsync(string email); // Renamed for clarity
        Task<bool> RequestMagicLinkAsync(string email, string redirectTo); // Overload with redirect URL
        Task<(bool Success, string AccessToken, string RefreshToken)> VerifyOtpAsync(string email, string otp); // OTP verification
        void ClearCachedLicense();
        Task RefreshLicenseStatusAsync(); // Force a re-validation of cached key
        void ClearCurrentLicense(); // Add this method

        // Events
        event EventHandler<LicenseDetails> LicenseStatusChanged; // No '?' on event or args type
    }

    /// <summary>
    /// Stores the details of a validated license, typically cached locally.
    /// Aligns with the 'data' object returned by the validation API on success.
    /// </summary>
    public class LicenseDetails
    {
        // Remove nullable annotations (?) from reference types
        public string LicenseKey { get; set; }
        public string ProductId { get; set; }
        public LicenseStatus LastKnownStatus { get; set; } = LicenseStatus.Unknown;
        public bool IsValid { get; set; } = false;
        public string LicenseTier { get; set; }
        public int? TrialDaysLeft { get; set; } // Nullable<int> is fine
        public string ActivationId { get; set; }
        public DateTime LastValidationUtc { get; set; }
        public DateTime? ExpiryDateUtc { get; set; } // Nullable<DateTime> is fine
        public string ResultMessage { get; set; }
        public string Email { get; set; }
        public Dictionary<string, JToken> Features { get; set; }
        public string UpgradeUrl { get; set; }
        public string ManageUrl { get; set; }

        /// <summary>
        /// Convenience method to check feature flags stored in the Features dictionary.
        /// </summary>
        /// <param name="key">The feature key (case-sensitive).</param>
        /// <param name="defaultValue">The value to return if the key is not found or is not a boolean.</param>
        /// <returns>The boolean value of the feature flag or the default value.</returns>
        public bool GetFeatureFlag(string key, bool defaultValue = false)
        {
            // Explicit null check for Features dictionary
            if (Features != null && Features.TryGetValue(key, out JToken element))
            {
                // Explicit null check for element and type check
                if (element != null && element.Type == JTokenType.Boolean)
                {
                    try
                    {
                        // Safely convert JToken to bool
                        return element.Value<bool>();
                    }
                    catch (Exception ex) // Catch potential exceptions during value conversion
                    {
                        // Log error or handle - shouldn't happen often if Type is Boolean
                        Console.WriteLine($"[QuantBoostLicensingSDK] WARN: Error converting feature flag '{key}' to bool: {ex.Message}");
                    }
                }
            }
            return defaultValue;
        }
    }

    // --- Helper Classes for API Interaction ---

    /// <summary>
    /// Represents the generic structure of a successful API response.
    /// </summary>
    /// <typeparam name="T">The type of the data payload.</typeparam>
    internal class ApiResponse<T>
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("data")]
        public T Data { get; set; }

        [JsonProperty("message")] // Optional: For general messages with success
        public string Message { get; set; }
    }

    /// <summary>
    /// Represents the structure of the license data object within the API response.
    /// This class was previously updated.
    /// </summary>
    internal class ApiLicenseData
    {
        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("isValid")]
        public bool IsValid { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("licenseTier")]
        public string LicenseTier { get; set; }

        [JsonProperty("trialDaysLeft")]
        public int? TrialDaysLeft { get; set; } // Changed to nullable int

        [JsonProperty("activationId")]
        public string ActivationId { get; set; }

        [JsonProperty("expiryDate")] // Assuming API sends "expiryDate"
        public DateTime? ExpiryDateUtc { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("features")]
        public Dictionary<string, JToken> Features { get; set; }

        [JsonProperty("upgradeUrl")]
        public string UpgradeUrl { get; set; }

        [JsonProperty("manageUrl")]
        public string ManageUrl { get; set; }

        [JsonProperty("licenseKey")] // Added
        public string LicenseKey { get; set; }
    }

    /// <summary>
    /// Represents the structure of an API error response.
    /// </summary>
    internal class ApiErrorResponse
    {
        [JsonProperty("success")]
        public bool Success { get; set; } // Should be false

        [JsonProperty("error")]
        public ApiError Error { get; set; }
    }

    /// <summary>
    /// Represents the error details in an API error response.
    /// </summary>
    internal class ApiError
    {
        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("code")] // Optional: if your API includes error codes
        public string Code { get; set; }
    }


    /// <summary>
    /// Manages license validation, activation requests, and caching for QuantBoost products.
    /// Uses Newtonsoft.Json for JSON operations. Compatible with .NET Framework 4.8.1, C# 7.3.
    /// </summary>
    public class QuantBoostLicensingManager : IQuantBoostLicensingManager, IDisposable
    {
        private readonly string _productId;
        private readonly string _apiBaseUrl;
        private readonly string _licenseCachePath;
        private LicenseDetails _currentLicense; // No '?'
        private readonly HttpClient _httpClient;
        private static readonly object _cacheLock = new object(); // Lock for file access
        private static readonly JsonSerializerSettings _jsonSettings = new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Ignore,
            Formatting = Formatting.Indented,
        };

        private const double CACHE_VALIDITY_MINUTES = 10;

        // Properties implementation - perform null checks on _currentLicense before accessing members
        public LicenseDetails CurrentLicense
        {
            get
            {
                lock (_cacheLock) // Ensure thread-safe access
                {
                    return _currentLicense;
                }
            }
            private set // Keep setter private or internal if needed
            {
                lock (_cacheLock)
                {
                    _currentLicense = value;
                }
            }
        }

        public LicenseStatus CurrentStatus
        {
            get
            {
                LicenseDetails license = CurrentLicense; // Read thread-safe property once
                return license != null ? license.LastKnownStatus : LicenseStatus.Unknown;
            }
        }
        public string LicenseTier
        {
            get
            {
                LicenseDetails license = CurrentLicense;
                return license != null ? license.LicenseTier : null;
            }
        }
        public int? TrialDaysLeft
        {
            get
            {
                LicenseDetails license = CurrentLicense;
                return license != null ? license.TrialDaysLeft : null;
            }
        }
        public string ActivationId
        {
            get
            {
                LicenseDetails license = CurrentLicense;
                return license != null ? license.ActivationId : null;
            }
        }
        public bool IsValid
        {
            get
            {
                LicenseDetails license = CurrentLicense;
                return license != null ? license.IsValid : false;
            }
        }

        // Event implementation - check for null before invoking
        public event EventHandler<LicenseDetails> LicenseStatusChanged;

        /// <summary>
        /// Initializes a new instance of the licensing manager.
        /// </summary>
        /// <param name="productId">The unique identifier for the product being licensed (e.g., "quantboost-suite").</param>
        /// <param name="apiBaseUrl">The base URL of the licensing API (e.g., "https://your-api.com").</param>
        public QuantBoostLicensingManager(string productId, string apiBaseUrl)
        {
            if (string.IsNullOrWhiteSpace(productId))
                throw new ArgumentNullException(nameof(productId));
            if (string.IsNullOrWhiteSpace(apiBaseUrl))
                throw new ArgumentNullException(nameof(apiBaseUrl));

            _productId = productId;
            _apiBaseUrl = apiBaseUrl.TrimEnd('/');
            _httpClient = new HttpClient() { BaseAddress = new Uri(_apiBaseUrl) };
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            string localAppData = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            string companyFolder = Path.Combine(localAppData, "QuantBoost");
            string productFolder = Path.Combine(companyFolder, _productId);
            _licenseCachePath = Path.Combine(productFolder, "license.cache");

            LoadCachedLicense();
        }

        // --- Caching Logic ---

        private void LoadCachedLicense()
        {
            lock (_cacheLock)
            {
                CurrentLicense = null; // Start fresh
                if (!File.Exists(_licenseCachePath)) return;

                try
                {
                    string json = File.ReadAllText(_licenseCachePath);
                    LicenseDetails loadedLicense = JsonConvert.DeserializeObject<LicenseDetails>(json);

                    // Sanity check loaded license
                    if (loadedLicense != null && loadedLicense.ProductId == _productId)
                    {
                        CurrentLicense = loadedLicense;
                    }
                    else if (loadedLicense != null) // Product ID mismatch
                    {
                         LogError($"Warning: Cached license is for a different Product ID ('{loadedLicense.ProductId}' vs '{_productId}'). Ignoring cache.");
                         CurrentLicense = null;
                    }
                    else // Deserialization returned null
                    {
                        LogError($"Warning: Cached license deserialized to null from '{_licenseCachePath}'. Ignoring cache.");
                        CurrentLicense = null;
                    }
                }
                catch (JsonException jsonEx)
                {
                    LogError($"Failed to parse cached license from '{_licenseCachePath}'. Error: {jsonEx.Message}");
                    CurrentLicense = null;
                    try { File.Delete(_licenseCachePath); } catch (Exception ex) { LogError($"Failed to delete corrupted cache file '{_licenseCachePath}'. Error: {ex.Message}"); }
                }
                catch (Exception ex)
                {
                    LogError($"Failed to load cached license from '{_licenseCachePath}'. Error: {ex.Message}");
                    CurrentLicense = null;
                    try { File.Delete(_licenseCachePath); } catch (Exception exInner) { LogError($"Failed to delete cache file after load error '{_licenseCachePath}'. Error: {exInner.Message}"); }
                }
            }
        }

        private void SaveCachedLicense()
        {
            LicenseDetails licenseToSave = null;
            lock (_cacheLock)
            {
                // Capture current license state inside lock
                licenseToSave = CurrentLicense;
            }

            // Perform file operations outside the main lock if possible,
            // but here we need the value from inside the lock.
            // Saving null is effectively clearing the cache file content.
            if (licenseToSave == null)
            {
                 // Optionally delete the file if license becomes null
                 // ClearCachedLicense(); // Or just write empty/null content below
                 return; // Decided not to write null to file, just don't save anything
            }

            // Lock again specifically for file writing to prevent race conditions
            // if another thread tries to save concurrently (though unlikely with private setter).
            // A dedicated file lock object could also be used.
            lock(_cacheLock)
            {
                try
                {
                    string dir = Path.GetDirectoryName(_licenseCachePath);
                    // Check directory name is not null/empty before creating
                    if (!string.IsNullOrEmpty(dir) && !Directory.Exists(dir))
                        Directory.CreateDirectory(dir);

                    string json = JsonConvert.SerializeObject(licenseToSave, _jsonSettings);
                    File.WriteAllText(_licenseCachePath, json);
                }
                catch (Exception ex)
                {
                    LogError($"Failed to save license cache to '{_licenseCachePath}'. Error: {ex.Message}");
                }
            }
        }

        public void ClearCachedLicense()
        {
            lock (_cacheLock)
            {
                try
                {
                     if (File.Exists(_licenseCachePath))
                        File.Delete(_licenseCachePath);
                }
                catch (Exception ex)
                {
                    LogError($"Failed to delete license cache file '{_licenseCachePath}'. Error: {ex.Message}");
                }
                finally
                {
                    CurrentLicense = null; // Clear in-memory cache too
                    FireStatusChanged(null); // Notify listeners with null
                }
            }
        }

        // --- Device ID ---

        public virtual string GetDeviceId()
        {
            try
            {
                string input = $"{Environment.MachineName}_{_productId}";
                // Traditional using block (C# 7.3)
                using (SHA256 sha256 = SHA256.Create())
                {
                    byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
                    StringBuilder builder = new StringBuilder();
                    for (int i = 0; i < hashBytes.Length; i++)
                    {
                        builder.Append(hashBytes[i].ToString("x2"));
                    }
                    return builder.ToString();
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to generate Device ID. Error: {ex.Message}");
                return Guid.NewGuid().ToString("N").ToLowerInvariant(); // Fallback
            }
        }

        // --- Core Licensing Methods ---

        public async Task<LicenseStatus> ValidateUsingStoredKeyAsync()
        {
            LicenseDetails cachedLicense = CurrentLicense; // Read thread-safe property

            if (cachedLicense != null && !string.IsNullOrEmpty(cachedLicense.LicenseKey))
            {
                // Key exists, check freshness
                if (cachedLicense.LastValidationUtc.AddMinutes(CACHE_VALIDITY_MINUTES) > DateTime.UtcNow)
                {
                    return cachedLicense.LastKnownStatus; // Return fresh cached status
                }
                else
                {
                    // Cache is stale, re-validate using the cached key
                    return await ValidateLicenseAsync(cachedLicense.LicenseKey).ConfigureAwait(false);
                }
            }
            return LicenseStatus.Unknown; // No key stored or license object is null
        }


        public async Task<LicenseStatus> ValidateLicenseAsync(string licenseKey)
        {
            if (string.IsNullOrWhiteSpace(licenseKey))
            {
                return LicenseStatus.InvalidKey;
            }

            string deviceId = GetDeviceId();
            var requestBody = new { licenseKey, productId = _productId, deviceId };
            HttpResponseMessage response = null; 
            LicenseDetails existingLicense = CurrentLicense; 
            LicenseStatus determinedStatus = LicenseStatus.Unknown; // Moved declaration here
            LicenseDetails tempLicenseDetails = null; // Moved declaration here

            try
            {
                string jsonBody = JsonConvert.SerializeObject(requestBody);
                // Traditional using block
                using (StringContent content = new StringContent(jsonBody, Encoding.UTF8, "application/json"))
                {
                    response = await _httpClient.PostAsync("/v1/licenses/validate", content).ConfigureAwait(false);
                } // content is disposed here

                // Ensure response is not null before proceeding (though PostAsync usually throws if it fails fundamentally)
                if (response == null)
                {
                    throw new InvalidOperationException("HTTP response was unexpectedly null after PostAsync.");
                }

                string responseString = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

                if (response.IsSuccessStatusCode) // Status 200 OK
                {
                    try
                    {
                        // Explicitly check for null before accessing members
                        ApiResponse<ApiLicenseData> apiResponse = JsonConvert.DeserializeObject<ApiResponse<ApiLicenseData>>(responseString);

                        if (apiResponse != null && apiResponse.Success && apiResponse.Data != null)
                        {
                            ApiLicenseData data = apiResponse.Data; // data cannot be null here                            // Parse status string, handle potential null or invalid enum value
                            if (!TryParseLicenseStatus(data.Status, out determinedStatus))
                            {
                                determinedStatus = LicenseStatus.Unknown; // Default if parse fails
                                LogError($"API success but received unknown license status string: '{data.Status ?? "null"}' for license {licenseKey}.");
                            }

                            tempLicenseDetails = new LicenseDetails
                            {
                                LicenseKey = licenseKey,
                                ProductId = _productId,
                                LastKnownStatus = determinedStatus,
                                IsValid = data.IsValid,
                                LicenseTier = data.LicenseTier, // Assign directly, null is ok
                                TrialDaysLeft = data.TrialDaysLeft > 0 ? (int?)data.TrialDaysLeft : null,
                                ActivationId = data.ActivationId, // Assign directly, null is ok
                                ExpiryDateUtc = data.ExpiryDateUtc, // Assign directly, null is ok
                                ResultMessage = data.Message, // Assign directly, null is ok
                                Email = data.Email, // Assign directly, null is ok
                                Features = data.Features, // Assign directly, null is ok
                                UpgradeUrl = data.UpgradeUrl, // Assign directly, null is ok
                                ManageUrl = data.ManageUrl, // Assign directly, null is ok
                                LastValidationUtc = DateTime.UtcNow
                            };
                        }
                        else
                        {
                             LogError($"API success status but invalid payload for license {licenseKey}. Success: {apiResponse?.Success}, Data: {(apiResponse?.Data == null ? "null" : "exists")}. Response: {responseString}");
                             determinedStatus = LicenseStatus.Unknown;
                             // Keep tempLicenseDetails null
                        }
                    }
                    catch (JsonException jsonEx)
                    {
                        LogError($"Failed to parse successful API response for license {licenseKey}. Error: {jsonEx.Message}. Response: {responseString}");
                        determinedStatus = LicenseStatus.Unknown;
                        // Keep tempLicenseDetails null
                    }
                }
                else // Handle API Error Responses (4xx, 5xx)
                {
                    LogError($"API validation failed for license {licenseKey}. Status: {(int)response.StatusCode}. Response: {responseString}");
                    string apiErrorMessage = $"API returned status {(int)response.StatusCode}";
                    try {
                        ApiErrorResponse errorResponse = JsonConvert.DeserializeObject<ApiErrorResponse>(responseString);
                        if (errorResponse != null && errorResponse.Error != null && !string.IsNullOrEmpty(errorResponse.Error.Message)) {
                            apiErrorMessage = errorResponse.Error.Message;
                        }
                    } catch (JsonException jsonEx) {
                         LogError($"Failed to parse error response for license {licenseKey}. Error: {jsonEx.Message}. Response: {responseString}");
                    } catch { /* Ignore other parse errors */ }

                    switch (response.StatusCode)
                    {
                        case HttpStatusCode.NotFound: // 404
                            determinedStatus = LicenseStatus.InvalidKey;
                            break;
                        case HttpStatusCode.Forbidden: // 403
                            determinedStatus = LicenseStatus.NotAssigned;
                            break;
                        case (HttpStatusCode)429: // Too Many Requests
                            determinedStatus = LicenseStatus.ActivationBlocked;
                            break;
                        default:
                            determinedStatus = LicenseStatus.Unknown; // Other errors
                            break;
                    }

                    // Update existing cache entry's status if validating cached key
                    if (existingLicense != null && existingLicense.LicenseKey == licenseKey) // This is around line 504
                    {
                       tempLicenseDetails = existingLicense; // Start with existing details
                       tempLicenseDetails.LastKnownStatus = determinedStatus;
                       tempLicenseDetails.ResultMessage = apiErrorMessage;
                       tempLicenseDetails.LastValidationUtc = DateTime.UtcNow;
                       tempLicenseDetails.IsValid = (determinedStatus == LicenseStatus.Active || determinedStatus == LicenseStatus.TrialActive || determinedStatus == LicenseStatus.GracePeriod);
                    }
                    else // Validating a key not currently cached, create minimal error entry
                    {
                        tempLicenseDetails = new LicenseDetails {
                            LicenseKey = licenseKey, ProductId = _productId,
                            LastKnownStatus = determinedStatus, ResultMessage = apiErrorMessage,
                            IsValid = false, // Explicitly false for error states
                            LastValidationUtc = DateTime.UtcNow
                        };
                    }
                }

                // Update main cache and fire event only if temp details were created/updated
                if (tempLicenseDetails != null)
                {
                    // Update the central cache using the property setter which handles locking
                    CurrentLicense = tempLicenseDetails;
                    SaveCachedLicense(); // Save the updated state
                    FireStatusChanged(CurrentLicense); // Pass updated details
                }
                // If tempLicenseDetails remained null (e.g., parse error on success), status remains Unknown
                // We might want to update the cache even on parse errors, setting status to Unknown.
                // Let's refine: If parsing a success response fails, still update cache to Unknown status.
                else if (response.IsSuccessStatusCode && determinedStatus == LicenseStatus.Unknown)
                {
                     // Create a minimal entry indicating parse failure
                     tempLicenseDetails = new LicenseDetails {
                         LicenseKey = licenseKey, ProductId = _productId,
                         LastKnownStatus = LicenseStatus.Unknown, ResultMessage = "Failed to parse successful API response.",
                         IsValid = false, LastValidationUtc = DateTime.UtcNow
                     };
                     CurrentLicense = tempLicenseDetails;
                     SaveCachedLicense();
                     FireStatusChanged(CurrentLicense);
                }

                return determinedStatus; // Return the status determined during processing
            }
            catch (HttpRequestException httpEx)
            {
                LogError($"Network error validating license {licenseKey}. Error: {httpEx.Message}");
                if (existingLicense != null && existingLicense.LicenseKey == licenseKey) { // This 'existingLicense' now refers to the one from the method's top scope
                     existingLicense.LastKnownStatus = LicenseStatus.NetworkError;
                     existingLicense.ResultMessage = "Network error during validation.";
                     existingLicense.LastValidationUtc = DateTime.UtcNow;
                     existingLicense.IsValid = false; // Network error means we can't confirm validity
                     CurrentLicense = existingLicense; // Update via property setter
                     SaveCachedLicense();
                     FireStatusChanged(CurrentLicense);
                }
                return LicenseStatus.NetworkError;
            }
            catch (JsonException jsonEx) // Catch JSON errors during request serialization
            {
                 LogError($"JSON error preparing validation request for license {licenseKey}. Error: {jsonEx.Message}");
                 return LicenseStatus.Unknown;
            }
            catch (Exception ex) // Catch-all
            {
                LogError($"Unexpected error validating license {licenseKey}. Error: {ex}");
                 // LicenseDetails existingLicense = CurrentLicense; // Remove re-declaration
                 // Replace property pattern
                 if (existingLicense != null && existingLicense.LicenseKey == licenseKey) {
                     existingLicense.LastKnownStatus = LicenseStatus.Unknown;
                     existingLicense.ResultMessage = "Unexpected error during validation.";
                     existingLicense.LastValidationUtc = DateTime.UtcNow;
                     existingLicense.IsValid = false; // Unknown error means we can't confirm validity
                     CurrentLicense = existingLicense;
                     SaveCachedLicense();
                     FireStatusChanged(CurrentLicense);
                 }
                return LicenseStatus.Unknown;
            }
            finally
            {
                // Explicit null check before Dispose
                if (response != null)
                {
                    response.Dispose();
                }
            }
        }


        public async Task<LicenseStatus> ValidateWithAccessTokenAsync(string accessToken)
        {
            if (string.IsNullOrWhiteSpace(accessToken))
            {
                return LicenseStatus.Unknown;
            }

            HttpResponseMessage response = null;
            LicenseDetails existingLicense = CurrentLicense;
            LicenseStatus determinedStatus = LicenseStatus.Unknown;
            LicenseDetails tempLicenseDetails = null;

            try
            {
                // --- START OF FIX ---

                // 1. Get the Device ID, which is needed for the request.
                string deviceId = GetDeviceId();

                // 2. Construct the correct URL with query parameters for a GET request.
                var validationUrl = $"/v1/licenses/validate?productId={_productId}&deviceId={deviceId}";

                // 3. Create the HttpRequestMessage with the correct method (GET) and URL.
                using (var requestMessage = new HttpRequestMessage(HttpMethod.Get, validationUrl))
                {
                    // 4. Add the authorization token to the header.
                    requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                    // A GET request does not have a body, so the old requestBody and StringContent are removed.

                    response = await _httpClient.SendAsync(requestMessage).ConfigureAwait(false);
                }

                // --- END OF FIX ---


                if (response == null)
                {
                    throw new InvalidOperationException("HTTP response was unexpectedly null after SendAsync for token validation.");
                }

                string responseString = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

                if (response.IsSuccessStatusCode)
                {
                    try
                    {
                        ApiResponse<ApiLicenseData> apiResponse = JsonConvert.DeserializeObject<ApiResponse<ApiLicenseData>>(responseString);

                        if (apiResponse != null && apiResponse.Success && apiResponse.Data != null)
                        {
                            ApiLicenseData data = apiResponse.Data;

                            if (!TryParseLicenseStatus(data.Status, out determinedStatus))
                            {
                                determinedStatus = LicenseStatus.Unknown;
                                LogError($"Token validation: API success but received unknown license status string: '{data.Status ?? "null"}'.");
                            }

                            tempLicenseDetails = new LicenseDetails
                            {
                                LicenseKey = data.LicenseKey,
                                ProductId = _productId,
                                LastKnownStatus = determinedStatus,
                                IsValid = data.IsValid,
                                LicenseTier = data.LicenseTier,
                                TrialDaysLeft = data.TrialDaysLeft > 0 ? data.TrialDaysLeft : null,
                                ActivationId = data.ActivationId,
                                ExpiryDateUtc = data.ExpiryDateUtc,
                                ResultMessage = apiResponse.Message ?? data.Message,
                                Email = data.Email,
                                Features = data.Features,
                                UpgradeUrl = data.UpgradeUrl,
                                ManageUrl = data.ManageUrl,
                                LastValidationUtc = DateTime.UtcNow
                            };
                        }
                        else
                        {
                            LogError($"Token validation: API success status but invalid payload. Success: {apiResponse?.Success}, Data: {(apiResponse?.Data == null ? "null" : "exists")}, API Message: {apiResponse?.Message}. Response: {responseString}");
                            determinedStatus = LicenseStatus.Unknown;
                        }
                    }
                    catch (JsonException jsonEx)
                    {
                        LogError($"Token validation: Failed to parse successful API response. Error: {jsonEx.Message}. Response: {responseString}");
                        determinedStatus = LicenseStatus.Unknown;
                    }
                }
                else
                {
                    LogError($"Token validation: API validation failed. Status: {(int)response.StatusCode}. Response: {responseString}");
                    string apiErrorMessage = $"API returned status {(int)response.StatusCode}";
                    try {
                        ApiErrorResponse errorResponse = JsonConvert.DeserializeObject<ApiErrorResponse>(responseString);
                        if (errorResponse != null && errorResponse.Error != null && !string.IsNullOrEmpty(errorResponse.Error.Message)) {
                            apiErrorMessage = errorResponse.Error.Message;
                        }
                    } catch (JsonException jsonEx) {
                         LogError($"Token validation: Failed to parse error response. Error: {jsonEx.Message}. Response: {responseString}");
                    } catch { /* Ignore other parse errors */ }

                    switch (response.StatusCode)
                    {
                        case HttpStatusCode.NotFound:
                            determinedStatus = LicenseStatus.NoLicenseFoundForUser;
                            break;
                        case HttpStatusCode.Unauthorized:
                        case HttpStatusCode.Forbidden:
                            determinedStatus = LicenseStatus.Unknown;
                            LogError($"Token validation: Authorization error. Status: {response.StatusCode}. Message: {apiErrorMessage}");
                            break;
                        default:
                            determinedStatus = LicenseStatus.Unknown;
                            break;
                    }

                    if (determinedStatus == LicenseStatus.NoLicenseFoundForUser)
                    {
                        if (existingLicense != null && existingLicense.ProductId == _productId)
                        {
                             LogInfo($"Token validation: No license found for user. Clearing previously cached license for product {_productId}.");
                             ClearCurrentLicense();
                        }
                    }
                }
            }
            catch (HttpRequestException httpEx)
            {
                LogError($"Token validation: Network error: {httpEx.Message}");
                determinedStatus = LicenseStatus.NetworkError;
            }
            catch (JsonException jsonEx)
            {
                 LogError($"Token validation: JSON error preparing request. Error: {jsonEx.Message}");
                 determinedStatus = LicenseStatus.Unknown;
            }
            catch (Exception ex)
            {
                LogError($"Token validation: Unexpected error: {ex.Message}");
                determinedStatus = LicenseStatus.Unknown;
            }
            finally
            {
                response?.Dispose();
            }

            if (tempLicenseDetails != null)
            {
                CurrentLicense = tempLicenseDetails;
                SaveCachedLicense();
                FireStatusChanged(CurrentLicense);
            }

            return determinedStatus;
        }

        // --- Logging and Event Helpers ---
        private void LogError(string message)
        {
            // Basic console logging. Replace with a proper logging framework if available.
            Console.WriteLine($"[QuantBoostLicensingSDK] ERROR: {message}");
            //System.Diagnostics.Trace.TraceError($"[QuantBoostLicensingSDK] ERROR: {message}");
        }

        private void LogInfo(string message)
        {
            // Basic console logging. Replace with a proper logging framework if available.
            Console.WriteLine($"[QuantBoostLicensingSDK] INFO: {message}");
            //System.Diagnostics.Trace.TraceInformation($"[QuantBoostLicensingSDK] INFO: {message}");
        }
        
        private void FireStatusChanged(LicenseDetails newDetails)
        {
            // Ensure event invocation is thread-safe if handlers can be added/removed concurrently
            // For simplicity, direct invocation is shown. Consider a copy of the handler list.
            LicenseStatusChanged?.Invoke(this, newDetails);
        }        // --- Enum Parsing Helper ---
        private bool TryParseLicenseStatus(string statusString, out LicenseStatus status)
        {
            status = LicenseStatus.Unknown;
            if (string.IsNullOrWhiteSpace(statusString))
            {
                return false;
            }

            // Convert snake_case to PascalCase for enum parsing
            string normalizedStatus = ConvertSnakeCaseToPascalCase(statusString);

            // Adjust for "Cancelled" vs "Canceled" if API sends "Cancelled"
            if ("Cancelled".Equals(normalizedStatus, StringComparison.OrdinalIgnoreCase))
            {
                status = LicenseStatus.Cancelled; // Ensure your enum uses "Cancelled"
                return true;
            }
            
            // Standard enum parsing with the normalized status
            return Enum.TryParse<LicenseStatus>(normalizedStatus, true, out status);
        }

        /// <summary>
        /// Converts snake_case strings to PascalCase for enum parsing.
        /// E.g., "trial_active" becomes "TrialActive"
        /// </summary>
        private string ConvertSnakeCaseToPascalCase(string snakeCaseString)
        {
            if (string.IsNullOrWhiteSpace(snakeCaseString))
            {
                return snakeCaseString;
            }

            // Split by underscore and capitalize each part
            string[] parts = snakeCaseString.Split('_');
            StringBuilder result = new StringBuilder();
            
            foreach (string part in parts)
            {
                if (!string.IsNullOrEmpty(part))
                {
                    // Capitalize first letter and append the rest in lowercase
                    result.Append(char.ToUpperInvariant(part[0]));
                    if (part.Length > 1)
                    {
                        result.Append(part.Substring(1).ToLowerInvariant());
                    }
                }
            }
            
            return result.ToString();
        }

        // --- Public Interface Method Implementations ---
        
        public void ClearCurrentLicense() // Implementation for IQuantBoostLicensingManager
        {
            lock (_cacheLock)
            {
                CurrentLicense = null; 
                // Optionally, trigger save to clear persisted cache immediately,
                // or rely on next SaveCachedLicense call if appropriate.
                // For now, let's ensure the file cache is also cleared.
                try
                {
                     if (File.Exists(_licenseCachePath))
                        File.Delete(_licenseCachePath);
                     LogInfo("Cleared cached license file and in-memory license.");
                }
                catch (Exception ex)
                {
                    LogError($"Failed to delete license cache file during ClearCurrentLicense: \'{_licenseCachePath}\'. Error: {ex.Message}");
                }
                finally
                {
                    FireStatusChanged(null); // Notify listeners
                }
            }
        }

        public bool IsFeatureEnabled(string featureKey)
        {
            LicenseDetails license = CurrentLicense; // Read thread-safe property
            if (license == null || !license.IsValid) return false;
            return license.GetFeatureFlag(featureKey, false);
        }

        public async Task RefreshLicenseStatusAsync()
        {
            LicenseDetails license = CurrentLicense; // Read thread-safe property
            if (license != null && !string.IsNullOrEmpty(license.LicenseKey))
            {
                // Re-validate using the stored key.
                // ValidateLicenseAsync will handle updating cache and firing events.
                await ValidateLicenseAsync(license.LicenseKey).ConfigureAwait(false);
            }
            else
            {
                // No key stored, or license object is null.
                // Optionally, if an access token was used previously and is available,
                // you might try to refresh with it. For now, this is a no-op.
                LogInfo("RefreshLicenseStatusAsync: No license key stored to refresh.");
                // We might want to set status to Unknown if no key and fire event
                // if (_currentLicense != null) // if there was some non-keyed state
                // {
                //     _currentLicense.LastKnownStatus = LicenseStatus.Unknown;
                //     SaveCachedLicense();
                //     FireStatusChanged(_currentLicense);
                // }
            }
        }

        public async Task<bool> RequestMagicLinkAsync(string email)
        {
            if (string.IsNullOrWhiteSpace(email) || !IsValidEmail(email))
            {
                LogError("RequestMagicLinkAsync: Invalid email address provided.");
                return false;
            }

            var requestBody = new { email, productId = _productId };
            HttpResponseMessage response = null;

            try
            {
                string jsonBody = JsonConvert.SerializeObject(requestBody);
                using (StringContent content = new StringContent(jsonBody, Encoding.UTF8, "application/json"))
                {
                    response = await _httpClient.PostAsync("/v1/auth/magic-link", content).ConfigureAwait(false);
                }

                if (response == null)
                {
                    throw new InvalidOperationException("HTTP response was unexpectedly null after PostAsync for magic link.");
                }
                
                // string responseString = await response.Content.ReadAsStringAsync().ConfigureAwait(false); // For debugging

                if (response.IsSuccessStatusCode) // Typically 200 or 204 No Content
                {
                    LogInfo($"Magic link request successful for email: {email}, product: {_productId}.");
                    return true;
                }
                else
                {
                    string errorContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                    LogError($"Magic link request failed for email: {email}. Status: {(int)response.StatusCode}. Response: {errorContent}");
                    return false;
                }
            }
            catch (HttpRequestException httpEx)
            {
                LogError($"Magic link request: Network error for email {email}. Error: {httpEx.Message}");
                return false;
            }
            catch (JsonException jsonEx)
            {
                 LogError($"Magic link request: JSON error preparing request for email {email}. Error: {jsonEx.Message}");
                 return false;
            }
            catch (Exception ex)
            {
                LogError($"Magic link request: Unexpected error for email {email}. Error: {ex.Message}");
                return false;
            }
            finally
            {
                response?.Dispose();
            }
        }

        public async Task<bool> RequestMagicLinkAsync(string email, string redirectTo)
        {
            if (string.IsNullOrWhiteSpace(email) || !IsValidEmail(email))
            {
                LogError("RequestMagicLinkAsync: Invalid email address provided.");
                return false;
            }

            var requestBody = new {
                email,
                productId = _productId,
                options = string.IsNullOrEmpty(redirectTo) ? null : new { redirectTo }
            };
            HttpResponseMessage response = null;

            try
            {
                string jsonBody = JsonConvert.SerializeObject(requestBody);
                using (StringContent content = new StringContent(jsonBody, Encoding.UTF8, "application/json"))
                {
                    response = await _httpClient.PostAsync("/v1/auth/magic-link", content).ConfigureAwait(false);
                }

                if (response == null)
                {
                    throw new InvalidOperationException("HTTP response was unexpectedly null after PostAsync for magic link.");
                }
                
                // string responseString = await response.Content.ReadAsStringAsync().ConfigureAwait(false); // For debugging

                if (response.IsSuccessStatusCode) // Typically 200 or 204 No Content
                {
                    LogInfo($"Magic link request successful for email: {email}, product: {_productId}.");
                    return true;
                }
                else
                {
                    string errorContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                    LogError($"Magic link request failed for email: {email}. Status: {(int)response.StatusCode}. Response: {errorContent}");
                    return false;
                }
            }
            catch (HttpRequestException httpEx)
            {
                LogError($"Magic link request: Network error for email {email}. Error: {httpEx.Message}");
                return false;
            }
            catch (JsonException jsonEx)
            {
                 LogError($"Magic link request: JSON error preparing request for email {email}. Error: {jsonEx.Message}");
                 return false;
            }
            catch (Exception ex)
            {
                LogError($"Magic link request: Unexpected error for email {email}. Error: {ex.Message}");
                return false;
            }
            finally
            {
                response?.Dispose();
            }
        }

        public async Task<(bool Success, string AccessToken, string RefreshToken)> VerifyOtpAsync(string email, string otp)
        {
            if (string.IsNullOrWhiteSpace(email) || !IsValidEmail(email))
            {
                LogError("VerifyOtpAsync: Invalid email address provided.");
                return (false, null, null);
            }

            if (string.IsNullOrWhiteSpace(otp) || otp.Length != 6)
            {
                LogError("VerifyOtpAsync: Invalid OTP provided.");
                return (false, null, null);
            }

            var requestBody = new { email, token = otp };
            HttpResponseMessage response = null;

            try
            {
                string jsonBody = JsonConvert.SerializeObject(requestBody);
                using (StringContent content = new StringContent(jsonBody, Encoding.UTF8, "application/json"))
                {
                    response = await _httpClient.PostAsync("/v1/auth/verify-otp", content).ConfigureAwait(false);
                }

                if (response == null)
                {
                    throw new InvalidOperationException("HTTP response was unexpectedly null after PostAsync for OTP verification.");
                }

                string responseBody = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

                if (response.IsSuccessStatusCode)
                {
                    try
                    {
                        var authResponse = JsonConvert.DeserializeObject<dynamic>(responseBody);
                        string accessToken = authResponse?.data?["accessToken"];
                        string refreshToken = authResponse?.data?["refreshToken"];

                        if (!string.IsNullOrEmpty(accessToken) && !string.IsNullOrEmpty(refreshToken))
                        {
                            LogInfo($"OTP verification successful for email: {email}.");
                            return (true, accessToken, refreshToken);
                        }
                        else
                        {
                            LogError($"OTP verification: API success but tokens missing for email {email}. Response: {responseBody}");
                            return (false, null, null);
                        }
                    }
                    catch (JsonException jsonEx)
                    {
                        LogError($"OTP verification: Failed to parse successful API response for email {email}. Error: {jsonEx.Message}. Response: {responseBody}");
                        return (false, null, null);
                    }
                }
                else
                {
                    LogError($"OTP verification failed for email: {email}. Status: {(int)response.StatusCode}. Response: {responseBody}");
                    return (false, null, null);
                }
            }
            catch (HttpRequestException httpEx)
            {
                LogError($"OTP verification: Network error for email {email}. Error: {httpEx.Message}");
                return (false, null, null);
            }
            catch (JsonException jsonEx)
            {
                LogError($"OTP verification: JSON error preparing request for email {email}. Error: {jsonEx.Message}");
                return (false, null, null);
            }
            catch (Exception ex)
            {
                LogError($"OTP verification: Unexpected error for email {email}. Error: {ex.Message}");
                return (false, null, null);
            }
            finally
            {
                response?.Dispose();
            }
        }

        // Helper for email validation (basic)
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Disposes of the HttpClient and other resources.
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}