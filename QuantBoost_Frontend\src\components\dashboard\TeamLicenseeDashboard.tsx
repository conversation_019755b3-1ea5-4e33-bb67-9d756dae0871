import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button, Badge } from '@/components/ui';
import { 
  UserType, 
  getLicenseStatusColor, 
  formatDate 
} from '@/lib/userTypeDetector';
import type { License } from '@/lib/api';

interface TeamLicenseeDashboardProps {
  license: License;
  userEmail?: string;
}

/**
 * Dashboard component specifically designed for team licensees (users with assigned licenses)
 * Shows only relevant information: license status, activation instructions, and downloads
 */
export function TeamLicenseeDashboard({ license, userEmail }: TeamLicenseeDashboardProps) {
  // Determine if this is a trial license
  const isTrial = license.status === 'trial_active' || (license as any).trial_expiry_date;
  
  // Get appropriate expiry date
  const expiryDate = license.expiry_date || (license as any).trial_expiry_date;
  
  // Calculate days until expiry
  const daysUntilExpiry = expiryDate ? 
    Math.ceil((new Date(expiryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 
    null;

  const getStatusBadge = (status: string) => (
    <Badge className={getLicenseStatusColor(status)}>
      {status.replace('_', ' ').toUpperCase()}
    </Badge>
  );

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could add a toast notification here if available
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const teamAdmin = (license as any).team_admin;

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Welcome to QuantBoost! 🚀</h1>
        <p className="text-muted-foreground">
          Your team license is ready to use. Get started by downloading and activating your add-ins.
        </p>
      </div>

      {/* License Status Card */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center gap-2">
                🔑 Your License Status
              </CardTitle>
              <CardDescription>
                {teamAdmin ? `Provided by ${teamAdmin}` : 'Provided by your organization'}
              </CardDescription>
            </div>
            {getStatusBadge(license.status)}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="font-medium">License Type:</span>
                <span>{license.license_tier || 'Standard'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Max Activations:</span>
                <span>{license.max_activations}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Assigned Email:</span>
                <span className="text-sm">{userEmail || license.email || 'N/A'}</span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="font-medium">License Key:</span>
                <div className="flex items-center gap-2">
                  <code className="text-sm bg-muted px-2 py-1 rounded">
                    {license.license_key.slice(0, 8)}...{license.license_key.slice(-4)}
                  </code>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(license.license_key)}
                    className="h-7 px-2"
                  >
                    📋
                  </Button>
                </div>
              </div>
              {expiryDate && (
                <div className="flex justify-between">
                  <span className="font-medium">
                    {isTrial ? 'Trial Expires:' : 'Expires:'}
                  </span>
                  <div className="text-right">
                    <div>{formatDate(expiryDate)}</div>
                    {daysUntilExpiry && (
                      <div className="text-xs text-muted-foreground">
                        {daysUntilExpiry > 0 ? `${daysUntilExpiry} days left` : 'Expired'}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Start Guide */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🚀 Quick Start Guide
          </CardTitle>
          <CardDescription>
            Follow these steps to activate your QuantBoost license
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
              <div>
                <p className="font-medium">Download the Add-ins</p>
                <p className="text-sm text-muted-foreground">Download the PowerPoint and/or Excel add-ins below</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
              <div>
                <p className="font-medium">Install the Add-ins</p>
                <p className="text-sm text-muted-foreground">Run the installer and follow the setup instructions</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
              <div>
                <p className="font-medium">Activate Your License</p>
                <p className="text-sm text-muted-foreground">Open PowerPoint/Excel → QuantBoost tab → "Activate License"</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">✓</div>
              <div>
                <p className="font-medium">You're All Set!</p>
                <p className="text-sm text-muted-foreground">Start using QuantBoost features to boost your productivity</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Download Links */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📊 PowerPoint Add-in
            </CardTitle>
            <CardDescription>
              Enhance your presentations with powerful analysis tools
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Excel Link Manager</li>
              <li>• Presentation Size Analyzer</li>
              <li>• Advanced formatting tools</li>
              <li>• Performance optimization</li>
            </ul>
            <Button className="w-full" asChild>
              <a 
                href="/downloads/quantboost-powerpoint" 
                target="_blank"
                rel="noopener noreferrer"
              >
                📥 Download PowerPoint Add-in
              </a>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📈 Excel Add-in
            </CardTitle>
            <CardDescription>
              Supercharge your spreadsheets with advanced features
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Excel Trace</li>
              <li>• File Size Analyzer</li>
              <li>• Formula optimization</li>
              <li>• Performance insights</li>
            </ul>
            <Button className="w-full" variant="outline" asChild>
              <a 
                href="/downloads/quantboost-excel" 
                target="_blank"
                rel="noopener noreferrer"
              >
                📥 Download Excel Add-in
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Activation Help */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            💡 Need Help?
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div>
              <p className="font-medium">Having trouble activating your license?</p>
              <p className="text-sm text-muted-foreground">
                Make sure you're using the full license key: <code className="bg-muted px-1 rounded text-xs">{license.license_key}</code>
              </p>
            </div>
            <div>
              <p className="font-medium">License issues?</p>
              <p className="text-sm text-muted-foreground">
                Contact your team administrator {teamAdmin && `(${teamAdmin})`} for license-related questions.
              </p>
            </div>
            <div>
              <p className="font-medium">Technical support needed?</p>
              <p className="text-sm text-muted-foreground">
                Our technical support team is here to help with installation and usage questions.
              </p>
            </div>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" asChild>
              <a href="/support" target="_blank" rel="noopener noreferrer">
                🛠️ Technical Support
              </a>
            </Button>
            <Button variant="outline" asChild>
              <a href="/docs" target="_blank" rel="noopener noreferrer">
                📚 Documentation
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Trial Warning (if applicable) */}
      {isTrial && daysUntilExpiry !== null && daysUntilExpiry <= 7 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-800">
              ⚠️ Trial Expiring Soon
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-yellow-700">
              Your trial license expires in {daysUntilExpiry} day{daysUntilExpiry !== 1 ? 's' : ''}. 
              Contact your team administrator to ensure continued access to QuantBoost features.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}