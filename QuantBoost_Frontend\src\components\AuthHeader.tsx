"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence, Variants } from 'motion/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import AnimatedQuantBoostLogo from '@/components/AnimatedQuantBoostLogo';
import { useSupabaseClient } from '@/hooks/useSupabaseClient';
import { ChevronDown, LogOut, Download, User, HelpCircle } from 'lucide-react';

// Animation variants 🎨
const logoVariants: Variants = {
  idle: { 
    scale: 1,
    rotate: 0,
    transition: { 
      duration: 0.6,
      ease: "easeOut"
    }
  },
  hover: { 
    scale: 1.08,
    rotate: 2,
    transition: { 
      duration: 0.08,
      type: "spring",
      stiffness: 800,
      damping: 15
    }
  }
};

const dropdownVariants: Variants = {
  hidden: { 
    opacity: 0, 
    scale: 0.95,
    y: -10,
    transition: {
      duration: 0.15,
      ease: "easeIn"
    }
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    y: 0,
    transition: {
      duration: 0.2,
      ease: "easeOut",
      staggerChildren: 0.05
    }
  }
};

const dropdownItemVariants: Variants = {
  hidden: { opacity: 0, x: -10 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { duration: 0.15 }
  }
};

const buttonVariants: Variants = {
  idle: { 
    scale: 1, 
    y: 0
  },
  hover: { 
    scale: 1.05,
    y: -1,
    transition: { 
      type: "spring",
      stiffness: 400,
      damping: 17
    }
  },
  tap: {
    scale: 0.98,
    transition: { duration: 0.1 }
  }
};

interface AuthHeaderProps {
  userEmail?: string | null;
  showDownload?: boolean;
}

export default function AuthHeader({ userEmail, showDownload = true }: AuthHeaderProps) {
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [loggingOut, setLoggingOut] = useState(false);
  const supabase = useSupabaseClient();
  const router = useRouter();

  // Get user initial for avatar 👤
  const getUserInitial = () => {
    if (!userEmail) return 'U';
    return userEmail.charAt(0).toUpperCase();
  };

  // Handle logout 🚪
  const handleLogout = async () => {
    try {
      setLoggingOut(true);
      await supabase.auth.signOut();
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setLoggingOut(false);
    }
  };

  // Close dropdowns on escape key ⌨️
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setUserMenuOpen(false);
        setMobileMenuOpen(false);
      }
    };
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  return (
    <motion.header 
      className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b shadow-sm px-4 py-2 md:px-6 md:py-3"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ 
        type: "spring",
        stiffness: 300,
        damping: 30,
        duration: 0.6
      }}
    >
      <div className="flex justify-between items-center max-w-7xl mx-auto">
        {/* Logo and Brand 🎯 */}
        <Link href="/dashboard" className="flex items-center gap-3 cursor-pointer">
          <motion.div
            variants={logoVariants}
            initial="idle"
            animate="idle"
            whileHover="hover"
            className="scale-95 md:scale-100"
          >
            <AnimatedQuantBoostLogo 
              size="2.25rem"
              showDrawAnimation={false}
            />
          </motion.div>
          <span className="text-lg md:text-xl font-bold text-gray-900 tracking-tight">QuantBoost</span>
        </Link>

        {/* Desktop Navigation 💻 */}
        <nav className="hidden md:flex items-center gap-4">
          {/* User Menu Dropdown 👤 */}
          <div 
            className="relative"
            onMouseEnter={() => setUserMenuOpen(true)}
            onMouseLeave={() => setUserMenuOpen(false)}
          >
            <motion.button 
              className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              variants={buttonVariants}
              initial="idle"
              whileHover="hover"
              aria-expanded={userMenuOpen}
              aria-haspopup="true"
            >
              <div className="w-8 h-8 bg-emerald-500 text-white rounded-full flex items-center justify-center font-semibold text-sm">
                {getUserInitial()}
              </div>
              <ChevronDown className={`w-4 h-4 text-gray-600 transition-transform ${userMenuOpen ? 'rotate-180' : ''}`} />
            </motion.button>

            <AnimatePresence>
              {userMenuOpen && (
                <motion.div
                  variants={dropdownVariants}
                  initial="hidden"
                  animate="visible"
                  exit="hidden"
                  className="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-xl overflow-hidden"
                >
                  {/* User Info Section 📧 */}
                  <div className="px-4 py-3 border-b border-gray-100">
                    <p className="text-sm font-medium text-gray-900">Signed in as</p>
                    <p className="text-sm text-gray-600 truncate">{userEmail || 'User'}</p>
                  </div>

                  {/* Menu Items 📋 */}
                  <div className="py-1">
                    <motion.a
                      href="/dashboard"
                      className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-emerald-50 hover:text-emerald-600 transition-colors"
                      variants={dropdownItemVariants}
                      whileHover={{ x: 2 }}
                    >
                      <User className="w-4 h-4" />
                      <span>Dashboard</span>
                    </motion.a>

                    {showDownload && (
                      <motion.a
                        href="/download"
                        className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-emerald-50 hover:text-emerald-600 transition-colors"
                        variants={dropdownItemVariants}
                        whileHover={{ x: 2 }}
                      >
                        <Download className="w-4 h-4" />
                        <span>Download Add-ins</span>
                      </motion.a>
                    )}

                    <motion.a
                      href="/help"
                      className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-emerald-50 hover:text-emerald-600 transition-colors"
                      variants={dropdownItemVariants}
                      whileHover={{ x: 2 }}
                    >
                      <HelpCircle className="w-4 h-4" />
                      <span>Help Center</span>
                    </motion.a>
                  </div>

                  {/* Logout Section 🚪 */}
                  <div className="border-t border-gray-100 py-1">
                    <motion.button
                      onClick={handleLogout}
                      disabled={loggingOut}
                      className="flex items-center gap-3 w-full px-4 py-2 text-left text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors disabled:opacity-50"
                      variants={dropdownItemVariants}
                      whileHover={{ x: 2 }}
                    >
                      <LogOut className="w-4 h-4" />
                      <span>{loggingOut ? 'Logging out...' : 'Log Out'}</span>
                    </motion.button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </nav>

        {/* Mobile Menu Button 📱 */}
        <div className="flex items-center gap-3 md:hidden">
          {/* Mobile User Avatar */}
          <div className="w-8 h-8 bg-emerald-500 text-white rounded-full flex items-center justify-center font-semibold text-sm">
            {getUserInitial()}
          </div>

          {/* Hamburger Menu */}
          <motion.button
            aria-label={mobileMenuOpen ? 'Close menu' : 'Open menu'}
            aria-expanded={mobileMenuOpen}
            className="p-2 rounded-lg border border-gray-300 text-gray-700 hover:border-emerald-300 hover:text-emerald-600"
            variants={buttonVariants}
            initial="idle"
            whileHover="hover"
            whileTap="tap"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              className="w-5 h-5"
            >
              {mobileMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5M3.75 17.25h16.5" />
              )}
            </svg>
          </motion.button>
        </div>

        {/* Mobile Dropdown Menu 📱 */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.98 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -8, scale: 0.98 }}
              transition={{ duration: 0.18, ease: 'easeOut' }}
              className="md:hidden absolute left-0 right-0 top-full bg-white border-b border-gray-200 shadow-lg"
            >
              <div className="px-4 py-3">
                {/* User Info */}
                <div className="pb-3 mb-3 border-b border-gray-100">
                  <p className="text-sm font-medium text-gray-900">Signed in as</p>
                  <p className="text-sm text-gray-600 truncate">{userEmail || 'User'}</p>
                </div>

                {/* Navigation Links */}
                <div className="space-y-2">
                  <div className="h-px bg-gray-200 my-2" />
                  
                  <button
                    onClick={handleLogout}
                    disabled={loggingOut}
                    className="block w-full text-left py-2 text-red-600 font-medium disabled:opacity-50"
                  >
                    {loggingOut ? 'Logging out...' : 'Log Out'}
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.header>
  );
}