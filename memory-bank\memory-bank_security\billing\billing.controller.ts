import { Controller, Post, Body, Headers, RawBodyRequest, Req, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { StripeService } from './stripe.service';
import Stripe from 'stripe';

@Controller('billing')
export class BillingController {
  private readonly logger = new Logger(BillingController.name);

  constructor(private readonly stripeService: StripeService) {}

  /**
   * Stripe webhook endpoint with robust signature verification
   * This endpoint handles all Stripe webhook events with comprehensive security
   */
  @Post('webhook')
  async handleStripeWebhook(
    @Req() request: RawBodyRequest<Request>,
    @Headers('stripe-signature') signature: string,
  ): Promise<{ received: boolean }> {
    let event: Stripe.Event;

    try {
      // Verify webhook signature to prevent request forgery
      if (!signature) {
        this.logger.error('Missing Stripe signature header');
        throw new HttpException(
          'Missing Stripe signature header',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Get the raw body for signature verification
      const payload = request.rawBody;
      if (!payload) {
        this.logger.error('Missing request body for webhook verification');
        throw new HttpException(
          'Missing request body',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Validate webhook signature
      event = this.stripeService.validateWebhookSignature(payload, signature);

      this.logger.log(`Received verified webhook: ${event.type} (${event.id})`);

      // Process the webhook event
      await this.stripeService.processWebhookEvent(event);

      return { received: true };
    } catch (error) {
      this.logger.error(`Webhook processing failed: ${error.message}`, error.stack);

      // Send security alert for failed webhook processing
      await this.stripeService.sendSecurityAlert({
        severity: 'high',
        details: `Webhook processing failed: ${error.message}`,
        actions: ['Logged error', 'Manual review required'],
      });

      // Return appropriate HTTP status based on error type
      if (error.message.includes('signature verification failed')) {
        throw new HttpException(
          'Invalid webhook signature',
          HttpStatus.UNAUTHORIZED,
        );
      }

      throw new HttpException(
        'Webhook processing failed',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Create payment intent with comprehensive security validation
   */
  @Post('payment-intent')
  async createPaymentIntent(
    @Body() createPaymentIntentDto: CreatePaymentIntentDto,
  ): Promise<{ clientSecret: string; paymentIntentId: string }> {
    try {
      this.logger.log(`Creating payment intent for customer: ${createPaymentIntentDto.customerId}`);

      // Validate input data
      this.validateCreatePaymentIntentInput(createPaymentIntentDto);

      const paymentIntent = await this.stripeService.createPaymentIntent({
        amount: createPaymentIntentDto.amount,
        currency: createPaymentIntentDto.currency,
        customerId: createPaymentIntentDto.customerId,
        customerEmail: createPaymentIntentDto.customerEmail,
        metadata: createPaymentIntentDto.metadata,
      });

      return {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
      };
    } catch (error) {
      this.logger.error(`Failed to create payment intent: ${error.message}`, error.stack);

      // Handle specific error types
      if (error.message.includes('Blocked email pattern')) {
        throw new HttpException(
          'Invalid email address',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (error.message.includes('Rate limit exceeded')) {
        throw new HttpException(
          'Too many requests. Please try again later.',
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }

      throw new HttpException(
        'Failed to create payment intent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Create subscription with security validation
   */
  @Post('subscription')
  async createSubscription(
    @Body() createSubscriptionDto: CreateSubscriptionDto,
  ): Promise<{ subscriptionId: string; clientSecret?: string }> {
    try {
      this.logger.log(`Creating subscription for customer: ${createSubscriptionDto.customerId}`);

      // Validate input data
      this.validateCreateSubscriptionInput(createSubscriptionDto);

      const subscription = await this.stripeService.createSubscription({
        customerId: createSubscriptionDto.customerId,
        priceId: createSubscriptionDto.priceId,
        customerEmail: createSubscriptionDto.customerEmail,
        trialPeriodDays: createSubscriptionDto.trialPeriodDays,
        metadata: createSubscriptionDto.metadata,
      });

      const response: { subscriptionId: string; clientSecret?: string } = {
        subscriptionId: subscription.id,
      };

      // If the subscription requires payment, return the client secret
      if (subscription.latest_invoice) {
        const invoice = subscription.latest_invoice as Stripe.Invoice;
        if (invoice.payment_intent) {
          const paymentIntent = invoice.payment_intent as Stripe.PaymentIntent;
          response.clientSecret = paymentIntent.client_secret;
        }
      }

      return response;
    } catch (error) {
      this.logger.error(`Failed to create subscription: ${error.message}`, error.stack);

      // Handle specific error types
      if (error.message.includes('Blocked email pattern')) {
        throw new HttpException(
          'Invalid email address',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (error.message.includes('Rate limit exceeded')) {
        throw new HttpException(
          'Too many requests. Please try again later.',
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }

      throw new HttpException(
        'Failed to create subscription',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Create or retrieve customer with validation
   */
  @Post('customer')
  async createCustomer(
    @Body() createCustomerDto: CreateCustomerDto,
  ): Promise<{ customerId: string }> {
    try {
      this.logger.log(`Creating/retrieving customer: ${createCustomerDto.email}`);

      // Validate input data
      this.validateCreateCustomerInput(createCustomerDto);

      const customer = await this.stripeService.createOrRetrieveCustomer({
        email: createCustomerDto.email,
        name: createCustomerDto.name,
        metadata: createCustomerDto.metadata,
      });

      return { customerId: customer.id };
    } catch (error) {
      this.logger.error(`Failed to create customer: ${error.message}`, error.stack);

      // Handle specific error types
      if (error.message.includes('Blocked email pattern')) {
        throw new HttpException(
          'Invalid email address',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (error.message.includes('Rate limit exceeded')) {
        throw new HttpException(
          'Too many requests. Please try again later.',
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }

      throw new HttpException(
        'Failed to create customer',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Validate payment intent creation input
   */
  private validateCreatePaymentIntentInput(dto: CreatePaymentIntentDto): void {
    if (!dto.amount || dto.amount <= 0) {
      throw new HttpException(
        'Amount must be greater than 0',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!dto.currency || dto.currency.length !== 3) {
      throw new HttpException(
        'Invalid currency code',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!dto.customerId) {
      throw new HttpException(
        'Customer ID is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!dto.customerEmail || !this.isValidEmail(dto.customerEmail)) {
      throw new HttpException(
        'Valid email address is required',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Validate subscription creation input
   */
  private validateCreateSubscriptionInput(dto: CreateSubscriptionDto): void {
    if (!dto.customerId) {
      throw new HttpException(
        'Customer ID is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!dto.priceId) {
      throw new HttpException(
        'Price ID is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!dto.customerEmail || !this.isValidEmail(dto.customerEmail)) {
      throw new HttpException(
        'Valid email address is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (dto.trialPeriodDays && (dto.trialPeriodDays < 0 || dto.trialPeriodDays > 365)) {
      throw new HttpException(
        'Trial period must be between 0 and 365 days',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Validate customer creation input
   */
  private validateCreateCustomerInput(dto: CreateCustomerDto): void {
    if (!dto.email || !this.isValidEmail(dto.email)) {
      throw new HttpException(
        'Valid email address is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (dto.name && dto.name.length > 256) {
      throw new HttpException(
        'Name must be less than 256 characters',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

/**
 * DTOs for request validation
 */
interface CreatePaymentIntentDto {
  amount: number;
  currency: string;
  customerId: string;
  customerEmail: string;
  metadata?: Record<string, string>;
}

interface CreateSubscriptionDto {
  customerId: string;
  priceId: string;
  customerEmail: string;
  trialPeriodDays?: number;
  metadata?: Record<string, string>;
}

interface CreateCustomerDto {
  email: string;
  name?: string;
  metadata?: Record<string, string>;
}