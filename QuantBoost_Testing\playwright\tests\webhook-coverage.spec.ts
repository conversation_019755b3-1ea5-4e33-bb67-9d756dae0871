import { test, expect } from '../fixtures/combined.fixture';
import type Strip<PERSON> from 'stripe';

test.describe('🔄 Comprehensive Webhook Coverage Tests', () => {

  // Setup hook to ensure test profile exists
  test.beforeEach(async ({ stripeCustomer, supabaseClient }) => {
    // Create test profile in Supabase if it doesn't exist
    const { data: existingProfile } = await supabaseClient
      .from('profiles')
      .select('id')
      .eq('stripe_customer_id', stripeCustomer.id)
      .maybeSingle();
    
    if (!existingProfile && stripeCustomer.email) {
      await supabaseClient
        .from('profiles')
        .upsert({
          id: crypto.randomUUID(),
          email: stripeCustomer.email,
          full_name: 'Test Customer',
          stripe_customer_id: stripeCustomer.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    }
  });

  test.describe('💳 Dispute Management Webhooks', () => {
    
    test('🚨 charge.dispute.created - Creates dispute record and sends alert', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      // Create a charge first (this would normally be done via payment flow)
      const chargeId = `ch_test_${Date.now()}`;
      
      // Insert a charge receipt for reference
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      await supabaseClient
        .from('charge_receipts')
        .insert({
          user_id: profile.id,
          stripe_charge_id: chargeId,
          amount: 2000,
          currency: 'usd',
          status: 'succeeded'
        });

      // Simulate dispute.created webhook
      const disputeData = {
        id: `dp_test_${Date.now()}`,
        charge: chargeId,
        amount: 2000,
        currency: 'usd',
        reason: 'fraudulent',
        status: 'warning_needs_response',
        created: Math.floor(Date.now() / 1000),
        evidence_details: {
          due_by: Math.floor((Date.now() + 7 * 24 * 60 * 60 * 1000) / 1000) // 7 days from now
        }
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.dispute.created',
          data: { object: disputeData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify dispute was created in database
      const { data: dispute, error } = await supabaseClient
        .from('disputes')
        .select('*')
        .eq('stripe_dispute_id', disputeData.id)
        .single();

      expect(error).toBeNull();
      expect(dispute).toBeTruthy();
      expect(dispute.stripe_charge_id).toBe(chargeId);
      expect(dispute.amount).toBe(2000);
      expect(dispute.reason).toBe('fraudulent');
      expect(dispute.status).toBe('warning_needs_response');
    });

    test('🔄 charge.dispute.updated - Updates dispute status', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      // Create initial dispute
      const disputeId = `dp_test_${Date.now()}`;
      const chargeId = `ch_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      await supabaseClient
        .from('disputes')
        .insert({
          stripe_dispute_id: disputeId,
          stripe_charge_id: chargeId,
          user_id: profile.id,
          amount: 2000,
          currency: 'usd',
          reason: 'fraudulent',
          status: 'warning_needs_response'
        });

      // Simulate dispute.updated webhook
      const disputeData = {
        id: disputeId,
        charge: chargeId,
        status: 'under_review'
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.dispute.updated',
          data: { object: disputeData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify dispute status was updated
      const { data: updatedDispute } = await supabaseClient
        .from('disputes')
        .select('status')
        .eq('stripe_dispute_id', disputeId)
        .single();

      expect(updatedDispute.status).toBe('under_review');
    });

    test('✅ charge.dispute.closed - Marks dispute as resolved', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      // Create initial dispute
      const disputeId = `dp_test_${Date.now()}`;
      const chargeId = `ch_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      await supabaseClient
        .from('disputes')
        .insert({
          stripe_dispute_id: disputeId,
          stripe_charge_id: chargeId,
          user_id: profile.id,
          amount: 2000,
          currency: 'usd',
          reason: 'fraudulent',
          status: 'under_review'
        });

      // Simulate dispute.closed webhook
      const disputeData = {
        id: disputeId,
        charge: chargeId,
        status: 'won'
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.dispute.closed',
          data: { object: disputeData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify dispute was closed
      const { data: closedDispute } = await supabaseClient
        .from('disputes')
        .select('status, closed_at')
        .eq('stripe_dispute_id', disputeId)
        .single();

      expect(closedDispute.status).toBe('won');
      expect(closedDispute.closed_at).toBeTruthy();
    });

    test('💸 charge.dispute.funds_withdrawn - Tracks fund withdrawal', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      // Create initial dispute
      const disputeId = `dp_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      await supabaseClient
        .from('disputes')
        .insert({
          stripe_dispute_id: disputeId,
          stripe_charge_id: `ch_test_${Date.now()}`,
          user_id: profile.id,
          amount: 2000,
          currency: 'usd',
          reason: 'fraudulent',
          status: 'under_review'
        });

      // Simulate funds withdrawal webhook
      const disputeData = {
        id: disputeId,
        amount: 2000
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.dispute.funds_withdrawn',
          data: { object: disputeData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify funds withdrawal was tracked
      const { data: dispute } = await supabaseClient
        .from('disputes')
        .select('funds_withdrawn, withdrawn_at')
        .eq('stripe_dispute_id', disputeId)
        .single();

      expect(dispute.funds_withdrawn).toBe(true);
      expect(dispute.withdrawn_at).toBeTruthy();
    });

    test('💰 charge.dispute.funds_reinstated - Tracks fund reinstatement', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      // Create initial dispute with funds withdrawn
      const disputeId = `dp_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      await supabaseClient
        .from('disputes')
        .insert({
          stripe_dispute_id: disputeId,
          stripe_charge_id: `ch_test_${Date.now()}`,
          user_id: profile.id,
          amount: 2000,
          currency: 'usd',
          reason: 'fraudulent',
          status: 'won',
          funds_withdrawn: true
        });

      // Simulate funds reinstatement webhook
      const disputeData = {
        id: disputeId,
        amount: 2000
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.dispute.funds_reinstated',
          data: { object: disputeData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify funds reinstatement was tracked
      const { data: dispute } = await supabaseClient
        .from('disputes')
        .select('funds_withdrawn, reinstated_at')
        .eq('stripe_dispute_id', disputeId)
        .single();

      expect(dispute.funds_withdrawn).toBe(false);
      expect(dispute.reinstated_at).toBeTruthy();
    });
  });

  test.describe('💸 Refund Management Webhooks', () => {
    
    test('💸 charge.refunded - Updates charge with refund information', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const chargeId = `ch_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      // Create initial charge receipt
      await supabaseClient
        .from('charge_receipts')
        .insert({
          user_id: profile.id,
          stripe_charge_id: chargeId,
          amount: 2000,
          currency: 'usd',
          status: 'succeeded'
        });

      // Simulate charge refunded webhook
      const chargeData = {
        id: chargeId,
        amount: 2000,
        amount_refunded: 1000 // Partial refund
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.refunded',
          data: { object: chargeData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify charge was updated with refund information
      const { data: charge } = await supabaseClient
        .from('charge_receipts')
        .select('refunded_amount, refund_status')
        .eq('stripe_charge_id', chargeId)
        .single();

      expect(charge.refunded_amount).toBe(1000);
      expect(charge.refund_status).toBe('partial');
    });

    test('🔄 refund.created - Creates refund record', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const chargeId = `ch_test_${Date.now()}`;
      const refundId = `re_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      // Create initial charge receipt
      await supabaseClient
        .from('charge_receipts')
        .insert({
          user_id: profile.id,
          stripe_charge_id: chargeId,
          amount: 2000,
          currency: 'usd',
          status: 'succeeded'
        });

      // Simulate refund created webhook
      const refundData = {
        id: refundId,
        charge: chargeId,
        amount: 2000,
        currency: 'usd',
        status: 'succeeded',
        reason: 'requested_by_customer',
        created: Math.floor(Date.now() / 1000)
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'refund.created',
          data: { object: refundData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify refund record was created
      const { data: refund } = await supabaseClient
        .from('refunds')
        .select('*')
        .eq('stripe_refund_id', refundId)
        .single();

      expect(refund).toBeTruthy();
      expect(refund.stripe_charge_id).toBe(chargeId);
      expect(refund.amount).toBe(2000);
      expect(refund.reason).toBe('requested_by_customer');
    });

    test('❌ refund.failed - Updates refund with failure information', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const refundId = `re_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      // Create initial refund record
      await supabaseClient
        .from('refunds')
        .insert({
          stripe_refund_id: refundId,
          stripe_charge_id: `ch_test_${Date.now()}`,
          user_id: profile.id,
          amount: 2000,
          currency: 'usd',
          status: 'pending'
        });

      // Simulate refund failed webhook
      const refundData = {
        id: refundId,
        status: 'failed',
        failure_reason: 'insufficient_funds'
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'refund.failed',
          data: { object: refundData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify refund failure was recorded
      const { data: refund } = await supabaseClient
        .from('refunds')
        .select('status, failure_reason, failed_at')
        .eq('stripe_refund_id', refundId)
        .single();

      expect(refund.status).toBe('failed');
      expect(refund.failure_reason).toBe('insufficient_funds');
      expect(refund.failed_at).toBeTruthy();
    });
  });

  test.describe('⚠️ Payment Intent Failure Webhooks', () => {
    
    test('❌ payment_intent.payment_failed - Handles payment failure', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const paymentIntentId = `pi_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      // Create incomplete subscription
      const { data: subscription } = await supabaseClient
        .from('subscriptions')
        .insert({
          user_id: profile.id,
          status: 'incomplete',
          quantity: 1
        })
        .select()
        .single();

      // Simulate payment intent failed webhook
      const paymentIntentData = {
        id: paymentIntentId,
        customer: stripeCustomer.id,
        amount: 2000,
        currency: 'usd',
        status: 'requires_payment_method',
        last_payment_error: {
          message: 'Your card was declined.',
          code: 'card_declined'
        },
        created: Math.floor(Date.now() / 1000)
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'payment_intent.payment_failed',
          data: { object: paymentIntentData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify subscription status was updated
      const { data: updatedSubscription } = await supabaseClient
        .from('subscriptions')
        .select('status, last_payment_error')
        .eq('id', subscription.id)
        .single();

      expect(updatedSubscription.status).toBe('payment_failed');
      expect(updatedSubscription.last_payment_error).toBe('Your card was declined.');

      // Verify payment event was created
      const { data: paymentEvent } = await supabaseClient
        .from('payment_events')
        .select('*')
        .eq('stripe_payment_intent_id', paymentIntentId)
        .single();

      expect(paymentEvent).toBeTruthy();
      expect(paymentEvent.event_type).toBe('payment_failed');
      expect(paymentEvent.status).toBe('failed');
    });

    test('🚫 payment_intent.canceled - Handles payment cancellation', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const paymentIntentId = `pi_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      // Create incomplete subscription
      const { data: subscription } = await supabaseClient
        .from('subscriptions')
        .insert({
          user_id: profile.id,
          status: 'incomplete',
          quantity: 1
        })
        .select()
        .single();

      // Simulate payment intent canceled webhook
      const paymentIntentData = {
        id: paymentIntentId,
        customer: stripeCustomer.id,
        amount: 2000,
        currency: 'usd',
        status: 'canceled',
        cancellation_reason: 'abandoned',
        created: Math.floor(Date.now() / 1000)
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'payment_intent.canceled',
          data: { object: paymentIntentData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify subscription was canceled
      const { data: updatedSubscription } = await supabaseClient
        .from('subscriptions')
        .select('status, cancellation_reason')
        .eq('id', subscription.id)
        .single();

      expect(updatedSubscription.status).toBe('canceled');
      expect(updatedSubscription.cancellation_reason).toBe('abandoned');
    });
  });

  test.describe('🔍 Fraud Review Webhooks', () => {
    
    test('🔍 review.opened - Creates fraud review record', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const reviewId = `prv_test_${Date.now()}`;
      const chargeId = `ch_test_${Date.now()}`;

      // Simulate review opened webhook
      const reviewData = {
        id: reviewId,
        charge: chargeId,
        reason: 'rule',
        created: Math.floor(Date.now() / 1000)
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'review.opened',
          data: { object: reviewData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify fraud review was created
      const { data: review } = await supabaseClient
        .from('fraud_reviews')
        .select('*')
        .eq('stripe_review_id', reviewId)
        .single();

      expect(review).toBeTruthy();
      expect(review.stripe_charge_id).toBe(chargeId);
      expect(review.reason).toBe('rule');
      expect(review.status).toBe('open');
    });

    test('✅ review.closed - Updates fraud review as closed', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const reviewId = `prv_test_${Date.now()}`;

      // Create initial fraud review
      await supabaseClient
        .from('fraud_reviews')
        .insert({
          stripe_review_id: reviewId,
          stripe_charge_id: `ch_test_${Date.now()}`,
          reason: 'rule',
          status: 'open'
        });

      // Simulate review closed webhook
      const reviewData = {
        id: reviewId,
        closed_reason: 'approved'
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'review.closed',
          data: { object: reviewData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify review was closed
      const { data: review } = await supabaseClient
        .from('fraud_reviews')
        .select('status, closed_reason, closed_at')
        .eq('stripe_review_id', reviewId)
        .single();

      expect(review.status).toBe('closed');
      expect(review.closed_reason).toBe('approved');
      expect(review.closed_at).toBeTruthy();
    });
  });

  test.describe('⚠️ Setup Intent Failure Webhooks', () => {
    
    test('❌ setup_intent.setup_failed - Records setup failure', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const setupIntentId = `seti_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      // Simulate setup intent failed webhook
      const setupIntentData = {
        id: setupIntentId,
        customer: stripeCustomer.id,
        status: 'requires_payment_method',
        last_setup_error: {
          message: 'Your card number is invalid.',
          code: 'invalid_number'
        }
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'setup_intent.setup_failed',
          data: { object: setupIntentData }
        }
      });

      expect(response.ok()).toBeTruthy();

      // Verify setup failure was recorded
      const { data: failure } = await supabaseClient
        .from('setup_failures')
        .select('*')
        .eq('stripe_setup_intent_id', setupIntentId)
        .single();

      expect(failure).toBeTruthy();
      expect(failure.user_id).toBe(profile.id);
      expect(failure.error_message).toBe('Your card number is invalid.');
      expect(failure.error_code).toBe('invalid_number');
      expect(failure.failed_at).toBeTruthy();
    });
  });

  test.describe('🔄 Partial Capture Webhooks', () => {
    
    test('💰 payment_intent.amount_capturable_updated - Logs capturable amount changes', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const paymentIntentId = `pi_test_${Date.now()}`;

      // Simulate amount capturable updated webhook (partial capture scenario)
      const paymentIntentData = {
        id: paymentIntentId,
        customer: stripeCustomer.id,
        amount: 2000,
        amount_capturable: 1500 // Partial capture available
      };

      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'payment_intent.amount_capturable_updated',
          data: { object: paymentIntentData }
        }
      });

      expect(response.ok()).toBeTruthy();
      
      // This webhook primarily logs information, so we verify the response was successful
      // In a real implementation, you might want to track these events in payment_events table
    });
  });

  test.describe('🎯 End-to-End Webhook Flow Tests', () => {
    
    test('🔄 Complete Dispute Lifecycle - From Creation to Resolution', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const chargeId = `ch_test_${Date.now()}`;
      const disputeId = `dp_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      // Create initial charge
      await supabaseClient
        .from('charge_receipts')
        .insert({
          user_id: profile.id,
          stripe_charge_id: chargeId,
          amount: 2000,
          currency: 'usd',
          status: 'succeeded'
        });

      // Step 1: Dispute created
      await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.dispute.created',
          data: {
            object: {
              id: disputeId,
              charge: chargeId,
              amount: 2000,
              currency: 'usd',
              reason: 'fraudulent',
              status: 'warning_needs_response',
              created: Math.floor(Date.now() / 1000),
              evidence_details: {
                due_by: Math.floor((Date.now() + 7 * 24 * 60 * 60 * 1000) / 1000)
              }
            }
          }
        }
      });

      // Step 2: Funds withdrawn
      await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.dispute.funds_withdrawn',
          data: { object: { id: disputeId, amount: 2000 } }
        }
      });

      // Step 3: Dispute updated to under review
      await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.dispute.updated',
          data: { object: { id: disputeId, status: 'under_review' } }
        }
      });

      // Step 4: Dispute won, funds reinstated
      await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.dispute.funds_reinstated',
          data: { object: { id: disputeId, amount: 2000 } }
        }
      });

      // Step 5: Dispute closed
      await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.dispute.closed',
          data: { object: { id: disputeId, status: 'won' } }
        }
      });

      // Verify final state
      const { data: finalDispute } = await supabaseClient
        .from('disputes')
        .select('*')
        .eq('stripe_dispute_id', disputeId)
        .single();

      expect(finalDispute.status).toBe('won');
      expect(finalDispute.funds_withdrawn).toBe(false);
      expect(finalDispute.closed_at).toBeTruthy();
      expect(finalDispute.reinstated_at).toBeTruthy();
      expect(finalDispute.withdrawn_at).toBeTruthy();
    });

    test('💸 Complete Refund Lifecycle - From Request to Processing', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const chargeId = `ch_test_${Date.now()}`;
      const refundId = `re_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      // Create initial charge
      await supabaseClient
        .from('charge_receipts')
        .insert({
          user_id: profile.id,
          stripe_charge_id: chargeId,
          amount: 2000,
          currency: 'usd',
          status: 'succeeded'
        });

      // Step 1: Refund created
      await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'refund.created',
          data: {
            object: {
              id: refundId,
              charge: chargeId,
              amount: 2000,
              currency: 'usd',
              status: 'succeeded',
              reason: 'requested_by_customer',
              created: Math.floor(Date.now() / 1000)
            }
          }
        }
      });

      // Step 2: Charge refunded event
      await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'charge.refunded',
          data: {
            object: {
              id: chargeId,
              amount: 2000,
              amount_refunded: 2000 // Full refund
            }
          }
        }
      });

      // Verify final state
      const { data: refund } = await supabaseClient
        .from('refunds')
        .select('*')
        .eq('stripe_refund_id', refundId)
        .single();

      const { data: charge } = await supabaseClient
        .from('charge_receipts')
        .select('refunded_amount, refund_status')
        .eq('stripe_charge_id', chargeId)
        .single();

      expect(refund).toBeTruthy();
      expect(refund.status).toBe('succeeded');
      expect(charge.refunded_amount).toBe(2000);
      expect(charge.refund_status).toBe('full');
    });
  });

  test.describe('🎪 Edge Cases & Error Scenarios', () => {
    
    test('🔍 Unknown webhook type - Graceful handling', async ({ page }) => {
      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'unknown.event.type',
          data: { object: { id: 'test_123' } }
        }
      });

      expect(response.ok()).toBeTruthy();
      
      // Should not crash, just log and return success
      const responseBody = await response.json();
      expect(responseBody.received).toBe(true);
    });

    test('⚠️ Missing customer data - Handles gracefully', async ({ page }) => {
      // Simulate webhook without customer reference
      const response = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: {
          type: 'payment_intent.payment_failed',
          data: {
            object: {
              id: `pi_test_${Date.now()}`,
              amount: 2000,
              currency: 'usd',
              status: 'requires_payment_method',
              // No customer field
              last_payment_error: {
                message: 'Generic failure'
              }
            }
          }
        }
      });

      expect(response.ok()).toBeTruthy();
      // Should handle missing customer gracefully
    });

    test('🔄 Duplicate webhook processing - Idempotency protection', async ({
      stripeCustomer,
      supabaseClient,
      page
    }) => {
      const disputeId = `dp_test_${Date.now()}`;
      const chargeId = `ch_test_${Date.now()}`;
      const eventId = `evt_test_${Date.now()}`;
      
      const { data: profile } = await supabaseClient
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', stripeCustomer.id)
        .single();

      await supabaseClient
        .from('charge_receipts')
        .insert({
          user_id: profile.id,
          stripe_charge_id: chargeId,
          amount: 2000,
          currency: 'usd',
          status: 'succeeded'
        });

      const webhookData = {
        id: eventId,
        type: 'charge.dispute.created',
        data: {
          object: {
            id: disputeId,
            charge: chargeId,
            amount: 2000,
            currency: 'usd',
            reason: 'fraudulent',
            status: 'warning_needs_response',
            created: Math.floor(Date.now() / 1000),
            evidence_details: {
              due_by: Math.floor((Date.now() + 7 * 24 * 60 * 60 * 1000) / 1000)
            }
          }
        }
      };

      // Send same webhook twice
      const firstResponse = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: webhookData
      });
      
      const secondResponse = await page.request.post('/api/test/simulate-stripe-webhook', {
        data: webhookData
      });

      expect(firstResponse.ok()).toBeTruthy();
      expect(secondResponse.ok()).toBeTruthy();

      // Should only have one dispute record (idempotency protection)
      const { data: disputes } = await supabaseClient
        .from('disputes')
        .select('*')
        .eq('stripe_dispute_id', disputeId);

      expect(disputes).toHaveLength(1);
    });
  });
});