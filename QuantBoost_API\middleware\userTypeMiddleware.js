const { supabaseAdmin } = require('../supabaseClient');

/**
 * User types for access control
 */
const UserType = {
  TEAM_ADMIN: 'team_admin',
  INDIVIDUAL_SUBSCRIBER: 'individual_subscriber', 
  TEAM_LICENSEE: 'team_licensee',
  NO_LICENSE: 'no_license'
};

/**
 * Determines user type and access level based on their subscriptions and licenses
 * 
 * @param {string} userId - The user ID to check
 * @returns {Promise<Object>} User type information and access level
 */
async function determineUserType(userId) {
  try {
    // Check if user owns any subscriptions
    const { data: subscriptions, error: subscriptionsError } = await supabaseAdmin
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId);

    if (subscriptionsError) {
      console.error('Error fetching subscriptions in userType middleware:', subscriptionsError);
      throw subscriptionsError;
    }

    if (subscriptions && subscriptions.length > 0) {
      // User owns subscription(s)
      
      // Check if user owns a team subscription (quantity > 1)
      const teamSubscription = subscriptions.find(s => s.quantity > 1);
      if (teamSubscription) {
        return {
          type: UserType.TEAM_ADMIN,
          subscription: teamSubscription,
          canAccessBilling: true,
          canManageSubscription: true,
          canManageTeam: true
        };
      }

      // User has individual subscription(s)
      return {
        type: UserType.INDIVIDUAL_SUBSCRIBER,
        subscription: subscriptions[0],
        canAccessBilling: true,
        canManageSubscription: true,
        canManageTeam: false
      };
    }

    // User doesn't own subscriptions, check if they have assigned license
    const { data: license, error: licenseError } = await supabaseAdmin
      .from('licenses')
      .select(`
        *,
        subscriptions (*)
      `)
      .eq('user_id', userId)
      .single();

    if (licenseError && licenseError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching license in userType middleware:', licenseError);
      throw licenseError;
    }

    if (license) {
      return {
        type: UserType.TEAM_LICENSEE,
        license,
        canAccessBilling: false,
        canManageSubscription: false,
        canManageTeam: false
      };
    }

    // User has no subscription or license
    return {
      type: UserType.NO_LICENSE,
      canAccessBilling: false,
      canManageSubscription: false,
      canManageTeam: false
    };

  } catch (error) {
    console.error('Error in determineUserType:', error);
    throw error;
  }
}

/**
 * Middleware to determine user type and attach to request object
 * This should be used before other access control middleware
 */
const attachUserType = async (req, res, next) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    const userTypeInfo = await determineUserType(req.user.id);
    req.userType = userTypeInfo;
    
    next();
  } catch (error) {
    console.error('Error in attachUserType middleware:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to determine user access level' 
    });
  }
};

/**
 * Middleware to require billing access
 * Blocks team licensees from accessing billing endpoints
 */
const requireBillingAccess = (req, res, next) => {
  if (!req.userType) {
    return res.status(500).json({ 
      success: false, 
      error: 'User type not determined. Use attachUserType middleware first.' 
    });
  }

  if (!req.userType.canAccessBilling) {
    return res.status(403).json({ 
      success: false, 
      error: 'Access denied. Billing features are not available for your account type. Contact your team administrator for billing questions.' 
    });
  }

  next();
};

/**
 * Middleware to require subscription management access
 * Blocks team licensees from accessing subscription management endpoints
 */
const requireSubscriptionAccess = (req, res, next) => {
  if (!req.userType) {
    return res.status(500).json({ 
      success: false, 
      error: 'User type not determined. Use attachUserType middleware first.' 
    });
  }

  if (!req.userType.canManageSubscription) {
    return res.status(403).json({ 
      success: false, 
      error: 'Access denied. Subscription management is not available for your account type. Contact your team administrator for subscription changes.' 
    });
  }

  next();
};

/**
 * Middleware to require team management access
 * Blocks individual subscribers and team licensees from accessing team management endpoints
 */
const requireTeamAccess = (req, res, next) => {
  if (!req.userType) {
    return res.status(500).json({ 
      success: false, 
      error: 'User type not determined. Use attachUserType middleware first.' 
    });
  }

  if (!req.userType.canManageTeam) {
    return res.status(403).json({ 
      success: false, 
      error: 'Access denied. Team management is only available for team administrators.' 
    });
  }

  next();
};

/**
 * Middleware to require subscription ownership
 * Checks if user owns the specific subscription being accessed
 */
const requireSubscriptionOwnership = async (req, res, next) => {
  try {
    const subscriptionId = req.params.subscriptionId || req.body.subscriptionId;
    
    if (!subscriptionId) {
      return res.status(400).json({ 
        success: false, 
        error: 'Subscription ID required' 
      });
    }

    // Check if user owns this specific subscription
    const { data: subscription, error } = await supabaseAdmin
      .from('subscriptions')
      .select('*')
      .eq('id', subscriptionId)
      .eq('user_id', req.user.id)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking subscription ownership:', error);
      return res.status(500).json({ 
        success: false, 
        error: 'Failed to verify subscription ownership' 
      });
    }

    if (!subscription) {
      return res.status(403).json({ 
        success: false, 
        error: 'Access denied. You do not own this subscription.' 
      });
    }

    req.subscription = subscription;
    next();
  } catch (error) {
    console.error('Error in requireSubscriptionOwnership middleware:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to verify subscription ownership' 
    });
  }
};

/**
 * Middleware to check license access
 * Allows access if user owns the subscription OR has the license assigned to them
 */
const requireLicenseAccess = async (req, res, next) => {
  try {
    const licenseId = req.params.licenseId || req.body.licenseId;
    
    if (!licenseId) {
      return res.status(400).json({ 
        success: false, 
        error: 'License ID required' 
      });
    }

    // Check if user has access to this license (owns subscription OR is assigned the license)
    const { data: license, error } = await supabaseAdmin
      .from('licenses')
      .select(`
        *,
        subscriptions (
          user_id
        )
      `)
      .eq('id', licenseId)
      .single();

    if (error) {
      console.error('Error checking license access:', error);
      return res.status(500).json({ 
        success: false, 
        error: 'Failed to verify license access' 
      });
    }

    if (!license) {
      return res.status(404).json({ 
        success: false, 
        error: 'License not found' 
      });
    }

    // Allow access if user owns the subscription OR is assigned the license
    const ownsSubscription = license.subscriptions && license.subscriptions.user_id === req.user.id;
    const hasLicenseAssigned = license.user_id === req.user.id;

    if (!ownsSubscription && !hasLicenseAssigned) {
      return res.status(403).json({ 
        success: false, 
        error: 'Access denied. You do not have access to this license.' 
      });
    }

    req.license = license;
    req.licenseAccess = {
      isOwner: ownsSubscription,
      isAssignee: hasLicenseAssigned
    };
    
    next();
  } catch (error) {
    console.error('Error in requireLicenseAccess middleware:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to verify license access' 
    });
  }
};

module.exports = {
  UserType,
  determineUserType,
  attachUserType,
  requireBillingAccess,
  requireSubscriptionAccess,
  requireTeamAccess,
  requireSubscriptionOwnership,
  requireLicenseAccess
};