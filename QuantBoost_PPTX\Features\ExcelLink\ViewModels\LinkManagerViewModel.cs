using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Input;
using QuantBoost_Powerpoint_Addin.ExcelLink;
using QuantBoost_Powerpoint_Addin.Features.ExcelLink.Models;
using QuantBoost_Powerpoint_Addin.Features.SizeAnalyzer.Mvvm;
using QuantBoost_Shared.Utilities;

namespace QuantBoost_Powerpoint_Addin.Features.ExcelLink.ViewModels
{
    /// <summary>
    /// ViewModel for the Excel Link Manager WPF UI.
    /// Implements MVVM pattern with proper data binding and command handling.
    /// </summary>
    public class LinkManagerViewModel : INotifyPropertyChanged
    {
        #region Fields
        private readonly ExcelLinkService _excelLinkService;
        
        private ObservableCollection<LinkDisplayModel> _links;
        private LinkDisplayModel _selectedLink;
        private bool _isRefreshing;
        private string _statusText;
        private int _linkCount;
        private double _progressPercent;
        private bool _hasLinks;
        private bool _isLinkSelected;
        private bool _hasActiveLinks;
        private string _linkCountDisplay;
        #endregion

        #region Properties
        /// <summary>
        /// Gets the collection of Excel links for data binding.
        /// </summary>
        public ObservableCollection<LinkDisplayModel> Links
        {
            get => _links;
            private set
            {
                if (_links != value)
                {
                    _links = value;
                    OnPropertyChanged(nameof(Links));
                    UpdateDerivedProperties();
                }
            }
        }

        /// <summary>
        /// Gets or sets the currently selected link.
        /// </summary>
        public LinkDisplayModel SelectedLink
        {
            get => _selectedLink;
            set
            {
                if (_selectedLink != value)
                {
                    _selectedLink = value;
                    OnPropertyChanged(nameof(SelectedLink));
                    UpdateDerivedProperties();
                }
            }
        }

        /// <summary>
        /// Gets or sets whether a refresh operation is currently running.
        /// </summary>
        public bool IsRefreshing
        {
            get => _isRefreshing;
            set
            {
                if (_isRefreshing != value)
                {
                    _isRefreshing = value;
                    OnPropertyChanged(nameof(IsRefreshing));
                    UpdateCommandStates();
                }
            }
        }

        /// <summary>
        /// Gets or sets the current status text.
        /// </summary>
        public string StatusText
        {
            get => _statusText;
            set
            {
                if (_statusText != value)
                {
                    _statusText = value;
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// Gets or sets the current progress percentage.
        /// </summary>
        public double ProgressPercent
        {
            get => _progressPercent;
            set
            {
                if (_progressPercent != value)
                {
                    _progressPercent = value;
                    OnPropertyChanged(nameof(ProgressPercent));
                }
            }
        }

        /// <summary>
        /// Gets the total number of links.
        /// </summary>
        public int LinkCount
        {
            get => _linkCount;
            private set
            {
                if (_linkCount != value)
                {
                    _linkCount = value;
                    OnPropertyChanged(nameof(LinkCount));
                    LinkCountDisplay = $"Links: {_linkCount}";
                }
            }
        }

        /// <summary>
        /// Gets the formatted link count display.
        /// </summary>
        public string LinkCountDisplay
        {
            get => _linkCountDisplay;
            private set
            {
                if (_linkCountDisplay != value)
                {
                    _linkCountDisplay = value;
                    OnPropertyChanged(nameof(LinkCountDisplay));
                }
            }
        }

        /// <summary>
        /// Gets whether there are links available.
        /// </summary>
        public bool HasLinks
        {
            get => _hasLinks;
            private set
            {
                if (_hasLinks != value)
                {
                    _hasLinks = value;
                    OnPropertyChanged(nameof(HasLinks));
                    UpdateCommandStates();
                }
            }
        }

        /// <summary>
        /// Gets whether a link is currently selected.
        /// </summary>
        public bool IsLinkSelected
        {
            get => _isLinkSelected;
            private set
            {
                if (_isLinkSelected != value)
                {
                    _isLinkSelected = value;
                    OnPropertyChanged(nameof(IsLinkSelected));
                    UpdateCommandStates();
                }
            }
        }

        /// <summary>
        /// Gets whether there are active links available.
        /// </summary>
        public bool HasActiveLinks
        {
            get => _hasActiveLinks;
            private set
            {
                if (_hasActiveLinks != value)
                {
                    _hasActiveLinks = value;
                    OnPropertyChanged(nameof(HasActiveLinks));
                    UpdateCommandStates();
                }
            }
        }


        /// <summary>
        /// Gets the command to refresh the selected link.
        /// </summary>
        public ICommand RefreshSelectedCommand { get; }

        /// <summary>
        /// Gets the command to refresh all links.
        /// </summary>
        public ICommand RefreshAllCommand { get; }

        /// <summary>
        /// Gets the command to refresh only active links.
        /// </summary>
        public ICommand RefreshActiveCommand { get; }

        /// <summary>
        /// Gets the command to navigate to the PowerPoint shape.
        /// </summary>
        public ICommand GoToShapeCommand { get; }

        /// <summary>
        /// Gets the command to navigate to the Excel source.
        /// </summary>
        public ICommand GoToSourceCommand { get; }

        /// <summary>
        /// Gets the command to break the selected link.
        /// </summary>
        public ICommand BreakLinkCommand { get; }

        /// <summary>
        /// Gets the command to export links to CSV.
        /// </summary>
        public ICommand ExportCommand { get; }

        /// <summary>
        /// Gets the command to load links.
        /// </summary>
        public ICommand LoadLinksCommand { get; }
        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the LinkManagerViewModel class.
        /// </summary>
        public LinkManagerViewModel()
        {
            // Initialize the service
            if (Globals.ThisAddIn?.Application != null)
            {
                _excelLinkService = new ExcelLinkService(Globals.ThisAddIn.Application);
            }

            // Initialize collections and properties
            Links = new ObservableCollection<LinkDisplayModel>();
            StatusText = "Ready. Click 'Load Links' to refresh.";
            LinkCountDisplay = "Links: 0";

            // Initialize commands
            RefreshSelectedCommand = new RelayCommand(
                execute: async _ => await ExecuteRefreshSelectedAsync(),
                canExecute: _ => IsLinkSelected && !IsRefreshing
            );

            RefreshAllCommand = new RelayCommand(
                execute: async _ => await ExecuteRefreshAllAsync(),
                canExecute: _ => HasLinks && !IsRefreshing
            );

            RefreshActiveCommand = new RelayCommand(
                execute: async _ => await ExecuteRefreshActiveAsync(),
                canExecute: _ => HasActiveLinks && !IsRefreshing
            );

            GoToShapeCommand = new RelayCommand(
                execute: _ => ExecuteGoToShape(),
                canExecute: _ => IsLinkSelected && !IsRefreshing
            );

            GoToSourceCommand = new RelayCommand(
                execute: _ => ExecuteGoToSource(),
                canExecute: _ => IsLinkSelected && !IsRefreshing
            );

            BreakLinkCommand = new RelayCommand(
                execute: async _ => await ExecuteBreakLinkAsync(),
                canExecute: _ => IsLinkSelected && !IsRefreshing
            );

            ExportCommand = new RelayCommand(
                execute: _ => ExecuteExport(),
                canExecute: _ => HasLinks && !IsRefreshing
            );

            LoadLinksCommand = new RelayCommand(
                execute: async _ => await ExecuteLoadLinksAsync(),
                canExecute: _ => !IsRefreshing
            );
        }
        #endregion

        #region Methods
        /// <summary>
        /// Loads Excel links from the presentation.
        /// </summary>
        private async Task ExecuteLoadLinksAsync()
        {
            if (_excelLinkService == null)
            {
                StatusText = "Excel Link Service not available.";
                ToastNotifier.ShowToast("Excel Link Service not available.", 3000, System.Drawing.Color.OrangeRed);
                return;
            }

            IsRefreshing = true;
            StatusText = "Loading links...";
            ProgressPercent = 0;

            try
            {
                var chartLinks = await _excelLinkService.GetAllLinksAsync();
                
                // Clear and populate the collection
                Links.Clear();
                if (chartLinks != null)
                {
                    foreach (var chartLink in chartLinks)
                    {
                        var displayModel = new LinkDisplayModel(chartLink, async () =>
                        {
                            UpdateDerivedProperties();
                            // Persist the change when IsActive is toggled
                            await PersistActiveStateChangeAsync(chartLink);
                        });
                        Links.Add(displayModel);
                    }
                }

                LinkCount = Links.Count;
                StatusText = $"Loaded {LinkCount} links successfully.";
                ProgressPercent = 100;

                // Brief delay to show completion
                await Task.Delay(1000);
                ProgressPercent = 0;
            }
            catch (Exception ex)
            {
                StatusText = $"Error loading links: {ex.Message}";
                ToastNotifier.ShowToast("Error loading links. See logs for details.", 3000, System.Drawing.Color.Red);
                ErrorHandlingService.LogException(ex, "Error loading links in ViewModel");
            }
            finally
            {
                IsRefreshing = false;
            }
        }

        /// <summary>
        /// Refreshes the selected link.
        /// </summary>
        private async Task ExecuteRefreshSelectedAsync()
        {
            if (SelectedLink?.ChartLink == null || _excelLinkService == null) return;

            IsRefreshing = true;
            StatusText = $"Refreshing '{SelectedLink.SourceIdentifierDisplay}'...";
            ProgressPercent = 50;

            try
            {
                await _excelLinkService.RefreshChartAsync(SelectedLink.ChartLink);
                StatusText = "Refresh completed successfully.";
                ToastNotifier.ShowToast("Refresh completed.", 1500, System.Drawing.Color.Green);
                
                // Reload to show updated timestamps
                await ExecuteLoadLinksAsync();
            }
            catch (Exception ex)
            {
                StatusText = $"Refresh failed: {ex.Message}";
                ToastNotifier.ShowToast("Refresh failed. See logs for details.", 3000, System.Drawing.Color.Red);
                ErrorHandlingService.HandleException(ex, "Error refreshing selected link");
            }
            finally
            {
                IsRefreshing = false;
                ProgressPercent = 0;
            }
        }

        /// <summary>
        /// Refreshes all links.
        /// </summary>
        private async Task ExecuteRefreshAllAsync()
        {
            if (_excelLinkService == null) return;

            IsRefreshing = true;
            StatusText = "Starting bulk refresh...";

            try
            {
                var progress = ToastNotifier.CreateProgressReporter("Bulk Link Refresh", showDetails: true);
                await _excelLinkService.RefreshAllChartsAsync(progress);
                
                StatusText = "Bulk refresh completed.";
                
                // Reload to show updated timestamps
                await ExecuteLoadLinksAsync();
            }
            catch (Exception ex)
            {
                StatusText = $"Bulk refresh failed: {ex.Message}";
                ErrorHandlingService.HandleException(ex, "Error during bulk link refresh");
            }
            finally
            {
                IsRefreshing = false;
            }
        }

        /// <summary>
        /// Refreshes only active links.
        /// </summary>
        private async Task ExecuteRefreshActiveAsync()
        {
            if (_excelLinkService == null) return;

            IsRefreshing = true;
            StatusText = "Refreshing active links...";

            try
            {
                var progress = ToastNotifier.CreateProgressReporter("Active Link Refresh", showDetails: true);
                await _excelLinkService.RefreshActiveChartsAsync(progress);
                
                StatusText = "Active links refresh completed.";
                ToastNotifier.ShowToast("Active links refresh completed.", 2000, System.Drawing.Color.Green);
                
                // Reload to show updated timestamps
                await ExecuteLoadLinksAsync();
            }
            catch (Exception ex)
            {
                StatusText = $"Active links refresh failed: {ex.Message}";
                ErrorHandlingService.HandleException(ex, "Error refreshing active links");
            }
            finally
            {
                IsRefreshing = false;
            }
        }

        /// <summary>
        /// Navigates to the PowerPoint shape.
        /// </summary>
        private void ExecuteGoToShape()
        {
            if (SelectedLink?.ChartLink == null) return;

            try
            {
                var link = SelectedLink.ChartLink;
                
                using (var pptWrapper = new PowerPointComWrapper(Globals.ThisAddIn.Application))
                {
                    var slide = pptWrapper.GetSlideByIndex(link.SlideIndex);
                    if (slide != null)
                    {
                        try { slide.Select(); } catch { /* Ignore slide selection errors */ }

                        var shape = pptWrapper.GetShapeById(slide, link.PowerPointShapeId);
                        if (shape != null)
                        {
                            try { shape.Select(Microsoft.Office.Core.MsoTriState.msoTrue); } catch { /* Ignore shape selection errors */ }
                            StatusText = $"Navigated to shape on slide {link.SlideIndex}.";
                        }
                        else
                        {
                            StatusText = $"Shape '{link.PowerPointShapeId}' not found on slide {link.SlideIndex}.";
                            ToastNotifier.ShowToast($"Shape not found on slide {link.SlideIndex}.", 2500, System.Drawing.Color.Orange);
                        }
                    }
                    else
                    {
                        StatusText = $"Slide {link.SlideIndex} not found.";
                        ToastNotifier.ShowToast($"Slide {link.SlideIndex} not found.", 2500, System.Drawing.Color.Orange);
                    }
                }
            }
            catch (Exception ex)
            {
                StatusText = "Error navigating to shape.";
                ErrorHandlingService.HandleException(ex, "Error navigating to shape");
            }
        }

        /// <summary>
        /// Navigates to the Excel source.
        /// </summary>
        private void ExecuteGoToSource()
        {
            if (SelectedLink?.ChartLink == null) return;

            // Capture PowerPoint window state before Excel operations
            var powerPointFocusManager = new QuantBoost_Powerpoint_Addin.ExcelLink.PowerPointFocusManager();

            try
            {
                var link = SelectedLink.ChartLink;

                if (string.IsNullOrEmpty(link.SourceFilePath))
                {
                    ToastNotifier.ShowToast("Source file path is missing.", 2500, System.Drawing.Color.Orange);
                    return;
                }

                // Check if source file is accessible (handles both local files and SharePoint URLs)
                if (!IsSourceFileAccessible(link.SourceFilePath))
                {
                    string fileType = IsSharePointUrl(link.SourceFilePath) ? "SharePoint file" : "local file";
                    ToastNotifier.ShowToast($"Source {fileType} not accessible:\n{link.SourceFilePath}", 3000, System.Drawing.Color.OrangeRed);
                    return;
                }

                string sourceType = IsSharePointUrl(link.SourceFilePath) ? "SharePoint" : "local";
                StatusText = $"Opening {sourceType} Excel file...";

                ErrorHandlingService.LogException(null, $"GoToSource: Attempting to open {sourceType} file: {link.SourceFilePath}");

                using (var excelWrapper = ExcelComWrapper.CreateForGoToSource())
                {
                    var workbook = excelWrapper.OpenWorkbook(link.SourceFilePath);
                    if (workbook == null)
                    {
                        string errorMsg = $"Failed to open {sourceType} workbook: {link.SourceFilePath}";
                        ErrorHandlingService.LogException(null, $"GoToSource: {errorMsg}");
                        throw new Exception(errorMsg);
                    }

                    ErrorHandlingService.LogException(null, $"GoToSource: Successfully opened workbook, looking for worksheet: {link.WorksheetName}");

                    var worksheet = excelWrapper.GetWorksheet(link.WorksheetName);
                    if (worksheet == null)
                    {
                        string errorMsg = $"Worksheet '{link.WorksheetName}' not found in {sourceType} file.";
                        ErrorHandlingService.LogException(null, $"GoToSource: {errorMsg}");
                        throw new Exception(errorMsg);
                    }

                    ErrorHandlingService.LogException(null, $"GoToSource: Found worksheet, making Excel visible and activating worksheet");

                    // Ensure Excel is visible and properly activated
                    excelWrapper.MakeVisible();
                    excelWrapper.ActivateExcelWindow();

                    // Navigate to specific object based on link type
                    if (link.LinkType == "Chart" && !string.IsNullOrEmpty(link.ChartNameOrId))
                    {
                        ErrorHandlingService.LogException(null, $"GoToSource: Navigating to chart: {link.ChartNameOrId}");
                        var chartObject = excelWrapper.GetChartObject(link.WorksheetName, link.ChartNameOrId);
                        if (chartObject != null)
                        {
                            try
                            {
                                chartObject.Select();
                                ErrorHandlingService.LogException(null, $"GoToSource: Successfully selected chart: {link.ChartNameOrId}");
                            }
                            finally
                            {
                                // Release the chart object COM reference
                                ExcelComWrapper.ReleaseComObject(chartObject);
                            }
                        }
                        else
                        {
                            ErrorHandlingService.LogException(null, $"GoToSource: Chart not found: {link.ChartNameOrId}");
                        }
                    }
                    else if (!string.IsNullOrEmpty(link.SourceRange))
                    {
                        ErrorHandlingService.LogException(null, $"GoToSource: Navigating to range: {link.SourceRange}");
                        var range = excelWrapper.GetRange(link.WorksheetName, link.SourceRange);
                        if (range != null)
                        {
                            try
                            {
                                range.Select();
                                ErrorHandlingService.LogException(null, $"GoToSource: Successfully selected range: {link.SourceRange}");
                            }
                            finally
                            {
                                // Release the range COM reference
                                ExcelComWrapper.ReleaseComObject(range);
                            }
                        }
                        else
                        {
                            ErrorHandlingService.LogException(null, $"GoToSource: Range not found: {link.SourceRange}");
                        }
                    }

                    // Final activation to ensure Excel window is in foreground
                    excelWrapper.ActivateExcelWindow();

                    StatusText = $"Successfully navigated to {sourceType} source in Excel.";
                    ToastNotifier.ShowToast($"Successfully opened {sourceType} file in Excel.", 2000, System.Drawing.Color.Green);
                    ErrorHandlingService.LogException(null, $"GoToSource: Operation completed successfully for {sourceType} file");

                    // Restore PowerPoint focus after successful navigation (fire and forget)
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await powerPointFocusManager.RestorePowerPointFocusAsync();
                        }
                        catch (Exception focusEx)
                        {
                            ErrorHandlingService.LogException(focusEx, "Failed to restore PowerPoint focus after Go to Source success");
                        }
                        finally
                        {
                            powerPointFocusManager?.Dispose();
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                var link = SelectedLink.ChartLink;
                string sourceType = IsSharePointUrl(link.SourceFilePath) ? "SharePoint" : "local";

                StatusText = $"Error navigating to {sourceType} source.";
                ErrorHandlingService.LogException(ex, $"GoToSource: Error navigating to {sourceType} source: {link.SourceFilePath}");

                // Provide specific error messages for different scenarios
                string errorMessage = $"Failed to open {sourceType} Excel file.";
                if (IsSharePointUrl(link.SourceFilePath))
                {
                    if (ex.Message.Contains("authentication") || ex.Message.Contains("access") || ex.Message.Contains("permission"))
                    {
                        errorMessage = "Authentication or permission error accessing SharePoint file. Please ensure you're signed in to Office and have access to the file.";
                    }
                    else if (ex.Message.Contains("network") || ex.Message.Contains("connection"))
                    {
                        errorMessage = "Network error accessing SharePoint file. Please check your internet connection.";
                    }
                    else
                    {
                        errorMessage = "Error accessing SharePoint file. The file may have been moved, deleted, or you may not have permission to access it.";
                    }
                }

                ToastNotifier.ShowToast(errorMessage, 4000, System.Drawing.Color.Red);

                // Restore PowerPoint focus even on error (fire and forget)
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await powerPointFocusManager.RestorePowerPointFocusAsync();
                    }
                    catch (Exception focusEx)
                    {
                        ErrorHandlingService.LogException(focusEx, "Failed to restore PowerPoint focus after Go to Source error");
                    }
                    finally
                    {
                        powerPointFocusManager?.Dispose();
                    }
                });
            }
        }

        /// <summary>
        /// Breaks the selected link.
        /// </summary>
        private async Task ExecuteBreakLinkAsync()
        {
            if (SelectedLink?.ChartLink == null || _excelLinkService == null) return;

            var link = SelectedLink.ChartLink;
            var confirmResult = MessageBox.Show(
                $"Are you sure you want to break the link for '{SelectedLink.SourceIdentifierDisplay}' on slide {link.SlideIndex}?\n\n" +
                "This will remove the link metadata but keep the shape as a static object. This action cannot be undone.",
                "Confirm Break Link", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2);

            if (confirmResult != DialogResult.Yes) return;

            IsRefreshing = true;
            StatusText = $"Breaking link for '{SelectedLink.SourceIdentifierDisplay}'...";

            try
            {
                var success = await _excelLinkService.BreakLinkAsync(link);
                if (success)
                {
                    StatusText = "Link broken successfully.";
                    ToastNotifier.ShowToast("Link broken successfully. Shape remains as static object.", 2000, System.Drawing.Color.Green);
                    
                    // Reload to remove the broken link from the list
                    await ExecuteLoadLinksAsync();
                }
                else
                {
                    StatusText = "Failed to break link.";
                    ToastNotifier.ShowToast("Failed to break link. See logs for details.", 3000, System.Drawing.Color.Red);
                }
            }
            catch (Exception ex)
            {
                StatusText = "Error breaking link.";
                ErrorHandlingService.HandleException(ex, "Error breaking link");
            }
            finally
            {
                IsRefreshing = false;
            }
        }

        /// <summary>
        /// Exports links to CSV.
        /// </summary>
        private void ExecuteExport()
        {
            if (_excelLinkService == null || !HasLinks) return;

            try
            {
                var chartLinks = Links.Select(l => l.ChartLink).ToList();
                var task = _excelLinkService.ExportLinksToCsvAsync(chartLinks);
                
                if (task.Result)
                {
                    StatusText = "Links exported successfully.";
                    ToastNotifier.ShowToast("Links exported successfully.", 2000, System.Drawing.Color.Green);
                }
                else
                {
                    StatusText = "Export was cancelled or failed.";
                }
            }
            catch (Exception ex)
            {
                StatusText = "Error exporting links.";
                ErrorHandlingService.HandleException(ex, "Error exporting links");
            }
        }

        /// <summary>
        /// Persists the active state change for a chart link back to the presentation metadata.
        /// </summary>
        /// <param name="chartLink">The chart link whose active state was changed.</param>
        private async Task PersistActiveStateChangeAsync(ChartLink chartLink)
        {
            if (_excelLinkService == null) return;

            try
            {
                // Update the metadata in the presentation
                await _excelLinkService.UpdateLinkMetadataAsync(chartLink);
                // Don't update status text for every checkbox change as it would be too noisy
                // StatusText = $"Updated active state for {chartLink.SourceIdentifierDisplay}";
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error updating link active state");
                StatusText = "Failed to update link state";
            }
        }

        /// <summary>
        /// Clears all links from the collection.
        /// </summary>
        public void ClearLinks()
        {
            Links.Clear();
            LinkCount = 0;
            StatusText = "Links cleared.";
        }

        /// <summary>
        /// Refreshes the links collection from a provided list (used by automatic cleanup)
        /// </summary>
        /// <param name="chartLinks">The updated list of chart links</param>
        public void RefreshLinksFromService(System.Collections.Generic.List<ChartLink> chartLinks)
        {
            try
            {
                // Clear and populate the collection
                Links.Clear();
                if (chartLinks != null)
                {
                    foreach (var chartLink in chartLinks)
                    {
                        var displayModel = new LinkDisplayModel(chartLink, async () =>
                        {
                            UpdateDerivedProperties();
                            // Persist the change when IsActive is toggled
                            await PersistActiveStateChangeAsync(chartLink);
                        });
                        Links.Add(displayModel);
                    }
                }

                LinkCount = Links.Count;
                UpdateDerivedProperties();

                // Only update status if this was called automatically (not during manual refresh)
                if (!IsRefreshing)
                {
                    StatusText = $"Links automatically updated ({LinkCount} links).";
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error refreshing links from service");
                if (!IsRefreshing)
                {
                    StatusText = "Error updating links automatically.";
                }
            }
        }

        /// <summary>
        /// Updates derived properties based on current state.
        /// </summary>
        private void UpdateDerivedProperties()
        {
            HasLinks = Links?.Count > 0;
            IsLinkSelected = SelectedLink != null;
            HasActiveLinks = Links?.Any(l => l.IsActive) == true;
            LinkCount = Links?.Count ?? 0;
        }

        /// <summary>
        /// Updates command states when properties change.
        /// </summary>
        private void UpdateCommandStates()
        {
            (RefreshSelectedCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (RefreshAllCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (RefreshActiveCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (GoToShapeCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (GoToSourceCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (BreakLinkCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ExportCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (LoadLinksCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }
        #endregion

        #region INotifyPropertyChanged Implementation
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Checks if a file path is a SharePoint URL
        /// </summary>
        /// <param name="filePath">The file path to check</param>
        /// <returns>True if the path is a SharePoint URL, false otherwise</returns>
        private bool IsSharePointUrl(string filePath)
        {
            if (string.IsNullOrEmpty(filePath)) return false;

            return Uri.TryCreate(filePath, UriKind.Absolute, out Uri uri) &&
                   (uri.Scheme == "http" || uri.Scheme == "https");
        }

        /// <summary>
        /// Checks if a source file is accessible (handles both local files and SharePoint URLs)
        /// </summary>
        /// <param name="filePath">The file path to check</param>
        /// <returns>True if the file is accessible, false otherwise</returns>
        private bool IsSourceFileAccessible(string filePath)
        {
            if (string.IsNullOrEmpty(filePath)) return false;

            try
            {
                // Check if this is a SharePoint URL
                if (IsSharePointUrl(filePath))
                {
                    // For SharePoint URLs, we assume they're accessible since Excel COM will handle authentication
                    // In a production scenario, you might want to add a lightweight HTTP HEAD request here
                    ErrorHandlingService.LogException(null, $"GoToSource: Treating SharePoint URL as accessible: {filePath}");
                    return true;
                }
                else
                {
                    // Local file - use File.Exists() check
                    bool exists = System.IO.File.Exists(filePath);
                    ErrorHandlingService.LogException(null, $"GoToSource: Local file exists = {exists}: {filePath}");
                    return exists;
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"GoToSource: Error checking file accessibility: {filePath}");
                return false;
            }
        }
        #endregion
    }
}