import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';

// Remove top-level clients; initialize inside handlers to prevent build-time secret access

// POST - Create invoice for enterprise customer
export async function POST(req: NextRequest) {
  try {
    const env = (globalThis as any).process?.env;
    const supabaseUrl = env?.NEXT_PUBLIC_SUPABASE_URL as string | undefined;
    const supabaseKey = env?.SUPABASE_SERVICE_KEY as string | undefined;
    const stripeKey = env?.STRIPE_SECRET_KEY as string | undefined;

    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ error: 'Server configuration error (Supabase not configured)' }, { status: 500 });
    }
    if (!stripeKey) {
      return NextResponse.json({ error: 'Server configuration error (Stripe not configured)' }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);
  const stripe = new Stripe(stripeKey, { apiVersion: '2025-08-27.basil' as any });

    // Get user session to verify sales role
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid authentication' }, { status: 401 });
    }

    // Check if user has sales or admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['sales', 'admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await req.json();
    const {
      enterprise_customer_id,
      description,
      amount_due,
      currency = 'USD',
      due_days = 30,
      line_items
    } = body;

    // Get enterprise customer details
    const { data: customer, error: customerError } = await supabase
      .from('enterprise_customers')
      .select('*')
      .eq('id', enterprise_customer_id)
      .single();

    if (customerError || !customer) {
      return NextResponse.json({ error: 'Enterprise customer not found' }, { status: 404 });
    }

    // Calculate due date
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + (Number(due_days) || 30));

    // Create Stripe invoice
    const stripeInvoice = await stripe.invoices.create({
      customer: customer.stripe_customer_id,
      collection_method: 'send_invoice',
      days_until_due: Number(due_days) || 30,
      description,
      currency,
      metadata: {
        enterprise_customer_id,
        created_by: user.id,
        license_quantity: customer.license_quantity.toString(),
        license_tier: customer.license_tier,
      },
      auto_advance: false, // Don't auto-finalize
    });

    // Add line items to the invoice
    if (line_items && line_items.length > 0) {
      for (const item of line_items) {
        await stripe.invoiceItems.create({
          customer: customer.stripe_customer_id,
          invoice: stripeInvoice.id,
          description: item.description,
          amount: Math.round(Number(item.amount) * 100), // Convert to cents
          currency,
          quantity: Number(item.quantity) || 1,
        });
      }
    } else {
      // Default line item for license subscription
      await stripe.invoiceItems.create({
        customer: customer.stripe_customer_id,
        invoice: stripeInvoice.id,
        description: `${customer.license_tier} License Subscription - ${customer.license_quantity} seats`,
        amount: Math.round(Number(amount_due) * 100), // Convert to cents
        currency,
        quantity: 1,
      });
    }

    // Finalize the invoice
    const finalizedInvoice = await stripe.invoices.finalizeInvoice(stripeInvoice.id!);

    // Create enterprise invoice record
    const { data: dbInvoice, error: invoiceError } = await supabase
      .from('enterprise_invoices')
      .insert({
        enterprise_customer_id,
        stripe_invoice_id: finalizedInvoice.id!,
        invoice_number: finalizedInvoice.number,
        amount_due: Number(amount_due),
        currency,
        due_date: dueDate.toISOString(),
        status: finalizedInvoice.status || 'sent',
        stripe_hosted_url: finalizedInvoice.hosted_invoice_url,
        stripe_pdf_url: finalizedInvoice.invoice_pdf,
        description,
        line_items,
        created_by: user.id,
      })
      .select()
      .single();

    if (invoiceError) {
      console.error('Error saving invoice to database:', invoiceError);
      // Don't fail the request, just log the error
    }

    // Send the invoice
    await stripe.invoices.sendInvoice(finalizedInvoice.id!);

    return NextResponse.json({ 
      success: true, 
      invoice: {
        id: dbInvoice?.id,
        stripe_invoice_id: finalizedInvoice.id!,
        invoice_number: finalizedInvoice.number,
        amount_due: Number(amount_due),
        status: finalizedInvoice.status,
        hosted_url: finalizedInvoice.hosted_invoice_url,
        pdf_url: finalizedInvoice.invoice_pdf,
      }
    });
  } catch (error) {
    console.error('Error creating enterprise invoice:', error);
    return NextResponse.json({ 
      error: 'Failed to create invoice',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET - List invoices for enterprise customer
export async function GET(req: NextRequest) {
  try {
    const env = (globalThis as any).process?.env;
    const supabaseUrl = env?.NEXT_PUBLIC_SUPABASE_URL as string | undefined;
    const supabaseKey = env?.SUPABASE_SERVICE_KEY as string | undefined;

    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ error: 'Server configuration error (Supabase not configured)' }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    const { searchParams } = new URL(req.url);
    const customerId = searchParams.get('customer_id');

    if (!customerId) {
      return NextResponse.json({ error: 'Customer ID required' }, { status: 400 });
    }

    // Get user session to verify sales role
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid authentication' }, { status: 401 });
    }

    // Check if user has sales or admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['sales', 'admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Fetch invoices for enterprise customer
    const { data: invoices, error } = await supabase
      .from('enterprise_invoices')
      .select(`
        *,
        enterprise_customer:enterprise_customer_id(company_name, contact_email),
        created_by_user:created_by(email, first_name, last_name)
      `)
      .eq('enterprise_customer_id', customerId)
      .order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(invoices);
  } catch (error) {
    console.error('Error fetching enterprise invoices:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
