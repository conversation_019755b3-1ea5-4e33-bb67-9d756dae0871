# Stripe API Upgrade: 2025-08-27.basil

This document tracks deployment notes for upgrading QuantBoost to Stripe API version 2025-08-27.basil.

Checklist for staging:
- [ ] Verify Dashboard webhook endpoints are set to 2025-08-27.basil (or Latest) for:
  - Next.js webhook: /api/webhooks/stripe
  - Azure SWA Function: api/webhooks/stripe
- [ ] Run Stripe CLI to send test events to both endpoints:
  - checkout.session.completed
  - invoice.payment_succeeded
  - customer.subscription.updated
- [ ] Confirm events are accepted (no signature issues) and processed once (idempotent).
- [ ] Smoke test flows end-to-end:
  - Checkout one-time and subscription
  - Update payment method and create setup intent
  - Customer Portal session
  - Upcoming invoice endpoint behavior
- [ ] Monitor logs for 48–72h post-release; compare payment failure rate to baseline.

Notes:
- We pinned `apiVersion: '2025-08-27.basil'` at all Stripe client instantiations.
- Some initializations use `as any` to satisfy SDK union types until the SDK publishes updated typings.
- No code paths rely on removed `total_count` expansion or legacy Upcoming Invoice endpoints.
