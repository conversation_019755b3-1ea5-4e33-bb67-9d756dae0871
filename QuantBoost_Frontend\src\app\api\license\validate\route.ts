import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseAdmin } from '../../../../../SupabaseServerClient';

export async function POST(req: NextRequest) {
  // Create Supabase admin client inside the function to avoid build-time issues
  const supabaseAdmin = getSupabaseAdmin();
  try {
    const { licenseKey, machineId, productId } = await req.json();
    if (!licenseKey || !machineId || !productId) {
      return NextResponse.json({
        isValid: false,
        status: 'INVALID_REQUEST',
        message: 'Missing licenseKey, machineId, or productId',
        expiryDateUtc: null,
        productName: null
      });
    }

    // Fetch license by key (including joined subscription)
    const { data: license, error } = await supabaseAdmin
      .from('licenses')
      .select('*, subscriptions(*)')
      .eq('license_key', licenseKey)
      .maybeSingle();

    if (error || !license) {
      return NextResponse.json({
        isValid: false,
        status: 'INVALID_KEY',
        message: 'License key not found',
        expiryDateUtc: null,
        productName: null
      });
    }

    // Defensive: check product match
    if (license.product_id !== productId) {
      return NextResponse.json({
        isValid: false,
        status: 'PRODUCT_MISMATCH',
        message: 'License not valid for this product',
        expiryDateUtc: null,
        productName: null
      });
    }

    // Evaluate subscription
    const sub = license.subscriptions;
    const now = new Date();
    let expiryDate = sub?.current_period_end ? new Date(sub.current_period_end) : null;
    let trialEnd = sub?.trial_end ? new Date(sub.trial_end) : null;

    if (sub) {
      if (
        sub.status === 'canceled'
        || sub.status === 'unpaid'
        || sub.status === 'incomplete_expired'
        || sub.status === 'incomplete'
        || sub.status === 'past_due'
      ) {
        return NextResponse.json({
          isValid: false,
          status: 'EXPIRED',
          message: 'Subscription expired or inactive',
          expiryDateUtc: expiryDate?.toISOString() ?? null,
          productName: license.product_id
        });
      }
      if (trialEnd && now > trialEnd) {
        return NextResponse.json({
          isValid: false,
          status: 'TRIAL_EXPIRED',
          message: 'Trial period expired',
          expiryDateUtc: trialEnd.toISOString(),
          productName: license.product_id
        });
      }
      if (expiryDate && now > expiryDate) {
        return NextResponse.json({
          isValid: false,
          status: 'EXPIRED',
          message: 'Subscription expired',
          expiryDateUtc: expiryDate.toISOString(),
          productName: license.product_id
        });
      }
    }

    // Check activation count
    const { data: activations } = await supabaseAdmin
      .from('license_activations')
      .select('*')
      .eq('license_id', license.id)
      .eq('is_active', true);

    const existingActivation = activations?.find(a => a.machine_id === machineId);

    if (!existingActivation && activations && activations.length >= license.max_activations) {
      return NextResponse.json({
        isValid: false,
        status: 'ACTIVATION_LIMIT_REACHED',
        message: 'Activation limit reached',
        expiryDateUtc: expiryDate?.toISOString() ?? null,
        productName: license.product_id
      });
    }

    // Upsert or update activation record
    if (!existingActivation) {
      await supabaseAdmin.from('license_activations').insert({
        license_id: license.id,
        machine_id: machineId,
        is_active: true,
        last_validated_at: new Date().toISOString()
      });
    } else {
      await supabaseAdmin.from('license_activations')
        .update({ last_validated_at: new Date().toISOString(), is_active: true })
        .eq('id', existingActivation.id);
    }

    // Provide licensing status
    return NextResponse.json({
      isValid: true,
      status: sub?.trial_end && now < new Date(sub.trial_end) ? 'TRIAL_ACTIVE' : 'ACTIVE',
      message: 'License valid',
      expiryDateUtc: expiryDate?.toISOString() ?? null,
      // If you want to show productName from your own reference,
      // you can use license.product_id or join with product table
      productName: license.product_id
    });
  } catch (err) {
    console.error('License validation error', err);
    return NextResponse.json({
      isValid: false,
      status: 'ERROR',
      message: 'Internal server error',
      expiryDateUtc: null,
      productName: null
    });
  }
}
