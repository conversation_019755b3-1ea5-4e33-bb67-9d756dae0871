<UserControl x:Class="QuantBoost_Powerpoint_Addin.Features.ExcelLink.Views.LinkManagerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:viewmodels="clr-namespace:QuantBoost_Powerpoint_Addin.Features.ExcelLink.ViewModels"
             xmlns:converters="clr-namespace:QuantBoost_Powerpoint_Addin.Features.ExcelLink.Converters"
             FontFamily="Segoe UI" 
             Background="#F5F7FA"
             TextOptions.TextFormattingMode="Ideal"
             UseLayoutRounding="True">
    
    <UserControl.DataContext>
        <viewmodels:LinkManagerViewModel />
    </UserControl.DataContext>
    
    <UserControl.Resources>
        <!-- Define QuantBoost color palette -->
        <SolidColorBrush x:Key="PrimaryBlueBrush" Color="#577BF9"/>
        <SolidColorBrush x:Key="HeaderBackgroundBrush" Color="#F0F2F5"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#DDE3EA"/>
        
        <!-- Value Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:LinkTypeToIconConverter x:Key="LinkTypeToIconConverter"/>
        <converters:ErrorStateToVisibilityConverter x:Key="ErrorStateToVisibilityConverter"/>
        
        <!-- DataGrid Styles -->
        <!-- Default column header style (left-aligned for text columns) -->
        <Style x:Key="LeftAlignedColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="{StaticResource HeaderBackgroundBrush}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
        </Style>

        <!-- Right-aligned column header style for numerical columns -->
        <Style x:Key="RightAlignedColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="{StaticResource HeaderBackgroundBrush}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>

        <!-- Center-aligned column header style -->
        <Style x:Key="CenterAlignedColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="{StaticResource HeaderBackgroundBrush}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
        </Style>

        <!-- Default style for all column headers (left-aligned) -->
        <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource LeftAlignedColumnHeaderStyle}"/>

        <!-- Reusable style for right-aligned data cells -->
        <Style x:Key="RightAlignedCellStyle" TargetType="TextBlock">
            <Setter Property="HorizontalAlignment" Value="Right"/>
        </Style>

        <!-- Reusable style for center-aligned data cells -->
        <Style x:Key="CenterAlignedCellStyle" TargetType="TextBlock">
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>
        
        <Style TargetType="DataGridRow">
            <Setter Property="Background" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F8F9FA"/>
                </Trigger>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="{StaticResource PrimaryBlueBrush}"/>
                    <Setter Property="Foreground" Value="White"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBlueBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="3"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#4A6CF7"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3B5BF5"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC"/>
                                <Setter Property="Foreground" Value="#888888"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Secondary Button Style -->
        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBlueBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBlueBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F8F9FA"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#E9ECEF"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Foreground" Value="#CCCCCC"/>
                                <Setter Property="BorderBrush" Value="#CCCCCC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Header -->
            <RowDefinition Height="Auto"/> <!-- Toolbar -->
            <RowDefinition Height="*"/>    <!-- DataGrid -->
            <RowDefinition Height="Auto"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <StackPanel Grid.Row="0" Margin="0,0,0,15">
            <TextBlock Text="Excel Link Manager"
                       FontSize="18"
                       FontWeight="Light"
                       Foreground="#34495E"
                       Margin="0,0,0,5"/>
            <TextBlock Text="Manage dynamic links between PowerPoint and Excel"
                       FontSize="10"
                       FontWeight="Normal"
                       Foreground="#7F8C8D"
                       Margin="0,0,0,10"/>
        </StackPanel>

        <!-- Toolbar Section with WrapPanel for better layout -->
        <WrapPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15">
            <!-- Load Links Button -->
            <Button Content="🔄 Load Links"
                    Command="{Binding LoadLinksCommand}"
                    Style="{StaticResource ModernButtonStyle}"
                    ToolTip="Load Excel links from the presentation"
                    Margin="0,0,8,5"/>

            <!-- Primary Actions -->
            <Button Content="Refresh Selected"
                    Command="{Binding RefreshSelectedCommand}"
                    IsEnabled="{Binding IsLinkSelected}"
                    Style="{StaticResource ModernButtonStyle}"
                    ToolTip="Refresh the selected Excel link"
                    Margin="0,0,8,5"/>
            
            <Button Content="Refresh All"
                    Command="{Binding RefreshAllCommand}"
                    IsEnabled="{Binding HasLinks}"
                    Style="{StaticResource ModernButtonStyle}"
                    ToolTip="Refresh all Excel links"
                    Margin="0,0,8,5"/>
            
            <Button Content="Refresh Active"
                    Command="{Binding RefreshActiveCommand}"
                    IsEnabled="{Binding HasActiveLinks}"
                    Style="{StaticResource ModernButtonStyle}"
                    ToolTip="Refresh only active Excel links (with checkboxes checked)"
                    Margin="0,0,15,5"/>

            <!-- Secondary Actions -->
            <Button Content="Go to Shape"
                    Command="{Binding GoToShapeCommand}"
                    IsEnabled="{Binding IsLinkSelected}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    ToolTip="Navigate to the linked shape in PowerPoint"
                    Margin="0,0,8,5"/>
            
            <Button Content="Go to Source"
                    Command="{Binding GoToSourceCommand}"
                    IsEnabled="{Binding IsLinkSelected}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    ToolTip="Open Excel and navigate to the source data"
                    Margin="0,0,8,5"/>
            
            <Button Content="Break Link"
                    Command="{Binding BreakLinkCommand}"
                    IsEnabled="{Binding IsLinkSelected}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    ToolTip="Remove the Excel link metadata (keeps the shape)"
                    Margin="0,0,8,5"/>

            <Button Content="Export CSV"
                    Command="{Binding ExportCommand}"
                    IsEnabled="{Binding HasLinks}"
                    Style="{StaticResource SecondaryButtonStyle}"
                    ToolTip="Export link details to CSV file"
                    Margin="0,0,0,5"/>
        </WrapPanel>

        <!-- Main DataGrid -->
        <DataGrid Grid.Row="2" 
                  ItemsSource="{Binding Links}" 
                  SelectedItem="{Binding SelectedLink}"
                  AutoGenerateColumns="False"
                  IsReadOnly="False"
                  GridLinesVisibility="Horizontal" 
                  BorderThickness="1"
                  BorderBrush="{StaticResource BorderBrush}"
                  Background="White"
                  RowHeaderWidth="0"
                  CanUserReorderColumns="False"
                  CanUserResizeRows="False"
                  CanUserSortColumns="True"
                  SelectionMode="Single">
            
            <DataGrid.Columns>
                <!-- Active CheckBox Column -->
                <DataGridCheckBoxColumn Header="Active"
                                      Binding="{Binding IsActive, Mode=TwoWay}"
                                      Width="70"
                                      CanUserSort="True"
                                      HeaderStyle="{StaticResource CenterAlignedColumnHeaderStyle}"
                                      IsReadOnly="False"/>

                <!-- Type Column with Icon -->
                <DataGridTemplateColumn Header="Type" Width="140" CanUserSort="True"
                                       HeaderStyle="{StaticResource LeftAlignedColumnHeaderStyle}"
                                       SortMemberPath="LinkType"
                                       IsReadOnly="True">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{Binding LinkType, Converter={StaticResource LinkTypeToIconConverter}}"
                                          FontSize="14"
                                          Margin="0,0,5,0"
                                          VerticalAlignment="Center"/>
                                <TextBlock Text="{Binding LinkType}"
                                          VerticalAlignment="Center"/>
                                <TextBlock Text="⚠️"
                                          FontSize="12"
                                          Foreground="Orange"
                                          Margin="5,0,0,0"
                                          VerticalAlignment="Center"
                                          ToolTip="{Binding ErrorState}"
                                          Visibility="{Binding HasError, Converter={StaticResource ErrorStateToVisibilityConverter}}"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <!-- Slide Column -->
                <DataGridTextColumn Header="Slide"
                                   Binding="{Binding SlideIndex}"
                                   Width="60"
                                   CanUserSort="True"
                                   HeaderStyle="{StaticResource CenterAlignedColumnHeaderStyle}"
                                   ElementStyle="{StaticResource CenterAlignedCellStyle}"
                                   IsReadOnly="True"/>

                <!-- Source Column -->
                <DataGridTextColumn Header="Source"
                                   Binding="{Binding SourceIdentifierDisplay}"
                                   Width="200"
                                   CanUserSort="True"
                                   HeaderStyle="{StaticResource LeftAlignedColumnHeaderStyle}"
                                   IsReadOnly="True"/>

                <!-- Source File Column -->
                <DataGridTextColumn Header="Source File"
                                   Binding="{Binding SourceFileName}"
                                   Width="180"
                                   CanUserSort="True"
                                   HeaderStyle="{StaticResource LeftAlignedColumnHeaderStyle}"
                                   IsReadOnly="True"/>

                <!-- Last Refresh Column -->
                <DataGridTextColumn Header="Last Refresh"
                                   Binding="{Binding LastRefreshedDisplay}"
                                   Width="140"
                                   CanUserSort="True"
                                   HeaderStyle="{StaticResource LeftAlignedColumnHeaderStyle}"
                                   IsReadOnly="True"/>

                <!-- Modified By Column -->
                <DataGridTextColumn Header="Modified By"
                                   Binding="{Binding ModifiedBy}"
                                   Width="120"
                                   CanUserSort="True"
                                   HeaderStyle="{StaticResource LeftAlignedColumnHeaderStyle}"
                                   IsReadOnly="True"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Status Bar -->
        <Grid Grid.Row="3" Margin="0,15,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Status Text and Link Count -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0"
                           Text="{Binding StatusText}" 
                           Foreground="Gray" 
                           FontSize="10"
                           VerticalAlignment="Center"/>
                
                <TextBlock Grid.Column="1"
                           Text="{Binding LinkCountDisplay}"
                           Foreground="#34495E"
                           FontSize="10"
                           FontWeight="SemiBold"
                           VerticalAlignment="Center"/>
            </Grid>
            
            <!-- Progress Bar -->
            <ProgressBar Grid.Row="1"
                         Value="{Binding ProgressPercent}" 
                         Height="4" 
                         Minimum="0" 
                         Maximum="100"
                         Background="#E0E0E0"
                         Foreground="{StaticResource PrimaryBlueBrush}"
                         Margin="0,5,0,0"
                         Visibility="{Binding IsRefreshing, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </Grid>
    </Grid>
</UserControl>