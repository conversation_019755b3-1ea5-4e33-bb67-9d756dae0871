Quick Action Plan — Simplified <PERSON> (2 weeks)

Focus: Minimal enterprise trials management for a solo founder. Core flow only: create 30‑day trial → bulk invite → accept → convert (Stripe Checkout). Skip invoices UI, analytics, complex contacts, and audit logs.

## Goal & Scope
- Trials with bulk invites and basic tracking (seats used, days left)
- Invite acceptance via token link with auto account creation
- Stripe Checkout for conversion (annual/quarterly), no in‑app invoice lifecycle

## Simplified Data Model (3 tables)
- Reuse existing: `public.enterprise_customers`
- Add:
	- `public.enterprise_trials`
	- `public.trial_invites`

```sql
CREATE TABLE IF NOT EXISTS public.enterprise_trials (
	id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
	customer_id uuid REFERENCES public.enterprise_customers(id) ON DELETE CASCADE,
	status text NOT NULL CHECK (status IN ('active','expired','converted')) DEFAULT 'active',
	seat_limit int NOT NULL DEFAULT 50,
	seats_used int NOT NULL DEFAULT 0,
	expires_at timestamptz NOT NULL,
	converted_at timestamptz,
	created_at timestamptz NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_trials_customer_id ON public.enterprise_trials(customer_id);

CREATE TABLE IF NOT EXISTS public.trial_invites (
	id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
	trial_id uuid REFERENCES public.enterprise_trials(id) ON DELETE CASCADE,
	email text NOT NULL,
	invite_token text UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
	accepted_at timestamptz,
	user_id uuid,
	created_at timestamptz NOT NULL DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_invites_trial_id ON public.trial_invites(trial_id);
CREATE INDEX IF NOT EXISTS idx_invites_email ON public.trial_invites(email);
```

RLS: Enable on both new tables; for MVP, interact via server routes (service role). Never expose `invite_token` in list responses.

## Core Features
1) Trial Management
- Create trial (company name, contact email, optional domain)
- Bulk invite via email list or CSV paste
- View trial status (X/50 seats used, days left)
- One‑click convert to paid via Stripe Checkout (returns URL)

2) Invite Flow
- Simple email invites with magic link
- `/invite/[token]` accept page
- Auto create/link Supabase Auth account and assign seat

3) Sales Dashboard (Single Page)
- Active trials list with metrics
- Quick actions: Invite More, Convert to Paid, View Invites
- Search/filter by company

## Minimal API Routes
```
GET  /api/sales/trials                  # list trials with customer info and days_left
POST /api/sales/trials                  # create trial (returns { trial, customer })
GET  /api/sales/trials/[trialId]        # trial detail with invites (redacted tokens)
POST /api/sales/trials/[trialId]/invite # bulk invite (emails[] | csv)
POST /api/invite/accept                 # accept invite by token
POST /api/sales/trials/[trialId]/convert# create Stripe Checkout session
```

## UI Components (Minimal)
- CreateTrialDialog (company, email, domain)
- TrialsTable (company, seats, expires, actions)
- BulkInviteDialog (textarea for emails/CSV)
- ViewInvitesDialog (list + accepted status)
- InviteAcceptPage at `/invite/[token]`

## Out of Scope (MVP)
- Contacts/role management
- Invoices UI and webhooks
- Analytics dashboards
- Audit logs and multi‑role RBAC

## 2‑Week Task List

Week 1 — Foundation
1. ✅ Database: create `enterprise_trials`, `trial_invites`; enable RLS; add indexes
2. ✅ APIs: list/create trials; bulk invites; invite accept
3. ✅ Email: minimal provider (Resend/Postmark) to send token links
4. ✅ UI: CreateTrialDialog + TrialsTable (basic list + actions)

Week 2 — UI & Conversion
5. ✅ UI: BulkInviteDialog + ViewInvitesDialog; show days left and seats chips
6. ✅ Stripe: ensure customer + Checkout session creation (annual/quarterly) for convert
7. ✅ InviteAcceptPage: polish success/error UX
8. Tests: unit (validation) + one Playwright happy path; README updates

## Success Metrics
- Create a trial in <30 seconds
- Bulk‑invite up to 50 users in a single action
- See seat utilization and days left at a glance
- Convert to paid with one click (Checkout URL)

## Env Vars
```
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
STRIPE_SECRET_KEY=
RESEND_API_KEY=
NEXT_PUBLIC_APP_URL=
```

Keep scope tight; defer anything beyond the core flow.