﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.Encoding</name>
  </assembly>
  <members>
    <member name="T:System.Security.Cryptography.AsnEncodedData">
      <summary>Represents Abstract Syntax Notation One (ASN.1)-encoded data.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.AsnEncodedData" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.AsnEncodedData" /> class using a byte array.</summary>
      <param name="rawData">A byte array that contains Abstract Syntax Notation One (ASN.1)-encoded data.</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.AsnEncodedData" /> class using an instance of the <see cref="T:System.Security.Cryptography.AsnEncodedData" /> class.</summary>
      <param name="asnEncodedData">An instance of the <see cref="T:System.Security.Cryptography.AsnEncodedData" /> class.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> is null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Security.Cryptography.Oid,System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.AsnEncodedData" /> class using an <see cref="T:System.Security.Cryptography.Oid" /> object and a byte array.</summary>
      <param name="oid">An <see cref="T:System.Security.Cryptography.Oid" /> object.</param>
      <param name="rawData">A byte array that contains Abstract Syntax Notation One (ASN.1)-encoded data.</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.String,System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.AsnEncodedData" /> class using a byte array.</summary>
      <param name="oid">A string that represents <see cref="T:System.Security.Cryptography.Oid" /> information.</param>
      <param name="rawData">A byte array that contains Abstract Syntax Notation One (ASN.1)-encoded data.</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Copies information from an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">The <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object to base the new object on.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData " />is null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.Format(System.Boolean)">
      <summary>Returns a formatted version of the Abstract Syntax Notation One (ASN.1)-encoded data as a string.</summary>
      <returns>A formatted string that represents the Abstract Syntax Notation One (ASN.1)-encoded data.</returns>
      <param name="multiLine">true if the return string should contain carriage returns; otherwise, false.</param>
    </member>
    <member name="P:System.Security.Cryptography.AsnEncodedData.Oid">
      <summary>Gets or sets the <see cref="T:System.Security.Cryptography.Oid" /> value for an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Oid" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AsnEncodedData.RawData">
      <summary>Gets or sets the Abstract Syntax Notation One (ASN.1)-encoded data represented in a byte array.</summary>
      <returns>A byte array that represents the Abstract Syntax Notation One (ASN.1)-encoded data.</returns>
      <exception cref="T:System.ArgumentNullException">The value is null.</exception>
    </member>
    <member name="T:System.Security.Cryptography.Oid">
      <summary>Represents a cryptographic object identifier. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.Security.Cryptography.Oid)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Oid" /> class using the specified <see cref="T:System.Security.Cryptography.Oid" /> object.</summary>
      <param name="oid">The object identifier information to use to create the new object identifier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oid " />is null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Oid" /> class using a string value of an <see cref="T:System.Security.Cryptography.Oid" /> object.</summary>
      <param name="oid">An object identifier.</param>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Oid" /> class using the specified value and friendly name.</summary>
      <param name="value">The dotted number of the identifier.</param>
      <param name="friendlyName">The friendly name of the identifier.</param>
    </member>
    <member name="P:System.Security.Cryptography.Oid.FriendlyName">
      <summary>Gets or sets the friendly name of the identifier.</summary>
      <returns>The friendly name of the identifier.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Oid.FromFriendlyName(System.String,System.Security.Cryptography.OidGroup)">
      <summary>Creates an <see cref="T:System.Security.Cryptography.Oid" /> object from an OID friendly name by searching the specified group.</summary>
      <returns>An object that represents the specified OID.</returns>
      <param name="friendlyName">The friendly name of the identifier.</param>
      <param name="group">The group to search in.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="friendlyName " /> is null.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The OID was not found.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Oid.FromOidValue(System.String,System.Security.Cryptography.OidGroup)">
      <summary>Creates an <see cref="T:System.Security.Cryptography.Oid" /> object by using the specified OID value and group.</summary>
      <returns>A new instance of an <see cref="T:System.Security.Cryptography.Oid" /> object.</returns>
      <param name="oidValue">The OID value.</param>
      <param name="group">The group to search in.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oidValue" /> is null.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The friendly name for the OID value was not found.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Oid.Value">
      <summary>Gets or sets the dotted number of the identifier.</summary>
      <returns>The dotted number of the identifier.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidCollection">
      <summary>Represents a collection of <see cref="T:System.Security.Cryptography.Oid" /> objects. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.OidCollection" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.Add(System.Security.Cryptography.Oid)">
      <summary>Adds an <see cref="T:System.Security.Cryptography.Oid" /> object to the <see cref="T:System.Security.Cryptography.OidCollection" /> object.</summary>
      <returns>The index of the added <see cref="T:System.Security.Cryptography.Oid" /> object.</returns>
      <param name="oid">The <see cref="T:System.Security.Cryptography.Oid" /> object to add to the collection.</param>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.CopyTo(System.Security.Cryptography.Oid[],System.Int32)">
      <summary>Copies the <see cref="T:System.Security.Cryptography.OidCollection" /> object into an array.</summary>
      <param name="array">The array to copy the <see cref="T:System.Security.Cryptography.OidCollection" /> object into.</param>
      <param name="index">The location where the copy operation starts.</param>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Count">
      <summary>Gets the number of <see cref="T:System.Security.Cryptography.Oid" /> objects in a collection. </summary>
      <returns>The number of <see cref="T:System.Security.Cryptography.Oid" /> objects in a collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.GetEnumerator">
      <summary>Returns an <see cref="T:System.Security.Cryptography.OidEnumerator" /> object that can be used to navigate the <see cref="T:System.Security.Cryptography.OidCollection" /> object.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.OidEnumerator" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Item(System.Int32)">
      <summary>Gets an <see cref="T:System.Security.Cryptography.Oid" /> object from the <see cref="T:System.Security.Cryptography.OidCollection" /> object.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Oid" /> object.</returns>
      <param name="index">The location of the <see cref="T:System.Security.Cryptography.Oid" /> object in the collection.</param>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Item(System.String)">
      <summary>Gets the first <see cref="T:System.Security.Cryptography.Oid" /> object that contains a value of the <see cref="P:System.Security.Cryptography.Oid.Value" /> property or a value of the <see cref="P:System.Security.Cryptography.Oid.FriendlyName" /> property that matches the specified string value from the <see cref="T:System.Security.Cryptography.OidCollection" /> object.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Oid" /> object.</returns>
      <param name="oid">A string that represents a <see cref="P:System.Security.Cryptography.Oid.Value" /> property or a <see cref="P:System.Security.Cryptography.Oid.FriendlyName" /> property.</param>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the <see cref="T:System.Security.Cryptography.OidCollection" /> object into an array.</summary>
      <param name="array">The array to copy the <see cref="T:System.Security.Cryptography.OidCollection" /> object to.</param>
      <param name="index">The location where the copy operation starts.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> cannot be a multidimensional array.-or-The length of <paramref name="array" /> is an invalid offset length.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of <paramref name="index" /> is out range.</exception>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.Cryptography.OidCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.Cryptography.OidCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an <see cref="T:System.Security.Cryptography.OidEnumerator" /> object that can be used to navigate the <see cref="T:System.Security.Cryptography.OidCollection" /> object.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.OidEnumerator" /> object that can be used to navigate the collection.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidEnumerator">
      <summary>Provides the ability to navigate through an <see cref="T:System.Security.Cryptography.OidCollection" /> object. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Security.Cryptography.OidEnumerator.Current">
      <summary>Gets the current <see cref="T:System.Security.Cryptography.Oid" /> object in an <see cref="T:System.Security.Cryptography.OidCollection" /> object.</summary>
      <returns>The current <see cref="T:System.Security.Cryptography.Oid" /> object in the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.OidEnumerator.MoveNext">
      <summary>Advances to the next <see cref="T:System.Security.Cryptography.Oid" /> object in an <see cref="T:System.Security.Cryptography.OidCollection" /> object.</summary>
      <returns>true, if the enumerator was successfully advanced to the next element; false, if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
    </member>
    <member name="M:System.Security.Cryptography.OidEnumerator.Reset">
      <summary>Sets an enumerator to its initial position.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
    </member>
    <member name="P:System.Security.Cryptography.OidEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current <see cref="T:System.Security.Cryptography.Oid" /> object in an <see cref="T:System.Security.Cryptography.OidCollection" /> object.</summary>
      <returns>The current <see cref="T:System.Security.Cryptography.Oid" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidGroup">
      <summary>Identifies Windows cryptographic object identifier (OID) groups.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.All">
      <summary>All the groups.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Attribute">
      <summary>The Windows group that is represented by CRYPT_RDN_ATTR_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.EncryptionAlgorithm">
      <summary>The Windows group that is represented by CRYPT_ENCRYPT_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.EnhancedKeyUsage">
      <summary>The Windows group that is represented by CRYPT_ENHKEY_USAGE_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.ExtensionOrAttribute">
      <summary>The Windows group that is represented by CRYPT_EXT_OR_ATTR_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.HashAlgorithm">
      <summary>The Windows group that is represented by CRYPT_HASH_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.KeyDerivationFunction">
      <summary>The Windows group that is represented by CRYPT_KDF_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Policy">
      <summary>The Windows group that is represented by CRYPT_POLICY_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.PublicKeyAlgorithm">
      <summary>The Windows group that is represented by CRYPT_PUBKEY_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.SignatureAlgorithm">
      <summary>The Windows group that is represented by CRYPT_SIGN_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Template">
      <summary>The Windows group that is represented by CRYPT_TEMPLATE_OID_GROUP_ID.</summary>
    </member>
  </members>
</doc>