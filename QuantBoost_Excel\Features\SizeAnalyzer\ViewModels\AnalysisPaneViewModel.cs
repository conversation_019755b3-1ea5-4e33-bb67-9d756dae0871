using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Input;
using QuantBoost_Excel.Features.SizeAnalyzer.Logic;
using QuantBoost_Shared.UI;

namespace QuantBoost_Excel.Features.SizeAnalyzer.ViewModels
{
    /// <summary>
    /// ViewModel for the Size Analyzer WPF UI.
    /// Implements MVVM pattern with proper data binding and command handling.
    /// </summary>
    public class AnalysisPaneViewModel : ViewModelBase
    {
        #region Fields
        private readonly AnalysisService _analysisService;
        private CancellationTokenSource _cancellationTokenSource;
        
        private ObservableCollection<WorksheetAnalysisDisplayItem> _analysisResults;
        private string _statusText;
        private bool _isAnalyzing;
        private int _progressPercent;
        private long _totalSize;
        private string _totalSizeDisplay;
        #endregion

        #region Properties
        /// <summary>
        /// Collection of analysis results for data binding.
        /// </summary>
        public ObservableCollection<WorksheetAnalysisDisplayItem> AnalysisResults
        {
            get => _analysisResults;
            set
            {
                _analysisResults = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasResults));
                if (ExportCommand is RelayCommand exportCmd)
                    exportCmd.RaiseCanExecuteChanged();
            }
        }

        /// <summary>
        /// Current status text for display.
        /// </summary>
        public string StatusText
        {
            get => _statusText;
            set
            {
                _statusText = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Indicates if analysis is currently running.
        /// </summary>
        public bool IsAnalyzing
        {
            get => _isAnalyzing;
            set
            {
                _isAnalyzing = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsNotAnalyzing));
                ((RelayCommand)AnalyzeCommand).RaiseCanExecuteChanged();
                // Also update ExportCommand since it depends on !IsAnalyzing
                if (ExportCommand is RelayCommand exportCmd)
                    exportCmd.RaiseCanExecuteChanged();
            }
        }

        /// <summary>
        /// Inverse of IsAnalyzing for UI binding.
        /// </summary>
        public bool IsNotAnalyzing => !_isAnalyzing;

        /// <summary>
        /// Indicates if there are results available for export.
        /// </summary>
        public bool HasResults => _analysisResults?.Count > 0;

        /// <summary>
        /// Current progress percentage (0-100).
        /// </summary>
        public int ProgressPercent
        {
            get => _progressPercent;
            set
            {
                _progressPercent = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Total size in bytes.
        /// </summary>
        public long TotalSize
        {
            get => _totalSize;
            set
            {
                _totalSize = value;
                OnPropertyChanged();
                UpdateTotalSizeDisplay();
            }
        }

        /// <summary>
        /// Formatted total size for display.
        /// </summary>
        public string TotalSizeDisplay
        {
            get => _totalSizeDisplay;
            set
            {
                _totalSizeDisplay = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Command to start/cancel analysis.
        /// </summary>
        public ICommand AnalyzeCommand { get; }

        /// <summary>
        /// Command to export results to CSV.
        /// </summary>
        public ICommand ExportCommand { get; }
        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the AnalysisPaneViewModel class.
        /// </summary>
        public AnalysisPaneViewModel()
        {
            _analysisService = new AnalysisService();
            AnalysisResults = new ObservableCollection<WorksheetAnalysisDisplayItem>();
            StatusText = "Ready to analyze.";
            TotalSizeDisplay = "Total Size: N/A";
            
            AnalyzeCommand = new RelayCommand(
                execute: async _ => await ExecuteAnalyzeAsync(),
                canExecute: _ => !IsAnalyzing
            );

            ExportCommand = new RelayCommand(
                execute: _ => ExecuteExport(),
                canExecute: _ => HasResults && !IsAnalyzing
            );
        }
        #endregion

        #region Methods
        /// <summary>
        /// Executes the analysis operation.
        /// </summary>
        private async Task ExecuteAnalyzeAsync()
        {
            if (IsAnalyzing)
            {
                // Cancel current analysis
                _cancellationTokenSource?.Cancel();
                return;
            }

            IsAnalyzing = true;
            AnalysisResults.Clear();
            TotalSize = 0;
            ProgressPercent = 0;

            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = new CancellationTokenSource();

            try
            {
                var progress = new Progress<(string status, int percentage)>(p =>
                {
                    StatusText = p.status;
                    ProgressPercent = p.percentage;
                });

                var results = await _analysisService.AnalyzeWorkbookAsync(progress, _cancellationTokenSource.Token);

                // Post-process to calculate percentages and convert to display items
                long total = results.Sum(r => r.SizeBytes);
                TotalSize = total;

                foreach (var result in results.OrderByDescending(r => r.SizeBytes))
                {
                    var displayItem = ConvertToDisplayItem(result, total);

                    // Debug logging to verify proportional allocation in UI
                    System.Diagnostics.Debug.WriteLine($"UI Display: {result.Name} = {result.SizeBytes:N0} bytes ({result.SizeBytes / 1024.0:F1} KB) = {displayItem.SizePercentage:F1}%");

                    AnalysisResults.Add(displayItem);
                }

                // Manually trigger property change notifications after populating the collection
                // This ensures the Export button becomes enabled after analysis completion
                OnPropertyChanged(nameof(HasResults));
                if (ExportCommand is RelayCommand exportCmd)
                    exportCmd.RaiseCanExecuteChanged();

                StatusText = $"Analysis complete. Found {results.Count} worksheets with proportional size allocation (processed in background).";
                ProgressPercent = 100;
            }
            catch (OperationCanceledException)
            {
                StatusText = "Analysis cancelled.";
                ProgressPercent = 0;
            }
            catch (Exception ex)
            {
                StatusText = $"Error: {ex.Message}";
                ProgressPercent = 0;
            }
            finally
            {
                IsAnalyzing = false;
            }
        }

        /// <summary>
        /// Converts a WorksheetAnalysisSummary to a display item.
        /// </summary>
        private WorksheetAnalysisDisplayItem ConvertToDisplayItem(WorksheetAnalysisSummary summary, long totalSize)
        {
            string contentType = "Data";
            if (summary.HasImages && summary.HasCharts)
                contentType = "Mixed Content";
            else if (summary.HasImages)
                contentType = "Images";
            else if (summary.HasCharts)
                contentType = "Charts";
            else if (summary.HasEmbeddedObjects)
                contentType = "Embedded Objects";

            double percentage = totalSize > 0 ? ((double)summary.SizeBytes / totalSize * 100) : 0;

            return new WorksheetAnalysisDisplayItem
            {
                WorksheetName = summary.Name,
                ContentType = contentType,
                SizeBytes = summary.SizeBytes,
                SizePercentage = percentage,
                ContentDetails = $"Cells: {summary.CellCount:N0}, Formulas: {summary.FormulaCount:N0}, Images: {summary.ImageCount}, Charts: {summary.ChartCount}, Objects: {summary.EmbeddedObjectCount}",
                CellCount = summary.CellCount,
                FormulaCount = summary.FormulaCount,
                ImageCount = summary.ImageCount,
                ChartCount = summary.ChartCount,
                EmbeddedObjectCount = summary.EmbeddedObjectCount
            };
        }

        /// <summary>
        /// Updates the total size display string.
        /// </summary>
        private void UpdateTotalSizeDisplay()
        {
            TotalSizeDisplay = $"Total Size: {FormatSize(TotalSize)}";
        }

        /// <summary>
        /// Executes the export to CSV operation.
        /// </summary>
        private void ExecuteExport()
        {
            try
            {
                if (AnalysisResults == null || AnalysisResults.Count == 0)
                {
                    MessageBox.Show("No analysis results to export.", "Export Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Show save file dialog
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*";
                    saveDialog.Title = "Export Analysis Results";
                    saveDialog.FileName = $"WorkbookAnalysis_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        ExportToCsv(saveDialog.FileName);
                        MessageBox.Show($"Analysis results exported successfully to:\n{saveDialog.FileName}",
                                      "Export Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error exporting results: {ex.Message}", "Export Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Exports the analysis results to a CSV file.
        /// </summary>
        /// <param name="filePath">The path to save the CSV file.</param>
        private void ExportToCsv(string filePath)
        {
            var csv = new StringBuilder();

            // Add header
            csv.AppendLine("Worksheet,Content Type,Size (KB),Size (%),Cells,Formulas,Images,Charts,Objects");

            // Add data rows
            foreach (var item in AnalysisResults.OrderByDescending(r => r.SizeBytes))
            {
                csv.AppendLine($"\"{item.WorksheetName}\"," +
                              $"\"{item.ContentType}\"," +
                              $"{item.SizeKB:F1}," +
                              $"{item.SizePercentage:F1}," +
                              $"{item.CellCount}," +
                              $"{item.FormulaCount}," +
                              $"{item.ImageCount}," +
                              $"{item.ChartCount}," +
                              $"{item.EmbeddedObjectCount}");
            }

            // Add summary row
            csv.AppendLine();
            csv.AppendLine($"\"TOTAL\",\"\",{TotalSize / 1024.0:F1},100.0," +
                          $"{AnalysisResults.Sum(r => r.CellCount)}," +
                          $"{AnalysisResults.Sum(r => r.FormulaCount)}," +
                          $"{AnalysisResults.Sum(r => r.ImageCount)}," +
                          $"{AnalysisResults.Sum(r => r.ChartCount)}," +
                          $"{AnalysisResults.Sum(r => r.EmbeddedObjectCount)}");

            File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
        }

        /// <summary>
        /// Formats a byte size into a human-readable string.
        /// </summary>
        private static string FormatSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024.0 * 1024.0):F1} MB";
            return $"{bytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
        }
        #endregion

        #region IDisposable
        /// <summary>
        /// Disposes resources used by the ViewModel.
        /// </summary>
        public void Dispose()
        {
            _cancellationTokenSource?.Dispose();
        }
        #endregion
    }
}
