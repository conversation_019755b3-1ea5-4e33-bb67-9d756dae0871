import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { SupabaseProvider } from '../components/SupabaseProvider';
import { ToastProvider } from '../components/ui/toast';
import { getNonce } from '../lib/nonce';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "QuantBoost",
  description: "QuantBoost SaaS Platform",
  icons: {
    icon: [
      { url: "/QuantBoost_LogoOnly_16x16.png", type: "image/png", sizes: "16x16" },
      { url: "/QuantBoost_LogoOnly_32x32.png", type: "image/png", sizes: "32x32" },
      { url: "/QuantBoost_LogoOnly_v0.png", type: "image/png" },
      { url: "/favicon.ico" }
    ],
    shortcut: ["/QuantBoost_LogoOnly_32x32.png"],
    apple: ["/QuantBoost_LogoOnly_32x32.png"],
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const nonce = await getNonce();

  return (
    <html lang="en">
      <head>
        {/* Preconnect to Stripe for faster checkout loading */}
        <link rel="preconnect" href="https://js.stripe.com" />
        {/* Explicit favicon links for broader browser support */}
  <link rel="icon" type="image/png" sizes="16x16" href="/QuantBoost_LogoOnly_16x16.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/QuantBoost_LogoOnly_32x32.png" />
  <link rel="shortcut icon" href="/QuantBoost_LogoOnly_32x32.png" />
  <link rel="apple-touch-icon" href="/QuantBoost_LogoOnly_32x32.png" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SupabaseProvider>
          <ToastProvider>
            {children}
          </ToastProvider>
        </SupabaseProvider>
        {/* Include nonce in a script for client-side access if needed */}
        <script nonce={nonce} dangerouslySetInnerHTML={{
          __html: `window.__CSP_NONCE__ = '${nonce}';`
        }} />
      </body>
    </html>
  );
}
