// Centralized Stripe Elements configuration for consistent appearance & options
// Extend or theme here; both checkout and dashboard update flows should import from this file.

import type { Appearance, StripeElementsOptions } from '@stripe/stripe-js';

export const stripeAppearance: Appearance = {
  theme: 'flat',
  variables: {
    colorPrimary: '#000000',
    colorBackground: '#ffffff',
    colorText: '#111827',
    colorDanger: '#dc2626',
    fontFamily: 'Inter, system-ui, sans-serif',
    borderRadius: '6px'
  },
  rules: {
    '.Input': {
      border: '1px solid #d1d5db',
      boxShadow: 'none'
    },
    '.Input:focus': {
      borderColor: '#111827'
    },
    '.Tab, .Block': {
      borderRadius: '6px'
    }
  }
};

export const baseElementsOptions = (clientSecret: string): StripeElementsOptions => ({
  clientSecret,
  appearance: stripeAppearance,
});

// Shared PaymentElement specific options for both flows
export const sharedPaymentElementOptions = {
  layout: { type: 'tabs', defaultCollapsed: false },
  fields: {
    billingDetails: {
  // We will collect name via our own inputs and pass it in confirmPayment.
  // Hide name/email inside the Payment Element to avoid duplication.
  name: 'never',
      email: 'never',
      address: { country: 'auto', line1: 'auto', line2: 'auto', city: 'auto', state: 'auto', postalCode: 'auto' }
    }
  },
  business: { name: 'QuantBoost' }
} as const;

// Friendly Stripe error code mapping
export function mapStripeError(code?: string, fallback?: string): string {
  switch (code) {
    case 'card_declined': return 'Your card was declined. Please try another card or contact your bank.';
    case 'expired_card': return 'This card has expired. Use a different card.';
    case 'incorrect_cvc': return 'The security code is incorrect.';
    case 'processing_error': return 'A network error occurred while processing. Please retry.';
    case 'insufficient_funds': return 'Insufficient funds. Try a different payment method.';
    default: return fallback || 'Payment failed. Please review your details and try again.';
  }
}
