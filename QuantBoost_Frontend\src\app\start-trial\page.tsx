'use client';

import { useState, useRef } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'motion/react';
import Header from '@/components/Header';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import Link from 'next/link';
import { Variants } from 'motion/react';

// Professional Motion Variants
const fadeInUp: Variants = {
  initial: { opacity: 0, y: 30 },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: 0.6, 
      ease: "easeOut" // Changed from array to string
    }
  }
};

const cardHover: Variants = {
  idle: { 
    scale: 1,
    boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.12)"
  },
  hover: { 
    scale: 1.02,
    boxShadow: "0 8px 25px 0 rgba(0, 0, 0, 0.15)",
    transition: { 
      duration: 0.3,
      ease: "easeOut"
    }
  }
};

const buttonVariants: Variants = {
  idle: { 
    scale: 1,
    boxShadow: "0 2px 4px 0 rgba(16, 185, 129, 0.2)"
  },
  hover: { 
    scale: 1.02,
    boxShadow: "0 4px 8px 0 rgba(16, 185, 129, 0.3)",
    transition: { 
      duration: 0.2,
      ease: "easeOut"
    }
  },
  tap: {
    scale: 0.98,
    transition: { duration: 0.1 }
  }
};

export default function StartTrialPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  // Motion scroll effects setup
  const mainRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: mainRef,
    offset: ["start start", "end start"]
  });

  // Professional grid opacity effects
  const gridOpacity = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0.03, 0.08, 0.12, 0.16]);
  const gridScale = useTransform(scrollYProgress, [0, 1], [1, 1.02]);
  const smoothGridOpacity = useSpring(gridOpacity, { stiffness: 50, damping: 25 });

  // Subtle parallax effects
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "15%"]);
  const smoothBackgroundY = useSpring(backgroundY, {
    stiffness: 100,
    damping: 30
  });

  const handleStartTrial = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/auth/start-trial', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Something went wrong');
      }

      setMessage(data.message || 'Trial started successfully!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred.';
      setMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Header />
      <main ref={mainRef} className="flex justify-center items-center min-h-screen overflow-hidden relative">
        {/* Excel Grid Background - Consistent with Pricing/Help Pages */}
        <motion.div 
          className="fixed inset-0 -z-20 overflow-hidden"
          style={{ 
            opacity: smoothGridOpacity,
            scale: gridScale,
            y: smoothBackgroundY
          }}
        >
          <svg
            className="absolute inset-0 w-full h-full"
            xmlns="http://www.w3.org/2000/svg"
            style={{ 
              width: '100%', 
              height: '100%',
              minHeight: '100vh'
            }}
          >
            <defs>
              <pattern
                id="excel-grid-trial"
                x="0"
                y="0"
                width="40"
                height="24"
                patternUnits="userSpaceOnUse"
              >
                <rect
                  x="0"
                  y="0"
                  width="40"
                  height="24"
                  fill="none"
                  stroke="#10B981"
                  strokeWidth="0.5"
                  opacity="0.8"
                />
                <rect
                  x="0"
                  y="0"
                  width="8"
                  height="4.8"
                  fill="#10B981"
                  opacity="0.03"
                />
              </pattern>
            </defs>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#excel-grid-trial)"
            />
          </svg>
        </motion.div>

        {/* Content Container with Motion */}
        <div className="bg-muted/40 px-4 pt-16 w-full min-h-screen flex items-center justify-center relative z-10">
          <motion.div
            variants={fadeInUp}
            initial="initial"
            animate="animate"
            className="w-full max-w-md"
          >
            <motion.div
              variants={cardHover}
              initial="idle"
              whileHover="hover"
              className="w-full"
            >
              <Card className="w-full bg-white/95 backdrop-blur-sm border-2 border-gray-200">
                <CardHeader className="text-center">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.6 }}
                  >
                    <CardTitle className="text-2xl font-bold text-gray-800">
                      Start Your Free 14-Day Trial
                    </CardTitle>
                    <CardDescription className="text-gray-600 mt-2">
                      No credit card required. Unlock all QuantBoost features instantly.
                    </CardDescription>
                  </motion.div>
                </CardHeader>
                
                <CardContent>
                  <motion.form 
                    onSubmit={handleStartTrial} 
                    className="space-y-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4, duration: 0.6 }}
                  >
                    <div className="space-y-2">
                      <motion.label 
                        htmlFor="email" 
                        className="text-sm font-medium text-gray-700"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5, duration: 0.5 }}
                      >
                        Email Address
                      </motion.label>
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6, duration: 0.5 }}
                      >
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          required
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          disabled={isLoading}
                          className="transition-all duration-200 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        />
                      </motion.div>
                    </div>
                    
                    <motion.div
                      variants={buttonVariants}
                      initial="idle"
                      whileHover="hover"
                      whileTap="tap"
                      className="w-full"
                    >
                      <Button 
                        type="submit" 
                        className="w-full bg-emerald-500 text-white hover:bg-emerald-600 border-emerald-500 transition-all duration-300 text-lg font-semibold py-3" 
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <motion.div 
                            className="flex items-center gap-2"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                          >
                            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                            Starting Trial...
                          </motion.div>
                        ) : (
                          'Start 14-Day Free Trial'
                        )}
                      </Button>
                    </motion.div>
                  </motion.form>
                </CardContent>
                
                <CardFooter className="flex flex-col items-center space-y-2">
                  {message && (
                    <motion.p 
                      className={`text-sm ${message.includes('error') || message.includes('wrong') ? 'text-red-600' : 'text-emerald-600'} font-medium`}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4 }}
                    >
                      {message}
                    </motion.p>
                  )}
                  <motion.p 
                    className="text-xs text-gray-500"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.8, duration: 0.5 }}
                  >
                    Already have an account?{' '}
                    <Link href="/auth/login" className="text-emerald-600 hover:text-emerald-700 underline font-medium transition-colors">
                      Log in
                    </Link>
                  </motion.p>
                </CardFooter>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </main>
    </>
  );
}