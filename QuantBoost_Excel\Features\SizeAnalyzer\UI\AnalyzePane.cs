// --- START OF FILE AnalyzePane.cs ---

using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using QuantBoost_Excel.Features.SizeAnalyzer.Logic;
using QuantBoost_Shared.Utilities;

namespace QuantBoost_Excel.Features.SizeAnalyzer.UI
{
    /// <summary>
    /// User control for the Excel Workbook File Size Analyzer.
    /// Adapted from the PowerPoint AnalyzePane for Excel-specific analysis.
    /// </summary>
    public class AnalyzePane : UserControl
    {
        #region UI Controls
        private Panel _filterPanel;
        private Button _runAnalysisButton;
        private Label _totalSizeLabel;

        private Panel _progressPanel;
        private Label _progressLabel;
        private ProgressBar _progressBar;

        private DataGridView _resultsGrid;
        private DataTable _resultsTable;

        private ToolTip _toolTip;
        #endregion

        #region State & Data
        private CancellationTokenSource _analysisCancellationTokenSource;
        private WorkbookAnalysisSummary _currentWorkbookSummary;
        private List<WorksheetAnalysisDisplayItem> _fullAnalysisResultsList;
        private AnalysisService _analysisService;
        #endregion

        #region Constants
        private const string RUN_ANALYSIS_TEXT = "Analyze Workbook";
        private const string CANCEL_ANALYSIS_TEXT = "Cancel Analysis";
        private const string CANCELLING_TEXT = "Cancelling...";
        #endregion

        #region Constructor & Initialization
        public AnalyzePane()
        {
            _analysisService = new AnalysisService();
            InitializeUI();
        }

        private void InitializeUI()
        {
            this.SuspendLayout();
            
            // Set up the main control
            this.Dock = DockStyle.Fill;
            this.BackColor = SystemColors.Control;
            this.Font = new Font("Segoe UI", 9f);

            // Initialize tooltip
            _toolTip = new ToolTip();

            // Create main layout
            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 3,
                ColumnCount = 1,
                BackColor = SystemColors.Control
            };
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 50)); // Filter panel
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 60)); // Progress panel
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // Results grid

            // Create filter panel
            CreateFilterPanel();
            mainLayout.Controls.Add(_filterPanel, 0, 0);

            // Create progress panel
            CreateProgressPanel();
            mainLayout.Controls.Add(_progressPanel, 0, 1);

            // Create results grid
            CreateResultsGrid();
            mainLayout.Controls.Add(_resultsGrid, 0, 2);

            this.Controls.Add(mainLayout);
            
            // Set initial UI state
            SetUIMode(UIMode.Idle);
            
            this.ResumeLayout(false);
        }

        private void CreateFilterPanel()
        {
            _filterPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = SystemColors.Control,
                Padding = new Padding(10, 8, 10, 8)
            };

            _runAnalysisButton = new Button
            {
                Dock = DockStyle.Left, Width = 150, Height = 34,
                Text = RUN_ANALYSIS_TEXT,
                Font = new Font("Segoe UI", 9f, FontStyle.Regular),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                UseVisualStyleBackColor = false
            };
            _runAnalysisButton.FlatAppearance.BorderSize = 0;
            _runAnalysisButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(0, 100, 190);
            _runAnalysisButton.FlatAppearance.MouseDownBackColor = Color.FromArgb(0, 90, 170);
            _runAnalysisButton.Click += OnRunAnalysisButtonClick;
            _toolTip.SetToolTip(_runAnalysisButton, "Start or cancel the workbook analysis.");

            _totalSizeLabel = new Label
            {
                Dock = DockStyle.Right, Width = 200, AutoSize = false,
                Font = new Font("Segoe UI", 9f, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = SystemColors.ControlDarkDark,
                Text = "Total Size: N/A"
            };
            _toolTip.SetToolTip(_totalSizeLabel, "Ready for analysis.");

            _filterPanel.Controls.Add(_runAnalysisButton);
            _filterPanel.Controls.Add(_totalSizeLabel);
        }

        private void CreateProgressPanel()
        {
            _progressPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = SystemColors.Control,
                Padding = new Padding(10, 5, 10, 5),
                Visible = false
            };

            _progressLabel = new Label
            {
                Dock = DockStyle.Top, Height = 20,
                Text = "Analyzing...",
                Font = new Font("Segoe UI", 8.5f),
                ForeColor = SystemColors.ControlDarkDark,
                TextAlign = ContentAlignment.MiddleLeft
            };

            _progressBar = new ProgressBar
            {
                Dock = DockStyle.Bottom, Height = 20,
                Style = ProgressBarStyle.Continuous,
                Minimum = 0, Maximum = 100, Value = 0
            };

            _progressPanel.Controls.Add(_progressLabel);
            _progressPanel.Controls.Add(_progressBar);
        }

        private void CreateResultsGrid()
        {
            _resultsGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                RowHeadersVisible = false,
                BackgroundColor = SystemColors.Window,
                BorderStyle = BorderStyle.None,
                Font = new Font("Segoe UI", 9f),
                GridColor = SystemColors.ControlLight,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = SystemColors.Window,
                    ForeColor = SystemColors.ControlText,
                    SelectionBackColor = SystemColors.Highlight,
                    SelectionForeColor = SystemColors.HighlightText
                }
            };

            // Create data table for results
            _resultsTable = new DataTable();
            _resultsTable.Columns.Add("Worksheet", typeof(string));
            _resultsTable.Columns.Add("Content Type", typeof(string));
            _resultsTable.Columns.Add("Size", typeof(string));
            _resultsTable.Columns.Add("Details", typeof(string));

            _resultsGrid.DataSource = _resultsTable;

            // Configure columns after DataSource is set
            ConfigureGridColumns();
        }

        /// <summary>
        /// Configures the DataGridView columns with proper widths and sizing modes.
        /// </summary>
        private void ConfigureGridColumns()
        {
            // Ensure columns exist before configuring them
            if (_resultsGrid.Columns.Count >= 4)
            {
                if (_resultsGrid.Columns["Worksheet"] != null)
                    _resultsGrid.Columns["Worksheet"].Width = 120;

                if (_resultsGrid.Columns["Content Type"] != null)
                    _resultsGrid.Columns["Content Type"].Width = 100;

                if (_resultsGrid.Columns["Size"] != null)
                    _resultsGrid.Columns["Size"].Width = 80;

                if (_resultsGrid.Columns["Details"] != null)
                    _resultsGrid.Columns["Details"].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
            }
        }
        #endregion

        #region UI State Management
        private enum UIMode
        {
            Idle,
            Analyzing,
            DisplayingResults,
            Error
        }

        private void SetUIMode(UIMode mode, string message = null)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<UIMode, string>(SetUIMode), mode, message);
                return;
            }

            switch (mode)
            {
                case UIMode.Idle:
                    if (_runAnalysisButton != null)
                    {
                        _runAnalysisButton.Text = RUN_ANALYSIS_TEXT;
                        _runAnalysisButton.Enabled = true;
                    }
                    if (_progressPanel != null) _progressPanel.Visible = false;
                    if (_totalSizeLabel != null) _totalSizeLabel.Text = "Total Size: N/A";
                    if (_toolTip != null && _totalSizeLabel != null) _toolTip.SetToolTip(_totalSizeLabel, "Ready for analysis.");
                    break;

                case UIMode.Analyzing:
                    if (_runAnalysisButton != null)
                    {
                        _runAnalysisButton.Text = CANCEL_ANALYSIS_TEXT;
                        _runAnalysisButton.Enabled = true;
                    }
                    if (_progressPanel != null) _progressPanel.Visible = true;
                    if (_progressLabel != null) _progressLabel.Text = "Starting analysis...";
                    if (_progressBar != null) _progressBar.Value = 0;
                    if (_totalSizeLabel != null) _totalSizeLabel.Text = "Analyzing...";
                    if (_toolTip != null && _totalSizeLabel != null) _toolTip.SetToolTip(_totalSizeLabel, "Analysis in progress.");
                    break;

                case UIMode.DisplayingResults:
                    if (_runAnalysisButton != null)
                    {
                        _runAnalysisButton.Text = RUN_ANALYSIS_TEXT;
                        _runAnalysisButton.Enabled = true;
                    }
                    if (_progressPanel != null) _progressPanel.Visible = false;
                    break;

                case UIMode.Error:
                    if (_runAnalysisButton != null)
                    {
                        _runAnalysisButton.Text = RUN_ANALYSIS_TEXT;
                        _runAnalysisButton.Enabled = true;
                    }
                    if (_progressPanel != null) _progressPanel.Visible = false;
                    if (_totalSizeLabel != null) _totalSizeLabel.Text = "Error";
                    if (_toolTip != null && _totalSizeLabel != null) _toolTip.SetToolTip(_totalSizeLabel, $"Error: {message ?? "An unknown error occurred."}");
                    break;
            }
        }
        #endregion

        #region Public Methods for Ribbon Interaction
        /// <summary>
        /// Performs analysis when called from the ribbon.
        /// </summary>
        public void PerformAnalysis()
        {
            OnRunAnalysisButtonClick(_runAnalysisButton, EventArgs.Empty);
        }

        /// <summary>
        /// Clears current results.
        /// </summary>
        public void ClearCurrentResults()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(ClearCurrentResults));
                return;
            }
            SetUIMode(UIMode.Idle);
        }

        /// <summary>
        /// Updates progress display from external sources.
        /// </summary>
        public void UpdateProgressFromExternal(ProgressState state) => UpdateProgressDisplay(state);
        #endregion

        #region Analysis Logic
        private async void OnRunAnalysisButtonClick(object sender, EventArgs e)
        {
            if (_runAnalysisButton.Text == CANCEL_ANALYSIS_TEXT || _runAnalysisButton.Text == CANCELLING_TEXT)
            {
                _runAnalysisButton.Text = CANCELLING_TEXT;
                _runAnalysisButton.Enabled = false;
                _analysisCancellationTokenSource?.Cancel();
                return;
            }

            // Check if there's an active workbook
            var workbook = Globals.ThisAddIn?.Application?.ActiveWorkbook;
            if (workbook == null)
            {
                MessageBox.Show("Please open an Excel workbook to analyze.", "No Workbook", MessageBoxButtons.OK, MessageBoxIcon.Information);
                SetUIMode(UIMode.Idle);
                return;
            }

            // Check if workbook is saved (UI thread check)
            if (string.IsNullOrEmpty(workbook.FullName) || workbook.Saved == false)
            {
                var result = MessageBox.Show(
                    "The workbook must be saved to perform an accurate size analysis. Would you like to save it now?",
                    "Save Required", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        workbook.Save();
                    }
                    catch (Exception saveEx)
                    {
                        MessageBox.Show($"Failed to save workbook: {saveEx.Message}", "Save Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        SetUIMode(UIMode.Idle);
                        return;
                    }
                }
                else
                {
                    SetUIMode(UIMode.Idle);
                    return;
                }
            }

            SetUIMode(UIMode.Analyzing);
            ClearAllResultsDataAndSummary();

            _analysisCancellationTokenSource?.Dispose();
            _analysisCancellationTokenSource = new CancellationTokenSource();
            var token = _analysisCancellationTokenSource.Token;
            var progressReporter = new Progress<(string status, int percentage)>(p =>
            {
                UpdateProgressDisplay(ProgressState.ForStep(p.status, p.percentage));
            });

            try
            {
                var worksheetSummaries = await _analysisService.AnalyzeWorkbookAsync(progressReporter, token);
                token.ThrowIfCancellationRequested();

                // Create workbook summary from worksheet summaries
                _currentWorkbookSummary = CreateWorkbookSummary(worksheetSummaries);
                _fullAnalysisResultsList = ConvertWorksheetSummariesToDisplayItems(worksheetSummaries);
                PopulateAndSummarizeResults();
                SetUIMode(UIMode.DisplayingResults);
            }
            catch (OperationCanceledException)
            {
                SetUIMode(UIMode.Idle);
                _toolTip.SetToolTip(_totalSizeLabel, "Analysis cancelled by user.");
            }
            catch (InvalidOperationException ioEx) when (ioEx.Message.Contains("workbook must be saved"))
            {
                SetUIMode(UIMode.Error, "Workbook not saved");
                MessageBox.Show(ioEx.Message, "Save Required", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            catch (FileNotFoundException fnfEx)
            {
                SetUIMode(UIMode.Error, $"File not found: {Path.GetFileName(fnfEx.FileName)}");
                MessageBox.Show($"The workbook file could not be found. Please ensure the file is saved and accessible.\n\nDetails: {fnfEx.Message}", "File Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (UnauthorizedAccessException uaEx)
            {
                SetUIMode(UIMode.Error, "Access denied");
                MessageBox.Show($"Access denied to the workbook file. Please check file permissions.\n\nDetails: {uaEx.Message}", "Access Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (InvalidOperationException ioEx) when (ioEx.Message.Contains("OpenXML"))
            {
                SetUIMode(UIMode.Error, "File format error");
                MessageBox.Show($"Unable to analyze the workbook file format. The file may be corrupted or in an unsupported format.\n\nDetails: {ioEx.Message}", "File Format Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                SetUIMode(UIMode.Error, ex.Message);
                MessageBox.Show($"Analysis failed due to an unexpected error.\n\nDetails: {ex.Message}", "Analysis Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateProgressDisplay(ProgressState state)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<ProgressState>(UpdateProgressDisplay), state);
                return;
            }

            if (_progressPanel?.Visible == true)
            {
                if (_progressLabel != null) _progressLabel.Text = state.CurrentStep ?? state.DetailMessage ?? "Processing...";
                if (_progressBar != null) _progressBar.Value = Math.Max(0, Math.Min(100, state.PercentComplete));
            }
        }

        private void ClearAllResultsDataAndSummary()
        {
            _resultsTable?.Clear();
            _currentWorkbookSummary = null;
            _fullAnalysisResultsList = null;
        }

        private List<WorksheetAnalysisDisplayItem> ConvertWorksheetSummariesToDisplayItems(List<WorksheetAnalysisSummary> worksheetSummaries)
        {
            long totalSize = worksheetSummaries.Sum(w => w.SizeBytes);

            return worksheetSummaries.Select(ws =>
            {
                string contentType = "Data";
                if (ws.HasImages && ws.HasCharts) contentType = "Mixed Content";
                else if (ws.HasImages) contentType = "Images";
                else if (ws.HasCharts) contentType = "Charts";
                else if (ws.HasEmbeddedObjects) contentType = "Embedded Objects";

                double percentage = totalSize > 0 ? ((double)ws.SizeBytes / totalSize * 100) : 0;

                return new WorksheetAnalysisDisplayItem
                {
                    WorksheetName = ws.Name,
                    ContentType = contentType,
                    SizeBytes = ws.SizeBytes,
                    SizePercentage = percentage,
                    ContentDetails = $"Cells: {ws.CellCount:N0}, Formulas: {ws.FormulaCount:N0}, Images: {ws.ImageCount}, Charts: {ws.ChartCount}, Objects: {ws.EmbeddedObjectCount}",
                    CellCount = ws.CellCount,
                    FormulaCount = ws.FormulaCount,
                    ImageCount = ws.ImageCount,
                    ChartCount = ws.ChartCount,
                    EmbeddedObjectCount = ws.EmbeddedObjectCount
                };
            }).ToList();
        }

        private void PopulateAndSummarizeResults()
        {
            _resultsTable.Clear();

            if (_fullAnalysisResultsList != null)
            {
                foreach (var item in _fullAnalysisResultsList)
                {
                    _resultsTable.Rows.Add(
                        item.WorksheetName,
                        item.ContentType,
                        FormatSize(item.SizeBytes),
                        item.ContentDetails
                    );
                }
            }

            // Ensure columns are properly configured after data is loaded
            ConfigureGridColumns();
            UpdateSummaryDisplay();
        }

        private void UpdateSummaryDisplay()
        {
            var sb = new StringBuilder();

            if (_currentWorkbookSummary != null && _totalSizeLabel != null)
            {
                _totalSizeLabel.Text = $"Total Size: {FormatSize(_currentWorkbookSummary.TotalWorkbookSizeBytes)}";
                sb.AppendLine($"Total Workbook Size: {FormatSize(_currentWorkbookSummary.TotalWorkbookSizeBytes)} across {_currentWorkbookSummary.WorksheetCount} worksheets.");
                sb.AppendLine($"Uses proportional allocation for accurate breakdown.");
                sb.AppendLine($"Total Cells: {_currentWorkbookSummary.TotalCells:N0}");
                sb.AppendLine($"Total Formulas: {_currentWorkbookSummary.TotalFormulas:N0}");

                if (!string.IsNullOrEmpty(_currentWorkbookSummary.LargestWorksheetName) && _currentWorkbookSummary.LargestWorksheetSizeBytes > 0)
                {
                    sb.AppendLine();
                    sb.AppendLine($"Largest Worksheet:");
                    sb.AppendLine($"  - Name: {_currentWorkbookSummary.LargestWorksheetName}");
                    sb.AppendLine($"  - Size: {FormatSize(_currentWorkbookSummary.LargestWorksheetSizeBytes)}");
                    sb.AppendLine($"  - Details: {_currentWorkbookSummary.LargestWorksheetDescription}");
                }
            }

            if (_toolTip != null && _totalSizeLabel != null)
            {
                _toolTip.SetToolTip(_totalSizeLabel, sb.ToString().Trim());
            }
        }

        private static string FormatSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024.0 * 1024.0):F1} MB";
            return $"{bytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
        }

        /// <summary>
        /// Creates a workbook summary from worksheet summaries.
        /// </summary>
        private WorkbookAnalysisSummary CreateWorkbookSummary(List<WorksheetAnalysisSummary> worksheetSummaries)
        {
            var largest = worksheetSummaries.OrderByDescending(w => w.SizeBytes).FirstOrDefault();
            long totalSize = worksheetSummaries.Sum(w => w.SizeBytes);

            return new WorkbookAnalysisSummary
            {
                WorksheetCount = worksheetSummaries.Count,
                TotalCells = worksheetSummaries.Sum(w => w.CellCount),
                TotalFormulas = worksheetSummaries.Sum(w => w.FormulaCount),
                TotalImages = worksheetSummaries.Sum(w => w.ImageCount),
                TotalCharts = worksheetSummaries.Sum(w => w.ChartCount),
                TotalEmbeddedObjects = worksheetSummaries.Sum(w => w.EmbeddedObjectCount),
                TotalWorkbookSizeBytes = totalSize,
                LargestWorksheetName = largest?.Name,
                LargestWorksheetSizeBytes = largest?.SizeBytes ?? 0,
                LargestWorksheetDescription = largest != null ?
                    $"Cells: {largest.CellCount:N0}, Formulas: {largest.FormulaCount:N0}, Images: {largest.ImageCount}, Charts: {largest.ChartCount}" :
                    "N/A"
            };
        }
        #endregion

        #region Cleanup
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _analysisCancellationTokenSource?.Dispose();
                _toolTip?.Dispose();
            }
            base.Dispose(disposing);
        }
        #endregion
    }
}

// --- END OF FILE AnalyzePane.cs ---
