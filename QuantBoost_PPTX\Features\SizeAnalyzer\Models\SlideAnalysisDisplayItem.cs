using System;
using System.ComponentModel;

namespace QuantBoost_Powerpoint_Addin.Features.SizeAnalyzer.Models
{
    /// <summary>
    /// Display model for slide analysis results in the WPF UI.
    /// Provides data binding properties for the DataGrid.
    /// </summary>
    public class SlideAnalysisDisplayItem : INotifyPropertyChanged
    {
        #region Private Fields
        private string _slideName;
        private string _contentType;
        private long _sizeBytes;
        private double _sizePercentage;
        private string _contentDetails;
        private int _imageCount;
        private int _chartCount;
        private int _mediaCount;
        private int _embeddedObjectCount;
        private int _excelLinkCount;
        #endregion

        #region Public Properties
        /// <summary>
        /// Gets or sets the slide name/title.
        /// </summary>
        public string SlideName
        {
            get => _slideName;
            set
            {
                if (_slideName != value)
                {
                    _slideName = value;
                    OnPropertyChanged(nameof(SlideName));
                }
            }
        }

        /// <summary>
        /// Gets or sets the content type description.
        /// </summary>
        public string ContentType
        {
            get => _contentType;
            set
            {
                if (_contentType != value)
                {
                    _contentType = value;
                    OnPropertyChanged(nameof(ContentType));
                }
            }
        }

        /// <summary>
        /// Gets or sets the size in bytes.
        /// </summary>
        public long SizeBytes
        {
            get => _sizeBytes;
            set
            {
                if (_sizeBytes != value)
                {
                    _sizeBytes = value;
                    OnPropertyChanged(nameof(SizeBytes));
                    OnPropertyChanged(nameof(SizeKB));
                }
            }
        }

        /// <summary>
        /// Gets the size in kilobytes for display.
        /// </summary>
        public double SizeKB => SizeBytes / 1024.0;

        /// <summary>
        /// Gets or sets the percentage of total presentation size.
        /// </summary>
        public double SizePercentage
        {
            get => _sizePercentage;
            set
            {
                if (Math.Abs(_sizePercentage - value) > 0.001)
                {
                    _sizePercentage = value;
                    OnPropertyChanged(nameof(SizePercentage));
                }
            }
        }

        /// <summary>
        /// Gets or sets the content details string.
        /// </summary>
        public string ContentDetails
        {
            get => _contentDetails;
            set
            {
                if (_contentDetails != value)
                {
                    _contentDetails = value;
                    OnPropertyChanged(nameof(ContentDetails));
                }
            }
        }

        /// <summary>
        /// Gets or sets the number of images on the slide.
        /// </summary>
        public int ImageCount
        {
            get => _imageCount;
            set
            {
                if (_imageCount != value)
                {
                    _imageCount = value;
                    OnPropertyChanged(nameof(ImageCount));
                }
            }
        }

        /// <summary>
        /// Gets or sets the number of charts on the slide.
        /// </summary>
        public int ChartCount
        {
            get => _chartCount;
            set
            {
                if (_chartCount != value)
                {
                    _chartCount = value;
                    OnPropertyChanged(nameof(ChartCount));
                }
            }
        }

        /// <summary>
        /// Gets or sets the number of media objects on the slide.
        /// </summary>
        public int MediaCount
        {
            get => _mediaCount;
            set
            {
                if (_mediaCount != value)
                {
                    _mediaCount = value;
                    OnPropertyChanged(nameof(MediaCount));
                }
            }
        }

        /// <summary>
        /// Gets or sets the number of embedded objects on the slide.
        /// </summary>
        public int EmbeddedObjectCount
        {
            get => _embeddedObjectCount;
            set
            {
                if (_embeddedObjectCount != value)
                {
                    _embeddedObjectCount = value;
                    OnPropertyChanged(nameof(EmbeddedObjectCount));
                }
            }
        }

        /// <summary>
        /// Gets or sets the number of Excel links on the slide.
        /// </summary>
        public int ExcelLinkCount
        {
            get => _excelLinkCount;
            set
            {
                if (_excelLinkCount != value)
                {
                    _excelLinkCount = value;
                    OnPropertyChanged(nameof(ExcelLinkCount));
                }
            }
        }
        #endregion

        #region INotifyPropertyChanged Implementation
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion
    }
}
