C:\VS projects\QuantBoost\QuantBoost.Licensing\bin\Debug\QuantBoost.Licensing.dll
C:\VS projects\QuantBoost\QuantBoost.Licensing\bin\Debug\QuantBoost.Licensing.pdb
C:\VS projects\QuantBoost\QuantBoost.Licensing\bin\Debug\Newtonsoft.Json.Bson.dll
C:\VS projects\QuantBoost\QuantBoost.Licensing\bin\Debug\Newtonsoft.Json.dll
C:\VS projects\QuantBoost\QuantBoost.Licensing\bin\Debug\Newtonsoft.Json.xml
C:\VS projects\QuantBoost\QuantBoost.Licensing\bin\Debug\Newtonsoft.Json.Bson.pdb
C:\VS projects\QuantBoost\QuantBoost.Licensing\bin\Debug\Newtonsoft.Json.Bson.xml
C:\VS projects\QuantBoost\QuantBoost.Licensing\obj\Debug\QuantBoost.Licensing.csproj.AssemblyReference.cache
C:\VS projects\QuantBoost\QuantBoost.Licensing\obj\Debug\QuantBoost.Licensing.csproj.CoreCompileInputs.cache
C:\VS projects\QuantBoost\QuantBoost.Licensing\obj\Debug\QuantBoo.DF007440.Up2Date
C:\VS projects\QuantBoost\QuantBoost.Licensing\obj\Debug\QuantBoost.Licensing.dll
C:\VS projects\QuantBoost\QuantBoost.Licensing\obj\Debug\QuantBoost.Licensing.pdb
