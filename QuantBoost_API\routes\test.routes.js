// Test routes for middleware testing
const express = require('express');
const router = express.Router();
const { sendSuccess } = require('../utils/responseHelpers');
const { authenticateApiKey } = require('../middleware/authMiddleware');

// Simple test route for middleware validation - protected by API key
router.get('/some-protected-route', authenticateApiKey, (req, res) => {
    sendSuccess(res, { test: true }, 'Test route accessed successfully');
});

module.exports = router;
