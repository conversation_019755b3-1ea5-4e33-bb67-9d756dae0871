C:\VS projects\QuantBoost\QuantBoost_Excel\bin\Release\QuantBoost_Excel.dll.config
C:\VS projects\QuantBoost\QuantBoost_Excel\bin\Release\QuantBoost_Excel.dll
C:\VS projects\QuantBoost\QuantBoost_Excel\bin\Release\QuantBoost_Excel.pdb
C:\VS projects\QuantBoost\QuantBoost_Excel\bin\Release\QuantBoost_Excel.dll.manifest
C:\VS projects\QuantBoost\QuantBoost_Excel\bin\Release\QuantBoost_Excel.vsto
C:\VS projects\QuantBoost\QuantBoost_Excel\bin\Release\DocumentFormat.OpenXml.dll
C:\VS projects\QuantBoost\QuantBoost_Excel\bin\Release\Microsoft.Office.Tools.Common.v4.0.Utilities.dll
C:\VS projects\QuantBoost\QuantBoost_Excel\bin\Release\Newtonsoft.Json.dll
C:\VS projects\QuantBoost\QuantBoost_Excel\bin\Release\QuantBoost_Licensing.dll
C:\VS projects\QuantBoost\QuantBoost_Excel\bin\Release\QuantBoost_Licensing.pdb
C:\VS projects\QuantBoost\QuantBoost_Excel\bin\Release\DocumentFormat.OpenXml.xml
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\QuantBoost_Excel.csproj.AssemblyReference.cache
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\Features\ExcelTrace\UI\TraceView.g.cs
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\Features\SizeAnalyzer\Views\AnalysisPaneView.g.cs
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\GeneratedInternalTypeHelper.g.cs
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\QuantBoost_Excel_MarkupCompile.cache
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\QuantBoost_Excel_MarkupCompile.lref
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\Features\ExcelTrace\UI\TraceView.baml
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\Features\SizeAnalyzer\Views\AnalysisPaneView.baml
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\QuantBoost_Excel.g.resources
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\QuantBoost_Excel.Properties.Resources.resources
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\QuantBoost_Excel.csproj.GenerateResource.cache
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\QuantBoost_Excel.csproj.CoreCompileInputs.cache
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\QuantBoo.2AC3173F.Up2Date
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\QuantBoost_Excel.dll
C:\VS projects\QuantBoost\QuantBoost_Excel\obj\Release\QuantBoost_Excel.pdb
