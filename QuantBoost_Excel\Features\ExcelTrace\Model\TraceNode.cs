// --- START OF FILE TraceNode.cs ---

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using Excel = Microsoft.Office.Interop.Excel;

namespace QuantBoost_Excel.Features.ExcelTrace.Model
{
    /// <summary>
    /// Defines the type of node in the precedent trace tree, used for UI display and navigation logic.
    /// </summary>
    public enum NodeType
    {
        /// <summary>
        /// The root node representing the starting cell being traced.
        /// </summary>
        Root,

        /// <summary>
        /// A precedent cell on the same worksheet as its parent.
        /// </summary>
        SameSheet,

        /// <summary>
        /// A precedent cell on a different worksheet within the same workbook.
        /// </summary>
        DifferentSheet,

        /// <summary>
        /// A precedent cell in an external workbook.
        /// </summary>
        ExternalWorkbook,

        /// <summary>
        /// A named range reference.
        /// </summary>
        NamedRange,

        /// <summary>
        /// A multi-cell range (e.g., AM28:AM33) that contains individual cells as children.
        /// EPIC 1 TASK 1.2: Added for hierarchical range node creation.
        /// </summary>
        Range,

        /// <summary>
        /// A summary node indicating truncated content for performance reasons.
        /// CRITICAL FIX: Added for large ranges that are truncated to improve UI performance.
        /// </summary>
        Summary,

        /// <summary>
        /// An error occurred while tracing this node (e.g., broken reference, access denied).
        /// </summary>
        Error,

        /// <summary>
        /// An individual element within an array.
        /// </summary>
        ArrayElement
    }

    /// <summary>
    /// Represents a single node in the precedent trace tree, containing all information needed
    /// for display, navigation, and cycle detection.
    /// </summary>
    public class TraceNode : INotifyPropertyChanged
    {
        #region Core Properties

        /// <summary>
        /// Gets or sets the display text for this node (e.g., "'Sheet1'!A1", "MyNamedRange", "[ExternalBook.xlsx]Sheet1!A1").
        /// This is the primary text shown in the tree view.
        /// </summary>
        public string DisplayText { get; set; }

        /// <summary>
        /// Gets or sets the fully qualified address used for cycle detection and uniqueness.
        /// Format: "[WorkbookName]SheetName!Address" for consistency across all node types.
        /// </summary>
        public string FullAddress { get; set; }

        /// <summary>
        /// Gets or sets the formatted display value from the cell (e.g., "1,586", "43%", "#REF!").
        /// This shows the actual content/result of the cell as it appears in Excel.
        /// </summary>
        public string DisplayValue { get; set; }

        /// <summary>
        /// Gets or sets the raw underlying value from the cell (Value2 property).
        /// Used for proper formatting in the UI without truncation issues.
        /// </summary>
        public object RawValue { get; set; }

        /// <summary>
        /// Gets or sets the Excel number format string for this cell.
        /// Used for accurate number formatting in the UI.
        /// </summary>
        public string NumberFormat { get; set; }

        /// <summary>
        /// Gets or sets the formula text if this cell contains a formula, otherwise null.
        /// For the root node, this is the formula being traced.
        /// </summary>
        public string Formula { get; set; }

        /// <summary>
        /// Gets or sets the type of this node, which determines display icons and navigation behavior.
        /// </summary>
        public NodeType NodeType { get; set; }

        /// <summary>
        /// Gets or sets the collection of child nodes (direct precedents of this node).
        /// Initialized as an empty list to prevent null reference exceptions.
        /// </summary>
        public List<TraceNode> Children { get; set; } = new List<TraceNode>();

        // REMOVED: SourceRange property to fix InvalidComObjectException
        // Live COM objects should never be stored in long-lived C# classes
        // Instead, we re-acquire ranges just-in-time using the stored address

        /// <summary>
        /// Gets or sets a value indicating whether an error occurred while processing this node.
        /// When true, DisplayValue typically contains the error description.
        /// </summary>
        public bool HasError { get; set; }

        #endregion

        #region Extended Properties

        /// <summary>
        /// Gets or sets the workbook name (without path) that contains this cell.
        /// Used for cross-workbook navigation and display purposes.
        /// </summary>
        public string WorkbookName { get; set; }

        /// <summary>
        /// Gets or sets the worksheet name that contains this cell.
        /// Used for cross-sheet navigation and display purposes.
        /// </summary>
        public string WorksheetName { get; set; }

        /// <summary>
        /// Gets or sets the cell address within the worksheet (e.g., "A1", "B5:C10").
        /// This is the local address without sheet or workbook qualifiers.
        /// </summary>
        public string CellAddress { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this node's children have been loaded.
        /// Used for lazy loading to improve performance with large precedent trees.
        /// </summary>
        public bool ChildrenLoaded { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this node is part of a circular reference.
        /// Set during trace processing when a cycle is detected.
        /// </summary>
        public bool IsCircularReference { get; set; }

        /// <summary>
        /// Gets or sets the depth level of this node in the trace tree (root = 0).
        /// Used for limiting recursion depth and display formatting.
        /// </summary>
        public int Depth { get; set; }

        /// <summary>
        /// Gets or sets additional error information when HasError is true.
        /// Contains detailed exception information for debugging purposes.
        /// </summary>
        public string ErrorDetails { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this node is currently expanded in the UI tree view.
        /// </summary>
        private bool _isExpanded;
        public bool IsExpanded
        {
            get => _isExpanded;
            set
            {
                if (_isExpanded != value)
                {
                    _isExpanded = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Gets or sets the start index of this node's reference within the parent's formula string.
        /// EPIC 1 TASK 1.2: Critical property for formula highlighting feature.
        /// </summary>
        public int FormulaStartIndex { get; set; }

        /// <summary>
        /// Gets or sets the length of this node's reference within the parent's formula string.
        /// EPIC 1 TASK 1.2: Critical property for formula highlighting feature.
        /// </summary>
        public int FormulaLength { get; set; }

        /// <summary>
        /// Gets or sets the group index for formula highlighting color coordination.
        /// Used to assign consistent colors to related precedents in the formula bar.
        /// </summary>
        public int GroupIndex { get; set; } = -1;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the <see cref="TraceNode"/> class with default values.
        /// </summary>
        public TraceNode()
        {
            Children = new List<TraceNode>();
            ChildrenLoaded = false;
            HasError = false;
            IsCircularReference = false;
            Depth = 0;
            _isExpanded = false; // Use backing field
            FormulaStartIndex = -1; // EPIC 1 TASK 1.2: Initialize to -1 to indicate no formula position
            FormulaLength = 0;      // EPIC 1 TASK 1.2: Initialize to 0
            GroupIndex = -1;        // Initialize to -1 to indicate no group assignment
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TraceNode"/> class with the specified display text and node type.
        /// </summary>
        /// <param name="displayText">The display text for this node.</param>
        /// <param name="nodeType">The type of this node.</param>
        public TraceNode(string displayText, NodeType nodeType) : this()
        {
            DisplayText = displayText ?? throw new ArgumentNullException(nameof(displayText));
            NodeType = nodeType;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets a value indicating whether this node has any child nodes.
        /// </summary>
        public bool HasChildren => Children != null && Children.Count > 0;

        /// <summary>
        /// Gets a value indicating whether this node represents an external reference
        /// (different workbook or named range).
        /// </summary>
        public bool IsExternalReference => NodeType == NodeType.ExternalWorkbook || NodeType == NodeType.NamedRange;

        /// <summary>
        /// Gets a value indicating whether this node can be navigated to
        /// (has a valid FullAddress and is not an error node).
        /// </summary>
        public bool CanNavigate => !HasError && !string.IsNullOrEmpty(FullAddress) && NodeType != NodeType.Error;

        /// <summary>
        /// Creates a formatted string representation of this node for debugging purposes.
        /// </summary>
        /// <returns>A string containing the key properties of this node.</returns>
        public override string ToString()
        {
            var parts = new List<string>
            {
                $"Type: {NodeType}",
                $"Display: {DisplayText ?? "null"}",
                $"Value: {DisplayValue ?? "null"}"
            };

            if (HasError)
                parts.Add("ERROR");
            
            if (IsCircularReference)
                parts.Add("CIRCULAR");

            if (HasChildren)
                parts.Add($"Children: {Children.Count}");

            return $"TraceNode[{string.Join(", ", parts)}]";
        }

        /// <summary>
        /// Creates a deep copy of this node without the SourceRange (to avoid COM object issues).
        /// Children are also deep copied recursively.
        /// </summary>
        /// <returns>A new TraceNode instance with copied data.</returns>
        public TraceNode CreateCopy()
        {
            var copy = new TraceNode
            {
                DisplayText = this.DisplayText,
                FullAddress = this.FullAddress,
                DisplayValue = this.DisplayValue,
                RawValue = this.RawValue,
                NumberFormat = this.NumberFormat,
                Formula = this.Formula,
                NodeType = this.NodeType,
                WorkbookName = this.WorkbookName,
                WorksheetName = this.WorksheetName,
                CellAddress = this.CellAddress,
                ChildrenLoaded = this.ChildrenLoaded,
                IsCircularReference = this.IsCircularReference,
                Depth = this.Depth,
                HasError = this.HasError,
                ErrorDetails = this.ErrorDetails,
                IsExpanded = this.IsExpanded,
                FormulaStartIndex = this.FormulaStartIndex, // EPIC 1 TASK 1.2: Copy formula position data
                FormulaLength = this.FormulaLength,         // EPIC 1 TASK 1.2: Copy formula position data
                GroupIndex = this.GroupIndex                // Copy group index for highlighting
                // Note: SourceRange property has been removed to avoid COM object issues
            };

            // Deep copy children
            if (this.Children != null)
            {
                copy.Children = this.Children.Select(child => child.CreateCopy()).ToList();
            }

            return copy;
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Occurs when a property value changes.
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event for the specified property.
        /// </summary>
        /// <param name="propertyName">The name of the property that changed.</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}

// --- END OF FILE TraceNode.cs ---
