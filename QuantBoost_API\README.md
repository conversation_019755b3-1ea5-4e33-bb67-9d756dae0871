# QuantBoost API - Production Ready

A production-ready Node.js API for QuantBoost licensing and user management, containerized and optimized for Azure Container Apps deployment.

## 🚀 Production Readiness Features

### ✅ **Implemented**
- **Structured Logging** with <PERSON> (cloud-native JSON format)
- **Health Check Endpoints** (`/health/live`, `/health/ready`)
- **Security Headers** with Helmet.js
- **Input Validation** with Joi schemas
- **Request Metrics & Telemetry** with Application Insights
- **Response Compression** for performance
- **Docker Containerization** with multi-stage builds
- **CI/CD Pipeline** with GitHub Actions
- **Infrastructure as Code** with Terraform
- **ESLint Configuration** enforcing logger usage
- **Environment Configuration** management

### 🔒 **Security Features**
- Content Security Policy (CSP)
- HSTS headers
- Input validation middleware
- Rate limiting
- Non-root container user
- Secret management via Azure Key Vault

### 📊 **Observability**
- Structured JSON logging for cloud platforms
- Request correlation IDs
- Performance metrics tracking
- Application Insights integration
- Health check probes

## 🛠 **Local Development**

### Prerequisites
- Node.js 18+
- Docker & Docker Compose
- Azure CLI (for deployment)

### Quick Start
```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Run with Docker
npm run docker:compose
```

### Available Scripts
```bash
npm start              # Start production server
npm run dev           # Start with nodemon
npm test              # Run Jest tests
npm run lint          # ESLint check
npm run lint:fix      # Auto-fix linting issues
npm run docker:build  # Build Docker image
npm run docker:run    # Run Docker container
```

## 🏗 **Infrastructure & Deployment**

### Azure Container Apps Architecture
```
Internet → Azure Front Door → Container Apps → Supabase
                ↓
        Application Insights ← Container Registry
                ↓
           Key Vault (Secrets)
```

### Deploy to Azure
1. **Set up infrastructure:**
   ```bash
   cd infrastructure
   terraform init
   terraform plan
   terraform apply
   ```

2. **Configure GitHub Secrets:**
   - `AZURE_CREDENTIALS`
   - `ACR_USERNAME`
   - `ACR_PASSWORD`

3. **Push to main branch** - CI/CD pipeline will automatically deploy

### Environment Variables
```bash
# Required
NODE_ENV=production
PORT=3000
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key
JWT_SECRET=your_jwt_secret

# Optional
LOG_LEVEL=info
APPLICATIONINSIGHTS_CONNECTION_STRING=your_ai_string
```

## 📋 **API Endpoints**

### Health Checks
- `GET /health/live` - Liveness probe
- `GET /health/ready` - Readiness probe with dependency checks

### Authentication
- `POST /v1/auth/login` - User login
- `POST /v1/auth/register` - User registration
- `POST /v1/auth/magic-link` - Magic link authentication

### Licenses
- `GET /v1/licenses/validate` - License validation
- `POST /v1/licenses/activate` - License activation

### Admin Routes
- `GET /v1/admin/users` - User management
- `GET /v1/admin/licenses` - License management
- `GET /v1/admin/subscriptions` - Subscription management

## 🔧 **Configuration**

### Logging
Structured JSON logging with Winston:
```javascript
const logger = require('./utils/logger');
logger.info('Message', { metadata: 'object' });
```

### Validation
Joi schemas for input validation:
```javascript
const { validate, schemas } = require('./middleware/validation');
app.post('/api/route', validate(schemas.example), handler);
```

### Telemetry
Application Insights integration:
```javascript
const telemetry = require('./utils/telemetry');
telemetry.trackEvent('user_action', { userId, action });
```

## 🐳 **Docker**

### Build & Run
```bash
# Build image
docker build -t quantboost-api .

# Run container
docker run -p 3000:3000 --env-file .env quantboost-api

# Docker Compose (development)
docker-compose up --build
```

### Production Image Features
- Multi-stage build for smaller images
- Non-root user for security
- Signal handling with dumb-init
- Optimized layer caching

## 📈 **Scaling & Performance**

### Auto-scaling Configuration
- **Min replicas:** 2 (high availability)
- **Max replicas:** 5
- **Scale trigger:** 50 concurrent requests per instance
- **CPU:** 0.5 cores per instance
- **Memory:** 1GB per instance

### Performance Optimizations
- Response compression (gzip)
- Connection pooling
- Request correlation for debugging
- Efficient Docker layering

## 🔍 **Monitoring**

### Logs
```bash
# View live logs in Azure
az containerapp logs show --name ca-quantboost-api --resource-group rg-quantboost-api-prod --follow

# Query structured logs
# Example: Find all errors for a specific request ID
| where customDimensions.requestId == "abc123" and customDimensions.level == "error"
```

### Metrics
- HTTP request duration
- Request count by status code
- Error rates
- Custom business metrics

## 🔄 **CI/CD Pipeline**

### GitHub Actions Workflow
1. **Test Stage:** Run tests and linting
2. **Build Stage:** Create Docker image
3. **Deploy Stage:** Push to ACR and update Container App

### Deployment Strategy
- **Blue-Green:** Zero-downtime deployments
- **Rollback:** Automatic on health check failures
- **Secrets:** Managed via Azure Key Vault

## 🛡 **Security Considerations**

### Container Security
- Non-root user execution
- Minimal base image (Alpine)
- No sensitive data in image layers
- Regular security updates

### Runtime Security
- HTTPS enforcement
- Security headers (CSP, HSTS)
- Input validation on all endpoints
- Rate limiting to prevent abuse

### Secrets Management
- Environment variables via Azure Key Vault
- No secrets in code or Docker images
- Rotation strategy for API keys

## 📝 **Development Guidelines**

### Logging Standards
```javascript
// ❌ Don't use console.log
console.log('User logged in');

// ✅ Use structured logging
logger.info('User authentication successful', {
  userId: user.id,
  email: user.email,
  loginMethod: 'magic-link'
});
```

### Error Handling
```javascript
// ✅ Proper error logging with context
logger.error('Database operation failed', {
  operation: 'getUserLicenses',
  error: err.message,
  stack: err.stack,
  userId,
  requestId: req.requestId
});
```

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Ensure linting passes
5. Submit a pull request

## 📞 **Support**

- **Documentation:** See `/docs` folder
- **Issues:** GitHub Issues
- **Monitoring:** Azure Application Insights dashboard

---

**Built for Azure Container Apps** 🚀 **Production Ready** ✅ **Enterprise Grade** 🏢