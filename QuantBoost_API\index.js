// --- START OF COMPLETE index.js FILE ---

// Add environment configuration at the very top
if (process.env.NODE_ENV !== 'production') {
    require('dotenv').config();
}

const telemetry = require('./utils/telemetry');
const logger = require('./utils/logger');
const express = require('express');
const cors = require('cors');
const compression = require('compression');
const securityHeaders = require('./middleware/security');
const metricsMiddleware = require('./middleware/metrics');
const { rateLimiter } = require('./middleware/rateLimitMiddleware');
const { authenticateJWT } = require('./middleware/authMiddleware');
const authRoutes = require('./routes/auth.routes');
const licenseRoutes = require('./routes/licenses.routes');
const adminLicenseRoutes = require('./routes/adminLicenses.routes');
const adminUserRoutes = require('./routes/adminUsers.routes.js');
const adminSubscriptionRoutes = require('./routes/adminSubscriptions.routes.js');
const adminActivationRoutes = require('./routes/adminActivations.routes.js');
const userProfileRoutes = require('./routes/userProfile.routes.js');
const userLicensesRoutes = require('./routes/userLicenses.routes.js');
const userSubscriptionsRoutes = require('./routes/userSubscriptions.routes.js');
const diagnosticRoutes = require('./routes/diagnostic.routes.js');
const teamLicensesRoutes = require('./routes/teamLicenses.routes.js');
const teamAdminRoutes = require('./routes/teamAdmin.routes.js');
const testRoutes = require('./routes/test.routes.js');
const healthRoutes = require('./routes/health.routes.js');

logger.info('Supabase Client Initialized', {
  url: process.env.SUPABASE_URL?.substring(0, 20) + '...'
});

const app = express();

// --- Global Middleware ---
// Security headers first
app.use(securityHeaders);

// Response compression
app.use(compression({
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  }
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cors());

// Metrics collection
app.use(metricsMiddleware);

// Update request logger middleware with proper metadata
app.use((req, res, next) => {
  const requestId = req.headers['x-request-id'] || Math.random().toString(36).substr(2, 9);
  req.requestId = requestId;
  
  logger.info('Incoming request', {
    requestId,
    method: req.method,
    path: req.path,
    query: req.query,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('user-agent'),
    correlationId: req.headers['x-correlation-id']
  });
  
  // Log response when finished
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('Request completed', {
      requestId,
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration,
      contentLength: res.get('content-length')
    });
  });
  
  next();
});

app.use(rateLimiter);

// --- Route Mounting ---
// Public routes - no JWT needed
app.use('/v1/auth', authRoutes);
app.use('/health', healthRoutes);
app.use('/v1', testRoutes);

// Protected routes - JWT is now required
app.use('/v1/licenses', authenticateJWT, licenseRoutes);
app.use('/v1/me', authenticateJWT, userProfileRoutes);
app.use('/v1/me', authenticateJWT, userLicensesRoutes);
app.use('/v1/me', authenticateJWT, userSubscriptionsRoutes);
app.use('/v1/me', authenticateJWT, diagnosticRoutes);
app.use('/v1/me', authenticateJWT, teamLicensesRoutes);
app.use('/v1/me/subscriptions/:subscriptionId', authenticateJWT, teamAdminRoutes);

const { authorizeAdmin } = require('./middleware/adminAuthMiddleware');

// Admin routes - Require both a valid JWT and an 'admin' role.
// The authorizeAdmin middleware MUST come after authenticateJWT.
app.use('/v1/admin/licenses', authenticateJWT, authorizeAdmin, adminLicenseRoutes);
app.use('/v1/admin/users', authenticateJWT, authorizeAdmin, adminUserRoutes);
app.use('/v1/admin/subscriptions', authenticateJWT, authorizeAdmin, adminSubscriptionRoutes);
app.use('/v1/admin/activations', authenticateJWT, authorizeAdmin, adminActivationRoutes);

// --- Auth Callback Route (Root Handler) ---
// Handle Supabase magic link redirects that come to the API root with auth tokens
app.get('/', (req, res) => {
    // Log the incoming request for debugging
    console.log('Root route accessed:', {
        url: req.url,
        query: req.query,
        headers: {
            referer: req.headers.referer,
            userAgent: req.headers['user-agent']
        }
    });
    
    // Check if this is a magic link callback with auth tokens in the URL fragment
    // Since URL fragments aren't sent to the server, we need to handle this with client-side JavaScript
    
    const hasAuthParams = req.url.includes('#') || 
                         req.query.access_token || 
                         req.query.code ||
                         req.query.error ||
                         req.headers.referer?.includes('supabase') ||
                         req.headers.referer?.includes('auth');
    
    // Check if coming from frontend (which means this is likely a magic link redirect)
    const frontendHostname = (process.env.FRONTEND_URL || 'https://app-quantboost-frontend-staging.azurewebsites.net').replace('https://', '');
    const fromFrontend = req.headers.referer?.includes(frontendHostname);
    
    console.log('Auth detection result:', {
        hasAuthParams,
        fromFrontend,
        queryKeys: Object.keys(req.query),
        hasReferer: !!req.headers.referer,
        refererUrl: req.headers.referer
    });
    
    if (hasAuthParams || fromFrontend || Object.keys(req.query).length > 0) {
        // This looks like an auth callback, serve a redirect page
        const frontendUrl = process.env.FRONTEND_URL || 'https://app-quantboost-frontend-staging.azurewebsites.net';
        const dashboardUrl = `${frontendUrl}/dashboard`;
        
        console.log('Serving auth callback redirect page');
        
        // Set CSP header to allow inline scripts for auth processing
        res.setHeader('Content-Security-Policy', 
            "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' https:; font-src 'self'");
        
        res.send(`
<!DOCTYPE html>
<html>
<head>
    <title>Redirecting to Dashboard...</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            text-align: center; 
            padding: 50px; 
            background: #f8f9fa;
            margin: 0;
        }
        .container { 
            max-width: 400px; 
            margin: 0 auto; 
            background: white; 
            padding: 40px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .spinner { 
            border: 3px solid #f3f3f3; 
            border-top: 3px solid #007bff; 
            border-radius: 50%; 
            width: 30px; 
            height: 30px; 
            animation: spin 1s linear infinite; 
            margin: 20px auto;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .debug { font-size: 12px; color: #666; margin-top: 20px; text-align: left; }
        .manual-link { color: #007bff; text-decoration: none; }
        .manual-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h2>✅ Authentication Successful!</h2>
        <div class="spinner"></div>
        <p id="status">Redirecting you to your dashboard...</p>
        <p><small>If you're not redirected automatically, <a href="${dashboardUrl}" class="manual-link" id="manual-link">click here</a>.</small></p>
        <div class="debug">
            <p><strong>Debug Info:</strong></p>
            <p id="debug-info">Processing authentication...</p>
        </div>
    </div>
    
    <script>
        console.log('Auth redirect page loaded');
        console.log('Current URL:', window.location.href);
        console.log('Hash:', window.location.hash);
        
        // Extract auth tokens from URL fragment and redirect to frontend with tokens
        function processAuthAndRedirect() {
            const statusEl = document.getElementById('status');
            const debugEl = document.getElementById('debug-info');
            
            try {
                const hash = window.location.hash.substring(1);
                const params = new URLSearchParams(hash);
                
                const accessToken = params.get('access_token');
                const refreshToken = params.get('refresh_token');
                const expiresIn = params.get('expires_in');
                const error = params.get('error');
                
                console.log('Extracted params:', {
                    hasAccessToken: !!accessToken,
                    hasRefreshToken: !!refreshToken,
                    accessTokenLength: accessToken?.length,
                    refreshTokenLength: refreshToken?.length,
                    error: error
                });
                
                debugEl.innerHTML = 'Access Token: ' + (accessToken ? 'Found (' + accessToken.length + ' chars)' : 'Not found') + 
                                   '<br>Refresh Token: ' + (refreshToken ? 'Found (' + refreshToken.length + ' chars)' : 'Not found');
                
                if (error) {
                    statusEl.innerHTML = 'Authentication error: ' + error;
                    debugEl.innerHTML += '<br>Error: ' + error;
                    return;
                }
                
                // Build frontend dashboard URL with auth tokens
                let dashboardUrl = '${dashboardUrl}';
                
                if (accessToken && refreshToken) {
                    // Add auth tokens as URL parameters for the frontend to process
                    dashboardUrl += '?auth=success';
                    dashboardUrl += '&access_token=' + encodeURIComponent(accessToken);
                    dashboardUrl += '&refresh_token=' + encodeURIComponent(refreshToken);
                    if (expiresIn) {
                        dashboardUrl += '&expires_in=' + encodeURIComponent(expiresIn);
                    }
                    dashboardUrl += '&payment=success'; // Indicate this came from payment flow
                    
                    statusEl.innerHTML = 'Tokens found! Redirecting to dashboard...';
                    debugEl.innerHTML += '<br>Redirecting to: ' + dashboardUrl.substring(0, 100) + '...';
                } else {
                    statusEl.innerHTML = 'No auth tokens found. Using fallback redirect...';
                    debugEl.innerHTML += '<br>No tokens found, using basic dashboard URL';
                }
                
                // Update manual link
                document.getElementById('manual-link').href = dashboardUrl;
                
                // Redirect to frontend dashboard
                console.log('Redirecting to:', dashboardUrl);
                setTimeout(() => {
                    window.location.href = dashboardUrl;
                }, 1000); // Small delay to show debug info
                
            } catch (err) {
                console.error('Error processing auth redirect:', err);
                statusEl.innerHTML = 'Error processing authentication. Please click the link below.';
                debugEl.innerHTML = 'Error: ' + err.message;
            }
        }
        
        // Process immediately when page loads
        processAuthAndRedirect();
        
        // Fallback redirect after 3 seconds
        setTimeout(() => {
            if (window.location.hostname.includes('azurecontainerapps.io')) {
                window.location.href = '${dashboardUrl}?auth=fallback';
            }
        }, 3000);
    </script>
</body>
</html>
        `);
    } else {
        // Regular API root request
        res.json({
            success: true,
            message: 'QuantBoost API is running',
            version: '1.0.0',
            timestamp: new Date().toISOString()
        });
    }
});

// --- Global Error Handling ---
app.use((err, req, res, next) => {
    logger.error('Unhandled error', {
        error: err.message,
        stack: err.stack,
        statusCode: err.statusCode || 500,
        requestId: req.requestId,
        method: req.method,
        path: req.path,
        body: req.body,
        query: req.query
    });
    
    const statusCode = err.statusCode || 500;
    const message = process.env.NODE_ENV === 'production' && statusCode === 500
                  ? 'An unexpected error occurred.'
                  : err.message || 'Internal Server Error';
    
    res.status(statusCode).json({
        success: false,
        error: {
          message,
          requestId: req.requestId
        }
    });
});

// --- 404 Handler for unmatched routes ---
app.use((req, res, next) => {
    res.status(404).json({
        success: false,
        error: { message: `Cannot ${req.method} ${req.originalUrl} - Route not found.` }
    });
});


// --- Server Initialization ---
const PORT = process.env.PORT || 3000;
const serverInstance = app.listen(PORT, () => {
  logger.info('QuantBoost API server started', {
    port: PORT,
    environment: process.env.NODE_ENV,
    nodeVersion: process.version
  });
});

module.exports = { app, serverInstance };