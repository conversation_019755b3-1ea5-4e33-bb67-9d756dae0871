const Joi = require('joi');
const logger = require('../utils/logger');

const schemas = {
  licenseValidation: Joi.object({
    licenseKey: Joi.string().required().min(10),
    productId: Joi.string().required(),
    deviceId: Joi.string().required()
  }),
  
  magicLink: Joi.object({
    email: Joi.string().email().required(),
    licenseKey: Joi.string().optional(),
    productId: Joi.string().optional()
  }),

  userRegistration: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(8).required(),
    firstName: Joi.string().required(),
    lastName: Joi.string().required()
  }),

  licenseCreation: Joi.object({
    productId: Joi.string().required(),
    userId: Joi.string().uuid().required(),
    maxActivations: Joi.number().integer().min(1).optional(),
    expiresAt: Joi.date().iso().optional()
  }),

  subscriptionUpdate: Joi.object({
    status: Joi.string().valid('active', 'cancelled', 'expired').required(),
    metadata: Joi.object().optional()
  })
};

const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      logger.warn('Validation failed', {
        error: error.details[0].message,
        requestId: req.requestId,
        method: req.method,
        path: req.path,
        body: req.body
      });
      
      return res.status(400).json({
        success: false,
        error: { message: error.details[0].message }
      });
    }
    next();
  };
};

module.exports = { schemas, validate };