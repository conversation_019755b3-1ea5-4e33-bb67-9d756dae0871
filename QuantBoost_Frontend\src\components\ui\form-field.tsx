import * as React from "react"
import { cn } from "@/lib/utils"

interface FormFieldProps {
  children: React.ReactNode
  className?: string
}

interface FormFieldLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean
}

interface FormFieldErrorProps {
  children?: React.ReactNode
  className?: string
}

interface FormFieldDescriptionProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode
}

const FormField = React.forwardRef<HTMLDivElement, FormFieldProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("space-y-2", className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)
FormField.displayName = "FormField"

const FormFieldLabel = React.forwardRef<HTMLLabelElement, FormFieldLabelProps>(
  ({ className, required, children, ...props }, ref) => {
    return (
      <label
        ref={ref}
        className={cn(
          "block text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
          className
        )}
        {...props}
      >
        {children}
        {required && <span className="text-destructive ml-1">*</span>}
      </label>
    )
  }
)
FormFieldLabel.displayName = "FormFieldLabel"

const FormFieldError = React.forwardRef<HTMLParagraphElement, FormFieldErrorProps>(
  ({ className, children, ...props }, ref) => {
    if (!children) return null
    
    return (
      <p
        ref={ref}
        className={cn(
          "text-sm text-destructive font-medium",
          className
        )}
        {...props}
      >
        {children}
      </p>
    )
  }
)
FormFieldError.displayName = "FormFieldError"

const FormFieldDescription = React.forwardRef<HTMLDivElement, FormFieldDescriptionProps>(
  ({ className, children, ...props }, ref) => {
    if (!children) return null

    return (
      <div
        ref={ref}
        className={cn(
          "text-sm text-muted-foreground",
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
FormFieldDescription.displayName = "FormFieldDescription"

export { FormField, FormFieldLabel, FormFieldError, FormFieldDescription }
