// adminSubscriptions.routes.js
// Routes for super admin subscription viewing operations

const express = require('express');
const router = express.Router();
const { supabase } = require('../supabaseClient'); // Adjusted path
const { sendSuccess, sendError } = require('../utils/responseHelpers'); // Adjusted path
const { authenticateApiKey } = require('../middleware/authMiddleware'); // Adjusted path

// Apply API key authentication to all routes in this file
router.use(authenticateApiKey);

// GET /v1/admin/subscriptions - List all subscriptions
router.get('/subscriptions', async (req, res) => {
    const {
        page = 1,
        pageSize = 10,
        user_id,
        status,
        stripe_subscription_id,
        stripe_customer_id,
        sortBy = 'created_at',
        sortOrder = 'desc'
    } = req.query;

    const pageInt = parseInt(page, 10);
    let pageSizeInt = parseInt(pageSize, 10);

    // Validate pageSize
    if (isNaN(pageSizeInt) || pageSizeInt <= 0 || pageSizeInt > 100) {
        pageSizeInt = 10;
    }
    // Validate page
    if (isNaN(pageInt) || pageInt <= 0) {
        return sendError(res, 400, 'page must be a positive integer.');
    }

    const offset = (pageInt - 1) * pageSizeInt;
    const validSortOrder = sortOrder.toLowerCase() === 'asc' ? 'asc' : 'desc';

    const allowedSortByFields = [
        'created_at',
        'updated_at',
        'user_id',
        'status',
        'current_period_start',
        'current_period_end',
        'trial_start',
        'trial_end',
        'id',
        'stripe_subscription_id',
        'stripe_customer_id'
    ];
    if (!allowedSortByFields.includes(sortBy)) {
        return sendError(res, 400, `Invalid sortBy field. Allowed fields: ${allowedSortByFields.join(', ')}.`);
    }

    try {
        let query = supabase
            .from('subscriptions')
            .select('*, profiles(email, full_name)', { count: 'exact' }); // Include related user profile info

        // Filtering
        if (user_id) {
            if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(user_id)) {
                return sendError(res, 400, 'Invalid user_id format. Must be a UUID.');
            }
            query = query.eq('user_id', user_id);
        }
        if (status) {
            const validStatuses = ['active', 'trialing', 'past_due', 'canceled', 'incomplete', 'incomplete_expired', 'unpaid', 'paused'];
            if (!validStatuses.includes(status.toLowerCase())) {
                return sendError(res, 400, `Invalid status filter. Allowed statuses: ${validStatuses.join(', ')}.`);
            }
            query = query.eq('status', status.toLowerCase());
        }
        if (stripe_subscription_id) {
            query = query.eq('stripe_subscription_id', stripe_subscription_id);
        }
        if (stripe_customer_id) {
            query = query.eq('stripe_customer_id', stripe_customer_id);
        }

        // Sorting
        query = query.order(sortBy, { ascending: validSortOrder === 'asc' });

        // Pagination
        query = query.range(offset, offset + pageSizeInt - 1);

        const { data: subscriptionsData, error, count: totalCount } = await query;

        if (error) {
            console.error('Error fetching subscriptions:', error);
            return sendError(res, 500, 'Failed to fetch subscriptions.', error.message);
        }

        sendSuccess(res, {
            subscriptions: subscriptionsData,
            total_count: totalCount,
            page: pageInt,
            pageSize: pageSizeInt
        });
    } catch (error) {
        console.error('Unexpected error fetching subscriptions:', error);
        sendError(res, 500, 'An unexpected error occurred.', error.message);
    }
});

// GET /v1/admin/subscriptions/:subscriptionId - Get a specific subscription by its ID
router.get('/subscriptions/:subscriptionId', async (req, res) => {
    const { subscriptionId } = req.params;

    // Validate that subscriptionId is a UUID if that's the format of your IDs
    // Or if it's a Stripe ID, validate its typical format (e.g., sub_xxxxxxxxxxxxxx)
    if (!subscriptionId) { // Basic check
        return sendError(res, 400, 'Subscription ID is required.');
    }
    // Example: UUID validation if your internal IDs are UUIDs
    // if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(subscriptionId)) {
    //     return sendError(res, 400, 'Invalid Subscription ID format.');
    // }

    try {
        const { data: subscriptionData, error } = await supabase
            .from('subscriptions')
            .select('*, profiles(email, full_name), licenses(*)') // Include related user and license info
            .eq('id', subscriptionId)
            .maybeSingle(); // Use maybeSingle() if the ID might not exist, single() if it must exist

        if (error) {
            console.error(`Error fetching subscription ${subscriptionId}:`, error);
            return sendError(res, 500, 'Failed to fetch subscription.', error.message);
        }

        if (!subscriptionData) {
            return sendError(res, 404, 'Subscription not found.');
        }

        sendSuccess(res, subscriptionData);
    } catch (error) {
        console.error(`Unexpected error fetching subscription ${subscriptionId}:`, error);
        sendError(res, 500, 'An unexpected error occurred.', error.message);
    }
});

module.exports = router;
