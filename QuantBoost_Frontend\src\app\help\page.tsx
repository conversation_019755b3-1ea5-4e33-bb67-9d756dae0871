"use client";

import React, { useRef } from 'react';
import { motion, useScroll, useTransform, useSpring, Variants } from 'motion/react';
import Header from '@/components/Header';
import AnimatedQuantBoostLogo from '@/components/AnimatedQuantBoostLogo';

// Professional animation variants
const fadeInUp: Variants = {
  hidden: { opacity: 0, y: 40 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const fadeInLeft: Variants = {
  hidden: { opacity: 0, x: -40 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const fadeInRight: Variants = {
  hidden: { opacity: 0, x: 40 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const fadeInScale: Variants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1
    }
  }
};

// Premium button animations
const helpButtonVariants: Variants = {
  idle: { 
    scale: 1, 
    y: 0,
    boxShadow: "0 4px 14px 0 rgba(16, 185, 129, 0.2)",
    borderColor: "#10B981"
  },
  hover: { 
    scale: 1.05,
    y: -2,
    boxShadow: "0 20px 25px -5px rgba(16, 185, 129, 0.4)",
    backgroundColor: "#F0FDF4",
    transition: { 
      duration: 0.08,
      type: "spring",
      stiffness: 800,
      damping: 15
    }
  },
  tap: {
    scale: 0.98,
    transition: { duration: 0.1 }
  }
};

const cardHoverVariants: Variants = {
  idle: { 
    scale: 1,
    y: 0,
    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
  },
  hover: {
    scale: 1.02,
    y: -5,
    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    transition: {
      duration: 0.2,
      type: "spring",
      stiffness: 400,
      damping: 25
    }
  }
};

export default function HelpPage() {
  const mainRef = useRef<HTMLElement>(null);
  const heroRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  const { scrollYProgress: globalScrollProgress } = useScroll({
    target: mainRef,
    offset: ["start start", "end end"]
  });

  // Parallax transforms
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "30%"]);
  const smoothBackgroundY = useSpring(backgroundY, { stiffness: 100, damping: 30, restDelta: 0.001 });

  // Excel grid animation
  const gridOpacity = useTransform(globalScrollProgress, [0, 0.3, 0.7, 1], [0.02, 0.06, 0.1, 0.15]);
  const gridScale = useTransform(globalScrollProgress, [0, 1], [1, 1.02]);
  const smoothGridOpacity = useSpring(gridOpacity, { stiffness: 50, damping: 25 });

  return (
    <>
      <Header />
      
      <main ref={mainRef} className="flex flex-col items-center justify-center min-h-screen overflow-hidden relative">
        {/* Excel Grid Background - Professional Brand Feel */}
        <motion.div 
          className="fixed inset-0 -z-20 overflow-hidden"
          style={{ 
            opacity: smoothGridOpacity,
            scale: gridScale
          }}
        >
          <svg
            className="absolute inset-0 w-full h-full"
            xmlns="http://www.w3.org/2000/svg"
            style={{ 
              width: '100%', 
              height: '100%',
              minHeight: '100vh'
            }}
          >
            <defs>
              <pattern
                id="excel-grid-help"
                x="0"
                y="0"
                width="40"
                height="24"
                patternUnits="userSpaceOnUse"
              >
                <rect
                  x="0"
                  y="0"
                  width="40"
                  height="24"
                  fill="none"
                  stroke="#10B981"
                  strokeWidth="0.5"
                  opacity="0.4"
                />
                <rect
                  x="0"
                  y="0"
                  width="200"
                  height="120"
                  fill="none"
                  stroke="#10B981"
                  strokeWidth="1"
                  opacity="0.6"
                  patternUnits="userSpaceOnUse"
                />
              </pattern>
            </defs>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#excel-grid-help)"
            />
          </svg>
        </motion.div>

        {/* Hero Section */}
        <motion.section 
          ref={heroRef}
          className="text-center py-24 px-6 relative z-10 max-w-6xl mx-auto"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={staggerContainer}
        >
          {/* Parallax Background Blobs */}
          <motion.div
            className="absolute inset-0 -z-10 opacity-10"
            style={{ y: smoothBackgroundY }}
          >
            <div className="absolute top-20 left-10 w-72 h-72 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
            <div className="absolute top-40 right-10 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            <div className="absolute bottom-20 left-20 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
          </motion.div>

          {/* Logo and Branding */}
          <motion.div 
            className="mb-8"
            variants={fadeInScale}
          >
            <AnimatedQuantBoostLogo 
              size="5rem"
              className="mx-auto mb-6"
              showDrawAnimation={false}
            />
          </motion.div>

          {/* Main Heading */}
          <motion.h1 
            className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 relative z-20"
            variants={fadeInUp}
          >
            Help Center
          </motion.h1>

          <motion.p 
            className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed relative z-20"
            variants={fadeInUp}
          >
            Get the support you need to maximize your productivity with QuantBoost. Find answers, tutorials, and expert guidance.
          </motion.p>

          {/* Help Categories Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {/* Getting Started */}
            <motion.div
              className="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-6 relative z-20 cursor-pointer"
              variants={cardHoverVariants}
              whileHover="hover"
              initial="idle"
            >
              <div className="text-4xl mb-4">🚀</div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Getting Started</h3>
              <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                New to QuantBoost? Learn the basics with our step-by-step guides and video tutorials.
              </p>
              <div className="flex flex-wrap gap-2 text-xs">
                <span className="px-2 py-1 bg-emerald-100 text-emerald-700 rounded-full">Installation</span>
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full">First Steps</span>
                <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full">Setup</span>
              </div>
            </motion.div>

            {/* Feature Guides */}
            <motion.div
              className="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-6 relative z-20 cursor-pointer"
              variants={cardHoverVariants}
              whileHover="hover"
              initial="idle"
            >
              <div className="text-4xl mb-4">📊</div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Feature Guides</h3>
              <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                Master every QuantBoost feature with detailed walkthroughs and pro tips from experts.
              </p>
              <div className="flex flex-wrap gap-2 text-xs">
                <span className="px-2 py-1 bg-emerald-100 text-emerald-700 rounded-full">Excel Link</span>
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full">Trace</span>
                <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full">Analyzers</span>
              </div>
            </motion.div>

            {/* Troubleshooting */}
            <motion.div
              className="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-6 relative z-20 cursor-pointer"
              variants={cardHoverVariants}
              whileHover="hover"
              initial="idle"
            >
              <div className="text-4xl mb-4">🔧</div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Troubleshooting</h3>
              <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                Resolve issues quickly with our comprehensive troubleshooting guides and FAQ.
              </p>
              <div className="flex flex-wrap gap-2 text-xs">
                <span className="px-2 py-1 bg-emerald-100 text-emerald-700 rounded-full">Common Issues</span>
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full">Error Codes</span>
                <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full">Performance</span>
              </div>
            </motion.div>

            {/* Video Tutorials */}
            <motion.div
              className="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-6 relative z-20 cursor-pointer"
              variants={cardHoverVariants}
              whileHover="hover"
              initial="idle"
            >
              <div className="text-4xl mb-4">🎥</div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Video Tutorials</h3>
              <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                Watch and learn with our library of professional video tutorials and demos.
              </p>
              <div className="flex flex-wrap gap-2 text-xs">
                <span className="px-2 py-1 bg-emerald-100 text-emerald-700 rounded-full">Demos</span>
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full">Webinars</span>
                <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full">Tips</span>
              </div>
            </motion.div>

            {/* Best Practices */}
            <motion.div
              className="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-6 relative z-20 cursor-pointer"
              variants={cardHoverVariants}
              whileHover="hover"
              initial="idle"
            >
              <div className="text-4xl mb-4">⭐</div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Best Practices</h3>
              <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                Learn from the pros with advanced techniques and workflow optimization strategies.
              </p>
              <div className="flex flex-wrap gap-2 text-xs">
                <span className="px-2 py-1 bg-emerald-100 text-emerald-700 rounded-full">Workflows</span>
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full">Efficiency</span>
                <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full">Pro Tips</span>
              </div>
            </motion.div>

            {/* Contact Support */}
            <motion.div
              className="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-6 relative z-20 cursor-pointer"
              variants={cardHoverVariants}
              whileHover="hover"
              initial="idle"
            >
              <div className="text-4xl mb-4">💬</div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Contact Support</h3>
              <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                Can't find what you're looking for? Get personalized help from our support team.
              </p>
              <div className="flex flex-wrap gap-2 text-xs">
                <span className="px-2 py-1 bg-emerald-100 text-emerald-700 rounded-full">Email</span>
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full">Priority</span>
                <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full">24/7</span>
              </div>
            </motion.div>
          </div>

          {/* Quick Action Buttons */}
          <motion.div 
            className="flex flex-wrap justify-center gap-6"
            variants={fadeInUp}
          >
            <motion.a 
              href="/training"
              className="inline-flex items-center gap-3 px-8 py-4 text-lg font-semibold text-emerald-700 border-2 rounded-xl transition-all duration-200 cursor-pointer relative z-30"
              variants={helpButtonVariants}
              initial="idle"
              whileHover="hover"
              whileTap="tap"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
              View Training Resources
            </motion.a>

            <motion.a 
              href="/download"
              className="inline-flex items-center gap-3 px-8 py-4 text-lg font-semibold text-emerald-700 border-2 rounded-xl transition-all duration-200 cursor-pointer relative z-30"
              variants={helpButtonVariants}
              initial="idle"
              whileHover="hover"
              whileTap="tap"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download QuantBoost
            </motion.a>
          </motion.div>
        </motion.section>
      </main>
    </>
  );
}
