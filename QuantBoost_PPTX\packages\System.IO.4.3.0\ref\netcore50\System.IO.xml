﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO</name>
  </assembly>
  <members>
    <member name="T:System.IO.BinaryReader">
      <summary>Reads primitive data types as binary values in a specific encoding.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.BinaryReader" /> class based on the specified stream and using UTF-8 encoding.</summary>
      <param name="input">The input stream. </param>
      <exception cref="T:System.ArgumentException">The stream does not support reading, is null, or is already closed. </exception>
    </member>
    <member name="M:System.IO.BinaryReader.#ctor(System.IO.Stream,System.Text.Encoding)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.BinaryReader" /> class based on the specified stream and character encoding.</summary>
      <param name="input">The input stream. </param>
      <param name="encoding">The character encoding to use. </param>
      <exception cref="T:System.ArgumentException">The stream does not support reading, is null, or is already closed. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="encoding" /> is null. </exception>
    </member>
    <member name="M:System.IO.BinaryReader.#ctor(System.IO.Stream,System.Text.Encoding,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.BinaryReader" /> class based on the specified stream and character encoding, and optionally leaves the stream open.</summary>
      <param name="input">The input stream.</param>
      <param name="encoding">The character encoding to use.</param>
      <param name="leaveOpen">true to leave the stream open after the <see cref="T:System.IO.BinaryReader" /> object is disposed; otherwise, false.</param>
      <exception cref="T:System.ArgumentException">The stream does not support reading, is null, or is already closed. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="encoding" /> or <paramref name="input" /> is null. </exception>
    </member>
    <member name="P:System.IO.BinaryReader.BaseStream">
      <summary>Exposes access to the underlying stream of the <see cref="T:System.IO.BinaryReader" />.</summary>
      <returns>The underlying stream associated with the BinaryReader.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.IO.BinaryReader" /> class.</summary>
    </member>
    <member name="M:System.IO.BinaryReader.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.BinaryReader" /> class and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.IO.BinaryReader.FillBuffer(System.Int32)">
      <summary>Fills the internal buffer with the specified number of bytes read from the stream.</summary>
      <param name="numBytes">The number of bytes to be read. </param>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached before <paramref name="numBytes" /> could be read. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Requested <paramref name="numBytes" /> is larger than the internal buffer size.</exception>
    </member>
    <member name="M:System.IO.BinaryReader.PeekChar">
      <summary>Returns the next available character and does not advance the byte or character position.</summary>
      <returns>The next available character, or -1 if no more characters are available or the stream does not support seeking.</returns>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ArgumentException">The current character cannot be decoded into the internal character buffer by using the <see cref="T:System.Text.Encoding" /> selected for the stream.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.Read">
      <summary>Reads characters from the underlying stream and advances the current position of the stream in accordance with the Encoding used and the specific character being read from the stream.</summary>
      <returns>The next character from the input stream, or -1 if no characters are currently available.</returns>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads the specified number of bytes from the stream, starting from a specified point in the byte array. </summary>
      <returns>The number of bytes read into <paramref name="buffer" />. This might be less than the number of bytes requested if that many bytes are not available, or it might be zero if the end of the stream is reached.</returns>
      <param name="buffer">The buffer to read data into. </param>
      <param name="index">The starting point in the buffer at which to begin reading into the buffer. </param>
      <param name="count">The number of bytes to read. </param>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. -or-The number of decoded characters to read is greater than <paramref name="count" />. This can happen if a Unicode decoder returns fallback characters or a surrogate pair.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.Read(System.Char[],System.Int32,System.Int32)">
      <summary>Reads the specified number of characters from the stream, starting from a specified point in the character array.</summary>
      <returns>The total number of characters read into the buffer. This might be less than the number of characters requested if that many characters are not currently available, or it might be zero if the end of the stream is reached.</returns>
      <param name="buffer">The buffer to read data into. </param>
      <param name="index">The starting point in the buffer at which to begin reading into the buffer. </param>
      <param name="count">The number of characters to read. </param>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. -or-The number of decoded characters to read is greater than <paramref name="count" />. This can happen if a Unicode decoder returns fallback characters or a surrogate pair.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.Read7BitEncodedInt">
      <summary>Reads in a 32-bit integer in compressed format.</summary>
      <returns>A 32-bit integer in compressed format.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.FormatException">The stream is corrupted.</exception>
    </member>
    <member name="M:System.IO.BinaryReader.ReadBoolean">
      <summary>Reads a Boolean value from the current stream and advances the current position of the stream by one byte.</summary>
      <returns>true if the byte is nonzero; otherwise, false.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadByte">
      <summary>Reads the next byte from the current stream and advances the current position of the stream by one byte.</summary>
      <returns>The next byte read from the current stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadBytes(System.Int32)">
      <summary>Reads the specified number of bytes from the current stream into a byte array and advances the current position by that number of bytes.</summary>
      <returns>A byte array containing data read from the underlying stream. This might be less than the number of bytes requested if the end of the stream is reached.</returns>
      <param name="count">The number of bytes to read. This value must be 0 or a non-negative number or an exception will occur.</param>
      <exception cref="T:System.ArgumentException">The number of decoded characters to read is greater than <paramref name="count" />. This can happen if a Unicode decoder returns fallback characters or a surrogate pair.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is negative. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadChar">
      <summary>Reads the next character from the current stream and advances the current position of the stream in accordance with the Encoding used and the specific character being read from the stream.</summary>
      <returns>A character read from the current stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ArgumentException">A surrogate character was read. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadChars(System.Int32)">
      <summary>Reads the specified number of characters from the current stream, returns the data in a character array, and advances the current position in accordance with the Encoding used and the specific character being read from the stream.</summary>
      <returns>A character array containing data read from the underlying stream. This might be less than the number of characters requested if the end of the stream is reached.</returns>
      <param name="count">The number of characters to read. </param>
      <exception cref="T:System.ArgumentException">The number of decoded characters to read is greater than <paramref name="count" />. This can happen if a Unicode decoder returns fallback characters or a surrogate pair.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is negative. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadDecimal">
      <summary>Reads a decimal value from the current stream and advances the current position of the stream by sixteen bytes.</summary>
      <returns>A decimal value read from the current stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadDouble">
      <summary>Reads an 8-byte floating point value from the current stream and advances the current position of the stream by eight bytes.</summary>
      <returns>An 8-byte floating point value read from the current stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadInt16">
      <summary>Reads a 2-byte signed integer from the current stream and advances the current position of the stream by two bytes.</summary>
      <returns>A 2-byte signed integer read from the current stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadInt32">
      <summary>Reads a 4-byte signed integer from the current stream and advances the current position of the stream by four bytes.</summary>
      <returns>A 4-byte signed integer read from the current stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadInt64">
      <summary>Reads an 8-byte signed integer from the current stream and advances the current position of the stream by eight bytes.</summary>
      <returns>An 8-byte signed integer read from the current stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadSByte">
      <summary>Reads a signed byte from this stream and advances the current position of the stream by one byte.</summary>
      <returns>A signed byte read from the current stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadSingle">
      <summary>Reads a 4-byte floating point value from the current stream and advances the current position of the stream by four bytes.</summary>
      <returns>A 4-byte floating point value read from the current stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadString">
      <summary>Reads a string from the current stream. The string is prefixed with the length, encoded as an integer seven bits at a time.</summary>
      <returns>The string being read.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadUInt16">
      <summary>Reads a 2-byte unsigned integer from the current stream using little-endian encoding and advances the position of the stream by two bytes.</summary>
      <returns>A 2-byte unsigned integer read from this stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadUInt32">
      <summary>Reads a 4-byte unsigned integer from the current stream and advances the position of the stream by four bytes.</summary>
      <returns>A 4-byte unsigned integer read from this stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadUInt64">
      <summary>Reads an 8-byte unsigned integer from the current stream and advances the position of the stream by eight bytes.</summary>
      <returns>An 8-byte unsigned integer read from this stream.</returns>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.BinaryWriter">
      <summary>Writes primitive types in binary to a stream and supports writing strings in a specific encoding.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.BinaryWriter" /> class that writes to a stream.</summary>
    </member>
    <member name="M:System.IO.BinaryWriter.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.BinaryWriter" /> class based on the specified stream and using UTF-8 encoding.</summary>
      <param name="output">The output stream. </param>
      <exception cref="T:System.ArgumentException">The stream does not support writing or is already closed. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="output" /> is null. </exception>
    </member>
    <member name="M:System.IO.BinaryWriter.#ctor(System.IO.Stream,System.Text.Encoding)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.BinaryWriter" /> class based on the specified stream and character encoding.</summary>
      <param name="output">The output stream. </param>
      <param name="encoding">The character encoding to use. </param>
      <exception cref="T:System.ArgumentException">The stream does not support writing or is already closed. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="output" /> or <paramref name="encoding" /> is null. </exception>
    </member>
    <member name="M:System.IO.BinaryWriter.#ctor(System.IO.Stream,System.Text.Encoding,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.BinaryWriter" /> class based on the specified stream and character encoding, and optionally leaves the stream open.</summary>
      <param name="output">The output stream.</param>
      <param name="encoding">The character encoding to use.</param>
      <param name="leaveOpen">true to leave the stream open after the <see cref="T:System.IO.BinaryWriter" /> object is disposed; otherwise, false.</param>
      <exception cref="T:System.ArgumentException">The stream does not support writing or is already closed. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="output" /> or <paramref name="encoding" /> is null. </exception>
    </member>
    <member name="P:System.IO.BinaryWriter.BaseStream">
      <summary>Gets the underlying stream of the <see cref="T:System.IO.BinaryWriter" />.</summary>
      <returns>The underlying stream associated with the BinaryWriter.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.IO.BinaryWriter" /> class.</summary>
    </member>
    <member name="M:System.IO.BinaryWriter.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.BinaryWriter" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.IO.BinaryWriter.Flush">
      <summary>Clears all buffers for the current writer and causes any buffered data to be written to the underlying device.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.BinaryWriter.Null">
      <summary>Specifies a <see cref="T:System.IO.BinaryWriter" /> with no backing store.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.BinaryWriter.OutStream">
      <summary>Holds the underlying stream.</summary>
    </member>
    <member name="M:System.IO.BinaryWriter.Seek(System.Int32,System.IO.SeekOrigin)">
      <summary>Sets the position within the current stream.</summary>
      <returns>The position with the current stream.</returns>
      <param name="offset">A byte offset relative to <paramref name="origin" />. </param>
      <param name="origin">A field of <see cref="T:System.IO.SeekOrigin" /> indicating the reference point from which the new position is to be obtained. </param>
      <exception cref="T:System.IO.IOException">The file pointer was moved to an invalid location. </exception>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.IO.SeekOrigin" /> value is invalid. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Boolean)">
      <summary>Writes a one-byte Boolean value to the current stream, with 0 representing false and 1 representing true.</summary>
      <param name="value">The Boolean value to write (0 or 1). </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Byte)">
      <summary>Writes an unsigned byte to the current stream and advances the stream position by one byte.</summary>
      <param name="value">The unsigned byte to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Byte[])">
      <summary>Writes a byte array to the underlying stream.</summary>
      <param name="buffer">A byte array containing the data to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Writes a region of a byte array to the current stream.</summary>
      <param name="buffer">A byte array containing the data to write. </param>
      <param name="index">The starting point in <paramref name="buffer" /> at which to begin writing. </param>
      <param name="count">The number of bytes to write. </param>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Char)">
      <summary>Writes a Unicode character to the current stream and advances the current position of the stream in accordance with the Encoding used and the specific characters being written to the stream.</summary>
      <param name="ch">The non-surrogate, Unicode character to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ch" /> is a single surrogate character.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Char[])">
      <summary>Writes a character array to the current stream and advances the current position of the stream in accordance with the Encoding used and the specific characters being written to the stream.</summary>
      <param name="chars">A character array containing the data to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Char[],System.Int32,System.Int32)">
      <summary>Writes a section of a character array to the current stream, and advances the current position of the stream in accordance with the Encoding used and perhaps the specific characters being written to the stream.</summary>
      <param name="chars">A character array containing the data to write. </param>
      <param name="index">The starting point in <paramref name="chars" /> from which to begin writing. </param>
      <param name="count">The number of characters to write. </param>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Decimal)">
      <summary>Writes a decimal value to the current stream and advances the stream position by sixteen bytes.</summary>
      <param name="value">The decimal value to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Double)">
      <summary>Writes an eight-byte floating-point value to the current stream and advances the stream position by eight bytes.</summary>
      <param name="value">The eight-byte floating-point value to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Int16)">
      <summary>Writes a two-byte signed integer to the current stream and advances the stream position by two bytes.</summary>
      <param name="value">The two-byte signed integer to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Int32)">
      <summary>Writes a four-byte signed integer to the current stream and advances the stream position by four bytes.</summary>
      <param name="value">The four-byte signed integer to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Int64)">
      <summary>Writes an eight-byte signed integer to the current stream and advances the stream position by eight bytes.</summary>
      <param name="value">The eight-byte signed integer to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.SByte)">
      <summary>Writes a signed byte to the current stream and advances the stream position by one byte.</summary>
      <param name="value">The signed byte to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Single)">
      <summary>Writes a four-byte floating-point value to the current stream and advances the stream position by four bytes.</summary>
      <param name="value">The four-byte floating-point value to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.String)">
      <summary>Writes a length-prefixed string to this stream in the current encoding of the <see cref="T:System.IO.BinaryWriter" />, and advances the current position of the stream in accordance with the encoding used and the specific characters being written to the stream.</summary>
      <param name="value">The value to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.UInt16)">
      <summary>Writes a two-byte unsigned integer to the current stream and advances the stream position by two bytes.</summary>
      <param name="value">The two-byte unsigned integer to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.UInt32)">
      <summary>Writes a four-byte unsigned integer to the current stream and advances the stream position by four bytes.</summary>
      <param name="value">The four-byte unsigned integer to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.UInt64)">
      <summary>Writes an eight-byte unsigned integer to the current stream and advances the stream position by eight bytes.</summary>
      <param name="value">The eight-byte unsigned integer to write. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write7BitEncodedInt(System.Int32)">
      <summary>Writes a 32-bit integer in a compressed format.</summary>
      <param name="value">The 32-bit integer to be written. </param>
      <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <exception cref="T:System.IO.IOException">The stream is closed. </exception>
    </member>
    <member name="T:System.IO.EndOfStreamException">
      <summary>The exception that is thrown when reading is attempted past the end of a stream.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.EndOfStreamException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.EndOfStreamException" /> class with its message string set to a system-supplied message and its HRESULT set to COR_E_ENDOFSTREAM.</summary>
    </member>
    <member name="M:System.IO.EndOfStreamException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.EndOfStreamException" /> class with its message string set to <paramref name="message" /> and its HRESULT set to COR_E_ENDOFSTREAM.</summary>
      <param name="message">A string that describes the error. The content of <paramref name="message" /> is intended to be understood by humans. The caller of this constructor is required to ensure that this string has been localized for the current system culture. </param>
    </member>
    <member name="M:System.IO.EndOfStreamException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.EndOfStreamException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">A string that describes the error. The content of <paramref name="message" /> is intended to be understood by humans. The caller of this constructor is required to ensure that this string has been localized for the current system culture. </param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception. </param>
    </member>
    <member name="T:System.IO.InvalidDataException">
      <summary>The exception that is thrown when a data stream is in an invalid format.</summary>
    </member>
    <member name="M:System.IO.InvalidDataException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.InvalidDataException" /> class.</summary>
    </member>
    <member name="M:System.IO.InvalidDataException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.InvalidDataException" /> class with a specified error message.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
    </member>
    <member name="M:System.IO.InvalidDataException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.InvalidDataException" /> class with a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not null, the current exception is raised in a catch block that handles the inner exception.</param>
    </member>
    <member name="T:System.IO.MemoryStream">
      <summary>Creates a stream whose backing store is memory.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.MemoryStream" /> class with an expandable capacity initialized to zero.</summary>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Byte[])">
      <summary>Initializes a new non-resizable instance of the <see cref="T:System.IO.MemoryStream" /> class based on the specified byte array.</summary>
      <param name="buffer">The array of unsigned bytes from which to create the current stream. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Byte[],System.Boolean)">
      <summary>Initializes a new non-resizable instance of the <see cref="T:System.IO.MemoryStream" /> class based on the specified byte array with the <see cref="P:System.IO.MemoryStream.CanWrite" /> property set as specified.</summary>
      <param name="buffer">The array of unsigned bytes from which to create this stream. </param>
      <param name="writable">The setting of the <see cref="P:System.IO.MemoryStream.CanWrite" /> property, which determines whether the stream supports writing. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>Initializes a new non-resizable instance of the <see cref="T:System.IO.MemoryStream" /> class based on the specified region (index) of a byte array.</summary>
      <param name="buffer">The array of unsigned bytes from which to create this stream. </param>
      <param name="index">The index into <paramref name="buffer" /> at which the stream begins. </param>
      <param name="count">The length of the stream in bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Byte[],System.Int32,System.Int32,System.Boolean)">
      <summary>Initializes a new non-resizable instance of the <see cref="T:System.IO.MemoryStream" /> class based on the specified region of a byte array, with the <see cref="P:System.IO.MemoryStream.CanWrite" /> property set as specified.</summary>
      <param name="buffer">The array of unsigned bytes from which to create this stream. </param>
      <param name="index">The index in <paramref name="buffer" /> at which the stream begins. </param>
      <param name="count">The length of the stream in bytes. </param>
      <param name="writable">The setting of the <see cref="P:System.IO.MemoryStream.CanWrite" /> property, which determines whether the stream supports writing. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> are negative. </exception>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />.</exception>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Byte[],System.Int32,System.Int32,System.Boolean,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.MemoryStream" /> class based on the specified region of a byte array, with the <see cref="P:System.IO.MemoryStream.CanWrite" /> property set as specified, and the ability to call <see cref="M:System.IO.MemoryStream.GetBuffer" /> set as specified.</summary>
      <param name="buffer">The array of unsigned bytes from which to create this stream. </param>
      <param name="index">The index into <paramref name="buffer" /> at which the stream begins. </param>
      <param name="count">The length of the stream in bytes. </param>
      <param name="writable">The setting of the <see cref="P:System.IO.MemoryStream.CanWrite" /> property, which determines whether the stream supports writing. </param>
      <param name="publiclyVisible">true to enable <see cref="M:System.IO.MemoryStream.GetBuffer" />, which returns the unsigned byte array from which the stream was created; otherwise, false. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. </exception>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.MemoryStream" /> class with an expandable capacity initialized as specified.</summary>
      <param name="capacity">The initial size of the internal array in bytes. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is negative. </exception>
    </member>
    <member name="P:System.IO.MemoryStream.CanRead">
      <summary>Gets a value indicating whether the current stream supports reading.</summary>
      <returns>true if the stream is open.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.MemoryStream.CanSeek">
      <summary>Gets a value indicating whether the current stream supports seeking.</summary>
      <returns>true if the stream is open.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.MemoryStream.CanWrite">
      <summary>Gets a value indicating whether the current stream supports writing.</summary>
      <returns>true if the stream supports writing; otherwise, false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.MemoryStream.Capacity">
      <summary>Gets or sets the number of bytes allocated for this stream.</summary>
      <returns>The length of the usable portion of the buffer for the stream.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">A capacity is set that is negative or less than the current length of the stream. </exception>
      <exception cref="T:System.ObjectDisposedException">The current stream is closed. </exception>
      <exception cref="T:System.NotSupportedException">set is invoked on a stream whose capacity cannot be modified. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.CopyToAsync(System.IO.Stream,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously reads all the bytes from the current stream and writes them to another stream, using a specified buffer size and cancellation token.</summary>
      <returns>A task that represents the asynchronous copy operation.</returns>
      <param name="destination">The stream to which the contents of the current stream will be copied.</param>
      <param name="bufferSize">The size, in bytes, of the buffer. This value must be greater than zero.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="buffersize" /> is negative or zero.</exception>
      <exception cref="T:System.ObjectDisposedException">Either the current stream or the destination stream is disposed.</exception>
      <exception cref="T:System.NotSupportedException">The current stream does not support reading, or the destination stream does not support writing.</exception>
    </member>
    <member name="M:System.IO.MemoryStream.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.MemoryStream" /> class and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.IO.MemoryStream.Flush">
      <summary>Overrides the <see cref="M:System.IO.Stream.Flush" /> method so that no action is performed.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Asynchronously clears all buffers for this stream, and monitors cancellation requests.</summary>
      <returns>A task that represents the asynchronous flush operation.</returns>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
    </member>
    <member name="P:System.IO.MemoryStream.Length">
      <summary>Gets the length of the stream in bytes.</summary>
      <returns>The length of the stream in bytes.</returns>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.MemoryStream.Position">
      <summary>Gets or sets the current position within the stream.</summary>
      <returns>The current position within the stream.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The position is set to a negative value or a value greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ObjectDisposedException">The stream is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Reads a block of bytes from the current stream and writes the data to a buffer.</summary>
      <returns>The total number of bytes written into the buffer. This can be less than the number of bytes requested if that number of bytes are not currently available, or zero if the end of the stream is reached before any bytes are read.</returns>
      <param name="buffer">When this method returns, contains the specified byte array with the values between <paramref name="offset" /> and (<paramref name="offset" /> + <paramref name="count" /> - 1) replaced by the characters read from the current stream. </param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin storing data from the current stream.</param>
      <param name="count">The maximum number of bytes to read. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> subtracted from the buffer length is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ObjectDisposedException">The current stream instance is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously reads a sequence of bytes from the current stream, advances the position within the stream by the number of bytes read, and monitors cancellation requests.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the total number of bytes read into the buffer. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the stream has been reached. </returns>
      <param name="buffer">The buffer to write the data into.</param>
      <param name="offset">The byte offset in <paramref name="buffer" /> at which to begin writing data from the stream.</param>
      <param name="count">The maximum number of bytes to read.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.NotSupportedException">The stream does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.MemoryStream.ReadByte">
      <summary>Reads a byte from the current stream.</summary>
      <returns>The byte cast to a <see cref="T:System.Int32" />, or -1 if the end of the stream has been reached.</returns>
      <exception cref="T:System.ObjectDisposedException">The current stream instance is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Sets the position within the current stream to the specified value.</summary>
      <returns>The new position within the stream, calculated by combining the initial reference point and the offset.</returns>
      <param name="offset">The new position within the stream. This is relative to the <paramref name="loc" /> parameter, and can be positive or negative. </param>
      <param name="loc">A value of type <see cref="T:System.IO.SeekOrigin" />, which acts as the seek reference point. </param>
      <exception cref="T:System.IO.IOException">Seeking is attempted before the beginning of the stream. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">There is an invalid <see cref="T:System.IO.SeekOrigin" />. -or-<paramref name="offset" /> caused an arithmetic overflow.</exception>
      <exception cref="T:System.ObjectDisposedException">The current stream instance is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.SetLength(System.Int64)">
      <summary>Sets the length of the current stream to the specified value.</summary>
      <param name="value">The value at which to set the length. </param>
      <exception cref="T:System.NotSupportedException">The current stream is not resizable and <paramref name="value" /> is larger than the current capacity.-or- The current stream does not support writing. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> is negative or is greater than the maximum length of the <see cref="T:System.IO.MemoryStream" />, where the maximum length is(<see cref="F:System.Int32.MaxValue" /> - origin), and origin is the index into the underlying buffer at which the stream starts. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.ToArray">
      <summary>Writes the stream contents to a byte array, regardless of the <see cref="P:System.IO.MemoryStream.Position" /> property.</summary>
      <returns>A new byte array.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.TryGetBuffer(System.ArraySegment{System.Byte}@)">
      <summary>Returns the array of unsigned bytes from which this stream was created. The return value indicates whether the conversion succeeded.</summary>
      <returns>true if the conversion was successful; otherwise, false.</returns>
      <param name="buffer">The byte array segment from which this stream was created.</param>
    </member>
    <member name="M:System.IO.MemoryStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Writes a block of bytes to the current stream using data read from a buffer.</summary>
      <param name="buffer">The buffer to write data from. </param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin copying bytes to the current stream.</param>
      <param name="count">The maximum number of bytes to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.NotSupportedException">The stream does not support writing. For additional information see <see cref="P:System.IO.Stream.CanWrite" />.-or- The current position is closer than <paramref name="count" /> bytes to the end of the stream, and the capacity cannot be modified. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> subtracted from the buffer length is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> are negative. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The current stream instance is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously writes a sequence of bytes to the current stream, advances the current position within this stream by the number of bytes written, and monitors cancellation requests.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">The buffer to write data from.</param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> from which to begin copying bytes to the stream.</param>
      <param name="count">The maximum number of bytes to write.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.NotSupportedException">The stream does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.MemoryStream.WriteByte(System.Byte)">
      <summary>Writes a byte to the current stream at the current position.</summary>
      <param name="value">The byte to write. </param>
      <exception cref="T:System.NotSupportedException">The stream does not support writing. For additional information see <see cref="P:System.IO.Stream.CanWrite" />.-or- The current position is at the end of the stream, and the capacity cannot be modified. </exception>
      <exception cref="T:System.ObjectDisposedException">The current stream is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.WriteTo(System.IO.Stream)">
      <summary>Writes the entire contents of this memory stream to another stream.</summary>
      <param name="stream">The stream to write this memory stream to. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The current or target stream is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.SeekOrigin">
      <summary>Specifies the position in a stream to use for seeking.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.IO.SeekOrigin.Begin">
      <summary>Specifies the beginning of a stream.</summary>
    </member>
    <member name="F:System.IO.SeekOrigin.Current">
      <summary>Specifies the current position within a stream.</summary>
    </member>
    <member name="F:System.IO.SeekOrigin.End">
      <summary>Specifies the end of a stream.</summary>
    </member>
    <member name="T:System.IO.Stream">
      <summary>Provides a generic view of a sequence of bytes. This is an abstract class.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Stream.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.Stream" /> class. </summary>
    </member>
    <member name="P:System.IO.Stream.CanRead">
      <summary>When overridden in a derived class, gets a value indicating whether the current stream supports reading.</summary>
      <returns>true if the stream supports reading; otherwise, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.Stream.CanSeek">
      <summary>When overridden in a derived class, gets a value indicating whether the current stream supports seeking.</summary>
      <returns>true if the stream supports seeking; otherwise, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.Stream.CanTimeout">
      <summary>Gets a value that determines whether the current stream can time out.</summary>
      <returns>A value that determines whether the current stream can time out.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Stream.CanWrite">
      <summary>When overridden in a derived class, gets a value indicating whether the current stream supports writing.</summary>
      <returns>true if the stream supports writing; otherwise, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Stream.CopyTo(System.IO.Stream)">
      <summary>Reads the bytes from the current stream and writes them to another stream.</summary>
      <param name="destination">The stream to which the contents of the current stream will be copied.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The current stream does not support reading.-or-<paramref name="destination" /> does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">Either the current stream or <paramref name="destination" /> were closed before the <see cref="M:System.IO.Stream.CopyTo(System.IO.Stream)" /> method was called.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred.</exception>
    </member>
    <member name="M:System.IO.Stream.CopyTo(System.IO.Stream,System.Int32)">
      <summary>Reads the bytes from the current stream and writes them to another stream, using a specified buffer size.</summary>
      <param name="destination">The stream to which the contents of the current stream will be copied.</param>
      <param name="bufferSize">The size of the buffer. This value must be greater than zero. The default size is 81920.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> is negative or zero.</exception>
      <exception cref="T:System.NotSupportedException">The current stream does not support reading.-or-<paramref name="destination" /> does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">Either the current stream or <paramref name="destination" /> were closed before the <see cref="M:System.IO.Stream.CopyTo(System.IO.Stream)" /> method was called.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred.</exception>
    </member>
    <member name="M:System.IO.Stream.CopyToAsync(System.IO.Stream)">
      <summary>Asynchronously reads the bytes from the current stream and writes them to another stream.</summary>
      <returns>A task that represents the asynchronous copy operation.</returns>
      <param name="destination">The stream to which the contents of the current stream will be copied.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> is null.</exception>
      <exception cref="T:System.ObjectDisposedException">Either the current stream or the destination stream is disposed.</exception>
      <exception cref="T:System.NotSupportedException">The current stream does not support reading, or the destination stream does not support writing.</exception>
    </member>
    <member name="M:System.IO.Stream.CopyToAsync(System.IO.Stream,System.Int32)">
      <summary>Asynchronously reads the bytes from the current stream and writes them to another stream, using a specified buffer size.</summary>
      <returns>A task that represents the asynchronous copy operation.</returns>
      <param name="destination">The stream to which the contents of the current stream will be copied.</param>
      <param name="bufferSize">The size, in bytes, of the buffer. This value must be greater than zero. The default size is 81920.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="buffersize" /> is negative or zero.</exception>
      <exception cref="T:System.ObjectDisposedException">Either the current stream or the destination stream is disposed.</exception>
      <exception cref="T:System.NotSupportedException">The current stream does not support reading, or the destination stream does not support writing.</exception>
    </member>
    <member name="M:System.IO.Stream.CopyToAsync(System.IO.Stream,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously reads the bytes from the current stream and writes them to another stream, using a specified buffer size and cancellation token.</summary>
      <returns>A task that represents the asynchronous copy operation.</returns>
      <param name="destination">The stream to which the contents of the current stream will be copied.</param>
      <param name="bufferSize">The size, in bytes, of the buffer. This value must be greater than zero. The default size is 81920.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="buffersize" /> is negative or zero.</exception>
      <exception cref="T:System.ObjectDisposedException">Either the current stream or the destination stream is disposed.</exception>
      <exception cref="T:System.NotSupportedException">The current stream does not support reading, or the destination stream does not support writing.</exception>
    </member>
    <member name="M:System.IO.Stream.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.IO.Stream" />.</summary>
    </member>
    <member name="M:System.IO.Stream.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.Stream" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.IO.Stream.Flush">
      <summary>When overridden in a derived class, clears all buffers for this stream and causes any buffered data to be written to the underlying device.</summary>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Stream.FlushAsync">
      <summary>Asynchronously clears all buffers for this stream and causes any buffered data to be written to the underlying device.</summary>
      <returns>A task that represents the asynchronous flush operation.</returns>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
    </member>
    <member name="M:System.IO.Stream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Asynchronously clears all buffers for this stream, causes any buffered data to be written to the underlying device, and monitors cancellation requests.</summary>
      <returns>A task that represents the asynchronous flush operation.</returns>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
    </member>
    <member name="P:System.IO.Stream.Length">
      <summary>When overridden in a derived class, gets the length in bytes of the stream.</summary>
      <returns>A long value representing the length of the stream in bytes.</returns>
      <exception cref="T:System.NotSupportedException">A class derived from Stream does not support seeking. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Stream.Null">
      <summary>A Stream with no backing store.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.Stream.Position">
      <summary>When overridden in a derived class, gets or sets the position within the current stream.</summary>
      <returns>The current position within the stream.</returns>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.NotSupportedException">The stream does not support seeking. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Stream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>When overridden in a derived class, reads a sequence of bytes from the current stream and advances the position within the stream by the number of bytes read.</summary>
      <returns>The total number of bytes read into the buffer. This can be less than the number of bytes requested if that many bytes are not currently available, or zero (0) if the end of the stream has been reached.</returns>
      <param name="buffer">An array of bytes. When this method returns, the buffer contains the specified byte array with the values between <paramref name="offset" /> and (<paramref name="offset" /> + <paramref name="count" /> - 1) replaced by the bytes read from the current source. </param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin storing the data read from the current stream. </param>
      <param name="count">The maximum number of bytes to be read from the current stream. </param>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.NotSupportedException">The stream does not support reading. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Stream.ReadAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Asynchronously reads a sequence of bytes from the current stream and advances the position within the stream by the number of bytes read.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the total number of bytes read into the buffer. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the stream has been reached. </returns>
      <param name="buffer">The buffer to write the data into.</param>
      <param name="offset">The byte offset in <paramref name="buffer" /> at which to begin writing data from the stream.</param>
      <param name="count">The maximum number of bytes to read.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.NotSupportedException">The stream does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.Stream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously reads a sequence of bytes from the current stream, advances the position within the stream by the number of bytes read, and monitors cancellation requests.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the total number of bytes read into the buffer. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the stream has been reached. </returns>
      <param name="buffer">The buffer to write the data into.</param>
      <param name="offset">The byte offset in <paramref name="buffer" /> at which to begin writing data from the stream.</param>
      <param name="count">The maximum number of bytes to read.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.NotSupportedException">The stream does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.Stream.ReadByte">
      <summary>Reads a byte from the stream and advances the position within the stream by one byte, or returns -1 if at the end of the stream.</summary>
      <returns>The unsigned byte cast to an Int32, or -1 if at the end of the stream.</returns>
      <exception cref="T:System.NotSupportedException">The stream does not support reading. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Stream.ReadTimeout">
      <summary>Gets or sets a value, in miliseconds, that determines how long the stream will attempt to read before timing out. </summary>
      <returns>A value, in miliseconds, that determines how long the stream will attempt to read before timing out.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.IO.Stream.ReadTimeout" /> method always throws an <see cref="T:System.InvalidOperationException" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Stream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>When overridden in a derived class, sets the position within the current stream.</summary>
      <returns>The new position within the current stream.</returns>
      <param name="offset">A byte offset relative to the <paramref name="origin" /> parameter. </param>
      <param name="origin">A value of type <see cref="T:System.IO.SeekOrigin" /> indicating the reference point used to obtain the new position. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.NotSupportedException">The stream does not support seeking, such as if the stream is constructed from a pipe or console output. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Stream.SetLength(System.Int64)">
      <summary>When overridden in a derived class, sets the length of the current stream.</summary>
      <param name="value">The desired length of the current stream in bytes. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.NotSupportedException">The stream does not support both writing and seeking, such as if the stream is constructed from a pipe or console output. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Stream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>When overridden in a derived class, writes a sequence of bytes to the current stream and advances the current position within this stream by the number of bytes written.</summary>
      <param name="buffer">An array of bytes. This method copies <paramref name="count" /> bytes from <paramref name="buffer" /> to the current stream. </param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> at which to begin copying bytes to the current stream. </param>
      <param name="count">The number of bytes to be written to the current stream. </param>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is greater than the buffer length.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" />  is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.IO.IOException">An I/O error occured, such as the specified file cannot be found.</exception>
      <exception cref="T:System.NotSupportedException">The stream does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="M:System.IO.Stream.Write(System.Byte[],System.Int32,System.Int32)" /> was called after the stream was closed.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Stream.WriteAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Asynchronously writes a sequence of bytes to the current stream and advances the current position within this stream by the number of bytes written.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">The buffer to write data from.</param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> from which to begin copying bytes to the stream.</param>
      <param name="count">The maximum number of bytes to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.NotSupportedException">The stream does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.Stream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Asynchronously writes a sequence of bytes to the current stream, advances the current position within this stream by the number of bytes written, and monitors cancellation requests.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">The buffer to write data from.</param>
      <param name="offset">The zero-based byte offset in <paramref name="buffer" /> from which to begin copying bytes to the stream.</param>
      <param name="count">The maximum number of bytes to write.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.NotSupportedException">The stream does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.Stream.WriteByte(System.Byte)">
      <summary>Writes a byte to the current position in the stream and advances the position within the stream by one byte.</summary>
      <param name="value">The byte to write to the stream. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.NotSupportedException">The stream does not support writing, or the stream is already closed. </exception>
      <exception cref="T:System.ObjectDisposedException">Methods were called after the stream was closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Stream.WriteTimeout">
      <summary>Gets or sets a value, in miliseconds, that determines how long the stream will attempt to write before timing out. </summary>
      <returns>A value, in miliseconds, that determines how long the stream will attempt to write before timing out.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.IO.Stream.WriteTimeout" /> method always throws an <see cref="T:System.InvalidOperationException" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.StreamReader">
      <summary>Implements a <see cref="T:System.IO.TextReader" /> that reads characters from a byte stream in a particular encoding.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StreamReader" /> class for the specified stream.</summary>
      <param name="stream">The stream to be read. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> does not support reading. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null. </exception>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StreamReader" /> class for the specified stream, with the specified byte order mark detection option.</summary>
      <param name="stream">The stream to be read. </param>
      <param name="detectEncodingFromByteOrderMarks">Indicates whether to look for byte order marks at the beginning of the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> does not support reading. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null. </exception>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream,System.Text.Encoding)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StreamReader" /> class for the specified stream, with the specified character encoding.</summary>
      <param name="stream">The stream to be read. </param>
      <param name="encoding">The character encoding to use. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> does not support reading. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> or <paramref name="encoding" /> is null. </exception>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream,System.Text.Encoding,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StreamReader" /> class for the specified stream, with the specified character encoding and byte order mark detection option.</summary>
      <param name="stream">The stream to be read. </param>
      <param name="encoding">The character encoding to use. </param>
      <param name="detectEncodingFromByteOrderMarks">Indicates whether to look for byte order marks at the beginning of the file. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> does not support reading. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> or <paramref name="encoding" /> is null. </exception>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream,System.Text.Encoding,System.Boolean,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StreamReader" /> class for the specified stream, with the specified character encoding, byte order mark detection option, and buffer size.</summary>
      <param name="stream">The stream to be read. </param>
      <param name="encoding">The character encoding to use. </param>
      <param name="detectEncodingFromByteOrderMarks">Indicates whether to look for byte order marks at the beginning of the file. </param>
      <param name="bufferSize">The minimum buffer size. </param>
      <exception cref="T:System.ArgumentException">The stream does not support reading. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> or <paramref name="encoding" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> is less than or equal to zero. </exception>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream,System.Text.Encoding,System.Boolean,System.Int32,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StreamReader" /> class for the specified stream based on the specified character encoding, byte order mark detection option, and buffer size, and optionally leaves the stream open.</summary>
      <param name="stream">The stream to read.</param>
      <param name="encoding">The character encoding to use.</param>
      <param name="detectEncodingFromByteOrderMarks">true to look for byte order marks at the beginning of the file; otherwise, false.</param>
      <param name="bufferSize">The minimum buffer size.</param>
      <param name="leaveOpen">true to leave the stream open after the <see cref="T:System.IO.StreamReader" /> object is disposed; otherwise, false.</param>
    </member>
    <member name="P:System.IO.StreamReader.BaseStream">
      <summary>Returns the underlying stream.</summary>
      <returns>The underlying stream.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.StreamReader.CurrentEncoding">
      <summary>Gets the current character encoding that the current <see cref="T:System.IO.StreamReader" /> object is using.</summary>
      <returns>The current character encoding used by the current reader. The value can be different after the first call to any <see cref="Overload:System.IO.StreamReader.Read" /> method of <see cref="T:System.IO.StreamReader" />, since encoding autodetection is not done until the first call to a <see cref="Overload:System.IO.StreamReader.Read" /> method.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.DiscardBufferedData">
      <summary>Clears the internal buffer.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.Dispose(System.Boolean)">
      <summary>Closes the underlying stream, releases the unmanaged resources used by the <see cref="T:System.IO.StreamReader" />, and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="P:System.IO.StreamReader.EndOfStream">
      <summary>Gets a value that indicates whether the current stream position is at the end of the stream.</summary>
      <returns>true if the current stream position is at the end of the stream; otherwise false.</returns>
      <exception cref="T:System.ObjectDisposedException">The underlying stream has been disposed.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.StreamReader.Null">
      <summary>A <see cref="T:System.IO.StreamReader" /> object around an empty stream.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.Peek">
      <summary>Returns the next available character but does not consume it.</summary>
      <returns>An integer representing the next character to be read, or -1 if there are no characters to be read or if the stream does not support seeking.</returns>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.Read">
      <summary>Reads the next character from the input stream and advances the character position by one character.</summary>
      <returns>The next character from the input stream represented as an <see cref="T:System.Int32" /> object, or -1 if no more characters are available.</returns>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.Read(System.Char[],System.Int32,System.Int32)">
      <summary>Reads a specified maximum of characters from the current stream into a buffer, beginning at the specified index.</summary>
      <returns>The number of characters that have been read, or 0 if at the end of the stream and no data was read. The number will be less than or equal to the <paramref name="count" /> parameter, depending on whether the data is available within the stream.</returns>
      <param name="buffer">When this method returns, contains the specified character array with the values between <paramref name="index" /> and (<paramref name="index + count - 1" />) replaced by the characters read from the current source. </param>
      <param name="index">The index of <paramref name="buffer" /> at which to begin writing. </param>
      <param name="count">The maximum number of characters to read. </param>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs, such as the stream is closed. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.ReadAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Reads a specified maximum number of characters from the current stream asynchronously and writes the data to a buffer, beginning at the specified index. </summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the total number of bytes read into the buffer. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the stream has been reached.</returns>
      <param name="buffer">When this method returns, contains the specified character array with the values between <paramref name="index" /> and (<paramref name="index" /> + <paramref name="count" /> - 1) replaced by the characters read from the current source.</param>
      <param name="index">The position in <paramref name="buffer" /> at which to begin writing.</param>
      <param name="count">The maximum number of characters to read. If the end of the stream is reached before the specified number of characters is written into the buffer, the current method returns.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="index" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.StreamReader.ReadBlock(System.Char[],System.Int32,System.Int32)">
      <summary>Reads a specified maximum number of characters from the current stream and writes the data to a buffer, beginning at the specified index.</summary>
      <returns>The number of characters that have been read. The number will be less than or equal to <paramref name="count" />, depending on whether all input characters have been read.</returns>
      <param name="buffer">When this method returns, contains the specified character array with the values between <paramref name="index" /> and (<paramref name="index + count - 1" />) replaced by the characters read from the current source.</param>
      <param name="index">The position in <paramref name="buffer" /> at which to begin writing.</param>
      <param name="count">The maximum number of characters to read.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.StreamReader" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurred. </exception>
    </member>
    <member name="M:System.IO.StreamReader.ReadBlockAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Reads a specified maximum number of characters from the current stream asynchronously and writes the data to a buffer, beginning at the specified index.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the total number of bytes read into the buffer. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the stream has been reached.</returns>
      <param name="buffer">When this method returns, contains the specified character array with the values between <paramref name="index" /> and (<paramref name="index" /> + <paramref name="count" /> - 1) replaced by the characters read from the current source.</param>
      <param name="index">The position in <paramref name="buffer" /> at which to begin writing.</param>
      <param name="count">The maximum number of characters to read. If the end of the stream is reached before the specified number of characters is written into the buffer, the method returns.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="index" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.StreamReader.ReadLine">
      <summary>Reads a line of characters from the current stream and returns the data as a string.</summary>
      <returns>The next line from the input stream, or null if the end of the input stream is reached.</returns>
      <exception cref="T:System.OutOfMemoryException">There is insufficient memory to allocate a buffer for the returned string. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.ReadLineAsync">
      <summary>Reads a line of characters asynchronously from the current stream and returns the data as a string.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the next line from the stream, or is null if all the characters have been read.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of characters in the next line is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.StreamReader.ReadToEnd">
      <summary>Reads all characters from the current position to the end of the stream.</summary>
      <returns>The rest of the stream as a string, from the current position to the end. If the current position is at the end of the stream, returns an empty string ("").</returns>
      <exception cref="T:System.OutOfMemoryException">There is insufficient memory to allocate a buffer for the returned string. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.ReadToEndAsync">
      <summary>Reads all characters from the current position to the end of the stream asynchronously and returns them as one string.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains a string with the characters from the current position to the end of the stream.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of characters is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="T:System.IO.StreamWriter">
      <summary>Implements a <see cref="T:System.IO.TextWriter" /> for writing characters to a stream in a particular encoding.To browse the .NET Framework source code for this type, see the Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StreamWriter" /> class for the specified stream by using UTF-8 encoding and the default buffer size.</summary>
      <param name="stream">The stream to write to. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> is not writable. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null. </exception>
    </member>
    <member name="M:System.IO.StreamWriter.#ctor(System.IO.Stream,System.Text.Encoding)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StreamWriter" /> class for the specified stream by using the specified encoding and the default buffer size.</summary>
      <param name="stream">The stream to write to. </param>
      <param name="encoding">The character encoding to use. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> or <paramref name="encoding" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> is not writable. </exception>
    </member>
    <member name="M:System.IO.StreamWriter.#ctor(System.IO.Stream,System.Text.Encoding,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StreamWriter" /> class for the specified stream by using the specified encoding and buffer size.</summary>
      <param name="stream">The stream to write to. </param>
      <param name="encoding">The character encoding to use. </param>
      <param name="bufferSize">The buffer size, in bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> or <paramref name="encoding" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> is negative. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> is not writable. </exception>
    </member>
    <member name="M:System.IO.StreamWriter.#ctor(System.IO.Stream,System.Text.Encoding,System.Int32,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StreamWriter" /> class for the specified stream by using the specified encoding and buffer size, and optionally leaves the stream open.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="encoding">The character encoding to use.</param>
      <param name="bufferSize">The buffer size, in bytes.</param>
      <param name="leaveOpen">true to leave the stream open after the <see cref="T:System.IO.StreamWriter" /> object is disposed; otherwise, false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> or <paramref name="encoding" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> is negative. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> is not writable. </exception>
    </member>
    <member name="P:System.IO.StreamWriter.AutoFlush">
      <summary>Gets or sets a value indicating whether the <see cref="T:System.IO.StreamWriter" /> will flush its buffer to the underlying stream after every call to <see cref="M:System.IO.StreamWriter.Write(System.Char)" />.</summary>
      <returns>true to force <see cref="T:System.IO.StreamWriter" /> to flush its buffer; otherwise, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.StreamWriter.BaseStream">
      <summary>Gets the underlying stream that interfaces with a backing store.</summary>
      <returns>The stream this StreamWriter is writing to.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.StreamWriter" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
      <exception cref="T:System.Text.EncoderFallbackException">The current encoding does not support displaying half of a Unicode surrogate pair.</exception>
    </member>
    <member name="P:System.IO.StreamWriter.Encoding">
      <summary>Gets the <see cref="T:System.Text.Encoding" /> in which the output is written.</summary>
      <returns>The <see cref="T:System.Text.Encoding" /> specified in the constructor for the current instance, or <see cref="T:System.Text.UTF8Encoding" /> if an encoding was not specified.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Flush">
      <summary>Clears all buffers for the current writer and causes any buffered data to be written to the underlying stream.</summary>
      <exception cref="T:System.ObjectDisposedException">The current writer is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error has occurred. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">The current encoding does not support displaying half of a Unicode surrogate pair. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.FlushAsync">
      <summary>Clears all buffers for this stream asynchronously and causes any buffered data to be written to the underlying device.</summary>
      <returns>A task that represents the asynchronous flush operation.</returns>
      <exception cref="T:System.ObjectDisposedException">The stream has been disposed.</exception>
    </member>
    <member name="F:System.IO.StreamWriter.Null">
      <summary>Provides a StreamWriter with no backing store that can be written to, but not read from.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Write(System.Char)">
      <summary>Writes a character to the stream.</summary>
      <param name="value">The character to write to the stream. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> is true or the <see cref="T:System.IO.StreamWriter" /> buffer is full, and current writer is closed. </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> is true or the <see cref="T:System.IO.StreamWriter" /> buffer is full, and the contents of the buffer cannot be written to the underlying fixed size stream because the <see cref="T:System.IO.StreamWriter" /> is at the end the stream. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Write(System.Char[])">
      <summary>Writes a character array to the stream.</summary>
      <param name="buffer">A character array containing the data to write. If <paramref name="buffer" /> is null, nothing is written. </param>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> is true or the <see cref="T:System.IO.StreamWriter" /> buffer is full, and current writer is closed. </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> is true or the <see cref="T:System.IO.StreamWriter" /> buffer is full, and the contents of the buffer cannot be written to the underlying fixed size stream because the <see cref="T:System.IO.StreamWriter" /> is at the end the stream. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Write(System.Char[],System.Int32,System.Int32)">
      <summary>Writes a subarray of characters to the stream.</summary>
      <param name="buffer">A character array that contains the data to write. </param>
      <param name="index">The character position in the buffer at which to start reading data. </param>
      <param name="count">The maximum number of characters to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> is true or the <see cref="T:System.IO.StreamWriter" /> buffer is full, and current writer is closed. </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> is true or the <see cref="T:System.IO.StreamWriter" /> buffer is full, and the contents of the buffer cannot be written to the underlying fixed size stream because the <see cref="T:System.IO.StreamWriter" /> is at the end the stream. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Write(System.String)">
      <summary>Writes a string to the stream.</summary>
      <param name="value">The string to write to the stream. If <paramref name="value" /> is null, nothing is written. </param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> is true or the <see cref="T:System.IO.StreamWriter" /> buffer is full, and current writer is closed. </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> is true or the <see cref="T:System.IO.StreamWriter" /> buffer is full, and the contents of the buffer cannot be written to the underlying fixed size stream because the <see cref="T:System.IO.StreamWriter" /> is at the end the stream. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.WriteAsync(System.Char)">
      <summary>Writes a character to the stream asynchronously.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="value">The character to write to the stream.</param>
      <exception cref="T:System.ObjectDisposedException">The stream writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream writer is currently in use by a previous write operation.</exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Writes a subarray of characters to the stream asynchronously.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">A character array that contains the data to write.</param>
      <param name="index">The character position in the buffer at which to begin reading data.</param>
      <param name="count">The maximum number of characters to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="index" /> plus <paramref name="count" /> is greater than the buffer length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteAsync(System.String)">
      <summary>Writes a string to the stream asynchronously.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="value">The string to write to the stream. If <paramref name="value" /> is null, nothing is written.</param>
      <exception cref="T:System.ObjectDisposedException">The stream writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream writer is currently in use by a previous write operation.</exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteLineAsync">
      <summary>Writes a line terminator asynchronously to the stream.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <exception cref="T:System.ObjectDisposedException">The stream writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream writer is currently in use by a previous write operation.</exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteLineAsync(System.Char)">
      <summary>Writes a character followed by a line terminator asynchronously to the stream.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="value">The character to write to the stream.</param>
      <exception cref="T:System.ObjectDisposedException">The stream writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream writer is currently in use by a previous write operation.</exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteLineAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Writes a subarray of characters followed by a line terminator asynchronously to the stream.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">The character array to write data from.</param>
      <param name="index">The character position in the buffer at which to start reading data.</param>
      <param name="count">The maximum number of characters to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="index" /> plus <paramref name="count" /> is greater than the buffer length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ObjectDisposedException">The stream writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteLineAsync(System.String)">
      <summary>Writes a string followed by a line terminator asynchronously to the stream.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="value">The string to write. If the value is null, only a line terminator is written. </param>
      <exception cref="T:System.ObjectDisposedException">The stream writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The stream writer is currently in use by a previous write operation.</exception>
    </member>
    <member name="T:System.IO.StringReader">
      <summary>Implements a <see cref="T:System.IO.TextReader" /> that reads from a string.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StringReader" /> class that reads from the specified string.</summary>
      <param name="s">The string to which the <see cref="T:System.IO.StringReader" /> should be initialized. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="s" /> parameter is null. </exception>
    </member>
    <member name="M:System.IO.StringReader.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.StringReader" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="M:System.IO.StringReader.Peek">
      <summary>Returns the next available character but does not consume it.</summary>
      <returns>An integer representing the next character to be read, or -1 if no more characters are available or the stream does not support seeking.</returns>
      <exception cref="T:System.ObjectDisposedException">The current reader is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.Read">
      <summary>Reads the next character from the input string and advances the character position by one character.</summary>
      <returns>The next character from the underlying string, or -1 if no more characters are available.</returns>
      <exception cref="T:System.ObjectDisposedException">The current reader is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.Read(System.Char[],System.Int32,System.Int32)">
      <summary>Reads a block of characters from the input string and advances the character position by <paramref name="count" />.</summary>
      <returns>The total number of characters read into the buffer. This can be less than the number of characters requested if that many characters are not currently available, or zero if the end of the underlying string has been reached.</returns>
      <param name="buffer">When this method returns, contains the specified character array with the values between <paramref name="index" /> and (<paramref name="index" /> + <paramref name="count" /> - 1) replaced by the characters read from the current source. </param>
      <param name="index">The starting index in the buffer. </param>
      <param name="count">The number of characters to read. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.ObjectDisposedException">The current reader is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.ReadAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Reads a specified maximum number of characters from the current string asynchronously and writes the data to a buffer, beginning at the specified index. </summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the total number of bytes read into the buffer. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the string has been reached.</returns>
      <param name="buffer">When this method returns, contains the specified character array with the values between <paramref name="index" /> and (<paramref name="index" /> + <paramref name="count" /> - 1) replaced by the characters read from the current source.</param>
      <param name="index">The position in <paramref name="buffer" /> at which to begin writing.</param>
      <param name="count">The maximum number of characters to read. If the end of the string is reached before the specified number of characters is written into the buffer, the method returns.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="index" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.ObjectDisposedException">The string reader has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.StringReader.ReadBlockAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Reads a specified maximum number of characters from the current string asynchronously and writes the data to a buffer, beginning at the specified index.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the total number of bytes read into the buffer. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the string has been reached.</returns>
      <param name="buffer">When this method returns, contains the specified character array with the values between <paramref name="index" /> and (<paramref name="index" /> + <paramref name="count" /> - 1) replaced by the characters read from the current source.</param>
      <param name="index">The position in <paramref name="buffer" /> at which to begin writing.</param>
      <param name="count">The maximum number of characters to read. If the end of the string is reached before the specified number of characters is written into the buffer, the method returns.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="index" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.ObjectDisposedException">The string reader has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.StringReader.ReadLine">
      <summary>Reads a line of characters from the current string and returns the data as a string.</summary>
      <returns>The next line from the current string, or null if the end of the string is reached.</returns>
      <exception cref="T:System.ObjectDisposedException">The current reader is closed. </exception>
      <exception cref="T:System.OutOfMemoryException">There is insufficient memory to allocate a buffer for the returned string. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.ReadLineAsync">
      <summary>Reads a line of characters asynchronously from the current string and returns the data as a string.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the next line from the string reader, or is null if all the characters have been read.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of characters in the next line is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The string reader has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.StringReader.ReadToEnd">
      <summary>Reads all characters from the current position to the end of the string and returns them as a single string.</summary>
      <returns>The content from the current position to the end of the underlying string.</returns>
      <exception cref="T:System.OutOfMemoryException">There is insufficient memory to allocate a buffer for the returned string. </exception>
      <exception cref="T:System.ObjectDisposedException">The current reader is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.ReadToEndAsync">
      <summary>Reads all characters from the current position to the end of the string asynchronously and returns them as a single string.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains a string with the characters from the current position to the end of the string. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of characters is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The string reader has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="T:System.IO.StringWriter">
      <summary>Implements a <see cref="T:System.IO.TextWriter" /> for writing information to a string. The information is stored in an underlying <see cref="T:System.Text.StringBuilder" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StringWriter" /> class.</summary>
    </member>
    <member name="M:System.IO.StringWriter.#ctor(System.IFormatProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StringWriter" /> class with the specified format control.</summary>
      <param name="formatProvider">An <see cref="T:System.IFormatProvider" /> object that controls formatting. </param>
    </member>
    <member name="M:System.IO.StringWriter.#ctor(System.Text.StringBuilder)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StringWriter" /> class that writes to the specified <see cref="T:System.Text.StringBuilder" />.</summary>
      <param name="sb">The StringBuilder to write to. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sb" /> is null. </exception>
    </member>
    <member name="M:System.IO.StringWriter.#ctor(System.Text.StringBuilder,System.IFormatProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.StringWriter" /> class that writes to the specified <see cref="T:System.Text.StringBuilder" /> and has the specified format provider.</summary>
      <param name="sb">The StringBuilder to write to. </param>
      <param name="formatProvider">An <see cref="T:System.IFormatProvider" /> object that controls formatting. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sb" /> is null. </exception>
    </member>
    <member name="M:System.IO.StringWriter.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.StringWriter" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="P:System.IO.StringWriter.Encoding">
      <summary>Gets the <see cref="T:System.Text.Encoding" /> in which the output is written.</summary>
      <returns>The Encoding in which the output is written.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.FlushAsync">
      <summary>Asynchronously clears all buffers for the current writer and causes any buffered data to be written to the underlying device. </summary>
      <returns>A task that represents the asynchronous flush operation.</returns>
    </member>
    <member name="M:System.IO.StringWriter.GetStringBuilder">
      <summary>Returns the underlying <see cref="T:System.Text.StringBuilder" />.</summary>
      <returns>The underlying StringBuilder.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.ToString">
      <summary>Returns a string containing the characters written to the current StringWriter so far.</summary>
      <returns>The string containing the characters written to the current StringWriter.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.Write(System.Char)">
      <summary>Writes a character to the string.</summary>
      <param name="value">The character to write. </param>
      <exception cref="T:System.ObjectDisposedException">The writer is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.Write(System.Char[],System.Int32,System.Int32)">
      <summary>Writes a subarray of characters to the string.</summary>
      <param name="buffer">The character array to write data from. </param>
      <param name="index">The position in the buffer at which to start reading data.</param>
      <param name="count">The maximum number of characters to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.ArgumentException">(<paramref name="index" /> + <paramref name="count" />)&gt; <paramref name="buffer" />. Length. </exception>
      <exception cref="T:System.ObjectDisposedException">The writer is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.Write(System.String)">
      <summary>Writes a string to the current string.</summary>
      <param name="value">The string to write. </param>
      <exception cref="T:System.ObjectDisposedException">The writer is closed. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.WriteAsync(System.Char)">
      <summary>Writes a character to the string asynchronously.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="value">The character to write to the string.</param>
      <exception cref="T:System.ObjectDisposedException">The string writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The string writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.StringWriter.WriteAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Writes a subarray of characters to the string asynchronously.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">The character array to write data from.</param>
      <param name="index">The position in the buffer at which to start reading data.</param>
      <param name="count">The maximum number of characters to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="index" /> plus <paramref name="count" /> is greater than the buffer length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ObjectDisposedException">The string writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The string writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.StringWriter.WriteAsync(System.String)">
      <summary>Writes a string to the current string asynchronously.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="value">The string to write. If <paramref name="value" /> is null, nothing is written to the text stream.</param>
      <exception cref="T:System.ObjectDisposedException">The string writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The string writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.StringWriter.WriteLineAsync(System.Char)">
      <summary>Writes a character followed by a line terminator asynchronously to the string.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="value">The character to write to the string.</param>
      <exception cref="T:System.ObjectDisposedException">The string writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The string writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.StringWriter.WriteLineAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Writes a subarray of characters followed by a line terminator asynchronously to the string.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">The character array to write data from.</param>
      <param name="index">The position in the buffer at which to start reading data.</param>
      <param name="count">The maximum number of characters to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="index" /> plus <paramref name="count" /> is greater than the buffer length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ObjectDisposedException">The string writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The string writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.StringWriter.WriteLineAsync(System.String)">
      <summary>Writes a string followed by a line terminator asynchronously to the current string.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="value">The string to write. If the value is null, only a line terminator is written.</param>
      <exception cref="T:System.ObjectDisposedException">The string writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The string writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="T:System.IO.TextReader">
      <summary>Represents a reader that can read a sequential series of characters.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.TextReader" /> class.</summary>
    </member>
    <member name="M:System.IO.TextReader.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.IO.TextReader" /> object.</summary>
    </member>
    <member name="M:System.IO.TextReader.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.TextReader" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="F:System.IO.TextReader.Null">
      <summary>Provides a TextReader with no data to read from.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.Peek">
      <summary>Reads the next character without changing the state of the reader or the character source. Returns the next available character without actually reading it from the reader.</summary>
      <returns>An integer representing the next character to be read, or -1 if no more characters are available or the reader does not support seeking.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextReader" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.Read">
      <summary>Reads the next character from the text reader and advances the character position by one character.</summary>
      <returns>The next character from the text reader, or -1 if no more characters are available. The default implementation returns -1.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextReader" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.Read(System.Char[],System.Int32,System.Int32)">
      <summary>Reads a specified maximum number of characters from the current reader and writes the data to a buffer, beginning at the specified index.</summary>
      <returns>The number of characters that have been read. The number will be less than or equal to <paramref name="count" />, depending on whether the data is available within the reader. This method returns 0 (zero) if it is called when no more characters are left to read.</returns>
      <param name="buffer">When this method returns, contains the specified character array with the values between <paramref name="index" /> and (<paramref name="index" /> + <paramref name="count" /> - 1) replaced by the characters read from the current source. </param>
      <param name="index">The position in <paramref name="buffer" /> at which to begin writing. </param>
      <param name="count">The maximum number of characters to read. If the end of the reader is reached before the specified number of characters is read into the buffer, the method returns. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextReader" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.ReadAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Reads a specified maximum number of characters from the current text reader asynchronously and writes the data to a buffer, beginning at the specified index. </summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the total number of bytes read into the buffer. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the text has been reached.</returns>
      <param name="buffer">When this method returns, contains the specified character array with the values between <paramref name="index" /> and (<paramref name="index" /> + <paramref name="count" /> - 1) replaced by the characters read from the current source.</param>
      <param name="index">The position in <paramref name="buffer" /> at which to begin writing.</param>
      <param name="count">The maximum number of characters to read. If the end of the text is reached before the specified number of characters is read into the buffer, the current method returns.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="index" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.ObjectDisposedException">The text reader has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.TextReader.ReadBlock(System.Char[],System.Int32,System.Int32)">
      <summary>Reads a specified maximum number of characters from the current text reader and writes the data to a buffer, beginning at the specified index.</summary>
      <returns>The number of characters that have been read. The number will be less than or equal to <paramref name="count" />, depending on whether all input characters have been read.</returns>
      <param name="buffer">When this method returns, this parameter contains the specified character array with the values between <paramref name="index" /> and (<paramref name="index" /> + <paramref name="count" /> -1) replaced by the characters read from the current source. </param>
      <param name="index">The position in <paramref name="buffer" /> at which to begin writing.</param>
      <param name="count">The maximum number of characters to read. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextReader" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.ReadBlockAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Reads a specified maximum number of characters from the current text reader asynchronously and writes the data to a buffer, beginning at the specified index.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the total number of bytes read into the buffer. The result value can be less than the number of bytes requested if the number of bytes currently available is less than the requested number, or it can be 0 (zero) if the end of the text has been reached.</returns>
      <param name="buffer">When this method returns, contains the specified character array with the values between <paramref name="index" /> and (<paramref name="index" /> + <paramref name="count" /> - 1) replaced by the characters read from the current source.</param>
      <param name="index">The position in <paramref name="buffer" /> at which to begin writing.</param>
      <param name="count">The maximum number of characters to read. If the end of the text is reached before the specified number of characters is read into the buffer, the current method returns.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="index" /> and <paramref name="count" /> is larger than the buffer length.</exception>
      <exception cref="T:System.ObjectDisposedException">The text reader has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.TextReader.ReadLine">
      <summary>Reads a line of characters from the text reader and returns the data as a string.</summary>
      <returns>The next line from the reader, or null if all characters have been read.</returns>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.OutOfMemoryException">There is insufficient memory to allocate a buffer for the returned string. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextReader" /> is closed. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of characters in the next line is larger than <see cref="F:System.Int32.MaxValue" /></exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.ReadLineAsync">
      <summary>Reads a line of characters asynchronously and returns the data as a string. </summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains the next line from the text reader, or is null if all of the characters have been read. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of characters in the next line is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The text reader has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="M:System.IO.TextReader.ReadToEnd">
      <summary>Reads all characters from the current position to the end of the text reader and returns them as one string.</summary>
      <returns>A string that contains all characters from the current position to the end of the text reader.</returns>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextReader" /> is closed. </exception>
      <exception cref="T:System.OutOfMemoryException">There is insufficient memory to allocate a buffer for the returned string. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of characters in the next line is larger than <see cref="F:System.Int32.MaxValue" /></exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.ReadToEndAsync">
      <summary>Reads all characters from the current position to the end of the text reader asynchronously and returns them as one string.</summary>
      <returns>A task that represents the asynchronous read operation. The value of the <paramref name="TResult" /> parameter contains a string with the characters from the current position to the end of the text reader. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of characters is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The text reader has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The reader is currently in use by a previous read operation. </exception>
    </member>
    <member name="T:System.IO.TextWriter">
      <summary>Represents a writer that can write a sequential series of characters. This class is abstract.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.IO.TextWriter" /> class.</summary>
    </member>
    <member name="M:System.IO.TextWriter.#ctor(System.IFormatProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.IO.TextWriter" /> class with the specified format provider.</summary>
      <param name="formatProvider">An <see cref="T:System.IFormatProvider" /> object that controls formatting. </param>
    </member>
    <member name="F:System.IO.TextWriter.CoreNewLine">
      <summary>Stores the newline characters used for this TextWriter.</summary>
    </member>
    <member name="M:System.IO.TextWriter.Dispose">
      <summary>Releases all resources used by the <see cref="T:System.IO.TextWriter" /> object.</summary>
    </member>
    <member name="M:System.IO.TextWriter.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.IO.TextWriter" /> and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources. </param>
    </member>
    <member name="P:System.IO.TextWriter.Encoding">
      <summary>When overridden in a derived class, returns the character encoding in which the output is written.</summary>
      <returns>The character encoding in which the output is written.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Flush">
      <summary>Clears all buffers for the current writer and causes any buffered data to be written to the underlying device.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.FlushAsync">
      <summary>Asynchronously clears all buffers for the current writer and causes any buffered data to be written to the underlying device. </summary>
      <returns>A task that represents the asynchronous flush operation. </returns>
      <exception cref="T:System.ObjectDisposedException">The text writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="P:System.IO.TextWriter.FormatProvider">
      <summary>Gets an object that controls formatting.</summary>
      <returns>An <see cref="T:System.IFormatProvider" /> object for a specific culture, or the formatting of the current culture if no other culture is specified.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.TextWriter.NewLine">
      <summary>Gets or sets the line terminator string used by the current TextWriter.</summary>
      <returns>The line terminator string for the current TextWriter.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.IO.TextWriter.Null">
      <summary>Provides a TextWriter with no backing store that can be written to, but not read from.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Boolean)">
      <summary>Writes the text representation of a Boolean value to the text string or stream.</summary>
      <param name="value">The Boolean value to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Char)">
      <summary>Writes a character to the text string or stream.</summary>
      <param name="value">The character to write to the text stream. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Char[])">
      <summary>Writes a character array to the text string or stream.</summary>
      <param name="buffer">The character array to write to the text stream. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Char[],System.Int32,System.Int32)">
      <summary>Writes a subarray of characters to the text string or stream.</summary>
      <param name="buffer">The character array to write data from. </param>
      <param name="index">The character position in the buffer at which to start retrieving data. </param>
      <param name="count">The number of characters to write. </param>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Decimal)">
      <summary>Writes the text representation of a decimal value to the text string or stream.</summary>
      <param name="value">The decimal value to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Double)">
      <summary>Writes the text representation of an 8-byte floating-point value to the text string or stream.</summary>
      <param name="value">The 8-byte floating-point value to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Int32)">
      <summary>Writes the text representation of a 4-byte signed integer to the text string or stream.</summary>
      <param name="value">The 4-byte signed integer to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Int64)">
      <summary>Writes the text representation of an 8-byte signed integer to the text string or stream.</summary>
      <param name="value">The 8-byte signed integer to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Object)">
      <summary>Writes the text representation of an object to the text string or stream by calling the ToString method on that object.</summary>
      <param name="value">The object to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Single)">
      <summary>Writes the text representation of a 4-byte floating-point value to the text string or stream.</summary>
      <param name="value">The 4-byte floating-point value to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.String)">
      <summary>Writes a string to the text string or stream.</summary>
      <param name="value">The string to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.String,System.Object)">
      <summary>Writes a formatted string to the text string or stream, using the same semantics as the <see cref="M:System.String.Format(System.String,System.Object)" /> method.</summary>
      <param name="format">A composite format string (see Remarks). </param>
      <param name="arg0">The object to format and write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is not a valid composite format string.-or- The index of a format item is less than 0 (zero), or greater than or equal to the number of objects to be formatted (which, for this method overload, is one). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.String,System.Object,System.Object)">
      <summary>Writes a formatted string to the text string or stream, using the same semantics as the <see cref="M:System.String.Format(System.String,System.Object,System.Object)" /> method.</summary>
      <param name="format">A composite format string (see Remarks). </param>
      <param name="arg0">The first object to format and write. </param>
      <param name="arg1">The second object to format and write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is not a valid composite format string.-or- The index of a format item is less than 0 (zero) or greater than or equal to the number of objects to be formatted (which, for this method overload, is two). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.String,System.Object,System.Object,System.Object)">
      <summary>Writes a formatted string to the text string or stream, using the same semantics as the <see cref="M:System.String.Format(System.String,System.Object,System.Object,System.Object)" /> method.</summary>
      <param name="format">A composite format string (see Remarks). </param>
      <param name="arg0">The first object to format and write. </param>
      <param name="arg1">The second object to format and write. </param>
      <param name="arg2">The third object to format and write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is not a valid composite format string.-or- The index of a format item is less than 0 (zero), or greater than or equal to the number of objects to be formatted (which, for this method overload, is three). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.String,System.Object[])">
      <summary>Writes a formatted string to the text string or stream, using the same semantics as the <see cref="M:System.String.Format(System.String,System.Object[])" /> method.</summary>
      <param name="format">A composite format string (see Remarks). </param>
      <param name="arg">An object array that contains zero or more objects to format and write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> or <paramref name="arg" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is not a valid composite format string.-or- The index of a format item is less than 0 (zero), or greater than or equal to the length of the <paramref name="arg" /> array. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.UInt32)">
      <summary>Writes the text representation of a 4-byte unsigned integer to the text string or stream.</summary>
      <param name="value">The 4-byte unsigned integer to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.UInt64)">
      <summary>Writes the text representation of an 8-byte unsigned integer to the text string or stream.</summary>
      <param name="value">The 8-byte unsigned integer to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteAsync(System.Char)">
      <summary>Writes a character to the text string or stream asynchronously.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="value">The character to write to the text stream.</param>
      <exception cref="T:System.ObjectDisposedException">The text writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The text writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteAsync(System.Char[])">
      <summary>Writes a character array to the text string or stream asynchronously.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">The character array to write to the text stream. If <paramref name="buffer" /> is null, nothing is written.</param>
      <exception cref="T:System.ObjectDisposedException">The text writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The text writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Writes a subarray of characters to the text string or stream asynchronously. </summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">The character array to write data from. </param>
      <param name="index">The character position in the buffer at which to start retrieving data. </param>
      <param name="count">The number of characters to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="index" /> plus <paramref name="count" /> is greater than the buffer length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ObjectDisposedException">The text writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The text writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteAsync(System.String)">
      <summary>Writes a string to the text string or stream asynchronously.</summary>
      <returns>A task that represents the asynchronous write operation. </returns>
      <param name="value">The string to write. If <paramref name="value" /> is null, nothing is written to the text stream.</param>
      <exception cref="T:System.ObjectDisposedException">The text writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The text writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine">
      <summary>Writes a line terminator to the text string or stream.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Boolean)">
      <summary>Writes the text representation of a Boolean value followed by a line terminator to the text string or stream.</summary>
      <param name="value">The Boolean value to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Char)">
      <summary>Writes a character followed by a line terminator to the text string or stream.</summary>
      <param name="value">The character to write to the text stream. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Char[])">
      <summary>Writes an array of characters followed by a line terminator to the text string or stream.</summary>
      <param name="buffer">The character array from which data is read. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Char[],System.Int32,System.Int32)">
      <summary>Writes a subarray of characters followed by a line terminator to the text string or stream.</summary>
      <param name="buffer">The character array from which data is read. </param>
      <param name="index">The character position in <paramref name="buffer" /> at which to start reading data. </param>
      <param name="count">The maximum number of characters to write. </param>
      <exception cref="T:System.ArgumentException">The buffer length minus <paramref name="index" /> is less than <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="buffer" /> parameter is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Decimal)">
      <summary>Writes the text representation of a decimal value followed by a line terminator to the text string or stream.</summary>
      <param name="value">The decimal value to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Double)">
      <summary>Writes the text representation of a 8-byte floating-point value followed by a line terminator to the text string or stream.</summary>
      <param name="value">The 8-byte floating-point value to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Int32)">
      <summary>Writes the text representation of a 4-byte signed integer followed by a line terminator to the text string or stream.</summary>
      <param name="value">The 4-byte signed integer to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Int64)">
      <summary>Writes the text representation of an 8-byte signed integer followed by a line terminator to the text string or stream.</summary>
      <param name="value">The 8-byte signed integer to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Object)">
      <summary>Writes the text representation of an object by calling the ToString method on that object, followed by a line terminator to the text string or stream.</summary>
      <param name="value">The object to write. If <paramref name="value" /> is null, only the line terminator is written. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Single)">
      <summary>Writes the text representation of a 4-byte floating-point value followed by a line terminator to the text string or stream.</summary>
      <param name="value">The 4-byte floating-point value to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.String)">
      <summary>Writes a string followed by a line terminator to the text string or stream.</summary>
      <param name="value">The string to write. If <paramref name="value" /> is null, only the line terminator is written. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.String,System.Object)">
      <summary>Writes a formatted string and a new line to the text string or stream, using the same semantics as the <see cref="M:System.String.Format(System.String,System.Object)" /> method.</summary>
      <param name="format">A composite format string (see Remarks).</param>
      <param name="arg0">The object to format and write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is not a valid composite format string.-or- The index of a format item is less than 0 (zero), or greater than or equal to the number of objects to be formatted (which, for this method overload, is one). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.String,System.Object,System.Object)">
      <summary>Writes a formatted string and a new line to the text string or stream, using the same semantics as the <see cref="M:System.String.Format(System.String,System.Object,System.Object)" /> method.</summary>
      <param name="format">A composite format string (see Remarks).</param>
      <param name="arg0">The first object to format and write. </param>
      <param name="arg1">The second object to format and write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is not a valid composite format string.-or- The index of a format item is less than 0 (zero), or greater than or equal to the number of objects to be formatted (which, for this method overload, is two). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.String,System.Object,System.Object,System.Object)">
      <summary>Writes out a formatted string and a new line, using the same semantics as <see cref="M:System.String.Format(System.String,System.Object)" />.</summary>
      <param name="format">A composite format string (see Remarks).</param>
      <param name="arg0">The first object to format and write. </param>
      <param name="arg1">The second object to format and write. </param>
      <param name="arg2">The third object to format and write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> is null. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is not a valid composite format string.-or- The index of a format item is less than 0 (zero), or greater than or equal to the number of objects to be formatted (which, for this method overload, is three). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.String,System.Object[])">
      <summary>Writes out a formatted string and a new line, using the same semantics as <see cref="M:System.String.Format(System.String,System.Object)" />.</summary>
      <param name="format">A composite format string (see Remarks).</param>
      <param name="arg">An object array that contains zero or more objects to format and write. </param>
      <exception cref="T:System.ArgumentNullException">A string or object is passed in as null. </exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> is not a valid composite format string.-or- The index of a format item is less than 0 (zero), or greater than or equal to the length of the <paramref name="arg" /> array. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.UInt32)">
      <summary>Writes the text representation of a 4-byte unsigned integer followed by a line terminator to the text string or stream.</summary>
      <param name="value">The 4-byte unsigned integer to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.UInt64)">
      <summary>Writes the text representation of an 8-byte unsigned integer followed by a line terminator to the text string or stream.</summary>
      <param name="value">The 8-byte unsigned integer to write. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.IO.TextWriter" /> is closed. </exception>
      <exception cref="T:System.IO.IOException">An I/O error occurs. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLineAsync">
      <summary>Writes a line terminator asynchronously to the text string or stream.</summary>
      <returns>A task that represents the asynchronous write operation. </returns>
      <exception cref="T:System.ObjectDisposedException">The text writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The text writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteLineAsync(System.Char)">
      <summary>Writes a character followed by a line terminator asynchronously to the text string or stream.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="value">The character to write to the text stream.</param>
      <exception cref="T:System.ObjectDisposedException">The text writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The text writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteLineAsync(System.Char[])">
      <summary>Writes an array of characters followed by a line terminator asynchronously to the text string or stream.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">The character array to write to the text stream. If the character array is null, only the line terminator is written. </param>
      <exception cref="T:System.ObjectDisposedException">The text writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The text writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteLineAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Writes a subarray of characters followed by a line terminator asynchronously to the text string or stream.</summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="buffer">The character array to write data from. </param>
      <param name="index">The character position in the buffer at which to start retrieving data. </param>
      <param name="count">The number of characters to write. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="index" /> plus <paramref name="count" /> is greater than the buffer length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> or <paramref name="count" /> is negative.</exception>
      <exception cref="T:System.ObjectDisposedException">The text writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The text writer is currently in use by a previous write operation. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteLineAsync(System.String)">
      <summary>Writes a string followed by a line terminator asynchronously to the text string or stream. </summary>
      <returns>A task that represents the asynchronous write operation.</returns>
      <param name="value">The string to write. If the value is null, only a line terminator is written. </param>
      <exception cref="T:System.ObjectDisposedException">The text writer is disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The text writer is currently in use by a previous write operation. </exception>
    </member>
  </members>
</doc>