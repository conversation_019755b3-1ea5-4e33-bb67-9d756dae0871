import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { authFetch } from '../authFetch';

// We will mock the supabase client creation inside authFetch via module mocking
vi.mock('@supabase/ssr', () => {
  const sessionStore: { token: string | null } = { token: 'initial-token' };
  return {
    createBrowserClient: () => ({
      auth: {
        getSession: vi.fn(async () => ({ data: { session: sessionStore.token ? { access_token: sessionStore.token } : null } })),
        refreshSession: vi.fn(async () => { sessionStore.token = 'refreshed-token'; return { data: { session: { access_token: sessionStore.token } } }; })
      }
    })
  };
});

describe('authFetch 401 refresh flow', () => {
  const originalFetch = global.fetch;

  beforeEach(() => {
    let firstCall = true;
    // Mock fetch: first call returns 401, second returns 200 with JSON
    global.fetch = vi.fn(async () => {
      if (firstCall) {
        firstCall = false;
        return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401, headers: { 'Content-Type': 'application/json' } });
      }
      return new Response(JSON.stringify({ ok: true }), { status: 200, headers: { 'Content-Type': 'application/json' } });
    }) as any;
  });

  afterEach(() => {
    global.fetch = originalFetch;
    vi.clearAllMocks();
  });

  it('retries after 401 with refreshed token', async () => {
    const res = await authFetch('/api/test', { method: 'GET' });
    expect(res.status).toBe(200);
    // fetch should have been called twice
    expect((global.fetch as any).mock.calls.length).toBe(2);
  });

  it('does not retry when retryOn401=false', async () => {
    let first = true;
    (global.fetch as any).mockImplementationOnce(async () => {
      first = false;
      return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401, headers: { 'Content-Type': 'application/json' } });
    });
    const res = await authFetch('/api/test-no-retry', { method: 'GET', retryOn401: false });
    expect(res.status).toBe(401);
    expect((global.fetch as any).mock.calls.length).toBe(1);
  });
});
