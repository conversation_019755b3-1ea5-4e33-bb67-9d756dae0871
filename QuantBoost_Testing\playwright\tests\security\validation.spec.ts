import { test, expect } from '@playwright/test';
import { fillLinkEmail } from '../../utils/stripe-elements';

const enableSecurity = (process.env.ENABLE_SECURITY_TESTS || 'true').toLowerCase() === 'true';
const ANNUAL_PRICE_ID = 'price_1RC3HTE6FvhUKV1bE9D6zf6e';

test.describe('Security Validation', () => {
  test.skip(!enableSecurity, 'Security tests disabled');

  test('XSS attempt in email input does not render HTML', async ({ page }) => {
    await page.goto(`/checkout/${ANNUAL_PRICE_ID}`);
  const payload = `<img src=x onerror=alert('xss')>`;
  const email = `user+${Date.now()}@example.com${payload}`;
  const filled = await fillLinkEmail(page, email);
  expect(filled).toBeTruthy();
    // Expect the payload not to appear raw in DOM (heuristic) - placeholder until selectors refined
    const content = await page.content();
    expect(content).not.toContain(payload);
  });
});
