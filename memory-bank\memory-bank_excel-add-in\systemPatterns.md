---
title: System Patterns for QuantBoost Excel Add-in
purpose: Describes the planned architecture, components, data flow, and design patterns for the QuantBoost Excel Add-in.
projects: ["excel-add-in"]
source_analysis: "Documentation review of CleanExcel Module Development Guide.md, Excel Trace Development Guide.md, Excel Trace Technical Specifications.md, Sheet Size Analyzer Development Guide.md, and project guidelines."
status: bootstrapping
last_updated: 2025-05-13T17:10:00Z # Will be updated by agent
tags: ["excel-add-in", "architecture", "components", "dataflow", "planned"]
---

## Planned Architecture and System Patterns: QuantBoost Excel Add-in

Based on the provided development documentation and project guidelines, the QuantBoost Excel Add-in will follow a modern VSTO add-in architecture, heavily influenced by the need for responsive UI, robust licensing, and modular feature design.

### High-Level Architecture
*   **Type:** VSTO (Visual Studio Tools for Office) Excel Add-in using .NET Framework 4.8.1 and C# 7.3.
*   **Model:** Event-driven, responding to Excel application events, user interactions with a custom Ribbon, and actions within Task Panes.
*   **Structure:** Will consist of a main add-in class (`ThisAddIn.cs`), UI components (Ribbon, Task Panes, Dialogs built with WinForms), dedicated service classes for each core feature (CleanExcel, Excel Trace, Sheet Size Analyzer), and shared utility/controller classes.
*   **Key Principles:**
    *   Asynchronous operations (`async`/`await` C# 7.3 style) are paramount to prevent UI freezing.
    *   Centralized licensing via the `QuantBoost.Licensing.dll` SDK will gate features and manage user entitlements. An active license (Active, TrialActive, GracePeriod) unlocks ALL features.
    *   Strict adherence to COM object management best practices.

### Main Planned Components and Responsibilities

*   **`ThisAddIn.cs` (Main Add-in Class):**
    *   Handles add-in lifecycle (`_Startup`, `_Shutdown`).
    *   Initializes and manages the `QuantBoostLicensingManager` (from `QuantBoost.Licensing.dll`) for the Excel add-in, using the correct product ID (e.g., "quantboost-excel-addin").
    *   Performs initial license validation **asynchronously** in `ThisAddIn_Startup` via `ValidateUsingStoredKeyAsync()`.
    *   Subscribes to `LicenseStatusChanged` events from the SDK to dynamically update UI elements (Ribbon controls, Task Pane features) based on the current license state.
    *   Manages global resources: `CancellationTokenSource` for background tasks, initializes `AsyncHelper`.
    *   Creates the Ribbon extensibility object (`CreateRibbonExtensibilityObject`).
*   **`QuantBoostExcelRibbon.cs` & `QuantBoostExcelRibbon.xml` (Ribbon & UI Controllers):**
    *   Implements `IRibbonExtensibility`. Constructor **MUST BE MINIMAL**.
    *   `OnLoad` callback caches `Office.IRibbonUI`.
    *   Uses `getEnabled`/`getVisible` callbacks for dynamic UI state based on license or context, updated via `IRibbonUI.Invalidate()`.
    *   Ribbon handlers delegate actions to specific controllers or services.
    *   Controllers (optional but recommended) manage logic for Task Panes, orchestrating calls to services and updating the UI.
*   **`CleanExcel (Name Scrubber)` Module (`Features/NameScrubber/`):**
    *   **`NameScrubberService`:** Contains logic to find problematic defined names (Hidden, Error, External, Empty) asynchronously using `Excel.Workbook.Names` and `Excel.Worksheet.Names`. Provides methods to delete selected names. Deletion requires active license and user confirmation.
    *   **`NameInfo` Model:** Data structure for identified defined names.
    *   **UI (Task Pane - WinForms `UserControl`):** Displays identified names, allows filtering, selection, and deletion. Shows license status.
*   **`Excel Trace` Module (`Features/DependencyTracer/`):**
    *   **`DependencyTracer` Service:** Asynchronously traces cell dependents (`Range.Dependents`) and precedents. Handles multi-sheet/area scenarios and COM exceptions. Feature requires an active license.
    *   **`DependentInfo` Model:** Data structure for traced dependencies.
    *   **UI (Task Pane - WinForms `UserControl`):** Displays traced dependencies, allows navigation.
    *   **Hotkey Manager:** Manages registration/unregistration of the trace hotkey (`Ctrl+Shift+[`) based on license status (active only).
*   **`Sheet Size Analyzer` Module (`Features/SheetAnalyzer/`):**
    *   **`SheetAnalyzerService`:** Asynchronously analyzes workbook size via `AnalyzeWorkbookAsync`.
        *   **Dual Path:**
            *   **OpenXML SDK v2.18.0:** Preferred for saved/accessible `.xlsx` files.
            *   **COM Interop Fallback:** For unsaved or protected workbooks, uses heuristics. UI must indicate "estimate".
    *   **`SheetSizeInfo` Model:** Data structure for per-sheet size analysis results.
    *   **UI (Task Pane - WinForms `UserControl`):** Displays sheet size breakdown, total size, analysis timestamp, and analysis mode.
*   **Shared `QuantBoost.Licensing.dll` SDK Integration:**
    *   All modules interact with the central `QuantBoostLicensingManager` initialized in `ThisAddIn.cs`.
    *   License validation (`ValidateUsingStoredKeyAsync()`) called asynchronously at startup.
    *   UI elements are enabled/disabled based on `LicenseStatusChanged` events (Active, TrialActive, GracePeriod vs. Inactive/Expired).
*   **`Utilities` Namespace (Shared Helpers):**
    *   **`AsyncHelper.cs`:** Initialized in `ThisAddIn_Startup`. Provides `RunOnUIThread`, `RunSafeAsync`, etc.
    *   **`ErrorHandlingService.cs`:** For centralized logging and user-facing error messages.
    *   **`ToastNotifier.cs`:** For non-modal notifications and progress reporting.
    *   **`ExcelComWrapper` (Assumed):** `IDisposable` wrapper for COM objects, used with `using` statements.

### Key Planned Data Flow Patterns
*   **Licensing Flow (Consistent with Project Guidelines):**
    1.  `ThisAddIn_Startup`: Initialize `QuantBoostLicensingManager`, call `ValidateUsingStoredKeyAsync()`.
    2.  `LicenseStatusChanged` event fires from SDK.
    3.  `ThisAddIn.cs` handles the event, potentially invalidating the Ribbon (`IRibbonUI.Invalidate()`) and notifying Task Panes.
    4.  UI elements (Ribbon, Panes) update on the UI thread (via `AsyncHelper` if needed from background task) to reflect current license status.
*   **Asynchronous Feature Analysis (e.g., Sheet Size Analyzer):**
    1.  User clicks Ribbon button.
    2.  Ribbon callback (or Controller) calls `SheetAnalyzerService.AnalyzeWorkbookAsync(workbook, progressReporter)` off the UI thread (`Task.Run`).
    3.  Service performs analysis (OpenXML or COM) on a background thread, reporting progress via `IProgress<T>` to `ToastNotifier`.
    4.  Upon completion, results are returned.
    5.  Controller/Callback updates the Task Pane UI on the main thread with the results.
    6.  Non-blocking toasts used for notifications/errors via `ToastNotifier` and `ErrorHandlingService`.
*   **Error Handling:** Consistent use of `try-catch` around COM calls. Errors logged via `ErrorHandlingService.LogException` (startup) or `ErrorHandlingService.HandleException` (runtime).

### Anticipated Design Patterns
*   **Singleton (via `Globals.ThisAddIn`):** For accessing the main add-in instance, licensing manager, or shared services from within the VSTO context.
*   **Event-Driven Architecture:** Core to VSTO and for handling license status changes.
*   **Observer Pattern:** For `LicenseStatusChanged` events.
*   **Service Layer:** For encapsulating business logic (e.g., `NameScrubberService`, `DependencyTracer`, `SheetAnalyzerService`).
*   **Model-View-Controller (MVC) or Model-View-Presenter (MVP) variants:** For structuring UI (Task Panes) and application logic.
*   **Strategy Pattern:** The `Sheet Size Analyzer`'s use of OpenXML vs. COM Interop based on file state.
*   **Facade Pattern:** Shared helper services (`AsyncHelper`, `ErrorHandlingService`) provide simplified interfaces.

This planned architecture aims for a robust, maintainable, and user-friendly Excel add-in that adheres strictly to the specified technical constraints and best practices.
