# Quant Boost CleanExcel (Name Scrubber) Add-in  
### Enhanced Development Guide v3.0 (with Shared Licensing SDK Integration)  
_Last updated: April 8, 2025_

---

## Overview

CleanExcel enables users to identify and clean up **hidden, broken, error, and external defined names** inside Excel workbooks, improving workbook health, security, and reducing file bloat. This add-in integrates seamlessly with Quant Boost SaaS licensing services to control access and promote upgrades.

---

## Table of Contents
- [1. Architecture Principles](#1-architecture-principles)  
- [2. Development Phases](#2-development-phases)  
   - [Phase 1: Project Setup](#phase-1-project-setup)  
   - [Phase 2: Name Discovery, Analysis & Deletion](#phase-2-name-discovery-analysis--deletion)  
   - [Phase 3: UI and Ribbon Integration](#phase-3-ui-and-ribbon-integration)  
   - [Phase 4: Licensing & UX Layer](#phase-4-licensing--ux-layer)  
   - [Phase 5: Testing, Telemetry, Deployment](#phase-5-testing-telemetry-deployment)  
- [3. Enhancements Incorporated](#3-enhancements-incorporated)  
- [4. Next Steps](#4-next-steps)  

---

## 1. Architecture Principles

- **Centralized licensing:** Use *shared QuantBoost Licensing SDK* exclusively.  
- **Async-first:** Async/design for responsive UI even during large scans.  
- **Modern UI/UX:** Task pane + Ribbon, **non-blocking toasts** for notifications.  
- **Non-destructive initial scan:** no changes without explicit user approval.  
- Expose **filter, search, preview** tools before delete.  
- Intuitive error messages, minimal modal dialogs.  
- Support **export for audits** & opt-in telemetry.  
- Modular logic for future expansion (e.g., formulas, links, sheet cleanup).  

---

## 2. Development Phases

### Phase 1: Project Setup

- [ ] Create Excel VSTO add-in project **QuantBoost.CleanExcelAddin** (.NET 4.8+)
- [x] **Reference shared Licensing SDK** DLL (SDK & backend implemented)
- [ ] Add folders:  
  - `Features/NameScrubber/` (service, model)  
  - `Controllers/`  
  - `UI/NameScrubber/` (User controls)  
- [ ] WiX-based signed installer pipeline setup  
- [ ] Basic Ribbon XML with thin callbacks  

---

### Phase 2: Name Discovery, Analysis & Deletion

**2.1 Data models**

- `NameScope` (Workbook / Worksheet)  
- `NameProblemType` (Hidden, ErrorRef, ErrorName, ExternalLink, EmptyRef, Other)  
- `NameInfo`:  
  ```csharp
  public class NameInfo {
    public string Name {get;set;}  
    public string RefersTo {get;set;}  
    public bool IsVisible {get;set;}  
    public NameScope Scope {get;set;}  
    public string WorksheetName {get;set;}  
    public NameProblemType Problem {get;set;}  
    public string ProblemDetails {get;set;}  
  }
  ```

**2.2 Service: NameScrubberService**

- [ ] `Task<List<NameInfo>> FindProblematicNamesAsync(Excel.Workbook wb, IProgress<string>)`  
  - Async scan workbook names, then each worksheet's  
  - Detect:  
    - Hidden (`!Visible`)  
    - Errors on eval or RefersTo string (`#REF!`, `#NAME?`)  
    - External links (`[...]`)  
    - Empty refs  
  - Release COM promptly (`Marshal.ReleaseComObject`)  
- [ ] `Task<int> DeleteNamesAsync(Excel.Workbook wb, List<NameInfo> names)`  
  - Confirmed names to delete  
  - Async loop, error handling  
  - Delete by name/scope lookup  
  - Release all COM  

**2.3 Analysis Enhancements**

- [ ] _Evaluate_ name (preferred) with `try{name.Value}` or `app.Evaluate()` catch errors.  
- [ ] String fallback (`RefersTo` parse) if eval fails or unsupported.  
- [ ] Classify error reason clearly.  
- [ ] Export `List<NameInfo>` → CSV or clipboard.

---

### Phase 3: UI and Ribbon Integration

**3.1 Ribbon**  

- [ ] Ribbon button _Name Scrubber_ with callbacks:  
  - Enable only when a workbook is active  
  - `OnShowNameScrubber_Click` triggers controller  
  - **Upgrade prompts dynamically gated by license tier/state** (hide/disable)  
  - Upgrade + Manage Subscription buttons  

**3.2 Task Pane UI**

- [ ] UserControl with:  
  - Status info label (with warnings, license state info)  
  - Filter checkboxes (hidden, errors, externals)  
  - **Live filter + search textbox** over loaded results  
  - DataGridView or ListView for names with sortable columns (Name, Scope, Worksheet, Status)  
  - Export to CSV & Copy buttons  
  - Buttons: **Rescan, Delete Selected, Delete All (confirm!)**  
- [ ] Dynamic badge/banner messages for:  
  - Trial countdown  
  - Grace period warnings  
  - License expired  
  - Upgrade CTA  
- [ ] Show **last analyzed date/time** & note if stale.

**3.3 User Experience**

- [ ] Async scan with progress updates, non-blocking.  
- [ ] After scan, _nothing deleted automatically_.  
- [ ] Show how many items fall under each problem type/filter.  
- [ ] Disable delete if no license / expired.  
- [ ] Confirm bulk deletes with irreversible warning.  
- [ ] Clear info if protected sheets prevent detection/deletion.  
- [ ] Undo support explicitely *not supported* (recommend backup).  

---

### Phase 4: Licensing & UX Layer

- [x] **Backend SaaS API + Licensing SDK implemented**
- [ ] Instantiate `QuantBoostLicensingManager` with `"cleanexcel"` and API URL early (async).  
- [ ] On startup or Ribbon click, **call `ValidateUsingStoredKeyAsync()`** asynchronously.  
- [ ] **Subscribe to `LicenseStatusChanged` event:**  
  - Enable/disable Ribbon commands live  
  - Update pane banners and feature gating  
  - Show toasts for trial/grace/expiry changes  
  - Trigger upgrade/activate prompts where relevant  
- [ ] **User activates via SDK dialogs** or during feature entry  
- [ ] Enforce SDK-derived gating for:  
  - Scan limits (future)  
  - Bulk delete access tiering  
  - CSV export (optional gating)  
- [ ] Toast + pane banner if plugin is in **grace mode**, expired, or trial ending  
- [ ] Prompt license purchase before deletion if unsubscribed or expired  

---

### Phase 5: Testing, Telemetry, Deployment

- [x] Backend licensing tested
- [ ] **Unit test** detection/classification logic separately  
- [ ] Multi-license state testing: trial, expired, grace  
- [ ] Varying workbook complexities:  
  - Many sheet/workbook names  
  - Protected sheets/workbooks  
  - Cross-linking and errors  
- [ ] Confirm COM released (profiling)  
- [ ] **Telemetry (opt-in):**  
  - Count/without content of name issues found  
  - Delete counts  
  - License statuses encountered  
- [ ] MSI signed build: includes Licensing SDK  
- [ ] Version manifest + update notification (future)  
- [ ] Documentation/help panel + in-app upgrade deep links  

---

## 3. Enhancements Incorporated

- Fully **integrated Quant Boost SDK**, async validation & caching  
- Licensing SDK **handles activation retries, grace period fallback, error resilience**  
- UI **reactive to license state & subscription tier**  
- Realtime **feature gating & CTA prompts with status banners**  
- Non-blocking async design: plugin stays responsive  
- Export features optional, premium gating ready  
- Emphasis on **undo risk alerts** before deletion  
- User-friendly upgrades: subtle toasts, banners, links  
- Telemetry opt-in, anonymized  
- Planned for **future Apple + multi-platform** readiness  

---

## 4. Next Steps

- [x] Backend API, Supabase, Stripe done
- [ ] Scaffold CleanExcel add-in
- [ ] Integrate SDK calls
- [ ] Build UI, detection, cleaning logic
- [ ] QA & deployment

---

**End of Quant Boost CleanExcel Development Guide (Enhanced Version)**