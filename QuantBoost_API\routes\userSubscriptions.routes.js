const express = require('express');
const router = express.Router();
const { supabase } = require('../supabaseClient');
const { sendSuccess, sendError } = require('../utils/responseHelpers');
const { authenticateJWT } = require('../middleware/authMiddleware');

// Apply JWT authentication to all routes in this file
router.use(authenticateJWT);

// GET /me/subscriptions - Fetches all subscriptions for the authenticated user
router.get('/subscriptions', async (req, res) => {
    const userId = req.user.id;
    const userEmail = req.user.email;

    if (!userId) {
        return sendError(res, 401, 'User not authenticated.');
    }

    console.log(`🔍 [SUBSCRIPTIONS] Fetching subscriptions for user: ${userId} (${userEmail})`);

    try {
        // Extract the JWT token from the Authorization header
        const authHeader = req.headers.authorization;
        const token = authHeader ? authHeader.split(' ')[1] : null;
        
        console.log(`🔍 [SUBSCRIPTIONS] Using auth token: ${token ? 'PRESENT' : 'MISSING'}`);
        
        // Create a Supabase client with the user's auth token to properly apply RLS
        const { createClient } = require('@supabase/supabase-js');
        const SUPABASE_URL = process.env.SUPABASE_URL;
        const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;
        
        const userSupabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
            global: {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            }
        });

        const { data: subscriptions, error } = await userSupabase
            .from('subscriptions') // Assuming your table is named 'subscriptions'
            .select('*') // Select all columns for now, adjust as needed
            .eq('user_id', userId);

        console.log(`🔍 [SUBSCRIPTIONS] Query result - error:`, error);
        console.log(`🔍 [SUBSCRIPTIONS] Query result - subscriptions count:`, subscriptions?.length || 0);
        console.log(`🔍 [SUBSCRIPTIONS] Query result - subscriptions data:`, JSON.stringify(subscriptions, null, 2));

        if (error) {
            console.error('Error fetching subscriptions for user:', error);
            return sendError(res, 500, 'Failed to fetch subscriptions.');
        }

        if (!subscriptions || subscriptions.length === 0) {
            console.log(`🔍 [SUBSCRIPTIONS] No subscriptions found for user ${userId} (${userEmail})`);
            return sendSuccess(res, [], 'No subscriptions found for this user.');
        }

        console.log(`🔍 [SUBSCRIPTIONS] Returning ${subscriptions.length} subscriptions for user ${userId} (${userEmail})`);
        sendSuccess(res, subscriptions);
    } catch (err) {
        console.error('Unexpected error fetching subscriptions:', err);
        sendError(res, 500, 'An unexpected error occurred while fetching subscriptions.');
    }
});



module.exports = router;
