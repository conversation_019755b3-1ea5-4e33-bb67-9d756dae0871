# Apply User Type Protection to Additional Billing Routes

Here are the commands to apply user type protection to the remaining billing API routes:

## Billing Routes to Update

Update each of these routes by adding the middleware check at the beginning of each handler:

```typescript
import { requireBillingAccess } from '@/lib/userTypeMiddleware';

export async function POST(req: NextRequest) {
  const accessDenied = await requireBillingAccess(req);
  if (accessDenied) return accessDenied;
  
  // ... existing route logic
}
```

### Routes that need billing access protection:
- ✅ `/api/billing/receipts` - DONE
- `/api/billing/cancel-subscription`
- `/api/billing/undo-cancellation` 
- `/api/billing/update-payment-method`
- `/api/billing/create-portal-session`
- `/api/billing/create-setup-intent`
- `/api/billing/get-payment-method`
- `/api/billing/subscription-details`
- `/api/billing/upcoming-invoice`
- `/api/billing/set-default-payment-method`
- `/api/billing/invoices`

### Routes that need subscription access protection:
```typescript
import { requireSubscriptionAccess } from '@/lib/userTypeMiddleware';
```
- `/api/billing/cancel-subscription`
- `/api/billing/undo-cancellation`
- `/api/billing/update-payment-method`

### Routes that need team access protection:
```typescript
import { requireTeamAccess } from '@/lib/userTypeMiddleware';
```
- `/api/team/invite`
- `/api/team/remove`

## Example Implementation

For a complete billing route update:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { requireBillingAccess } from '@/lib/userTypeMiddleware';

export async function POST(req: NextRequest) {
  // Check user access before processing
  const accessDenied = await requireBillingAccess(req);
  if (accessDenied) return accessDenied;

  try {
    // ... existing route logic
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
```

This ensures team licensees get a proper 403 error with a helpful message when trying to access billing features they shouldn't have access to.