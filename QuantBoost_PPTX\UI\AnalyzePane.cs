// --- START OF FILE AnalyzePane.cs ---

using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using QuantBoost_Powerpoint_Addin.Analysis; // For AnalysisService, SlideAnalysisSummary, OverallAnalysisSummary
using QuantBoost_Powerpoint_Addin.Utilities; // For FormattingUtils
using QuantBoost_Shared.Utilities; // For ProgressState

namespace QuantBoost_Powerpoint_Addin.UI
{
    public class AnalyzePane : UserControl
    {
        // --- UI Controls ---
        private Panel _filterPanel;
        private Button _runAnalysisButton;
        private Label _totalSizeLabel; // New summary label

        private Panel _progressPanel;
        private Label _progressLabel;
        private ProgressBar _progressBar;

        private DataGridView _resultsGrid;
        private DataTable _resultsTable;

        private ToolTip _toolTip;

        // --- State & Data ---
        private CancellationTokenSource _analysisCancellationTokenSource;
        private OverallAnalysisSummary _currentOverallSummary;
        private List<AnalysisResultDisplayItem> _fullAnalysisResultsList;

        // --- Constants ---
        private const string RUN_ANALYSIS_TEXT = "▶ Run Analysis"; // Changed icon for better clarity
        private const string CANCEL_ANALYSIS_TEXT = "■ Cancel"; // Changed icon
        private const string CANCELLING_TEXT = "⏳ Cancelling...";

        private class AnalysisResultDisplayItem
        {
            public string SlideIdentifier { get; set; }
            public string AssetType { get; set; }
            public long SizeBytes { get; set; }
            public string AssetDetails { get; set; }
        }

        public AnalyzePane()
        {
            this.Dock = DockStyle.Fill;
            this.MinimumSize = new Size(380, 300);
            this.BackColor = SystemColors.Control;

            InitializeUserInterface();
            SetUIMode(UIMode.Idle);
        }

        private void InitializeUserInterface()
        {
            _toolTip = new ToolTip { AutoPopDelay = 10000, InitialDelay = 500, ReshowDelay = 500, ShowAlways = true };

            // --- Top Panel for Controls ---
            _filterPanel = new Panel { Dock = DockStyle.Top, Height = 45, Padding = new Padding(8), BackColor = SystemColors.ControlLight, BorderStyle = BorderStyle.FixedSingle};
            
            _runAnalysisButton = new Button
            {
                Location = new Point(8, 8), Size = new Size(160, 28),
                Font = new Font("Segoe UI", 9f, FontStyle.Bold), FlatStyle = FlatStyle.Flat, Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter, BackColor = Color.FromArgb(0, 120, 215), ForeColor = Color.White,
            };
            _runAnalysisButton.FlatAppearance.BorderSize = 0;
            _runAnalysisButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(0, 100, 190);
            _runAnalysisButton.FlatAppearance.MouseDownBackColor = Color.FromArgb(0, 90, 170);
            _runAnalysisButton.Click += OnRunAnalysisButtonClick;
            _toolTip.SetToolTip(_runAnalysisButton, "Start or cancel the presentation analysis.");

            _totalSizeLabel = new Label
            {
                Dock = DockStyle.Right, Width = 200, AutoSize = false,
                Font = new Font("Segoe UI", 9f, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = SystemColors.ControlDarkDark,
                Text = "Est. Total Size: N/A"
            };
            _toolTip.SetToolTip(_totalSizeLabel, "Ready for analysis.");

            _filterPanel.Controls.Add(_runAnalysisButton);
            _filterPanel.Controls.Add(_totalSizeLabel);


            // --- Progress Panel ---
            _progressPanel = new Panel { Dock = DockStyle.Top, Height = 45, Padding = new Padding(5), Visible = false, BackColor = SystemColors.Info, BorderStyle = BorderStyle.FixedSingle };
            _progressLabel = new Label { Dock = DockStyle.Top, Height = 20, TextAlign = ContentAlignment.MiddleLeft, Font = new Font("Segoe UI", 8.5f, FontStyle.Bold), AutoEllipsis = true, ForeColor = SystemColors.InfoText };
            _progressBar = new ProgressBar { Dock = DockStyle.Fill, Style = ProgressBarStyle.Continuous, Minimum = 0, Maximum = 100 };
            _progressPanel.Controls.AddRange(new Control[] { _progressBar, _progressLabel });

            // --- Results Grid Data Source ---
            _resultsTable = new DataTable("AnalysisResults");
            _resultsTable.Columns.Add("SlideIdentifier", typeof(string));
            _resultsTable.Columns.Add("AssetType", typeof(string));
            _resultsTable.Columns.Add("SizeBytes", typeof(long));
            _resultsTable.Columns.Add("AssetDetails", typeof(string));

            // --- Results Grid ---
            _resultsGrid = new DataGridView
            {
                Dock = DockStyle.Fill, DataSource = _resultsTable, ReadOnly = true, AllowUserToAddRows = false,
                AllowUserToDeleteRows = false, AllowUserToOrderColumns = true, AllowUserToResizeRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect, BackgroundColor = SystemColors.Window,
                BorderStyle = BorderStyle.None, AutoGenerateColumns = false,
                RowHeadersVisible = false, ColumnHeadersVisible = true,
                EnableHeadersVisualStyles = false, // Crucial for custom header styles
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single,
                ColumnHeadersHeight = 30,
            };
            DefineGridColumns();
            _resultsGrid.CellFormatting += OnResultsGridCellFormatting;
            _resultsGrid.SortCompare += OnResultsGridSortCompare;
            
            // --- Add Controls to Pane in correct order for docking ---
            this.Controls.Add(_resultsGrid); // Fill first
            this.Controls.Add(_progressPanel);
            this.Controls.Add(_filterPanel);
        }

        private void DefineGridColumns()
        {
            // Fix: Explicitly set header styles to ensure they are visible.
            _resultsGrid.ColumnHeadersDefaultCellStyle.BackColor = SystemColors.Control;
            _resultsGrid.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            _resultsGrid.ColumnHeadersDefaultCellStyle.ForeColor = SystemColors.ControlText;
            _resultsGrid.ColumnHeadersDefaultCellStyle.Padding = new Padding(4, 0, 4, 0);
            _resultsGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
            
            _resultsGrid.Columns.Clear();
            _resultsGrid.Columns.Add(new DataGridViewTextBoxColumn { Name = "ColSlideIdentifier", DataPropertyName = "SlideIdentifier", HeaderText = "Slide", Width = 120, Frozen = true });
            _resultsGrid.Columns.Add(new DataGridViewTextBoxColumn { Name = "ColAssetType", DataPropertyName = "AssetType", HeaderText = "Primary Asset", Width = 110 });
            _resultsGrid.Columns.Add(new DataGridViewTextBoxColumn { Name = "ColSizeBytes", DataPropertyName = "SizeBytes", HeaderText = "Size", Width = 80, DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleRight, Padding = new Padding(0,0,5,0) } });
            _resultsGrid.Columns.Add(new DataGridViewTextBoxColumn { Name = "ColAssetDetails", DataPropertyName = "AssetDetails", HeaderText = "Details", AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill });
        }

        private enum UIMode { Idle, Analyzing, DisplayingResults, Error }

        private void SetUIMode(UIMode mode, string message = null)
        {
            if (this.InvokeRequired) { this.Invoke(new Action(() => SetUIMode(mode, message))); return; }

            _filterPanel.Enabled = (mode == UIMode.Idle || mode == UIMode.DisplayingResults || mode == UIMode.Error);
            _progressPanel.Visible = (mode == UIMode.Analyzing);

            switch (mode)
            {
                case UIMode.Idle:
                    _runAnalysisButton.Text = RUN_ANALYSIS_TEXT;
                    _runAnalysisButton.Enabled = true;
                    _progressBar.Value = 0; _progressBar.Style = ProgressBarStyle.Continuous;
                    _progressLabel.Text = "Ready for analysis.";
                    if (_fullAnalysisResultsList == null || !_fullAnalysisResultsList.Any()) ClearAllResultsDataAndSummary();
                    break;
                case UIMode.Analyzing:
                    _runAnalysisButton.Text = CANCEL_ANALYSIS_TEXT; _runAnalysisButton.Enabled = true;
                    _progressBar.Style = ProgressBarStyle.Marquee; _progressLabel.Text = "Initializing analysis...";
                    break;
                case UIMode.DisplayingResults:
                    _runAnalysisButton.Text = RUN_ANALYSIS_TEXT; _runAnalysisButton.Enabled = true;
                    break;
                case UIMode.Error:
                    _runAnalysisButton.Text = RUN_ANALYSIS_TEXT; _runAnalysisButton.Enabled = true;
                    _totalSizeLabel.Text = "Error";
                    _toolTip.SetToolTip(_totalSizeLabel, $"Error: {message ?? "An unknown error occurred."}");
                    break;
            }
        }
        
        // --- Public Methods for Ribbon Interaction ---
        public void PerformAnalysis()
        {
            OnRunAnalysisButtonClick(_runAnalysisButton, EventArgs.Empty);
        }

        public void ClearCurrentResults()
        {
            if (this.InvokeRequired) { this.Invoke(new Action(ClearCurrentResults)); return; }
            SetUIMode(UIMode.Idle);
        }

        public void UpdateProgressFromExternal(ProgressState state) => UpdateProgressDisplay(state);
        
        // --- End Public Methods ---

        private async void OnRunAnalysisButtonClick(object sender, EventArgs e)
        {
            if (_runAnalysisButton.Text == CANCEL_ANALYSIS_TEXT || _runAnalysisButton.Text == CANCELLING_TEXT)
            {
                _runAnalysisButton.Text = CANCELLING_TEXT; _runAnalysisButton.Enabled = false;
                _analysisCancellationTokenSource?.Cancel();
                return;
            }

            string presentationPath = GetActivePresentationPath();
            if (string.IsNullOrEmpty(presentationPath)) { SetUIMode(UIMode.Idle); return; }

            SetUIMode(UIMode.Analyzing);
            ClearAllResultsDataAndSummary();

            _analysisCancellationTokenSource?.Dispose();
            _analysisCancellationTokenSource = new CancellationTokenSource();
            var token = _analysisCancellationTokenSource.Token;
            var progressReporter = new Progress<ProgressState>(UpdateProgressDisplay);

            try
            {
                var result = await AnalysisService.AnalyzePresentationWithProgressAsync(presentationPath, progressReporter, token);
                var slideSummaries = result.slideSummaries;
                var overallSummary = result.overallSummary;
                token.ThrowIfCancellationRequested();

                _currentOverallSummary = overallSummary;
                _fullAnalysisResultsList = ConvertSlideSummariesToDisplayItems(slideSummaries);
                PopulateAndSummarizeResults();
                SetUIMode(UIMode.DisplayingResults);
            }
            catch (OperationCanceledException)
            {
                SetUIMode(UIMode.Idle);
                _toolTip.SetToolTip(_totalSizeLabel, "Analysis cancelled by user.");
            }
            catch (FileNotFoundException fnfEx)
            {
                SetUIMode(UIMode.Error, $"File not found: {Path.GetFileName(fnfEx.FileName)}");
                MessageBox.Show(fnfEx.Message, "File Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                SetUIMode(UIMode.Error, ex.Message);
                System.Diagnostics.Debug.WriteLine($"Analysis Error: {ex}");
                MessageBox.Show($"An error occurred during analysis: {ex.Message}", "Analysis Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                if (_runAnalysisButton.Text == CANCELLING_TEXT) SetUIMode(UIMode.Idle);
                _analysisCancellationTokenSource?.Dispose(); _analysisCancellationTokenSource = null;
            }
        }

        private string GetActivePresentationPath()
        {
            try
            {
                var pptApp = Globals.ThisAddIn.Application;
                if (pptApp?.ActivePresentation == null)
                {
                    MessageBox.Show("No active presentation is open.", "No Presentation", MessageBoxButtons.OK, MessageBoxIcon.Information); return null;
                }
                var presentation = pptApp.ActivePresentation;
                if (string.IsNullOrEmpty(presentation.FullName) || !Path.IsPathRooted(presentation.FullName) ||
                    presentation.FullName.Equals(presentation.Name, StringComparison.OrdinalIgnoreCase) || !File.Exists(presentation.FullName))
                {
                    MessageBox.Show("The active presentation must be saved to a valid file location first.", "Presentation Not Saved", MessageBoxButtons.OK, MessageBoxIcon.Warning); return null;
                }
                return presentation.FullName;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting presentation path: {ex.Message}");
                MessageBox.Show($"Error accessing presentation details: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error); return null;
            }
        }

        private void UpdateProgressDisplay(ProgressState state)
        {
            if (this.InvokeRequired) { this.Invoke(new Action<ProgressState>(UpdateProgressDisplay), state); return; }
            if (state == null) return;

            _progressBar.Style = (state.PercentComplete > 0 && state.PercentComplete < 100 && !state.IsComplete) ? ProgressBarStyle.Continuous : ProgressBarStyle.Marquee;
            _progressBar.Value = Math.Max(0, Math.Min(100, state.PercentComplete));
            _progressLabel.Text = $"{state.PercentComplete}% - {(!string.IsNullOrEmpty(state.CurrentStep) ? state.CurrentStep : state.DetailMessage ?? "Processing...")}";
        }

        private List<AnalysisResultDisplayItem> ConvertSlideSummariesToDisplayItems(List<SlideAnalysisSummary> slideSummaries)
        {
            if (slideSummaries == null) return new List<AnalysisResultDisplayItem>();
            return slideSummaries.Select(ss =>
            {
                string assetTypeDisplay = "General";
                if (ss.ExcelLinkCount > 0) assetTypeDisplay = "Excel Link";
                else if (ss.EmbeddedObjectCount > 0 && (ss.Title.IndexOf("Excel", StringComparison.OrdinalIgnoreCase) >= 0 || ss.Title.IndexOf("Sheet", StringComparison.OrdinalIgnoreCase) >= 0)) assetTypeDisplay = "Excel Embed";
                else if (ss.EmbeddedObjectCount > 0) assetTypeDisplay = "Embedded Object";
                else if (ss.MediaCount > 0) assetTypeDisplay = "Media";
                else if (ss.ChartCount > 0) assetTypeDisplay = "Chart";
                else if (ss.ImageCount > 0) assetTypeDisplay = "Image(s)";
                return new AnalysisResultDisplayItem
                {
                    SlideIdentifier = ss.Title ?? $"Slide {ss.SlideIndex}", AssetType = assetTypeDisplay, SizeBytes = ss.TotalAssetSizeBytes,
                    AssetDetails = $"Images: {ss.ImageCount}, Charts: {ss.ChartCount}, Media: {ss.MediaCount}, Embedded: {ss.EmbeddedObjectCount}, Excel Links: {ss.ExcelLinkCount}"
                };
            }).ToList();
        }

        private void PopulateAndSummarizeResults()
        {
            if (this.InvokeRequired) { this.Invoke(new Action(PopulateAndSummarizeResults)); return; }
            _resultsTable.Rows.Clear();
            if (_fullAnalysisResultsList != null)
            {
                foreach (var item in _fullAnalysisResultsList.OrderByDescending(i => i.SizeBytes))
                {
                    _resultsTable.Rows.Add(item.SlideIdentifier, item.AssetType, item.SizeBytes, item.AssetDetails);
                }
            }
            UpdateSummaryDisplay();
        }

        private void ClearAllResultsDataAndSummary()
        {
            if (this.InvokeRequired) { this.Invoke(new Action(ClearAllResultsDataAndSummary)); return; }
            _resultsTable.Rows.Clear();
            _fullAnalysisResultsList?.Clear();
            _currentOverallSummary = null;
            UpdateSummaryDisplay();
        }

        private void UpdateSummaryDisplay()
        {
            if (this.InvokeRequired) { this.Invoke(new Action(UpdateSummaryDisplay)); return; }
            
            var sb = new StringBuilder();

            if (_currentOverallSummary != null)
            {
                _totalSizeLabel.Text = $"Est. Total Size: {FormattingUtils.FormatSize(_currentOverallSummary.TotalAssetSizeBytes)}";
                sb.AppendLine($"Total Analyzed Size: {FormattingUtils.FormatSize(_currentOverallSummary.TotalAssetSizeBytes)} across {_currentOverallSummary.SlideCount} slides.");
                if (!string.IsNullOrEmpty(_currentOverallSummary.LargestAssetType) && _currentOverallSummary.LargestAssetSizeBytes > 0)
                {
                    sb.AppendLine();
                    sb.AppendLine($"Largest Contributor:");
                    sb.AppendLine($"  - Type: {_currentOverallSummary.LargestAssetType}");
                    sb.AppendLine($"  - Size: {FormattingUtils.FormatSize(_currentOverallSummary.LargestAssetSizeBytes)}");
                    sb.AppendLine($"  - Details: {_currentOverallSummary.LargestAssetDescription}");
                }
            }
            else 
            {
                _totalSizeLabel.Text = "Est. Total Size: N/A";
                sb.Append("Ready for analysis. Click 'Run Analysis' to begin.");
            }
            
            _toolTip.SetToolTip(_totalSizeLabel, sb.ToString().Trim());
        }

        private void OnResultsGridCellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex < 0 || e.RowIndex >= _resultsGrid.Rows.Count || e.ColumnIndex < 0) return;
            if (_resultsGrid.Columns[e.ColumnIndex].DataPropertyName == "SizeBytes" && e.Value is long size)
            {
                e.Value = FormattingUtils.FormatSize(size); e.FormattingApplied = true;
                if (size > 5 * 1024 * 1024) e.CellStyle.BackColor = Color.FromArgb(255, 205, 205); // Lighter red
                else if (size > 1 * 1024 * 1024) e.CellStyle.BackColor = Color.FromArgb(255, 230, 205); // Lighter orange
                else if (size > 500 * 1024) e.CellStyle.BackColor = Color.FromArgb(255, 255, 210); // Lighter yellow
            }
        }

        private void OnResultsGridSortCompare(object sender, DataGridViewSortCompareEventArgs e)
        {
            if (e.Column.DataPropertyName == "SizeBytes")
            {
                try
                {
                    DataRowView rowView1 = (DataRowView)_resultsGrid.Rows[e.RowIndex1].DataBoundItem;
                    DataRowView rowView2 = (DataRowView)_resultsGrid.Rows[e.RowIndex2].DataBoundItem;
                    long val1 = Convert.ToInt64(rowView1["SizeBytes"]); long val2 = Convert.ToInt64(rowView2["SizeBytes"]);
                    e.SortResult = val1.CompareTo(val2);
                }
                catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"SortCompare Error: {ex.Message}"); e.SortResult = String.Compare(e.CellValue1?.ToString(), e.CellValue2?.ToString()); }
                e.Handled = true;
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _analysisCancellationTokenSource?.Cancel(); _analysisCancellationTokenSource?.Dispose();
                _toolTip?.Dispose();
                if (_runAnalysisButton != null) _runAnalysisButton.Click -= OnRunAnalysisButtonClick;
                if (_resultsGrid != null) { _resultsGrid.CellFormatting -= OnResultsGridCellFormatting; _resultsGrid.SortCompare -= OnResultsGridSortCompare; _resultsGrid.DataSource = null; _resultsGrid.Dispose(); }
                _resultsTable?.Dispose();
            }
            _analysisCancellationTokenSource = null; _toolTip = null; _resultsGrid = null; _resultsTable = null;
            base.Dispose(disposing);
        }
    }
}