const express = require('express');
const router = express.Router();
const { supabase } = require('../supabaseClient');
const { sendSuccess, sendError } = require('../utils/responseHelpers');
const { authenticateApiKey } = require('../middleware/authMiddleware');
const { logLicenseEvent } = require('../utils/eventLogger');
// Add other necessary requires (e.g., specific logic helpers for admin operations)

// Apply API key authentication to all routes in this file
router.use(authenticateApiKey);

// POST /v1/admin/licenses/ - Create or activate a license by key
router.post('/', async (req, res) => {
    const { license_key, product_id } = req.body;
    const actorEmail = "admin_via_api_key"; // For logging

    await logLicenseEvent(supabase, 'key_activation_attempt', null, 'admin_create_license_route', actorEmail, 'attempt_activate_key');

    if (!license_key || !product_id) {
        await logLicenseEvent(supabase, 'key_activation_failure', null, 'admin_create_license_route', actorEmail, 'failure_missing_params_activate');
        return sendError(res, 'License key and product ID are required', 400);
    }

    try {
        const { data: existingLicense, error: fetchError } = await supabase
            .from('licenses')
            .select('*')
            .eq('license_key', license_key)
            .maybeSingle();

        if (fetchError) {
            console.error(`Error fetching license ${license_key} for activation:`, fetchError.message);
            await logLicenseEvent(supabase, 'key_activation_error', null, 'admin_create_license_route', actorEmail, 'failure_db_error_fetch_activate');
            return sendError(res, 'Database error fetching license.', 500, { details: fetchError.message });
        }

        if (existingLicense) {
            if (existingLicense.status === 'active') {
                await logLicenseEvent(supabase, 'key_activation_failure', existingLicense.id, 'admin_create_license_route', actorEmail, 'failure_already_active');
                return sendError(res, 'License key already exists and is active', 409);
            } else { // License exists but is not 'active'
                if (existingLicense.product_id === product_id) {
                    const { data: updatedLicense, error: updateError } = await supabase
                        .from('licenses')
                        .update({ status: 'active', updated_at: new Date().toISOString() })
                        .eq('id', existingLicense.id)
                        .select()
                        .single();

                    if (updateError) {
                        console.error(`Error reactivating license ${license_key}:`, updateError.message);
                        await logLicenseEvent(supabase, 'key_activation_error', existingLicense.id, 'admin_create_license_route', actorEmail, 'failure_db_error_reactivate');
                        return sendError(res, 'Database error reactivating license.', 500, { details: updateError.message });
                    }
                    await logLicenseEvent(supabase, 'key_activation_success', existingLicense.id, 'admin_create_license_route', actorEmail, 'success_reactivated');
                    return sendSuccess(res, { message: 'License reactivated successfully', license_key: updatedLicense.license_key, product_id: updatedLicense.product_id, status: updatedLicense.status }, 200);
                } else { // product_id mismatch
                    await logLicenseEvent(supabase, 'key_activation_failure', existingLicense.id, 'admin_create_license_route', actorEmail, 'failure_inactive_product_mismatch_activate');
                    return sendError(res, 'License key exists with a different product ID. Cannot reactivate with a new product ID.', 409);
                }
            }
        } else { // License key does not exist, create new one
            const newLicenseData = {
                license_key,
                product_id,
                status: 'active',
                license_tier: 'standard', // Default value
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            const { data: createdLicense, error: insertError } = await supabase
                .from('licenses')
                .insert(newLicenseData)
                .select()
                .single();

            if (insertError) {
                console.error(`Error creating new license ${license_key}:`, insertError.message);
                if (insertError.message.includes('duplicate key value violates unique constraint')) {
                     await logLicenseEvent(supabase, 'key_activation_failure', null, 'admin_create_license_route', actorEmail, 'failure_duplicate_key_on_create_activate');
                    return sendError(res, 'License key already exists.', 409);
                }
                await logLicenseEvent(supabase, 'key_activation_error', null, 'admin_create_license_route', actorEmail, 'failure_db_error_create_activate');
                return sendError(res, 'Database error creating license.', 500, { details: insertError.message });
            }
            await logLicenseEvent(supabase, 'key_activation_success', createdLicense.id, 'admin_create_license_route', actorEmail, 'success_newly_activated');
            return sendSuccess(res, { message: 'License activated successfully', license_key: createdLicense.license_key, product_id: createdLicense.product_id, status: createdLicense.status }, 201);
        }
    } catch (error) {
        console.error('Unexpected error in /v1/admin/licenses (create/activate):', error.message, error.stack);
        await logLicenseEvent(supabase, 'key_activation_error', null, 'admin_create_license_route', actorEmail, 'failure_unexpected_error_activate');
        return sendError(res, 'An unexpected error occurred during license activation.', 500, { details: error.message });
    }
});

// PUT /v1/admin/licenses/:identifier - Update a license
router.put('/:identifier', async (req, res) => {
    const { identifier } = req.params;
    const { status, expiry_date, license_tier, max_activations, notes, product_id, user_id, email } = req.body;
    const actorEmail = "admin_via_api_key"; // For logging

    const updates = {};
    if (status) updates.status = status;
    if (expiry_date) updates.expiry_date = expiry_date;
    if (license_tier) updates.license_tier = license_tier;
    if (max_activations !== undefined) updates.max_activations = max_activations;
    if (notes !== undefined) updates.notes = notes; // Allow explicitly setting notes to null or empty string
    if (product_id) updates.product_id = product_id;
    if (user_id) updates.user_id = user_id;
    if (email) updates.email = email;

    if (Object.keys(updates).length === 0) {
        return sendError(res, 'No update fields provided', 400);
    }
    updates.updated_at = new Date().toISOString();

    await logLicenseEvent(supabase, 'admin_license_update_attempt', null, 'admin_update_license_route', actorEmail, 'attempt_update_license');

    try {
        let licenseToUpdate;
        // Try to find by ID (UUID) first
        if (identifier.length === 36) { // Basic check for UUID format
            const { data, error } = await supabase.from('licenses').select('id, license_key').eq('id', identifier).maybeSingle();
            if (error) throw error;
            licenseToUpdate = data;
        }

        // If not found by ID, try by license_key
        if (!licenseToUpdate) {
            const { data, error } = await supabase.from('licenses').select('id, license_key').eq('license_key', identifier).maybeSingle();
            if (error) throw error;
            licenseToUpdate = data;
        }

        if (!licenseToUpdate) {
            await logLicenseEvent(supabase, 'admin_license_update_failure', null, 'admin_update_license_route', actorEmail, 'failure_license_not_found');
            return sendError(res, 'License not found', 404);
        }

        const { data: updatedLicense, error: updateError } = await supabase
            .from('licenses')
            .update(updates)
            .eq('id', licenseToUpdate.id)
            .select()
            .single();

        if (updateError) {
            console.error(`Error updating license ${licenseToUpdate.id} (key: ${licenseToUpdate.license_key}):`, updateError.message);
            await logLicenseEvent(supabase, 'admin_license_update_error', licenseToUpdate.id, 'admin_update_license_route', actorEmail, 'failure_db_error_update');
            return sendError(res, 'Database error updating license', 500, { details: updateError.message });
        }

        await logLicenseEvent(supabase, 'admin_license_update_success', updatedLicense.id, 'admin_update_license_route', actorEmail, 'success_license_updated');
        return sendSuccess(res, { message: 'License updated successfully', license: updatedLicense }, 200);

    } catch (error) {
        console.error(`Unexpected error updating license (identifier: ${identifier}):`, error.message, error.stack);
        await logLicenseEvent(supabase, 'admin_license_update_error', null, 'admin_update_license_route', actorEmail, 'failure_unexpected_error_update');
        return sendError(res, 'An unexpected error occurred during license update', 500, { details: error.message });
    }
});

// GET /v1/admin/licenses - List/search all licenses
router.get('/', async (req, res) => {
    const { page = 1, limit = 10, status, product_id, license_key, email, user_id, subscription_id, sortBy = 'created_at', order = 'desc' } = req.query;
    const actorEmail = "admin_via_api_key"; // For logging

    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const offset = (pageNum - 1) * limitNum;

    let query = supabase.from('licenses').select('*_count_exact', { count: 'exact' });

    // Apply filters
    if (status) query = query.eq('status', status);
    if (product_id) query = query.eq('product_id', product_id);
    if (license_key) query = query.ilike('license_key', `%${license_key}%`);
    if (email) query = query.ilike('email', `%${email}%`);
    if (user_id) query = query.eq('user_id', user_id);
    if (subscription_id) query = query.eq('subscription_id', subscription_id);

    // Apply sorting
    const validSortByFields = ['created_at', 'updated_at', 'expiry_date', 'status', 'license_tier', 'product_id', 'email', 'license_key'];
    if (validSortByFields.includes(sortBy)) {
        query = query.order(sortBy, { ascending: order === 'asc' });
    } else {
        query = query.order('created_at', { ascending: false }); // Default sort
    }

    // Apply pagination
    query = query.range(offset, offset + limitNum - 1);

    await logLicenseEvent(supabase, 'admin_list_licenses_attempt', null, 'admin_list_licenses_route', actorEmail, 'attempt_list_licenses');

    try {
        const { data: licenses, error, count } = await query;

        if (error) {
            console.error('Error listing licenses:', error.message);
            await logLicenseEvent(supabase, 'admin_list_licenses_error', null, 'admin_list_licenses_route', actorEmail, 'failure_db_error_list');
            return sendError(res, 'Database error listing licenses', 500, { details: error.message });
        }

        await logLicenseEvent(supabase, 'admin_list_licenses_success', null, 'admin_list_licenses_route', actorEmail, 'success_list_licenses');
        return sendSuccess(res, {
            licenses,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total_count: count,
                total_pages: Math.ceil(count / limitNum)
            }
        }, 200);

    } catch (error) {
        console.error('Unexpected error listing licenses:', error.message, error.stack);
        await logLicenseEvent(supabase, 'admin_list_licenses_error', null, 'admin_list_licenses_route', actorEmail, 'failure_unexpected_error_list');
        return sendError(res, 'An unexpected error occurred while listing licenses', 500, { details: error.message });
    }
});

// GET /v1/admin/licenses/:identifier - Get a specific license
router.get('/:identifier', async (req, res) => {
    const { identifier } = req.params;
    const actorEmail = "admin_via_api_key"; // For logging

    await logLicenseEvent(supabase, 'admin_get_license_attempt', null, 'admin_get_license_route', actorEmail, 'attempt_get_license');

    try {
        let query = supabase.from('licenses').select('*');

        // Check if identifier is a UUID (for id) or a string (for license_key)
        if (identifier.length === 36 && identifier.includes('-')) { // Basic UUID check
            query = query.eq('id', identifier);
        } else {
            query = query.eq('license_key', identifier);
        }

        const { data: license, error } = await query.maybeSingle();

        if (error) {
            console.error(`Error fetching license ${identifier}:`, error.message);
            await logLicenseEvent(supabase, 'admin_get_license_error', null, 'admin_get_license_route', actorEmail, 'failure_db_error_get');
            return sendError(res, 'Database error fetching license', 500, { details: error.message });
        }

        if (!license) {
            await logLicenseEvent(supabase, 'admin_get_license_failure', null, 'admin_get_license_route', actorEmail, 'failure_license_not_found_get');
            return sendError(res, 'License not found', 404);
        }

        await logLicenseEvent(supabase, 'admin_get_license_success', license.id, 'admin_get_license_route', actorEmail, 'success_get_license');
        return sendSuccess(res, { license }, 200);

    } catch (error) {
        console.error(`Unexpected error fetching license ${identifier}:`, error.message, error.stack);
        await logLicenseEvent(supabase, 'admin_get_license_error', null, 'admin_get_license_route', actorEmail, 'failure_unexpected_error_get');
        return sendError(res, 'An unexpected error occurred while fetching the license', 500, { details: error.message });
    }
});

// POST /v1/admin/licenses/assign - Assign a license to a user
router.post('/assign', async (req, res) => {
    const { license_identifier, user_identifier } = req.body; // license_identifier can be id or key, user_identifier can be id or email
    const actorEmail = "admin_via_api_key";

    await logLicenseEvent(supabase, 'admin_license_assign_attempt', null, 'admin_assign_license_route', actorEmail, 'attempt_assign_license');

    if (!license_identifier || !user_identifier) {
        await logLicenseEvent(supabase, 'admin_license_assign_failure', null, 'admin_assign_license_route', actorEmail, 'failure_missing_params_assign');
        return sendError(res, 'License identifier and user identifier are required', 400);
    }

    try {
        // 1. Fetch the license
        let licenseQuery = supabase.from('licenses').select('*');
        if (license_identifier.length === 36 && license_identifier.includes('-')) {
            licenseQuery = licenseQuery.eq('id', license_identifier);
        } else {
            licenseQuery = licenseQuery.eq('license_key', license_identifier);
        }
        const { data: license, error: licenseError } = await licenseQuery.maybeSingle();

        if (licenseError) {
            console.error(`Error fetching license for assignment (${license_identifier}):`, licenseError.message);
            await logLicenseEvent(supabase, 'admin_license_assign_error', null, 'admin_assign_license_route', actorEmail, 'failure_db_error_fetch_license_assign');
            return sendError(res, 'Database error fetching license', 500, { details: licenseError.message });
        }
        if (!license) {
            await logLicenseEvent(supabase, 'admin_license_assign_failure', null, 'admin_assign_license_route', actorEmail, 'failure_license_not_found_assign');
            return sendError(res, 'License not found', 404);
        }

        // 2. Fetch the user profile
        let userProfileQuery = supabase.from('profiles').select('id, email');
        if (user_identifier.includes('@')) { // Assume email
            userProfileQuery = userProfileQuery.eq('email', user_identifier);
        } else { // Assume user_id (UUID)
            userProfileQuery = userProfileQuery.eq('id', user_identifier);
        }
        const { data: userProfile, error: userError } = await userProfileQuery.maybeSingle();

        if (userError) {
            console.error(`Error fetching user profile for assignment (${user_identifier}):`, userError.message);
            await logLicenseEvent(supabase, 'admin_license_assign_error', license.id, 'admin_assign_license_route', actorEmail, 'failure_db_error_fetch_user_assign');
            return sendError(res, 'Database error fetching user profile', 500, { details: userError.message });
        }
        if (!userProfile) {
            await logLicenseEvent(supabase, 'admin_license_assign_failure', license.id, 'admin_assign_license_route', actorEmail, 'failure_user_not_found_assign');
            return sendError(res, 'User profile not found', 404);
        }

        // 3. Check for conflicts (e.g., license already assigned to someone else)
        if (license.user_id && license.user_id !== userProfile.id) {
            await logLicenseEvent(supabase, 'admin_license_assign_failure', license.id, 'admin_assign_license_route', actorEmail, 'failure_license_already_assigned_other_user');
            return sendError(res, `License is already assigned to a different user (ID: ${license.user_id}). Unassign first.`, 409);
        }
        if (license.user_id === userProfile.id && license.status === 'active'){
            await logLicenseEvent(supabase, 'admin_license_assign_failure', license.id, 'admin_assign_license_route', actorEmail, 'failure_license_already_assigned_same_user_active');
            return sendError(res, 'License is already assigned to this user and active.', 409);
        }

        // 4. Prepare license updates
        const updates = {
            user_id: userProfile.id,
            email: userProfile.email,
            status: 'active', // Assignment implies activation
            assigned_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        // Handle expiry_date based on subscription_id
        if (license.subscription_id) {
            const { data: subscription, error: subError } = await supabase
                .from('subscriptions')
                .select('current_period_end, status')
                .eq('id', license.subscription_id)
                .single();

            if (subError || !subscription) {
                console.error(`Error fetching subscription ${license.subscription_id} for license ${license.id}:`, subError ? subError.message : 'Not found');
                await logLicenseEvent(supabase, 'admin_license_assign_warning', license.id, 'admin_assign_license_route', actorEmail, 'warning_subscription_fetch_failed_assign');
                // Decide if this is a hard error or just a warning. For now, proceed without setting expiry from sub.
            } else {
                if (subscription.current_period_end) {
                    updates.expiry_date = subscription.current_period_end;
                } else {
                    await logLicenseEvent(supabase, 'admin_license_assign_warning', license.id, 'admin_assign_license_route', actorEmail, 'warning_subscription_no_expiry_assign');
                }
                if (subscription.status !== 'active' && subscription.status !== 'trialing') {
                    await logLicenseEvent(supabase, 'admin_license_assign_warning', license.id, 'admin_assign_license_route', actorEmail, 'warning_subscription_not_active_assign');
                }
            }
        } else if (!license.expiry_date || new Date(license.expiry_date) < new Date()) {
            // If standalone and no valid expiry, set a default (e.g., 1 year from now)
            const defaultExpiry = new Date();
            defaultExpiry.setFullYear(defaultExpiry.getFullYear() + 1);
            updates.expiry_date = defaultExpiry.toISOString();
            await logLicenseEvent(supabase, 'admin_license_assign_info', license.id, 'admin_assign_license_route', actorEmail, 'info_default_expiry_set_assign');
        }
        
        // Ensure active license has a future expiry date if not perpetual (perpetual not explicitly handled here yet)
        if (updates.status === 'active' && (!updates.expiry_date || new Date(updates.expiry_date) < new Date())) {
            // This case should ideally be caught by the logic above, but as a safeguard:
            if (!license.subscription_id) { // Only if not tied to a sub that might be misconfigured
                 await logLicenseEvent(supabase, 'admin_license_assign_failure', license.id, 'admin_assign_license_route', actorEmail, 'failure_activating_with_past_expiry_assign');
                 return sendError(res, 'Cannot activate a license with a past or missing expiry date. Please update license expiry first.', 400);
            }
        }

        // 5. Update the license
        const { data: updatedLicense, error: updateError } = await supabase
            .from('licenses')
            .update(updates)
            .eq('id', license.id)
            .select()
            .single();

        if (updateError) {
            console.error(`Error assigning license ${license.id} to user ${userProfile.id}:`, updateError.message);
            await logLicenseEvent(supabase, 'admin_license_assign_error', license.id, 'admin_assign_license_route', actorEmail, 'failure_db_error_update_license_assign');
            return sendError(res, 'Database error assigning license', 500, { details: updateError.message });
        }

        await logLicenseEvent(supabase, 'admin_license_assign_success', updatedLicense.id, 'admin_assign_license_route', actorEmail, 'success_license_assigned');
        return sendSuccess(res, { message: 'License assigned successfully', license: updatedLicense }, 200);

    } catch (error) {
        console.error(`Unexpected error assigning license (${license_identifier} to ${user_identifier}):`, error.message, error.stack);
        await logLicenseEvent(supabase, 'admin_license_assign_error', null, 'admin_assign_license_route', actorEmail, 'failure_unexpected_error_assign');
        return sendError(res, 'An unexpected error occurred during license assignment', 500, { details: error.message });
    }
});

// POST /v1/admin/licenses/invite-user - Invite a user to a license (pre-assign by email)
router.post('/invite-user', async (req, res) => {
    const { license_identifier, email: invite_email } = req.body;
    const actorEmail = "admin_via_api_key";

    await logLicenseEvent(supabase, 'admin_license_invite_attempt', null, 'admin_invite_user_route', actorEmail, 'attempt_invite_user');

    if (!license_identifier || !invite_email) {
        await logLicenseEvent(supabase, 'admin_license_invite_failure', null, 'admin_invite_user_route', actorEmail, 'failure_missing_params_invite');
        return sendError(res, 'License identifier and email are required', 400);
    }

    // Basic email validation (can be enhanced with a library if needed)
    if (!/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/.test(invite_email)) {
        await logLicenseEvent(supabase, 'admin_license_invite_failure', null, 'admin_invite_user_route', actorEmail, 'failure_invalid_email_format_invite');
        return sendError(res, 'Invalid email format provided', 400);
    }

    try {
        // 1. Fetch the license
        let licenseQuery = supabase.from('licenses').select('*');
        if (license_identifier.length === 36 && license_identifier.includes('-')) {
            licenseQuery = licenseQuery.eq('id', license_identifier);
        } else {
            licenseQuery = licenseQuery.eq('license_key', license_identifier);
        }
        const { data: license, error: licenseError } = await licenseQuery.maybeSingle();

        if (licenseError) {
            console.error(`Error fetching license for invite (${license_identifier}):`, licenseError.message);
            await logLicenseEvent(supabase, 'admin_license_invite_error', null, 'admin_invite_user_route', actorEmail, 'failure_db_error_fetch_license_invite');
            return sendError(res, 'Database error fetching license', 500, { details: licenseError.message });
        }
        if (!license) {
            await logLicenseEvent(supabase, 'admin_license_invite_failure', null, 'admin_invite_user_route', actorEmail, 'failure_license_not_found_invite');
            return sendError(res, 'License not found', 404);
        }

        // 2. Check if license is already fully assigned to an existing user ID
        if (license.user_id) {
            // If it's assigned to the same email, it's a bit redundant but not an error, could inform admin.
            // If it's assigned to a different user_id, then it's a conflict.
            if (license.email !== invite_email) {
                 await logLicenseEvent(supabase, 'admin_license_invite_failure', license.id, 'admin_invite_user_route', actorEmail, 'failure_license_already_assigned_to_user_id_invite');
                 return sendError(res, `License is already assigned to user ID ${license.user_id} (email: ${license.email || 'N/A'}). Unassign first to invite a different email.`, 409);
            }
        } else if (license.email && license.email !== invite_email) {
            // License is invited to someone else but not yet claimed (no user_id)
            await logLicenseEvent(supabase, 'admin_license_invite_failure', license.id, 'admin_invite_user_route', actorEmail, 'failure_license_already_invited_to_other_email');
            return sendError(res, `License is already invited to ${license.email}. Unassign first to invite a different email.`, 409);
        }

        // 3. Prepare license updates for invitation
        const updates = {
            email: invite_email,
            user_id: null, // Explicitly set user_id to null as it's an invitation
            status: 'assigned', // 'assigned' indicates it's earmarked for someone
            assigned_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        
        // Handle expiry_date based on subscription_id (similar to /assign)
        if (license.subscription_id) {
            const { data: subscription, error: subError } = await supabase
                .from('subscriptions')
                .select('current_period_end, status')
                .eq('id', license.subscription_id)
                .single();

            if (subError || !subscription) {
                await logLicenseEvent(supabase, 'admin_license_invite_warning', license.id, 'admin_invite_user_route', actorEmail, 'warning_subscription_fetch_failed_invite');
            } else {
                if (subscription.current_period_end) {
                    updates.expiry_date = subscription.current_period_end;
                } else {
                     await logLicenseEvent(supabase, 'admin_license_invite_warning', license.id, 'admin_invite_user_route', actorEmail, 'warning_subscription_no_expiry_invite');
                }
                 if (subscription.status !== 'active' && subscription.status !== 'trialing') {
                    await logLicenseEvent(supabase, 'admin_license_invite_warning', license.id, 'admin_invite_user_route', actorEmail, 'warning_subscription_not_active_invite');
                }
            }
        } else if ((!license.expiry_date || new Date(license.expiry_date) < new Date()) && updates.status === 'assigned') {
            // If standalone, status is 'assigned' (implying it will become active), and no valid expiry, set a default.
            const defaultExpiry = new Date();
            defaultExpiry.setFullYear(defaultExpiry.getFullYear() + 1);
            updates.expiry_date = defaultExpiry.toISOString();
            await logLicenseEvent(supabase, 'admin_license_invite_info', license.id, 'admin_invite_user_route', actorEmail, 'info_default_expiry_set_on_invite');
        }

        // 4. Update the license
        const { data: updatedLicense, error: updateError } = await supabase
            .from('licenses')
            .update(updates)
            .eq('id', license.id)
            .select()
            .single();

        if (updateError) {
            console.error(`Error inviting user ${invite_email} to license ${license.id}:`, updateError.message);
            await logLicenseEvent(supabase, 'admin_license_invite_error', license.id, 'admin_invite_user_route', actorEmail, 'failure_db_error_update_license_invite');
            return sendError(res, 'Database error inviting user to license', 500, { details: updateError.message });
        }

        // TODO: Consider sending an email notification to invite_email (out of scope for this immediate task)
        // For now, just log success.

        await logLicenseEvent(supabase, 'admin_license_invite_success', updatedLicense.id, 'admin_invite_user_route', actorEmail, 'success_user_invited_to_license');
        return sendSuccess(res, { message: `License successfully prepared for user ${invite_email}. They can claim it upon next login/validation.`, license: updatedLicense }, 200);

    } catch (error) {
        console.error(`Unexpected error inviting user to license (${license_identifier} for ${invite_email}):`, error.message, error.stack);
        await logLicenseEvent(supabase, 'admin_license_invite_error', null, 'admin_invite_user_route', actorEmail, 'failure_unexpected_error_invite');
        return sendError(res, 'An unexpected error occurred during user invitation', 500, { details: error.message });
    }
});

// POST /v1/admin/licenses/unassign - Unassign a license from a user
router.post('/unassign', async (req, res) => {
    const { license_identifier } = req.body; // license_identifier can be id or key
    const actorEmail = "admin_via_api_key";

    await logLicenseEvent(supabase, 'admin_license_unassign_attempt', null, 'admin_unassign_license_route', actorEmail, 'attempt_unassign_license');

    if (!license_identifier) {
        await logLicenseEvent(supabase, 'admin_license_unassign_failure', null, 'admin_unassign_license_route', actorEmail, 'failure_missing_params_unassign');
        return sendError(res, 'License identifier is required', 400);
    }

    try {
        // 1. Fetch the license
        let licenseQuery = supabase.from('licenses').select('id, user_id, email, status, license_key');
        if (license_identifier.length === 36 && license_identifier.includes('-')) { // Basic UUID check
            licenseQuery = licenseQuery.eq('id', license_identifier);
        } else {
            licenseQuery = licenseQuery.eq('license_key', license_identifier);
        }
        const { data: license, error: licenseError } = await licenseQuery.maybeSingle();

        if (licenseError) {
            console.error(`Error fetching license for unassignment (${license_identifier}):`, licenseError.message);
            await logLicenseEvent(supabase, 'admin_license_unassign_error', null, 'admin_unassign_license_route', actorEmail, 'failure_db_error_fetch_license_unassign');
            return sendError(res, 'Database error fetching license', 500, { details: licenseError.message });
        }
        if (!license) {
            await logLicenseEvent(supabase, 'admin_license_unassign_failure', null, 'admin_unassign_license_route', actorEmail, 'failure_license_not_found_unassign');
            return sendError(res, 'License not found', 404);
        }

        // 2. Check if the license is actually assigned
        if (!license.user_id && !license.email) {
            await logLicenseEvent(supabase, 'admin_license_unassign_failure', license.id, 'admin_unassign_license_route', actorEmail, 'failure_license_not_assigned_unassign');
            return sendError(res, 'License is not currently assigned to any user or email', 400);
        }

        const previous_user_id = license.user_id;
        const previous_email = license.email;

        // 3. Prepare updates to unassign
        const updates = {
            user_id: null,
            email: null,
            assigned_at: null,
            // Optionally, change status. For now, let's keep it as is or set to 'available' if it was 'active' due to assignment.
            // If you want to deactivate it upon unassignment:
            // status: 'available', // or 'inactive' or a specific status like 'unassigned'
            updated_at: new Date().toISOString()
        };
        
        // If the license was active primarily due to assignment, consider making it 'available' or another appropriate status.
        // This logic might depend on whether the license is subscription-based or perpetual, etc.
        // For simplicity, we are just clearing user_id and email. Status can be managed separately if needed.
        // if (license.status === 'active') {\r\n        //    updates.status = \'available\'; // Or a more specific status\r\n        // }\r\n
        // 4. Update the license
        const { data: updatedLicense, error: updateError } = await supabase
            .from('licenses')
            .update(updates)
            .eq('id', license.id)
            .select()
            .single();

        if (updateError) {
            console.error(`Error unassigning license ${license.id}:`, updateError.message);
            await logLicenseEvent(supabase, 'admin_license_unassign_error', license.id, 'admin_unassign_license_route', actorEmail, 'failure_db_error_update_license_unassign');
            return sendError(res, 'Database error unassigning license', 500, { details: updateError.message });
        }

        await logLicenseEvent(supabase, 'admin_license_unassign_success', updatedLicense.id, 'admin_unassign_license_route', actorEmail, 'success_license_unassigned');
        return sendSuccess(res, { message: 'License unassigned successfully', license: updatedLicense }, 200);

    } catch (error) {
        console.error(`Unexpected error unassigning license (${license_identifier}):`, error.message, error.stack);
        await logLicenseEvent(supabase, 'admin_license_unassign_error', null, 'admin_unassign_license_route', actorEmail, 'failure_unexpected_error_unassign');
        return sendError(res, 'An unexpected error occurred during license unassignment', 500, { details: error.message });
    }
});

// POST /v1/admin/licenses/:identifier/revoke - Revoke a license
router.post('/:identifier/revoke', async (req, res) => {
    const { identifier } = req.params; // identifier can be id or key
    const actorEmail = "admin_via_api_key";

    await logLicenseEvent(supabase, 'admin_license_revoke_attempt', null, 'admin_revoke_license_route', actorEmail, 'attempt_revoke_license');

    try {
        // 1. Fetch the license by ID or Key
        let licenseQuery = supabase.from('licenses').select('id, status, license_key');
        if (identifier.length === 36 && identifier.includes('-')) { // Basic UUID check for ID
            licenseQuery = licenseQuery.eq('id', identifier);
        } else { // Assume it's a license_key
            licenseQuery = licenseQuery.eq('license_key', identifier);
        }
        const { data: license, error: fetchError } = await licenseQuery.maybeSingle();

        if (fetchError) {
            console.error(`Error fetching license for revocation (${identifier}):`, fetchError.message);
            await logLicenseEvent(supabase, 'admin_license_revoke_error', null, 'admin_revoke_license_route', actorEmail, 'failure_db_error_fetch_revoke');
            return sendError(res, 'Database error fetching license', 500, { details: fetchError.message });
        }

        if (!license) {
            await logLicenseEvent(supabase, 'admin_license_revoke_failure', null, 'admin_revoke_license_route', actorEmail, 'failure_license_not_found_revoke');
            return sendError(res, 'License not found', 404);
        }

        if (license.status === 'revoked') {
            await logLicenseEvent(supabase, 'admin_license_revoke_failure', license.id, 'admin_revoke_license_route', actorEmail, 'failure_already_revoked');
            return sendError(res, 'License is already revoked', 409); // 409 Conflict or 400 Bad Request
        }

        // 2. Prepare updates to revoke the license
        const updates = {
            status: 'revoked',
            updated_at: new Date().toISOString(),
            // Consider if other fields should be cleared/updated upon revocation, e.g.,
            // user_id: null,
            // email: null,
            // assigned_at: null,
            // expiry_date: new Date().toISOString() // Or null, or keep as is for record
        };

        // 3. Update the license in the database
        const { data: revokedLicense, error: updateError } = await supabase
            .from('licenses')
            .update(updates)
            .eq('id', license.id)
            .select()
            .single();

        if (updateError) {
            console.error(`Error revoking license ${license.id} (key: ${license.license_key}):`, updateError.message);
            await logLicenseEvent(supabase, 'admin_license_revoke_error', license.id, 'admin_revoke_license_route', actorEmail, 'failure_db_error_update_revoke');
            return sendError(res, 'Database error revoking license', 500, { details: updateError.message });
        }

        // 4. Optionally, deactivate all active machine activations for this license
        const { error: deactivateError } = await supabase
            .from('license_activations')
            .update({ is_active: false, updated_at: new Date().toISOString() })
            .eq('license_id', license.id)
            .eq('is_active', true);

        if (deactivateError) {
            // Log this error but don't fail the whole revocation process because of it
            console.error(`Error deactivating machine activations for revoked license ${license.id}:`, deactivateError.message);
            await logLicenseEvent(supabase, 'admin_license_revoke_warning', license.id, 'admin_revoke_license_route', actorEmail, 'warning_deactivation_failed_revoke');
        }

        await logLicenseEvent(supabase, 'admin_license_revoke_success', revokedLicense.id, 'admin_revoke_license_route', actorEmail, 'success_license_revoked');
        return sendSuccess(res, { message: 'License revoked successfully', license: revokedLicense }, 200);

    } catch (error) {
        console.error(`Unexpected error revoking license (${identifier}):`, error.message, error.stack);
        await logLicenseEvent(supabase, 'admin_license_revoke_error', null, 'admin_revoke_license_route', actorEmail, 'failure_unexpected_error_revoke');
        return sendError(res, 'An unexpected error occurred during license revocation', 500, { details: error.message });
    }
});

module.exports = router;
