import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { requireBillingAccess } from '@/lib/userTypeMiddleware';

export async function POST(request: NextRequest) {
  // Check user access before processing
  const accessDenied = await requireBillingAccess(request);
  if (accessDenied) return accessDenied;

  try {
    const { subscriptionId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      );
    }

    const key = (globalThis as any).process?.env?.STRIPE_SECRET_KEY as string | undefined;
    if (!key) {
      return NextResponse.json({ error: 'Stripe secret key not configured' }, { status: 500 });
    }
  const stripe = new Stripe(key, { apiVersion: '2025-08-27.basil' as any });

    // Get the subscription to find the customer and default payment method
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    if (!subscription.customer || !subscription.default_payment_method) {
      return NextResponse.json(
        { error: 'No payment method found for this subscription' },
        { status: 404 }
      );
    }

    const customerId = subscription.customer as string;
    const pmId = subscription.default_payment_method as string;

    // Retrieve the payment method details
    const paymentMethod = await stripe.paymentMethods.retrieve(pmId);

    // Ensure the payment method is actually attached to this customer
    if (paymentMethod.customer && paymentMethod.customer !== customerId) {
      return NextResponse.json(
        { error: 'Payment method not attached to this customer' },
        { status: 403 }
      );
    }

    return NextResponse.json(paymentMethod);
  } catch (error) {
    console.error('Error retrieving payment method:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve payment method' },
      { status: 500 }
    );
  }
}
