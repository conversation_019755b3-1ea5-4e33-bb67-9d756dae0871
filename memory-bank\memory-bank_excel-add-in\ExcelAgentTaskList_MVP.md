---

### **Project Plan: Excel AI Agent - Local MVP**

**Core Objective:** Validate the end-to-end functionality of a tool-using agent within a VSTO Excel add-in, from user input in a WPF pane to direct cell manipulation, using a local, containerized backend.

**Tech Stack:**
*   **Frontend:** VSTO Add-in with a WPF Task Pane
*   **Backend:** Python service running locally in a Docker container
*   **Agent Framework:** LangChain (using Python)
*   **LLM Access:** OpenRouter API (for model flexibility and ease of use)
*   **Core Capability:** A "Level 3" Proactive Formula Helper Agent

---

### **Phase 0: Project Setup & Scaffolding (1-2 Days)**

*Goal: Get the basic project structure and all necessary components in place.*

**Epic 0.1: Local Environment & Project Initialization**
*   **Task 0.1.1:** Set up a new VSTO Add-in project in Visual Studio.
    *   Subtask: Create a new Git repository for the project.
*   **Task 0.1.2:** Scaffold the WPF Task Pane.
    *   Subtask: Add a new WPF User Control to the VSTO project.
    *   Subtask: Write the necessary code in `ThisAddIn.cs` to display the WPF control in a custom task pane on the right-hand side of Excel on startup.
    *   Subtask: Design a minimal UI: a text area for chat history, a text box for user input, and a "Send" button.
*   **Task 0.1.3:** Set up the Python Backend Project.
    *   Subtask: Create a new Python project folder.
    *   Subtask: Initialize a `requirements.txt` file with initial dependencies (`langchain`, `fastapi`, `uvicorn`, `python-dotenv`).
*   **Task 0.1.4:** Configure Local Secrets Management.
    *   Subtask: Create a `.env` file in the backend project to store the `OPENROUTER_API_KEY`.
    *   Subtask: Add `.env` to the `.gitignore` file.

---

### **Phase 1: Backend - The Agent's "Brain" & "Hands" (3-5 Days)**

*Goal: Build the core agent logic and the tools it will use to interact with Excel. The backend will be callable via a simple API.*

**Epic 1.1: Implement the Excel Tooling Layer**
*   **Task 1.1.1:** Design the API for Excel interaction. The VSTO add-in will host a local server (or use another IPC method) that the Python backend can call.
    *   Subtask: Define simple endpoints like `POST /get_cell_formula` and `POST /set_cell_formula`.
    *   *Note: For an MVP, a simple local HTTP server inside the VSTO add-in is a fast way to enable communication from the Python Docker container.*
*   **Task 1.1.2:** Implement the "Read" tools in VSTO/C#.
    *   Subtask: Implement the `get_selection_address()` function.
    *   Subtask: Implement the `get_range_values(range_address)` function.
    *   Subtask: Implement the `get_cell_formula(cell_address)` function.
*   **Task 1.1.3:** Implement the "Write" tools in VSTO/C#.
    *   Subtask: Implement the `set_cell_formula(cell_address, formula)` function.
    *   Subtask: Implement basic Undo/Redo support for `set_cell_formula`.

**Epic 1.2: Build the LangChain Agents**
*   **Task 1.2.1:** Create LangChain custom `Tool` wrappers.
    *   Subtask: Write Python functions that make HTTP requests to the local VSTO server's endpoints (e.g., a Python `get_cell_formula` function that calls `http://localhost:port/get_cell_formula`).
    *   Subtask: Wrap these Python functions into LangChain `Tool` objects.
*   **Task 1.2.2:** Implement the **`FormulaHelperSpecialist` Agent**.
    *   Subtask: Create a new LangChain agent (e.g., using the ReAct framework).
    *   Subtask: Provide it with all the Excel tools created in Task 1.2.1.
    *   Subtask: Write a specific system prompt for this agent, instructing it to be an expert in analyzing and writing Excel formulas based on user requests and worksheet context.
*   **Task 1.2.3:** Implement the **`PrimaryDelegator` Agent**.
    *   Subtask: Create another LangChain agent.
    *   Subtask: Its ONLY tool will be the `FormulaHelperSpecialist` agent itself (as a `Tool`).
    *   Subtask: Write its system prompt to be a general-purpose assistant whose job is to understand the user's request and delegate any formula-related tasks to its specialist tool.

**Epic 1.3: Expose the Agent via a Local API**
*   **Task 1.3.1:** Create a FastAPI application in the Python backend.
*   **Task 1.3.2:** Create a single API endpoint: `POST /chat`.
    *   Subtask: This endpoint will accept a user's prompt as input.
    *   Subtask: It will invoke the `PrimaryDelegator` agent with the prompt.
    *   Subtask: It will stream the agent's response (including intermediate "thinking" steps and the final answer) back to the caller.

---

### **Phase 2: Integration & Validation (2-3 Days)**

*Goal: Connect the frontend and backend, containerize the backend for easy local deployment, and perform end-to-end testing.*

**Epic 2.1: Containerize the Backend**
*   **Task 2.1.1:** Write a `Dockerfile` for the Python/FastAPI application.
*   **Task 2.1.2:** Write a `docker-compose.yml` file.
    *   Subtask: This will define the agent service.
    *   Subtask: Configure it to use the `.env` file for secrets.
    *   Subtask: Configure networking so the container can access the host machine's localhost (for calling the VSTO server). Use `host.docker.internal` for this.
*   **Task 2.1.3:** Build and run the Docker container to verify the `/chat` endpoint is accessible.

**Epic 2.2: Connect Frontend to Backend**
*   **Task 2.2.1:** Implement the `OnClick` event for the "Send" button in the WPF Task Pane.
*   **Task 2.2.2:** In the event handler, make an HTTP POST request to the local Docker container's `/chat` endpoint (`http://localhost:port/chat`).
*   **Task 2.2.3:** Implement logic to receive the streamed response from the agent and display it in the chat history text area.

**Epic 2.3: End-to-End MVP Testing**
*   **Task 2.3.1:** Launch Excel (which loads the VSTO add-in and its local server).
*   **Task 2.3.2:** Run `docker-compose up` to start the agent backend.
*   **Task 2.3.3:** Perform a "Level 3" test scenario:
    1.  Select a cell.
    2.  Type a complex request into the WPF pane: "In the 'Commission' column, write a formula to calculate 5% of the 'Revenue' column for this row."
    3.  **Validate the entire chain:**
        *   Does the `PrimaryDelegator` correctly delegate to the `FormulaHelperSpecialist`? (Check logs).
        *   Does the `FormulaHelperSpecialist` correctly call the `get_range_values` tool to read the headers? (Check logs).
        *   Does it correctly construct the formula string?
        *   Does it correctly call the `set_cell_formula` tool to write the formula into the correct cell in Excel?
        *   Is the final confirmation message displayed correctly in the WPF pane?
    4.  **Validate Undo:** Press Ctrl+Z in Excel and confirm the formula is removed.

---

### **MVP Completion Criteria:**

The MVP is considered complete when a user can successfully perform the end-to-end test scenario described in Task 2.3.3 on a local machine. This proves that the core agentic architecture and its communication with Excel are fundamentally sound, providing a solid foundation for building the full enterprise solution.