import { NextRequest } from 'next/server';
import { getSupabaseAdmin, getUserIdFromAuthHeader } from '@/lib/supabaseServer';
import { inviteAcceptSchema } from '@/lib/salesTrialsValidation';

export async function POST(req: NextRequest) {
  const body = await req.json();
  const parsed = inviteAcceptSchema.safeParse(body);
  if (!parsed.success) return Response.json({ error: parsed.error.flatten() }, { status: 400 });

  const supa = getSupabaseAdmin();
  const userId = getUserIdFromAuthHeader(req) || null;
  if (!userId) {
    return Response.json({ error: 'auth_required' }, { status: 401 });
  }
  const token = parsed.data.token;

  // Call RPC for atomic validation and increment
  try {
  const { data, error } = await supa.rpc('accept_trial_invite', { p_token: token, p_user_id: userId });
    if (error) {
      const msg = (error as any).message || String(error);
      const map: Record<string, number> = {
        invalid_token: 404,
        trial_not_active: 400,
        trial_expired: 400,
        trial_full: 409,
        invite_already_used: 409,
      };
      const key = Object.keys(map).find(k => msg.includes(k));
      return Response.json({ error: key || 'invite_error' }, { status: key ? map[key] : 400 });
    }
    return Response.json({ ok: true, result: data?.[0] || null });
  } catch (e: any) {
    return Response.json({ error: e.message || 'rpc_failed' }, { status: 500 });
  }
}
