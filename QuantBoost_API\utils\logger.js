const winston = require('winston');

// Custom format for development that preserves metadata
const devFormat = winston.format.printf(({ timestamp, level, message, service, ...metadata }) => {
  let msg = `${timestamp} [${service}] ${level}: ${message}`;
  if (Object.keys(metadata).length > 0) {
    msg += ` ${JSON.stringify(metadata)}`;
  }
  return msg;
});

// Create the logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
    winston.format.errors({ stack: true })
  ),
  defaultMeta: { 
    service: 'quantboost-api',
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0'
  },
  transports: [
    new winston.transports.Console({
      format: process.env.NODE_ENV === 'production'
        ? winston.format.json() // Structured JSON for cloud platforms to parse
        : winston.format.combine(
            winston.format.colorize(),
            devFormat
          )
    })
  ]
});

// Handle uncaught exceptions and rejections ONLY in production
// In development, let them crash the process for immediate visibility
if (process.env.NODE_ENV === 'production') {
  logger.exceptions.handle(
    new winston.transports.Console({
      format: winston.format.json()
    })
  );
  
  logger.rejections.handle(
    new winston.transports.Console({
      format: winston.format.json()
    })
  );
}

module.exports = logger;