/**
 * Utility helpers for handling session/email mismatch after checkout.
 * The core rule: if a Supabase session exists for a different email than
 * the one associated with the successful purchase, we must sign out and
 * establish a session for the purchase email (if possible) to avoid
 * attributing licenses/subscriptions to the wrong account.
 */

/** Normalize an email for comparison (lowercase + trim). */
export function normalizeEmail(email?: string | null): string {
  return (email || '').trim().toLowerCase();
}

/**
 * Determine whether we should switch account based on the current session email
 * and the expected (purchase) email.
 */
export function shouldSwitchAccount(currentSessionEmail?: string | null, purchaseEmail?: string | null): boolean {
  const cur = normalizeEmail(currentSessionEmail);
  const pur = normalizeEmail(purchaseEmail);
  if (!pur) return false; // no expected email -> nothing to do
  if (!cur) return false; // no current session -> will proceed with normal auto login
  return cur !== pur; // mismatch
}

/**
 * Small helper to build diagnostic context for window instrumentation (optional for QA / E2E).
 */
export function buildMismatchDiagnostics(params: {
  currentSessionEmail?: string | null;
  purchaseEmail?: string | null;
  paymentIntentId?: string | null;
  autoLoginAttempted?: boolean;
  switched?: boolean;
}) {
  return {
    ...params,
    normalizedCurrent: normalizeEmail(params.currentSessionEmail),
    normalizedPurchase: normalizeEmail(params.purchaseEmail),
    mismatch: shouldSwitchAccount(params.currentSessionEmail, params.purchaseEmail)
  };
}
