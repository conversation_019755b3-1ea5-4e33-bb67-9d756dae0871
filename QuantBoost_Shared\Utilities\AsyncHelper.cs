// --- START OF FILE AsyncHelper.cs ---
using System;
using System.Threading;

namespace QuantBoost_Shared.Utilities
{
    /// <summary>
    /// Provides utilities for managing asynchronous operations and marshaling between threads.
    /// Particularly useful for updating UI from background tasks in Office Add-ins.
    /// 
    /// CRITICAL: Call Initialize() once from the UI thread (e.g., ThisAddIn_Startup) before first use.
    /// </summary>
    public static class AsyncHelper
    {
        private static SynchronizationContext _uiContext;
        private static int _uiThreadId;

        /// <summary>
        /// CRITICAL: Initializes the AsyncHelper, capturing the UI thread's SynchronizationContext.
        /// MUST be called once from the UI thread (e.g., ThisAddIn_Startup) before use.
        /// </summary>
        public static void Initialize()
        {
            _uiThreadId = Thread.CurrentThread.ManagedThreadId;
            _uiContext = SynchronizationContext.Current;

            System.Diagnostics.Debug.WriteLine(
                $"[DEBUG_SYNC] AsyncHelper.Initialize: Method called. Current Thread ID: {Thread.CurrentThread.ManagedThreadId}, IsBackground: {Thread.CurrentThread.IsBackground}. Current SynchronizationContext is: {(SynchronizationContext.Current == null ? "NULL" : SynchronizationContext.Current.GetType().ToString())}"
            );

            if (_uiContext != null)
            {
                // Successfully captured the UI context
                // Informational log for success
                System.Diagnostics.Debug.WriteLine($"[SUCCESS] AsyncHelper.Initialize: UI SynchronizationContext captured. Type: {_uiContext.GetType().Name}. UI Thread ID: {_uiThreadId}");
                ErrorHandlingService.LogException(null, "AsyncHelper Initialized with UI SynchronizationContext.");
            }
            else
            {
                // Failed to capture UI context - critical error
                string errorMessage = "Failed to capture UI SynchronizationContext during Initialize (SynchronizationContext.Current was NULL). Ensure called from UI thread.";
                System.Diagnostics.Debug.WriteLine("[ERROR] AsyncHelper.Initialize: " + errorMessage);
                ErrorHandlingService.LogException(new InvalidOperationException(errorMessage), "AsyncHelper Initialization Critical Error");
            }
        }

        /// <summary>
        /// Gets whether the AsyncHelper has been properly initialized.
        /// </summary>
        public static bool IsInitialized => _uiContext != null;

        /// <summary>
        /// Executes an action on the UI thread.
        /// Use this to update UI elements from background threads.
        /// </summary>
        public static void RunOnUIThread(Action action)
        {
            if (_uiContext == null)
            {
                string errorMsg = "UI SynchronizationContext not available in RunOnUIThread. UI update skipped. Ensure AsyncHelper.Initialize() was called correctly and successfully.";
                System.Diagnostics.Debug.WriteLine("[ERROR] AsyncHelper.RunOnUIThread: " + errorMsg);
                ErrorHandlingService.LogException(new InvalidOperationException(errorMsg), "AsyncHelper Execution Error");
                return;
            }

            if (Thread.CurrentThread.ManagedThreadId == _uiThreadId)
            {
                // Already on UI thread, execute directly
                try
                {
                    action();
                }
                catch (Exception ex)
                {
                    ErrorHandlingService.HandleException(ex, "Error executing action directly on UI thread via AsyncHelper");
                }
            }
            else
            {
                // On background thread, marshal to UI thread
                _uiContext.Post(state =>
                {
                    try
                    {
                        action();
                    }
                    catch (Exception ex)
                    {
                        ErrorHandlingService.HandleException(ex, "Error executing action posted to UI thread via AsyncHelper");
                    }
                }, null);
            }
        }

        /// <summary>
        /// Executes an action on the UI thread and waits for completion.
        /// WARNING: Can cause deadlocks if called from UI thread. Use RunOnUIThread for fire-and-forget operations.
        /// </summary>
        public static void RunOnUIThreadSync(Action action)
        {
            if (_uiContext == null)
            {
                string errorMsg = "UI SynchronizationContext not available in RunOnUIThreadSync. Operation skipped. Ensure AsyncHelper.Initialize() was called correctly.";
                System.Diagnostics.Debug.WriteLine("[ERROR] AsyncHelper.RunOnUIThreadSync: " + errorMsg);
                ErrorHandlingService.LogException(new InvalidOperationException(errorMsg), "AsyncHelper Sync Execution Error");
                return;
            }

            if (Thread.CurrentThread.ManagedThreadId == _uiThreadId)
            {
                // Already on UI thread, execute directly
                try
                {
                    action();
                }
                catch (Exception ex)
                {
                    ErrorHandlingService.HandleException(ex, "Error executing action directly on UI thread via AsyncHelper sync");
                }
            }
            else
            {
                // On background thread, marshal to UI thread and wait
                Exception thrownException = null;
                var resetEvent = new ManualResetEventSlim(false);

                _uiContext.Post(state =>
                {
                    try
                    {
                        action();
                    }
                    catch (Exception ex)
                    {
                        thrownException = ex;
                        ErrorHandlingService.LogException(ex, "Error executing action posted to UI thread via AsyncHelper sync");
                    }
                    finally
                    {
                        resetEvent.Set();
                    }
                }, null);

                resetEvent.Wait();

                if (thrownException != null)
                {
                    throw thrownException;
                }
            }
        }

        /// <summary>
        /// Gets the captured UI thread ID for debugging purposes.
        /// </summary>
        public static int UiThreadId => _uiThreadId;

        /// <summary>
        /// Gets whether the current thread is the UI thread.
        /// </summary>
        public static bool IsOnUIThread => Thread.CurrentThread.ManagedThreadId == _uiThreadId;
    }
}
// --- END OF FILE AsyncHelper.cs ---