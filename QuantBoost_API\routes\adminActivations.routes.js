// adminActivations.routes.js
// Routes for super admin license activation viewing operations

const express = require('express');
const router = express.Router();
const { supabase } = require('../supabaseClient'); // Adjusted path
const { sendSuccess, sendError } = require('../utils/responseHelpers'); // Adjusted path
const { authenticateApiKey } = require('../middleware/authMiddleware'); // Adjusted path

// Apply API key authentication to all routes in this file
router.use(authenticateApiKey);

// GET /v1/admin/activations - List all license activations
router.get('/activations', async (req, res) => {
    const {
        page = 1,
        pageSize = 10,
        user_id,
        license_id,
        machine_id,
        is_active, // Filter by activation status (true, false, or undefined for all)
        sortBy = 'created_at',
        sortOrder = 'desc'
    } = req.query;

    const pageInt = parseInt(page, 10);
    let pageSizeInt = parseInt(pageSize, 10);

    // Validate pageSize
    if (isNaN(pageSizeInt) || pageSizeInt <= 0 || pageSizeInt > 100) {
        pageSizeInt = 10; // Default to 10 if invalid or out of range (max 100)
    }
    // Validate page
    if (isNaN(pageInt) || pageInt <= 0) {
        return sendError(res, 400, 'page must be a positive integer.');
    }

    const offset = (pageInt - 1) * pageSizeInt;
    const validSortOrder = sortOrder.toLowerCase() === 'asc' ? 'asc' : 'desc';

    const allowedSortByFields = [
        'id',
        'license_id',
        'user_id',
        'machine_id',
        'activated_at',
        'is_active',
        'created_at',
        'updated_at'
    ];
    if (!allowedSortByFields.includes(sortBy)) {
        return sendError(res, 400, `Invalid sortBy field. Allowed fields: ${allowedSortByFields.join(', ')}.`);
    }

    try {
        let query = supabase
            .from('license_activations')
            .select('*, licenses(license_key, product_id, license_tier), profiles(email, full_name)', { count: 'exact' }); // Include related license and user profile info

        // Filtering
        if (user_id) {
            if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(user_id)) {
                return sendError(res, 400, 'Invalid user_id format. Must be a UUID.');
            }
            query = query.eq('user_id', user_id);
        }
        if (license_id) {
             if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(license_id)) {
                return sendError(res, 400, 'Invalid license_id format. Must be a UUID.');
            }
            query = query.eq('license_id', license_id);
        }
        if (machine_id) {
            query = query.eq('machine_id', machine_id);
        }
        if (is_active !== undefined) {
            const isActiveBool = String(is_active).toLowerCase() === 'true';
            query = query.eq('is_active', isActiveBool);
        }

        // Sorting
        query = query.order(sortBy, { ascending: validSortOrder === 'asc' });

        // Pagination
        query = query.range(offset, offset + pageSizeInt - 1);

        const { data: activationsData, error, count: totalCount } = await query;

        if (error) {
            console.error('Error fetching license activations:', error);
            return sendError(res, 500, 'Failed to fetch license activations.', error.message);
        }

        sendSuccess(res, {
            activations: activationsData,
            total_count: totalCount,
            page: pageInt,
            pageSize: pageSizeInt
        });
    } catch (error) {
        console.error('Unexpected error fetching license activations:', error);
        sendError(res, 500, 'An unexpected error occurred.', error.message);
    }
});

// POST /v1/admin/activations/:activationId/deactivate - Manually deactivate a specific machine activation
router.post('/activations/:activationId/deactivate', async (req, res) => {
    const { activationId } = req.params;

    if (!activationId) {
        return sendError(res, 400, 'Activation ID is required.');
    }
    // Optional: Validate if activationId is a UUID if that is your table's PK type
    // if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(activationId)) {
    //     return sendError(res, 400, 'Invalid Activation ID format. Must be a UUID.');
    // }

    try {
        // First, check if the activation record exists and is currently active
        const { data: existingActivation, error: fetchError } = await supabase
            .from('license_activations')
            .select('id, is_active, license_id, user_id, machine_id')
            .eq('id', activationId)
            .maybeSingle();

        if (fetchError) {
            console.error(`Error fetching activation ${activationId}:`, fetchError);
            return sendError(res, 500, 'Failed to fetch activation record for deactivation.', fetchError.message);
        }

        if (!existingActivation) {
            return sendError(res, 404, 'Activation record not found.');
        }

        if (!existingActivation.is_active) {
            return sendSuccess(res, { message: 'Activation is already inactive.', activation: existingActivation });
        }

        // Deactivate the activation
        const { data: updatedActivation, error: updateError } = await supabase
            .from('license_activations')
            .update({ is_active: false, updated_at: new Date().toISOString() })
            .eq('id', activationId)
            .select()
            .single(); // Use single as we expect one record to be updated

        if (updateError) {
            console.error(`Error deactivating activation ${activationId}:`, updateError);
            return sendError(res, 500, 'Failed to deactivate license activation.', updateError.message);
        }

        // Log the deactivation event
        // Consider creating a helper for logging if not already done
        if (existingActivation.license_id) { // Check if license_id is available to log against
            try {
                await supabase.from('license_events').insert({
                    license_id: existingActivation.license_id,
                    event_type: 'admin_deactivation',
                    user_id: existingActivation.user_id, // User associated with the activation, if any
                    details: `Activation ID ${activationId} for machine ${existingActivation.machine_id} was manually deactivated by an admin.`
                    // ip_address: req.ip // Optional: log admin's IP if relevant
                });
            } catch (logError) {
                console.error('Failed to log admin deactivation event:', logError);
                // Non-critical error, so don't fail the whole request
            }
        }

        sendSuccess(res, { message: 'License activation deactivated successfully.', activation: updatedActivation });

    } catch (error) {
        console.error(`Unexpected error deactivating activation ${activationId}:`, error);
        sendError(res, 500, 'An unexpected error occurred during deactivation.', error.message);
    }
});

module.exports = router;
