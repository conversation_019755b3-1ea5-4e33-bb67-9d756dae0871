<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
	<!-- QuantBoost MSI installer: installs files under Program Files and sets up Office VSTO registry entries.
			 Build this project to produce the MSI. The Burn bootstrapper (Bundle.wxs) should live in a separate WiX Bootstrapper project. -->
	<Product
		Id="*"
		Name="QuantBoost for Office"
		Language="1033"
		Version="0.1.0"
		Manufacturer="QuantBoost.ai"
		UpgradeCode="3E84C29E-7B8B-4D7C-9B99-6E65F4E3B5B1">

		<Package InstallerVersion="500" Compressed="yes" InstallScope="perMachine" />
		<MediaTemplate />

		<!-- Ensure smooth major upgrades -->
		<MajorUpgrade AllowDowngrades="no"
									DowngradeErrorMessage="A newer version of [ProductName] is already installed." />

		<!-- ARP metadata -->
		<Property Id="ARPCONTACT"><EMAIL></Property>
		<Property Id="ARPURLINFOABOUT">https://quantboost.ai</Property>
		<Property Id="ARPHELPLINK">https://quantboost.ai/support</Property>

		<!-- Install to Program Files\QuantBoost by default; user can change via WixUI_InstallDir -->
		<Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

			<Directory Id="TARGETDIR" Name="SourceDir">
				<Directory Id="ProgramFiles64Folder">
					<Directory Id="INSTALLDIR" Name="QuantBoost">
					<Directory Id="EXCELDIR" Name="Excel" />
					<Directory Id="POWERPOINTDIR" Name="PowerPoint" />
				</Directory>
			</Directory>
		</Directory>

		<!-- UI -->
		<UIRef Id="WixUI_InstallDir" />
		<WixVariable Id="WixUILicenseRtf" Value="License.rtf" />

		<!-- FEATURES: include payload + registry components from TestReg.wxs -->
		<Feature Id="MainFeature" Title="QuantBoost Add-ins" Level="1" Absent="disallow" ConfigurableDirectory="INSTALLDIR">
			<ComponentGroupRef Id="ExcelFiles" />
			<ComponentGroupRef Id="PowerPointFiles" />
			<!-- Registry components are authored in TestReg.wxs -->
			<ComponentRef Id="Registry.Excel" />
			<ComponentRef Id="Registry.PowerPoint" />
		</Feature>

		<!-- Require 64-bit OS -->
		<Condition Message="64-bit Windows is required to install QuantBoost.">VersionNT64</Condition>

	</Product>
</Wix>
