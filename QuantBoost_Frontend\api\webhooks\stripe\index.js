// Azure Static Web App API Route: /api/webhooks/stripe
// Handles Stripe webhook events for subscription management

const Stripe = require('stripe');
const { createClient } = require('@supabase/supabase-js');

// Initialize Stripe with secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-08-27.basil',
});

// Initialize Supabase admin client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY,
  {
    auth: { persistSession: false }
  }
);

// Webhook signing secret
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Utility functions
function toIsoDate(secOrMs) {
  if (!secOrMs) return null;
  return new Date(secOrMs * 1000).toISOString();
}

function generateLicenseKey() {
  return require('crypto').randomUUID();
}

module.exports = async function (context, req) {
  context.log('🚀 Stripe webhook function called');
  // Set CORS headers for webhook endpoint
  context.res = {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type, stripe-signature',
    }
  };

  if (req.method !== 'POST') {
    context.res.status = 405;
    context.res.body = 'Method not allowed';
    return;
  }

  // Get raw body and signature
  const rawBody = req.rawBody || req.body;
  const signature = req.headers['stripe-signature'];

  if (!signature) {
    context.log.error('❌ Missing Stripe signature header');
    context.res.status = 400;
    context.res.body = 'Missing Stripe signature';
    return;
  }

  let event;
  // Track event log id for error updates
  let eventLogId = null;

  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(rawBody, signature, webhookSecret);
    context.log(`✅ Stripe event verified: ${event.type}, ID: ${event.id}`);
  } catch (err) {
    context.log.error('❌ Stripe signature verification failed:', err.message);
    context.res.status = 400;
    context.res.body = `Webhook signature verification failed: ${err.message}`;
    return;
  }

  try {
    // Check for duplicate events (idempotency protection)
    const { data: existingEvent } = await supabase
      .from('webhook_events')
      .select('id')
      .eq('stripe_event_id', event.id)
      .maybeSingle();

    if (existingEvent) {
      context.log(`⚠️ Duplicate event detected, skipping: ${event.id}`);
      context.res.status = 200;
      context.res.body = JSON.stringify({ received: true, message: 'Event already processed' });
      return;
    }

    // Log the event for tracking
    const { data: eventLog, error: eventLogError } = await supabase
      .from('webhook_events')
      .insert({
        stripe_event_id: event.id,
        event_type: event.type,
        processed_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (eventLogError) {
      context.log.error('Failed to log webhook event:', eventLogError);
      context.res.status = 500;
      context.res.body = 'Failed to log event';
      return;
    }
    eventLogId = eventLog.id;

    // Process the event based on type
    await processStripeEvent(context, event);

    // Update event log as successfully processed
    await supabase
      .from('webhook_events')
      .update({ 
        status: 'processed',
        processed_at: new Date().toISOString()
      })
      .eq('id', eventLogId);

    context.res.status = 200;
    context.res.body = JSON.stringify({ received: true });

  } catch (error) {
    context.log.error('❌ Webhook processing error:', error);
    
    // Update event log with error status
    if (eventLogId) {
      await supabase
        .from('webhook_events')
        .update({ 
          status: 'failed',
          error_message: error.message,
          processed_at: new Date().toISOString()
        })
        .eq('id', eventLogId);
    }

    context.res.status = 500;
    context.res.body = 'Webhook processing failed';
  }
};

async function processStripeEvent(context, event) {
  switch (event.type) {
    case 'checkout.session.completed': {
      context.log('👉 Handling checkout.session.completed');
      const session = event.data.object;
      
      if (session.mode !== 'subscription') {
        context.log('Skipping non-subscription checkout session');
        break;
      }

      // Get customer details from session
      const customerEmail = session.customer_details?.email || session.customer_email;
      const customerName = session.customer_details?.name || '';
      // Parse first/last name if available
      const nameParts = (customerName || '').trim().split(' ').filter(Boolean);
      const firstName = nameParts[0] || null;
      const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : null;

      if (!customerEmail) {
        context.log.error('No customer email found in checkout session');
        break;
      }

      // Find or create profile
      let { data: profile, error } = await supabase
        .from('profiles')
        .select('id, first_name, last_name')
        .eq('email', customerEmail)
        .maybeSingle();

      if (error) {
        context.log.error('Error searching for profile:', error);
        break;
      }

      if (!profile) {
        // Create new profile (include name if available)
        const { data: newProfile, error: createError } = await supabase
          .from('profiles')
          .insert({
            email: customerEmail,
            stripe_customer_id: session.customer,
            first_name: firstName,
            last_name: lastName,
            created_at: new Date().toISOString(),
          })
          .select('id, first_name, last_name')
          .single();

        if (createError) {
          context.log.error('Error creating profile:', createError);
          break;
        }
        profile = newProfile;
      } else {
        // Update existing profile with Stripe customer ID and fill missing name fields
        const updateData = { stripe_customer_id: session.customer };
        if (firstName && !profile.first_name) updateData.first_name = firstName;
        if (lastName && !profile.last_name) updateData.last_name = lastName;

        await supabase
          .from('profiles')
          .update(updateData)
          .eq('id', profile.id);
      }

      context.log(`✅ Checkout session processed for customer: ${customerEmail}`);
      break;
    }

    case 'payment_intent.succeeded': {
      context.log('👉 Handling payment_intent.succeeded');
      const paymentIntent = event.data.object;

      // Find profile by Stripe customer ID
      const customerId = paymentIntent.customer;
      let { data: profile } = await supabase
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', customerId)
        .maybeSingle();

      if (!profile) {
        context.log.error(`No profile found for customer: ${customerId}`);
        break;
      }

      // Retrieve associated charge
      let chargeData = null;
      try {
        const charges = await stripe.charges.list({ payment_intent: paymentIntent.id, limit: 1 });
        if (charges.data && charges.data.length > 0) {
          chargeData = charges.data[0];
        }
      } catch (e) {
        context.log.error('Error retrieving charge for payment intent:', e);
      }

      if (!chargeData) {
        context.log('No charge found for payment intent');
        break;
      }

      // Get subscription ID from invoice if available
      let stripeSubscriptionId = null;
      try {
        const invoiceId = chargeData.invoice || paymentIntent.invoice;
        if (invoiceId) {
          const invoice = await stripe.invoices.retrieve(invoiceId);
          stripeSubscriptionId = invoice?.subscription || null;
        }
      } catch (e) {
        context.log.error('Error retrieving invoice for subscription lookup:', e);
      }

      // Optionally enrich profile with billing name if missing
      try {
        const billingName = chargeData.billing_details?.name || null;
        if (billingName && profile?.id) {
          const parts = billingName.trim().split(' ').filter(Boolean);
          const f = parts[0] || null;
          const l = parts.length > 1 ? parts.slice(1).join(' ') : null;
          const update = {};
          // fetch current fields
          const { data: cur } = await supabase
            .from('profiles')
            .select('first_name, last_name')
            .eq('id', profile.id)
            .maybeSingle();
          if (f && !cur?.first_name) update.first_name = f;
          if (l && !cur?.last_name) update.last_name = l;
          if (Object.keys(update).length > 0) {
            await supabase
              .from('profiles')
              .update(update)
              .eq('id', profile.id);
          }
        }
      } catch (e) {
        context.log.error('Error updating profile name from billing details:', e);
      }

      // Upsert charge receipt
      const receiptPayload = {
        stripe_charge_id: chargeData.id,
        stripe_payment_intent_id: paymentIntent.id,
        stripe_subscription_id: stripeSubscriptionId,
        user_id: profile.id,
        amount: chargeData.amount,
        currency: chargeData.currency,
        receipt_url: chargeData.receipt_url,
        status: 'succeeded',
        created_at: new Date((chargeData.created || Math.floor(Date.now() / 1000)) * 1000).toISOString(),
        updated_at: new Date().toISOString()
      };

      const { error: receiptError } = await supabase
        .from('charge_receipts')
        .upsert(receiptPayload, { onConflict: 'stripe_charge_id' });

      if (receiptError) {
        context.log.error('Error creating charge receipt:', receiptError);
      } else {
        context.log(`✅ Charge receipt created for: ${chargeData.id}`);
      }

      // Log payment event (success)
      const paymentEvent = {
        stripe_event_id: event.id,
        event_type: event.type,
        stripe_payment_intent_id: paymentIntent.id,
        stripe_charge_id: chargeData.id,
        stripe_subscription_id: stripeSubscriptionId,
        stripe_customer_id: customerId,
        user_id: profile.id,
        amount: paymentIntent.amount_received || paymentIntent.amount,
        currency: paymentIntent.currency,
        status: 'succeeded',
        payment_method_type: paymentIntent.payment_method_types?.[0] || null,
        event_data: paymentIntent,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { error: payEvtError } = await supabase
        .from('payment_events')
        .insert(paymentEvent);

      if (payEvtError) {
        context.log.error('Error logging payment event (succeeded):', payEvtError);
      }

      break;
    }

    case 'payment_intent.payment_failed': {
      context.log('👉 Handling payment_intent.payment_failed');
      const paymentIntent = event.data.object;

      // Find profile by Stripe customer ID
      const customerId = paymentIntent.customer;
      let { data: profile } = await supabase
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', customerId)
        .maybeSingle();

      // Determine subscription id from invoice if available
      let stripeSubscriptionId = null;
      try {
        if (paymentIntent.invoice) {
          const invoice = await stripe.invoices.retrieve(paymentIntent.invoice);
          stripeSubscriptionId = invoice?.subscription || null;
        }
      } catch (e) {
        context.log.error('Error retrieving invoice for failed payment:', e);
      }

      // Log payment failure event
      const failure = paymentIntent.last_payment_error || {};
      const paymentEvent = {
        stripe_event_id: event.id,
        event_type: event.type,
        stripe_payment_intent_id: paymentIntent.id,
        stripe_subscription_id: stripeSubscriptionId,
        stripe_customer_id: customerId,
        user_id: profile?.id || null,
        amount: paymentIntent.amount || null,
        currency: paymentIntent.currency || 'usd',
        status: 'failed',
        failure_code: failure.code || null,
        failure_message: failure.message || null,
        event_data: paymentIntent,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { error: payEvtError } = await supabase
        .from('payment_events')
        .insert(paymentEvent);

      if (payEvtError) {
        context.log.error('Error logging payment event (failed):', payEvtError);
      } else {
        context.log(`✅ Payment failure event logged for: ${paymentIntent.id}`);
      }

      break;
    }

    case 'customer.updated': {
      context.log('👉 Handling customer.updated');
      const customer = event.data.object;
      const previousAttributes = event.data.previous_attributes || {};

      // Find profile by Stripe customer ID
      let { data: profile } = await supabase
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', customer.id)
        .maybeSingle();

      const changedFields = Object.keys(previousAttributes);
      if (changedFields.length === 0) {
        context.log('No significant changes to log for customer.updated');
        break;
      }

      // Build previous/current snapshots limited to changed fields
      const previous_data = {};
      const current_data = {};
      for (const key of changedFields) {
        previous_data[key] = previousAttributes[key];
        current_data[key] = customer[key];
      }

      const { error: custEvtErr } = await supabase
        .from('customer_events')
        .insert({
          stripe_event_id: event.id,
          event_type: event.type,
          stripe_customer_id: customer.id,
          user_id: profile?.id || null,
          previous_data,
          current_data,
          changes: { changed_fields: changedFields },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (custEvtErr) {
        context.log.error('Error logging customer event:', custEvtErr);
      } else {
        context.log(`✅ Customer update event logged for: ${customer.id}`);
      }
      break;
    }

    case 'customer.subscription.created':
    case 'customer.subscription.updated': {
      context.log(`👉 Handling ${event.type}`);
      const subscription = event.data.object;
      const firstItem = subscription.items?.data?.[0];

      // Find profile by Stripe customer ID
      let { data: profile, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', subscription.customer)
        .maybeSingle();

      if (error) {
        context.log.error('Error searching for profile:', error);
        break;
      }

      if (!profile) {
        context.log.error(`No profile found for Stripe customer: ${subscription.customer}`);
        break;
      }

      // Map to existing schema columns
      const subscriptionData = {
        user_id: profile.id,
        stripe_subscription_id: subscription.id,
        status: subscription.status,
        quantity: firstItem?.quantity || 1,
        current_period_start: toIsoDate(subscription.current_period_start),
        current_period_end: toIsoDate(subscription.current_period_end),
        trial_start: toIsoDate(subscription.trial_start),
        trial_end: toIsoDate(subscription.trial_end),
        cancel_at_period_end: subscription.cancel_at_period_end,
        canceled_at: toIsoDate(subscription.canceled_at),
        updated_at: new Date().toISOString(),
        // Optional enrichments if available
        plan_id: firstItem?.price?.id || null,
        amount: firstItem?.price?.unit_amount || null,
        currency: firstItem?.price?.currency || 'usd',
        interval: firstItem?.price?.recurring?.interval || null,
        interval_count: firstItem?.price?.recurring?.interval_count || null,
      };

      // Upsert by unique constraint on stripe_subscription_id
      const { data: subRows, error: upsertErr } = await supabase
        .from('subscriptions')
        .upsert(subscriptionData, { onConflict: 'stripe_subscription_id' })
        .select('id, quantity')
        .eq('stripe_subscription_id', subscription.id);

      if (upsertErr) {
        context.log.error('Error upserting subscription:', upsertErr);
        break;
      }
      const subRow = subRows?.[0];
      if (!subRow) {
        context.log.error('Upserted subscription not returned');
        break;
      }

      // Handle license creation/updates to match quantity
      const desiredQty = subRow.quantity;
      const { count: existingCount } = await supabase
        .from('licenses')
        .select('*', { count: 'exact', head: true })
        .eq('subscription_id', subRow.id);

      if ((existingCount || 0) < desiredQty) {
        const toCreate = desiredQty - (existingCount || 0);
        const licensesToCreate = Array.from({ length: toCreate }, () => ({
          subscription_id: subRow.id,
          license_key: generateLicenseKey(),
          status: subscription.status === 'active' || subscription.status === 'trialing' ? 'active' : 'inactive',
          created_at: new Date().toISOString(),
        }));

        const { error: licenseError } = await supabase
          .from('licenses')
          .insert(licensesToCreate);

        if (licenseError) {
          context.log.error('Error creating licenses:', licenseError);
        } else {
          context.log(`✅ Created ${licensesToCreate.length} new licenses`);
        }
      }

      // Update license statuses based on subscription status
      const statusMap = {
        active: 'active',
        trialing: 'active',
        canceled: 'canceled',
        unpaid: 'inactive',
        past_due: 'inactive',
        incomplete: 'inactive'
      };
      const newLicenseStatus = statusMap[subscription.status] || 'inactive';

      await supabase
        .from('licenses')
        .update({ status: newLicenseStatus })
        .eq('subscription_id', subRow.id);

      context.log(`✅ Subscription ${event.type} processed: ${subscription.id}`);
      break;
    }

    case 'customer.subscription.deleted': {
      context.log('👉 Handling customer.subscription.deleted');
      const subscription = event.data.object;
      
      const { data: subData, error: subError } = await supabase
        .from('subscriptions')
        .update({ status: 'canceled' })
        .eq('stripe_subscription_id', subscription.id)
        .select('id')
        .single();

      if (subError) {
        context.log.error('Error updating subscription as canceled:', subError);
        break;
      }

      if (subData?.id) {
        await supabase
          .from('licenses')
          .update({ status: 'canceled' })
          .eq('subscription_id', subData.id);
      }

      context.log(`✅ Subscription deleted: ${subscription.id}`);
      break;
    }

    case 'invoice.paid': {
      context.log('👉 Handling invoice.paid');
      const invoice = event.data.object;

      if (!invoice.subscription) {
        context.log('Skipping non-subscription invoice');
        break;
      }

      // Validate this is for our QuantBoost product
      const validPriceIds = [
        'price_1RyhsAE6FvhUKV1bImD5Ft34', // Monthly
        'price_1RC3HTE6FvhUKV1bE9D6zf6e'  // Annual
      ];

      const quantBoostLineItem = invoice.lines.data.find(item =>
        item.price && validPriceIds.includes(item.price.id)
      );

      if (!quantBoostLineItem) {
        context.log('Invoice is not for QuantBoost product, skipping');
        break;
      }

      // Update subscription status to active
      await supabase
        .from('subscriptions')
        .update({ 
          status: 'active',
          updated_at: new Date().toISOString()
        })
        .eq('stripe_subscription_id', invoice.subscription);

      context.log(`✅ Invoice payment processed for subscription: ${invoice.subscription}`);
      break;
    }

    case 'invoice.payment_failed': {
      context.log('👉 Handling invoice.payment_failed');
      const invoice = event.data.object;

      if (!invoice.subscription) {
        context.log('Skipping non-subscription invoice');
        break;
      }

      // Update subscription status
      await supabase
        .from('subscriptions')
        .update({ 
          status: 'past_due',
          updated_at: new Date().toISOString()
        })
        .eq('stripe_subscription_id', invoice.subscription);

      context.log(`✅ Invoice payment failure processed for subscription: ${invoice.subscription}`);
      break;
    }

    default:
      context.log(`👀 Unhandled Stripe event type: ${event.type}`);
      break;
  }
}
