﻿<?xml version="1.0" encoding="utf-8"?><wixObject version="3.0.2002.0" xmlns="http://schemas.microsoft.com/wix/2006/objects"><section id="QuantBoost Installer" type="bundle"><table name="ChainPackage"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*23"><field>QuantBoostMsi</field><field>Msi</field><field>QuantBoostMsi</field><field /><field /><field /><field /><field /><field /><field>0</field><field>1</field><field /><field /><field /><field /><field>WixBundleLog_QuantBoostMsi</field><field>WixBundleRollbackLog_QuantBoostMsi</field><field /><field>-**********</field><field /><field /><field /><field>0</field></row></table><table name="Payload"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*23"><field>QuantBoostMsi</field><field>QuantBoost.msi</field><field>C:\VS projects\QuantBoost\QuantBoost_WixInstaller\bin\x64\Release\QuantBoost.msi</field><field /><field /><field>C:\VS projects\QuantBoost\QuantBoost_WixInstaller\bin\x64\Release\QuantBoost.msi</field><field>1</field></row></table><table name="RelatedBundle"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*6"><field>{1B4C9B2B-4FD1-4B97-9A8D-8C2D5E8B9A33}</field><field>1</field></row></table><table name="Variable"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*18"><field>InstallScope</field><field>perMachine</field><field>string</field><field>0</field><field>0</field></row></table><table name="WixBundle"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*6"><field>0.1.0</field><field>Copyright (c) QuantBoost.ai. All rights reserved.</field><field>QuantBoost Installer</field><field /><field /><field /><field /><field /><field /><field>QuantBoost.ai</field><field /><field /><field>WixBundleLog:QuantBoost_Installer.log</field><field /><field /><field /><field /><field>X64</field><field /><field>{1B4C9B2B-4FD1-4B97-9A8D-8C2D5E8B9A33}</field><field /><field /><field /></row></table><table name="WixChain"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*20"><field>0</field></row></table><table name="WixGroup"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*21"><field>WixChain</field><field>PackageGroup</field><field>NetFx481</field><field>PackageGroup</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*22"><field>WixChain</field><field>PackageGroup</field><field>VSTORuntime</field><field>PackageGroup</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*23"><field>QuantBoostMsi</field><field>Package</field><field>QuantBoostMsi</field><field>Payload</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*23"><field>WixChain</field><field>PackageGroup</field><field>QuantBoostMsi</field><field>Package</field></row></table><table name="WixOrdering"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*22"><field>PackageGroup</field><field>VSTORuntime</field><field>PackageGroup</field><field>NetFx481</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*23"><field>Package</field><field>QuantBoostMsi</field><field>PackageGroup</field><field>VSTORuntime</field></row></table><table name="WixSimpleReference"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*11"><field>WixBootstrapperApplication</field><field>WixStandardBootstrapperApplication.RtfLicense</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*21"><field>ChainPackageGroup</field><field>NetFx481</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*22"><field>ChainPackageGroup</field><field>VSTORuntime</field></row></table><table name="WixStdbaOptions"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*12"><field /><field /><field /><field>1</field><field /><field /></row></table><table name="WixVariable"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*12"><field>WixStdbaLicenseRtf</field><field>License.rtf</field><field>0</field></row></table></section><section type="fragment"><table name="ChainPackage"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*29"><field>NetFx481Web</field><field>Exe</field><field>NetFx481Web</field><field /><field>/passive /norestart</field><field>/passive /norestart</field><field>/uninstall /passive /norestart</field><field /><field /><field>0</field><field>1</field><field>1</field><field>(NetFxRelease32 &gt;= 533320) OR (NetFxRelease64 &gt;= 533320)</field><field /><field>1</field><field>WixBundleLog_NetFx481Web</field><field>WixBundleRollbackLog_NetFx481Web</field><field /><field>-**********</field><field /><field /><field /><field /></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*48"><field>VSTORedist</field><field>Exe</field><field>VSTORedist</field><field /><field>/passive /norestart</field><field /><field /><field /><field /><field>0</field><field>1</field><field>1</field><field>(VstoInstall32 &gt;= 1) OR (VstoInstall64 &gt;= 1)</field><field /><field /><field>WixBundleLog_VSTORedist</field><field>WixBundleRollbackLog_VSTORedist</field><field /><field>-**********</field><field /><field /><field /><field /></row></table><table name="ChainPackageGroup"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*28"><field>NetFx481</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*47"><field>VSTORuntime</field></row></table><table name="ExitCode"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*42"><field>NetFx481Web</field><field>0</field><field>success</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*43"><field>NetFx481Web</field><field>3010</field><field>success</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*59"><field>VSTORedist</field><field>0</field><field>success</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*60"><field>VSTORedist</field><field>3010</field><field>success</field></row></table><table name="Payload"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*29"><field>NetFx481Web</field><field>ndp481-web.exe</field><field>ndp481-web.exe</field><field>https://dotnet.microsoft.com/en-us/download/dotnet-framework/thank-you/net481-web-installer</field><field>0</field><field>ndp481-web.exe</field><field>1</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*48"><field>VSTORedist</field><field>vstor_redist.exe</field><field>vstor_redist.exe</field><field>https://download.microsoft.com/download/8/6/4/8641e164-7796-4b34-81c7-30d24a5bd533/vstor_redist.exe</field><field>0</field><field>vstor_redist.exe</field><field>1</field></row></table><table name="PayloadDisplayInformation"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*29"><field>NetFx481Web</field><field>Microsoft .NET Framework 4.8.1</field><field /></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*48"><field>VSTORedist</field><field>Microsoft Visual Studio Tools for Office Runtime</field><field /></row></table><table name="WixGroup"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*29"><field>NetFx481Web</field><field>Package</field><field>NetFx481Web</field><field>Payload</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*29"><field>NetFx481</field><field>PackageGroup</field><field>NetFx481Web</field><field>Package</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*48"><field>VSTORedist</field><field>Package</field><field>VSTORedist</field><field>Payload</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*48"><field>VSTORuntime</field><field>PackageGroup</field><field>VSTORedist</field><field>Package</field></row></table></section><section type="fragment"><table name="WixRegistrySearch"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*66"><field>NetFxReleaseSearch32</field><field>2</field><field>SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full</field><field>Release</field><field>9</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*72"><field>NetFxReleaseSearch64</field><field>2</field><field>SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full</field><field>Release</field><field>41</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*79"><field>VstoInstallSearch32</field><field>2</field><field>SOFTWARE\Microsoft\VSTO Runtime Setup\v4</field><field>Install</field><field>9</field></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*85"><field>VstoInstallSearch64</field><field>2</field><field>SOFTWARE\Microsoft\VSTO Runtime Setup\v4</field><field>Install</field><field>41</field></row></table><table name="WixSearch"><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*66"><field>NetFxReleaseSearch32</field><field>NetFxRelease32</field><field /></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*72"><field>NetFxReleaseSearch64</field><field>NetFxRelease64</field><field /></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*79"><field>VstoInstallSearch32</field><field>VstoInstall32</field><field /></row><row sourceLineNumber="C:\VS projects\QuantBoost\QuantBoost_Bootstrapper\Bundle.wxs*85"><field>VstoInstallSearch64</field><field>VstoInstall64</field><field /></row></table></section></wixObject>