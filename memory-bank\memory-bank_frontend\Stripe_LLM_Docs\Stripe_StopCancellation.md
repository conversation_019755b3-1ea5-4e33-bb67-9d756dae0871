Stop a pending cancellation
You can stop a scheduled cancellation through the Update Subscription API or the Dashboard. You can’t reactivate a canceled subscription.


Dashboard

API
To stop a scheduled cancellation using the API, set cancel_at_period_end to false. This action only affects subscriptions that haven’t reached the end of their billing period.

Command Line

curl https://api.stripe.com/v1/subscriptions/{{SUBSCRIPTION_ID}} \
  -u "sk_test_51R9yKDE6FvhUKV1btBmW82fjOd04xqzSH2vO2samPv66v15IUPtG8DRG4YvYb21CCcQUXDnHpI4FiOx8CjPmBUTy00uIHDv7TQ:" \
  -d cancel_at_period_end=false


Set a custom cancel date
Use the cancel_at parameter to cancel a subscription at a future timestamp.

Command Line

curl https://api.stripe.com/v1/subscriptions/{{SUBSCRIPTION_ID}} \
  -u "sk_test_51R9yKDE6FvhUKV1btBmW82fjOd04xqzSH2vO2samPv66v15IUPtG8DRG4YvYb21CCcQUXDnHpI4FiOx8CjPmBUTy00uIHDv7TQ:" \
  -d cancel_at=1723766400
When you schedule a cancel date that occurs before the billing period ends, the subscription’s items’ current_period_end updates to match the cancel_at date. This creates prorations for the change in the current period, unless your update disables prorations.

For billing_mode=flexible subscriptions with status=trialing, adding, updating, or removing the cancel_at value doesn’t affect the trial_end date.

For subscriptions using billing_mode, you can use enum helpers with the cancel_at parameter to simplify canceling a subscription at the end of a billing period. The min_period_end and max_period_end helpers allow you to specify when to cancel a subscription based on the billing periods for all subscription items.

Event	Description
min_period_end	Selects the timestamp for the billing period that ends earliest.
max_period_end	Selects the timestamp for the billing period that ends last.
These helpers resolve timestamps immediately, reducing the risk of later changes to an item’s current_period_end affecting the resolved cancellation timestamp.

If you schedule a cancel date that occurs more than one period away, the subscription’s cycle remains unaffected until the subscription renews into the period that contains the cancel_at date. The items.current_period_end for the renewal then shortens to match the cancel_at date.

For example, your customer subscribes to a 120 USD per year licensed subscription that renews on January 1, 2024. They set the subscription to cancel on July 1, 2024. The final invoice subtotal on January 1 calculates as 60 USD and the items.current_period_end is July 1.

Changing, adding, or removing a scheduled cancel date within the current period updates the items.current_period_end and creates prorations. In the above example, on February 15 you update the cancel date to October 1. The current period end becomes October 1, and Stripe creates prorations for 30 USD for the additional quarter. To invoice the prorated items immediately instead of in a final invoice on October 1, pass a proration_behavior of always_invoice in the update, or separately create a one-off invoice.

---- 
Stripe does not offer a direct "undo cancellation" API for subscriptions that have already been fully canceled. Once a subscription's status changes to canceled, it cannot be reactivated directly.
However, there are two primary scenarios and their corresponding solutions:
1. Stopping a Scheduled Cancellation (Before it takes effect):
If a subscription was scheduled for cancellation at the end of the current billing period (using cancel_at_period_end: true), you can prevent the cancellation from occurring before the period ends.
API: Update the subscription and set cancel_at_period_end to false.
Code

    Stripe::Subscription.update(
      'sub_YOUR_SUBSCRIPTION_ID',
      { cancel_at_period_end: false }
    )
(Replace 'sub_YOUR_SUBSCRIPTION_ID' with the actual subscription ID.)
2. Reactivating a Fully Canceled Subscription:
If a subscription has already reached the canceled status, you cannot "un-cancel" it. Instead, you need to create a new subscription for the customer.
API: Create a new subscription for the customer, specifying the desired plan and other relevant details.
Code

    Stripe::Subscription.create(
      customer: 'cus_YOUR_CUSTOMER_ID',
      items: [
        { price: 'price_YOUR_PRICE_ID' }
      ]
    )
(Replace 'cus_YOUR_CUSTOMER_ID' and 'price_YOUR_PRICE_ID' with the actual customer and price IDs.)
Important Considerations:
Invoice Collection:
If automatic collection of finalized invoices was stopped upon the original cancellation, you may need to manually resume it for any outstanding invoices related to the new subscription.
Trial Periods:
If the original subscription had a trial, you can choose whether to apply a new trial to the new subscription or start billing immediately.