// Email service integration for team invitations
// This module provides email sending functionality for team license invitations

interface EmailTemplate {
  to: string;
  subject: string;
  html: string;
  text: string;
}

interface InvitationEmailData {
  targetEmail: string;
  inviterName?: string;
  teamName?: string;
  activationUrl: string;
  expirationDays?: number;
}

export class EmailService {
  private apiKey: string;
  private fromEmail: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.RESEND_API_KEY || process.env.SENDGRID_API_KEY || '';
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
  }

  /**
   * Create team invitation email template
   */
  createInvitationEmail(data: InvitationEmailData): EmailTemplate {
    const { targetEmail, inviterName, teamName, activationUrl, expirationDays = 7 } = data;

    const subject = `You've been invited to join ${teamName || 'a QuantBoost team'}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>QuantBoost Team Invitation</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #10b981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
            .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; }
            .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb; font-size: 12px; color: #6b7280; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>QuantBoost Team Invitation</h1>
            </div>
            <div class="content">
              <h2>You've been invited!</h2>
              <p>Hello,</p>
              <p>${inviterName ? `${inviterName} has` : 'You have been'} invited you to join ${teamName || 'a QuantBoost team'} license.</p>
              
              <p>QuantBoost provides powerful productivity tools for Excel and PowerPoint that will help you:</p>
              <ul>
                <li>🔗 Link Excel models to PowerPoint slides</li>
                <li>🔍 Trace complex formulas and dependencies</li>
                <li>📊 Analyze and optimize file sizes</li>
                <li>🧹 Clean and streamline workbooks</li>
                <li>⌨️ Manage custom keyboard shortcuts</li>
              </ul>

              <p>Click the button below to activate your license and get started:</p>
              
              <p style="text-align: center; margin: 30px 0;">
                <a href="${activationUrl}" class="button">Activate Your License</a>
              </p>
              
              <p><strong>Important:</strong> This invitation will expire in ${expirationDays} days. If you don't have a QuantBoost account yet, you'll be prompted to create one during the activation process.</p>
              
              <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
              <p style="word-break: break-all; background: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                ${activationUrl}
              </p>
            </div>
            <div class="footer">
              <p>This email was sent from QuantBoost. If you didn't expect this invitation, you can safely ignore this email.</p>
              <p>Visit us at <a href="https://quantboost.com">quantboost.com</a> | <a href="mailto:<EMAIL>">Contact Support</a></p>
            </div>
          </div>
        </body>
      </html>
    `;

    const text = `
QuantBoost Team Invitation

Hello,

${inviterName ? `${inviterName} has` : 'You have been'} invited you to join ${teamName || 'a QuantBoost team'} license.

QuantBoost provides powerful productivity tools for Excel and PowerPoint that will help you work more efficiently.

Activate your license: ${activationUrl}

Important: This invitation will expire in ${expirationDays} days.

If you don't have a QuantBoost account yet, you'll be prompted to create one during the activation process.

If you didn't expect this invitation, you can safely ignore this email.

Visit us at https://quantboost.com | Contact Support: <EMAIL>
    `;

    return {
      to: targetEmail,
      subject,
      html,
      text
    };
  }

  /**
   * Send email using configured service
   */
  async sendEmail(template: EmailTemplate): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      if (!this.apiKey) {
        console.warn('No email API key configured. Email would be sent to:', template.to);
        return { 
          success: false, 
          error: 'Email service not configured. Please set RESEND_API_KEY or SENDGRID_API_KEY environment variable.' 
        };
      }

      // Detect email service based on API key format or environment variable
      if (process.env.RESEND_API_KEY) {
        return await this.sendWithResend(template);
      } else if (process.env.SENDGRID_API_KEY) {
        return await this.sendWithSendGrid(template);
      } else {
        return { 
          success: false, 
          error: 'Unknown email service. Please configure RESEND_API_KEY or SENDGRID_API_KEY.' 
        };
      }
    } catch (error) {
      console.error('Email sending error:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown email error' 
      };
    }
  }

  /**
   * Send email using Resend service
   */
  private async sendWithResend(template: EmailTemplate): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: this.fromEmail,
          to: [template.to],
          subject: template.subject,
          html: template.html,
          text: template.text,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Resend API error: ${errorData.message || response.statusText}`);
      }

      const data = await response.json();
      return { success: true, messageId: data.id };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Resend email failed' 
      };
    }
  }

  /**
   * Send email using SendGrid service
   */
  private async sendWithSendGrid(template: EmailTemplate): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          personalizations: [{
            to: [{ email: template.to }],
            subject: template.subject,
          }],
          from: { email: this.fromEmail },
          content: [
            { type: 'text/plain', value: template.text },
            { type: 'text/html', value: template.html },
          ],
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`SendGrid API error: ${errorData || response.statusText}`);
      }

      // SendGrid returns empty body on success, but includes X-Message-Id header
      const messageId = response.headers.get('X-Message-Id');
      return { success: true, messageId: messageId || undefined };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'SendGrid email failed' 
      };
    }
  }

  /**
   * Send team invitation email
   */
  async sendTeamInvitation(data: InvitationEmailData): Promise<{ success: boolean; messageId?: string; error?: string }> {
    const template = this.createInvitationEmail(data);
    return await this.sendEmail(template);
  }
}

// Export singleton instance
export const emailService = new EmailService();

// Helper function for team invitations
export async function sendTeamInvitationEmail(
  targetEmail: string, 
  activationUrl: string, 
  options: { inviterName?: string; teamName?: string; expirationDays?: number } = {}
) {
  return await emailService.sendTeamInvitation({
    targetEmail,
    activationUrl,
    ...options
  });
}