[
  {
    "event_message": "POST | 201 | ************** | 9688d4290fae2acc | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "6298f19a-a21f-4cb9-b42c-686a2283c059",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087937464000
  },
  {
    "event_message": "PATCH | 204 | ************** | 9688d4284b562b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f | node",
    "id": "4d2eed60-2f19-45c5-90ac-00ca7c7c8dcd",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "PATCH",
    "path": "/rest/v1/license_activations",
    "search": "?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f",
    "status_code": 204,
    "timestamp": 1754087937340000
  },
  {
    "event_message": "GET | 200 | ************** | 9688d427eadc2b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9 | node",
    "id": "c7e55b41-97be-4d3b-8e20-5dabe9741771",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/license_activations",
    "search": "?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9",
    "status_code": 200,
    "timestamp": 1754087937274000
  },
  {
    "event_message": "GET | 200 | ************** | 9688d4275a412b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/licenses?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite | node",
    "id": "7ebb3949-dfb9-4b4a-a2a0-e621fb7adf31",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/licenses",
    "search": "?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite",
    "status_code": 200,
    "timestamp": 1754087937191000
  },
  {
    "event_message": "POST | 201 | ************** | 9688d426b9772b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "6b33a0a2-1a7f-470e-bf76-2345ab481676",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087937101000
  },
  {
    "event_message": "GET | 200 | ************** | 9688d425f84d2b9c | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "842eac02-bd26-4dab-a08a-4a181c7038a6",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087936969000
  },
  {
    "event_message": "GET | 200 | ************** | 9688d4258fcd2b9c | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "3e2b4930-39b7-4c32-9567-c9196a1bb309",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087936899000
  },
  {
    "event_message": "POST | 200 | ************** | 9688d423bdc72b9c | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/token?grant_type=refresh_token | node",
    "id": "fa361175-7e18-4db4-90b2-e39e652eaa00",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/token",
    "search": "?grant_type=refresh_token",
    "status_code": 200,
    "timestamp": 1754087936615000
  },
  {
    "event_message": "POST | 201 | ************** | 9688d0a638a009cf | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "38515429-9a58-48fb-9c55-9538b2ebc8be",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087793642000
  },
  {
    "event_message": "PATCH | 204 | ************** | 9688d0a56aa22b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f | node",
    "id": "8ffd33af-586e-4cec-bc2d-71d7347a18ae",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "PATCH",
    "path": "/rest/v1/license_activations",
    "search": "?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f",
    "status_code": 204,
    "timestamp": 1754087793517000
  },
  {
    "event_message": "GET | 200 | ************** | 9688d0a4ea052b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9 | node",
    "id": "6ec88ac7-9d8b-4ff7-bef2-53a135cb5730",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/license_activations",
    "search": "?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9",
    "status_code": 200,
    "timestamp": 1754087793432000
  },
  {
    "event_message": "GET | 200 | ************** | 9688d0a489a82b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/licenses?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite | node",
    "id": "57e922ee-e578-4609-84b8-cd3c419f710f",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/licenses",
    "search": "?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite",
    "status_code": 200,
    "timestamp": 1754087793376000
  },
  {
    "event_message": "POST | 201 | ************** | 9688d0a348282b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "ee0ad1e9-b188-4dbc-920d-394aa67b6de2",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087793181000
  },
  {
    "event_message": "GET | 200 | ************** | 9688d0a2bfab2b9c | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "659aea8e-df7d-4625-9768-8187fad801c6",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087793092000
  },
  {
    "event_message": "GET | 200 | ************** | 9688d0a25f4c2b9c | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "a983d106-5067-4985-8a36-e7d35ee0fd9b",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087793028000
  },
  {
    "event_message": "POST | 200 | ************** | 9688d09f8bfb2b9c | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/token?grant_type=refresh_token | node",
    "id": "faa815df-1013-4d35-adbc-5c70c0e4ece6",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/token",
    "search": "?grant_type=refresh_token",
    "status_code": 200,
    "timestamp": 1754087792590000
  },
  {
    "event_message": "POST | 201 | ************** | 9688ce6508912acc | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "befd7718-566e-4315-a0e9-a48c55677a1c",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087701301000
  },
  {
    "event_message": "PATCH | 204 | ************** | 9688ce645a18cb94 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f | node",
    "id": "04970a8e-0c2d-4120-b3e1-72ac453cdb71",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "PATCH",
    "path": "/rest/v1/license_activations",
    "search": "?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f",
    "status_code": 204,
    "timestamp": 1754087701188000
  },
  {
    "event_message": "GET | 200 | ************** | 9688ce63e8a4cb94 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9 | node",
    "id": "783c2ce8-e2b0-4ce6-9316-53d0c2dcdb97",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/license_activations",
    "search": "?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9",
    "status_code": 200,
    "timestamp": 1754087701113000
  },
  {
    "event_message": "GET | 200 | ************** | 9688ce636f20cb94 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/licenses?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite | node",
    "id": "793cde46-7d94-4950-9c91-0073af210e09",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/licenses",
    "search": "?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite",
    "status_code": 200,
    "timestamp": 1754087701031000
  },
  {
    "event_message": "POST | 201 | ************** | 9688ce62cd2fcb94 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "870e17fc-bfac-4b94-8bab-3f80d7a7bd09",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087700927000
  },
  {
    "event_message": "GET | 200 | ************** | 9688ce624bcecb94 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "b9c668fd-33aa-409d-a393-f43d10571a73",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087700851000
  },
  {
    "event_message": "GET | 200 | ************** | 9688ce61ba32cb94 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "cd7138a1-f126-4b80-9148-5206e31fdb8f",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087700764000
  },
  {
    "event_message": "POST | 200 | ************** | 9688ce604df9cb94 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/token?grant_type=refresh_token | node",
    "id": "756e18cc-34b1-4cd7-aa7e-81cc56878a6a",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/token",
    "search": "?grant_type=refresh_token",
    "status_code": 200,
    "timestamp": 1754087700531000
  },
  {
    "event_message": "POST | 500 | ************ | 9688cd7d0cb9de86 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/otp?redirect_to=https%3A%2F%2Fapp-quantboost-frontend-staging.azurewebsites.net%2Fdashboard%3Fpayment%3Dsuccess | Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "id": "54cede5f-58de-4407-a32f-7d294d61dff8",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/otp",
    "search": "?redirect_to=https%3A%2F%2Fapp-quantboost-frontend-staging.azurewebsites.net%2Fdashboard%3Fpayment%3Dsuccess",
    "status_code": 500,
    "timestamp": ****************
  },
  {
    "event_message": "OPTIONS | 200 | ************ | 9688cd7c2ba5de86 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/otp?redirect_to=https%3A%2F%2Fapp-quantboost-frontend-staging.azurewebsites.net%2Fdashboard%3Fpayment%3Dsuccess | Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "id": "40196c10-d49c-490e-aa7c-c67fbd65ff85",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "OPTIONS",
    "path": "/auth/v1/otp",
    "search": "?redirect_to=https%3A%2F%2Fapp-quantboost-frontend-staging.azurewebsites.net%2Fdashboard%3Fpayment%3Dsuccess",
    "status_code": 200,
    "timestamp": 1754087664037000
  },
  {
    "event_message": "POST | 201 | ************** | 9688cc195aad2acc | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "3ee95b1c-7ca3-47b9-819c-1541a0fc4d2c",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087607266000
  },
  {
    "event_message": "PATCH | 204 | ************** | 9688cc188eeb09cf | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f | node",
    "id": "fd9b28c7-58c6-40c9-8186-bf29d9cf169b",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "PATCH",
    "path": "/rest/v1/license_activations",
    "search": "?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f",
    "status_code": 204,
    "timestamp": 1754087607130000
  },
  {
    "event_message": "GET | 200 | ************** | 9688cc182dd209cf | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9 | node",
    "id": "62f20360-4b49-44c8-ae2a-34dd07d62c0d",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/license_activations",
    "search": "?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9",
    "status_code": 200,
    "timestamp": 1754087607070000
  },
  {
    "event_message": "GET | 200 | ************** | 9688cc17bca609cf | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/licenses?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite | node",
    "id": "5713fce8-02a2-4e12-aa23-6c19045cdfdd",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/licenses",
    "search": "?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite",
    "status_code": 200,
    "timestamp": 1754087607004000
  },
  {
    "event_message": "POST | 201 | ************** | 9688cc174b7c09cf | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "bf041554-1d5e-4ef2-a567-d7aa1f0248f1",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087606930000
  },
  {
    "event_message": "GET | 200 | ************** | 9688cc16da4809cf | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "2bda2dd5-c088-4667-96ce-49f468dc7b1d",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087606862000
  },
  {
    "event_message": "GET | 200 | ************** | 9688cc16795509cf | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "fbc150d9-deab-420c-b9fe-c5785dfe95af",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087606804000
  },
  {
    "event_message": "POST | 200 | ************** | 9688cc152dc109cf | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/token?grant_type=refresh_token | node",
    "id": "af199d6b-5dbe-4b09-8f85-9f59b5398bb1",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/token",
    "search": "?grant_type=refresh_token",
    "status_code": 200,
    "timestamp": 1754087606589000
  },
  {
    "event_message": "POST | 201 | ************** | 9688caa43b4409cf | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "1a1e27ca-2ab8-400f-a655-6b3a0ec7a8cc",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087547563000
  },
  {
    "event_message": "PATCH | 204 | ************** | 9688caa3795b0fb9 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f | node",
    "id": "f2d06aa6-c2a7-4323-bf65-9d601da8d1b0",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "PATCH",
    "path": "/rest/v1/license_activations",
    "search": "?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f",
    "status_code": 204,
    "timestamp": 1754087547451000
  },
  {
    "event_message": "GET | 200 | ************** | 9688caa318ae0fb9 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9 | node",
    "id": "b6ecb48e-1ecc-41c3-8273-3904fc0d0a2e",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/license_activations",
    "search": "?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9",
    "status_code": 200,
    "timestamp": 1754087547390000
  },
  {
    "event_message": "GET | 200 | ************** | 9688caa26fc80fb9 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/licenses?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite | node",
    "id": "978fdaad-4314-4721-b454-7162426b5270",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/licenses",
    "search": "?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite",
    "status_code": 200,
    "timestamp": 1754087547284000
  },
  {
    "event_message": "POST | 201 | ************** | 9688caa1cf2c0fb9 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "8ab4bbb5-ed84-4314-a815-977dd5baecc2",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087547174000
  },
  {
    "event_message": "GET | 200 | ************** | 9688caa14ea50fb9 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "18423a5d-1b63-45c5-bc51-b65d0cab1969",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087547092000
  },
  {
    "event_message": "GET | 200 | ************** | 9688caa0ae220fb9 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "aee0498c-2a81-47e8-91c0-1b4eb30f3377",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087547004000
  },
  {
    "event_message": "POST | 200 | ************** | 9688ca9eec340fb9 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/token?grant_type=refresh_token | node",
    "id": "77e45bd9-b58f-46aa-837e-d683581e22aa",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/token",
    "search": "?grant_type=refresh_token",
    "status_code": 200,
    "timestamp": 1754087546745000
  },
  {
    "event_message": "POST | 201 | ************** | 9688c9af8f770fb9 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "e8a18148-4c3d-403a-b7f8-74cb77d56b95",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087508412000
  },
  {
    "event_message": "PATCH | 204 | ************** | 9688c9ae7c4c2acc | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f | node",
    "id": "e22ce5e6-1564-4ffe-9b48-63cb428c4642",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "PATCH",
    "path": "/rest/v1/license_activations",
    "search": "?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f",
    "status_code": 204,
    "timestamp": 1754087508245000
  },
  {
    "event_message": "GET | 200 | ************** | 9688c9adeb9a2acc | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9 | node",
    "id": "62079114-9986-4d9a-8f11-407f6d00589b",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/license_activations",
    "search": "?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9",
    "status_code": 200,
    "timestamp": 1754087508155000
  },
  {
    "event_message": "GET | 200 | ************** | 9688c9ad2ada2acc | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/licenses?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite | node",
    "id": "8d48e456-1c32-4d7e-a4a0-fc649f8a0215",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/licenses",
    "search": "?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite",
    "status_code": 200,
    "timestamp": 1754087508042000
  },
  {
    "event_message": "POST | 201 | ************** | 9688c9ab69202acc | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "a075b464-7c6e-4835-a64e-5587d45a033e",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087507763000
  },
  {
    "event_message": "GET | 200 | ************** | 9688c9ab08b72acc | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "c87023a5-49c4-4e72-bd37-0b9722faa7a1",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087507695000
  },
  {
    "event_message": "GET | 200 | ************** | 9688c9aa5fe22acc | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "8588af9f-41d7-435b-87a1-2ffae6c998cb",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087507585000
  },
  {
    "event_message": "POST | 200 | ************** | 9688c9a84def2acc | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/token?grant_type=refresh_token | node",
    "id": "b6544042-7e5f-48c7-9597-7b63ca403fe1",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/token",
    "search": "?grant_type=refresh_token",
    "status_code": 200,
    "timestamp": 1754087507259000
  },
  {
    "event_message": "POST | 500 | ************ | 9688c8a2fdb74743 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users | node",
    "id": "20e50259-0186-424d-88f3-************",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/admin/users",
    "search": null,
    "status_code": 500,
    "timestamp": ****************
  },
  {
    "event_message": "GET | 200 | ************ | 9688c8a19bec4743 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users?page=&per_page= | node",
    "id": "924b9bcb-c81c-4096-a272-3034aa6572f6",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/admin/users",
    "search": "?page=&per_page=",
    "status_code": 200,
    "timestamp": 1754087465228000
  },
  {
    "event_message": "POST | 500 | ************ | 9688c89f28954743 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users | node",
    "id": "ece308ac-4d09-476e-ad13-c23a9cb20ac1",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/admin/users",
    "search": null,
    "status_code": 500,
    "timestamp": ****************
  },
  {
    "event_message": "GET | 200 | ************ | 9688c89dbebd4743 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users?page=&per_page= | node",
    "id": "ef2fb09a-692a-4274-982b-f2b298c3b4fc",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/admin/users",
    "search": "?page=&per_page=",
    "status_code": 200,
    "timestamp": 1754087464616000
  },
  {
    "event_message": "GET | 200 | ************ | 9688c89bac344743 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/profiles?select=id&stripe_customer_id=eq.cus_Sn1O9qeQqN4yEr | node",
    "id": "c0662857-c0b6-4cf9-97ea-5f47d224e721",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/profiles",
    "search": "?select=id&stripe_customer_id=eq.cus_Sn1O9qeQqN4yEr",
    "status_code": 200,
    "timestamp": 1754087464282000
  },
  {
    "event_message": "POST | 201 | ************ | 9688c89acb244743 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/webhook_events | node",
    "id": "11564110-1ebc-4954-811f-8866d433e24b",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/webhook_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087464141000
  },
  {
    "event_message": "GET | 200 | ************ | 9688c8996e1da918 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/webhook_events?select=id&stripe_event_id=eq.evt_3RrRLrE6FvhUKV1b1jJdAKYr | node",
    "id": "8cfc7c26-38ed-4706-8425-3c5463234e88",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/webhook_events",
    "search": "?select=id&stripe_event_id=eq.evt_3RrRLrE6FvhUKV1b1jJdAKYr",
    "status_code": 200,
    "timestamp": 1754087463911000
  },
  {
    "event_message": "POST | 500 | ************ | 9688c8989fde4743 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users | node",
    "id": "27ddeff9-38a5-4401-8b27-5efa3077834f",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/admin/users",
    "search": null,
    "status_code": 500,
    "timestamp": 1754087463787000
  },
  {
    "event_message": "GET | 200 | ************ | 9688c8961c8e4743 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users?page=&per_page= | node",
    "id": "b02cda86-1b8e-49fb-9701-115047d6055b",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/admin/users",
    "search": "?page=&per_page=",
    "status_code": 200,
    "timestamp": 1754087463398000
  },
  {
    "event_message": "POST | 201 | ************** | 9688c43fae06de86 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "7b79a936-f195-41a4-bd1e-4c1425bf7e93",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087285724000
  },
  {
    "event_message": "PATCH | 204 | ************** | 9688c43eb98b2b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f | node",
    "id": "a8e1a07c-5e94-4291-afb5-966c4865f094",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "PATCH",
    "path": "/rest/v1/license_activations",
    "search": "?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f",
    "status_code": 204,
    "timestamp": 1754087285569000
  },
  {
    "event_message": "GET | 200 | ************** | 9688c43e08c82b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9 | node",
    "id": "849955d4-df57-4fe0-aa7d-1a2dca17845f",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/license_activations",
    "search": "?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9",
    "status_code": 200,
    "timestamp": 1754087285457000
  },
  {
    "event_message": "GET | 200 | ************** | 9688c43d48052b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/licenses?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite | node",
    "id": "0ef27d0f-048d-4331-a965-0939974bd9c3",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/licenses",
    "search": "?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite",
    "status_code": 200,
    "timestamp": 1754087285333000
  },
  {
    "event_message": "POST | 201 | ************** | 9688c43c8f2b2b9c | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "7b27ed18-5abe-4482-b29f-99c6b17a2201",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087285211000
  },
  {
    "event_message": "GET | 200 | ************** | 9688c43bbe552b9c | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "238ba83a-0253-4e50-9e55-39be7eb96b89",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087285093000
  },
  {
    "event_message": "GET | 200 | ************** | 9688c43aed152b9c | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "72891d2a-1411-4866-9eaa-ce9c7521cad1",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087284958000
  },
  {
    "event_message": "POST | 200 | ************** | 9688c438dabe2b9c | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/token?grant_type=refresh_token | node",
    "id": "2aa71998-f116-4483-9033-014edf68fe8f",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/token",
    "search": "?grant_type=refresh_token",
    "status_code": 200,
    "timestamp": 1754087284623000
  },
  {
    "event_message": "POST | 201 | ************** | 9688be677cf1dbe1 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "279353f7-ae72-4af7-aba4-88455deda215",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087046333000
  },
  {
    "event_message": "PATCH | 204 | ************** | 9688be668caf09cf | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f | node",
    "id": "4bba6156-dc36-4c85-a3b1-ff1ab48bcc66",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "PATCH",
    "path": "/rest/v1/license_activations",
    "search": "?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f",
    "status_code": 204,
    "timestamp": 1754087046171000
  },
  {
    "event_message": "GET | 200 | ************** | 9688be65fafd09cf | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9 | node",
    "id": "da365e96-6015-4e68-a99b-6f00dd9e008d",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/license_activations",
    "search": "?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9",
    "status_code": 200,
    "timestamp": 1754087046086000
  },
  {
    "event_message": "GET | 200 | ************** | 9688be65898c09cf | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/licenses?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite | node",
    "id": "fe070e0e-88ed-4953-a88a-ed88e716d3f8",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/licenses",
    "search": "?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite",
    "status_code": 200,
    "timestamp": 1754087046009000
  },
  {
    "event_message": "POST | 201 | ************** | 9688be649f1909cf | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "7c8b6955-6a67-4856-9377-3c178dd6621e",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754087045866000
  },
  {
    "event_message": "GET | 200 | ************** | 9688be642dbf09cf | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "c31c53b8-7dd5-43bb-82e4-d9d52e5f646e",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087045793000
  },
  {
    "event_message": "GET | 200 | ************** | 9688be63bc9509cf | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "a84b0bca-15ca-4205-9217-acafcc9c13bf",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754087045719000
  },
  {
    "event_message": "POST | 200 | ************** | 9688be617ea809cf | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/token?grant_type=refresh_token | node",
    "id": "7c8709f5-995d-409d-a739-287cec0ec6b9",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/token",
    "search": "?grant_type=refresh_token",
    "status_code": 200,
    "timestamp": 1754087045374000
  },
  {
    "event_message": "POST | 500 | ************ | 9688bcd1a827a918 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users | node",
    "id": "1f2275c6-fe3d-4559-9e54-037b3dc19801",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/admin/users",
    "search": null,
    "status_code": 500,
    "timestamp": 1754086981399000
  },
  {
    "event_message": "GET | 200 | ************ | 9688bcd11e21a918 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/profiles?select=id&email=eq.test002_08125%40gmail.com | node",
    "id": "3dcbc3e3-371b-48e6-a3a2-7558056998da",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/profiles",
    "search": "?select=id&email=eq.test002_08125%40gmail.com",
    "status_code": 200,
    "timestamp": 1754086981306000
  },
  {
    "event_message": "GET | 200 | ************ | 9688bcd06bdda918 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/profiles?select=id&stripe_customer_id=eq.cus_Sn1O9qeQqN4yEr | node",
    "id": "56a3a2d9-183c-4ee2-9b44-1193735b6360",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/profiles",
    "search": "?select=id&stripe_customer_id=eq.cus_Sn1O9qeQqN4yEr",
    "status_code": 200,
    "timestamp": 1754086981194000
  },
  {
    "event_message": "POST | 201 | ************ | 9688bcccf806a918 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/webhook_events | node",
    "id": "021f2553-eb66-4da1-b71c-10cbcf6a7324",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/webhook_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754086980640000
  },
  {
    "event_message": "GET | 200 | ************ | 9688bcca7f26a918 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/webhook_events?select=id&stripe_event_id=eq.evt_1RrRLrE6FvhUKV1bpOj6NOb7 | node",
    "id": "2dcc5bc1-4174-4a98-bac5-3979b3eac221",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/webhook_events",
    "search": "?select=id&stripe_event_id=eq.evt_1RrRLrE6FvhUKV1bpOj6NOb7",
    "status_code": 200,
    "timestamp": 1754086980241000
  },
  {
    "event_message": "POST | 201 | ************** | 96888537fcaf7c49 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "c6a0a582-1701-469e-a552-48ee30b075a4",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754084704006000
  },
  {
    "event_message": "PATCH | 204 | ************** | 96888537299b135e | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f | node",
    "id": "1c6911d3-cc7c-4645-a1eb-5fb2018dd824",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "PATCH",
    "path": "/rest/v1/license_activations",
    "search": "?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f",
    "status_code": 204,
    "timestamp": 1754084703871000
  },
  {
    "event_message": "GET | 200 | ************** | 96888536a86d135e | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9 | node",
    "id": "23536e2f-2535-423a-953a-9763be0175cc",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/license_activations",
    "search": "?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9",
    "status_code": 200,
    "timestamp": 1754084703793000
  },
  {
    "event_message": "GET | 200 | ************** | 968885361f05135e | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/licenses?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite | node",
    "id": "5e27d1d3-a1d2-44a6-a7ba-8faa8173d498",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/licenses",
    "search": "?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite",
    "status_code": 200,
    "timestamp": 1754084703708000
  },
  {
    "event_message": "POST | 201 | ************** | 96888534fbd7135e | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "75297abd-593b-4737-9865-b65e30c6d497",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754084703521000
  },
  {
    "event_message": "GET | 200 | ************** | 968885347a90135e | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "dedfe1fc-46b9-4050-bcb5-91e37cfbe1fe",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754084703438000
  },
  {
    "event_message": "GET | 200 | ************** | 96888533e924135e | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "30047a15-69d2-4377-91a0-786bd7fd46e8",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754084703352000
  },
  {
    "event_message": "POST | 200 | ************** | 968885314a00135e | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/token?grant_type=refresh_token | node",
    "id": "74a6d1ca-5147-4732-a717-544dad06caf8",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/token",
    "search": "?grant_type=refresh_token",
    "status_code": 200,
    "timestamp": 1754084702937000
  },
  {
    "event_message": "POST | 201 | ************** | 96887f2b3a3208ea | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "d9c22ec7-9b92-43ea-9a36-22345fefa206",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754084456219000
  },
  {
    "event_message": "PATCH | 204 | ************** | 96887f2a2c472f58 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f | node",
    "id": "75f12ebe-0960-4cf3-a1d2-5b931e1dda7a",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "PATCH",
    "path": "/rest/v1/license_activations",
    "search": "?id=eq.902fdff1-1bc8-421e-93bb-d5d5bafa858f",
    "status_code": 204,
    "timestamp": 1754084456048000
  },
  {
    "event_message": "GET | 200 | ************** | 96887f297b602f58 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_activations?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9 | node",
    "id": "a2f1d5b3-6116-4592-9e0e-1b4a1dc1ec7f",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/license_activations",
    "search": "?select=id%2Cis_active&license_id=eq.10423a33-4fa2-4f2b-a39b-8e54d01896f7&machine_id=eq.0ef8331b026f0e5ec8089d7585b13dc6f56e5d5c16e6d870750ef31c6cd225c9",
    "status_code": 200,
    "timestamp": 1754084455926000
  },
  {
    "event_message": "GET | 200 | ************** | 96887f28fad82f58 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/licenses?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite | node",
    "id": "dfc451e7-faba-421c-9822-c57cbb8f6fab",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/rest/v1/licenses",
    "search": "?select=*&user_id=eq.faa048a8-1e7f-4d53-92ad-ab0b8842b5c7&product_id=eq.quantboost-suite",
    "status_code": 200,
    "timestamp": 1754084455852000
  },
  {
    "event_message": "POST | 201 | ************** | 96887f2849b52f58 | https://izoutrnsxaaoueljiimu.supabase.co/rest/v1/license_events | node",
    "id": "84a8c670-ee0f-4e9c-9a38-2e90877de31a",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/rest/v1/license_events",
    "search": null,
    "status_code": 201,
    "timestamp": 1754084455737000
  },
  {
    "event_message": "GET | 200 | ************** | 96887f27a8e42f58 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "e1394349-98d9-4deb-8247-370f32678d64",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754084455641000
  },
  {
    "event_message": "GET | 200 | ************** | 96887f26e8182f58 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/user | node",
    "id": "1f67862b-b2e6-4c4b-9ce0-b3fa64ec2431",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "GET",
    "path": "/auth/v1/user",
    "search": null,
    "status_code": 200,
    "timestamp": 1754084455516000
  },
  {
    "event_message": "POST | 200 | ************** | 96887f24dde52f58 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/token?grant_type=refresh_token | node",
    "id": "6b89adcc-9c06-44b6-afbd-7678f7407851",
    "identifier": "izoutrnsxaaoueljiimu",
    "method": "POST",
    "path": "/auth/v1/token",
    "search": "?grant_type=refresh_token",
    "status_code": 200,
    "timestamp": ****************
  }
]

#500 error details
{
  "event_message": "POST | 500 | ************ | 9688cd7d0cb9de86 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/otp?redirect_to=https%3A%2F%2Fapp-quantboost-frontend-staging.azurewebsites.net%2Fdashboard%3Fpayment%3Dsuccess | Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "id": "54cede5f-58de-4407-a32f-7d294d61dff8",
  "metadata": [
    {
      "load_balancer_experimental_routing": null,
      "load_balancer_geo_aware_info": [],
      "load_balancer_redirect_identifier": null,
      "logflare_worker": [
        {
          "worker_id": "H217FB"
        }
      ],
      "request": [
        {
          "cf": [
            {
              "asOrganization": "Charter Communications LLC",
              "asn": 20115,
              "botManagement": [
                {
                  "corporateProxy": false,
                  "detectionIds": [],
                  "ja3Hash": "838cd5fffd7a09601c8c637377886097",
                  "ja4": "q13d0312h3_55b375c5d22e_5a06198afb93",
                  "ja4Signals": [
                    {
                      "browser_ratio_1h": 0.**************,
                      "cache_ratio_1h": 0.**************,
                      "h2h3_ratio_1h": 1,
                      "heuristic_ratio_1h": 0.****************,
                      "ips_quantile_1h": 0.**************,
                      "ips_rank_1h": 9,
                      "paths_rank_1h": 10,
                      "reqs_quantile_1h": 0.**************,
                      "reqs_rank_1h": 9,
                      "uas_rank_1h": 21
                    }
                  ],
                  "jsDetection": [
                    {
                      "passed": false
                    }
                  ],
                  "score": 99,
                  "staticResource": false,
                  "verifiedBot": false
                }
              ],
              "city": "Pasadena",
              "clientAcceptEncoding": "gzip, deflate, br",
              "clientTcpRtt": null,
              "clientTrustScore": 99,
              "colo": "LAX",
              "continent": "NA",
              "country": "US",
              "edgeRequestKeepAliveStatus": 1,
              "httpProtocol": "HTTP/3",
              "isEUCountry": null,
              "latitude": "34.14778",
              "longitude": "-118.14452",
              "metroCode": null,
              "postalCode": "91101",
              "region": "California",
              "regionCode": "CA",
              "requestPriority": null,
              "timezone": "America/Los_Angeles",
              "tlsCipher": "AEAD-AES128-GCM-SHA256",
              "tlsClientAuth": [
                {
                  "certPresented": "0",
                  "certRevoked": "0",
                  "certVerified": "NONE"
                }
              ],
              "tlsClientCiphersSha1": "3HTt3+R/6BL3zeALJDSq0pR1yOQ=",
              "tlsClientExtensionsSha1": "+c9rBi73XHkYRd1eNS40r92hmHI=",
              "tlsClientExtensionsSha1Le": "Ib1FuhTYaViDh/0oidP32Ra07bk=",
              "tlsClientHelloLength": "1992",
              "tlsClientRandom": "IG4kKo+Qmgqf/qUggIECWVERYvaOCYOpzsDAMqmCDYk=",
              "tlsExportedAuthenticator": [
                {
                  "clientFinished": "a7e3b80312e82951b9a4a944069893b81d2c9eeb6cab82c7eb591c2d378895af",
                  "clientHandshake": "39c96cef7f64b8ee0b3f8b1c9cf5fe9d45ae6083fe80ce6f7ada4deebc4af6bf",
                  "serverFinished": "214d702538e7da79ac66a8fb0e5d0d2ddcc5d722519030440406700da2522b1c",
                  "serverHandshake": "299872b6c6f0890772b5681fd41926f6f55e383726faf0ffd557270a7ef33f1b"
                }
              ],
              "tlsVersion": "TLSv1.3",
              "verifiedBotCategory": null
            }
          ],
          "headers": [
            {
              "accept": "*/*",
              "cf_cache_status": null,
              "cf_connecting_ip": "************",
              "cf_ipcountry": "US",
              "cf_ray": "9688cd7d0cb9de86",
              "content_length": "184",
              "content_location": null,
              "content_range": null,
              "content_type": "application/json;charset=UTF-8",
              "date": null,
              "host": "izoutrnsxaaoueljiimu.supabase.co",
              "prefer": null,
              "range": null,
              "referer": "https://app-quantboost-frontend-staging.azurewebsites.net/",
              "sb_gateway_mode": null,
              "sb_gateway_version": null,
              "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
              "x_client_info": "supabase-ssr/0.5.2",
              "x_forwarded_host": null,
              "x_forwarded_proto": "https",
              "x_forwarded_user_agent": null,
              "x_kong_proxy_latency": null,
              "x_kong_upstream_latency": null,
              "x_real_ip": "************"
            }
          ],
          "host": "izoutrnsxaaoueljiimu.supabase.co",
          "method": "POST",
          "path": "/auth/v1/otp",
          "port": null,
          "protocol": "https:",
          "sb": [
            {
              "apikey": [
                {
                  "authorization": [
                    {
                      "error": null,
                      "prefix": "sb_publishable_3ZZ_2"
                    }
                  ]
                }
              ],
              "auth_user": null,
              "jwt": []
            }
          ],
          "search": "?redirect_to=https%3A%2F%2Fapp-quantboost-frontend-staging.azurewebsites.net%2Fdashboard%3Fpayment%3Dsuccess",
          "url": "https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/otp?redirect_to=https%3A%2F%2Fapp-quantboost-frontend-staging.azurewebsites.net%2Fdashboard%3Fpayment%3Dsuccess"
        }
      ],
      "response": [
        {
          "headers": [
            {
              "cf_cache_status": "DYNAMIC",
              "cf_ray": "9688cd7d3171de86-LAX",
              "content_length": "72",
              "content_location": null,
              "content_range": null,
              "content_type": "application/json",
              "date": "Fri, 01 Aug 2025 22:34:24 GMT",
              "sb_gateway_mode": null,
              "sb_gateway_version": "1",
              "transfer_encoding": null,
              "x_kong_proxy_latency": null,
              "x_kong_upstream_latency": null,
              "x_sb_error_code": "unexpected_failure"
            }
          ],
          "origin_time": 145,
          "status_code": 500
        }
      ]
    }
  ],
  "timestamp": ****************
}

#500 error 2:
{
  "event_message": "POST | 500 | ************ | 9688c8a2fdb74743 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users | node",
  "id": "20e50259-0186-424d-88f3-************",
  "metadata": [
    {
      "load_balancer_experimental_routing": null,
      "load_balancer_geo_aware_info": [],
      "load_balancer_redirect_identifier": null,
      "logflare_worker": [
        {
          "worker_id": "Q3BJF8"
        }
      ],
      "request": [
        {
          "cf": [
            {
              "asOrganization": "Microsoft Corporation",
              "asn": 8075,
              "botManagement": [
                {
                  "corporateProxy": false,
                  "detectionIds": [],
                  "ja3Hash": "70cb5ca646080902703ffda87036a5ea",
                  "ja4": "t13d5912h1_a33745022dd6_dbd39dd1d406",
                  "ja4Signals": [
                    {
                      "browser_ratio_1h": 0.***************,
                      "cache_ratio_1h": 0.***************,
                      "h2h3_ratio_1h": 0.**************,
                      "heuristic_ratio_1h": 0.***************,
                      "ips_quantile_1h": 0.**************,
                      "ips_rank_1h": 207,
                      "paths_rank_1h": 87,
                      "reqs_quantile_1h": 0.99982273578644,
                      "reqs_rank_1h": 64,
                      "uas_rank_1h": 142
                    }
                  ],
                  "jsDetection": [
                    {
                      "passed": false
                    }
                  ],
                  "score": 4,
                  "staticResource": false,
                  "verifiedBot": false
                }
              ],
              "city": "Des Moines",
              "clientAcceptEncoding": "gzip, deflate, br",
              "clientTcpRtt": 19,
              "clientTrustScore": 4,
              "colo": "DFW",
              "continent": "NA",
              "country": "US",
              "edgeRequestKeepAliveStatus": 1,
              "httpProtocol": "HTTP/1.1",
              "isEUCountry": null,
              "latitude": "41.60054",
              "longitude": "-93.60911",
              "metroCode": null,
              "postalCode": "50307",
              "region": "Iowa",
              "regionCode": "IA",
              "requestPriority": null,
              "timezone": "America/Chicago",
              "tlsCipher": "AEAD-AES256-GCM-SHA384",
              "tlsClientAuth": [
                {
                  "certPresented": "0",
                  "certRevoked": "0",
                  "certVerified": "NONE"
                }
              ],
              "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=",
              "tlsClientExtensionsSha1": "GWeb1cCR2UBICwtIDbeP9YjL/PU=",
              "tlsClientExtensionsSha1Le": "LddRTv85Gcz7xx7AQg+t+GZR5bs=",
              "tlsClientHelloLength": "667",
              "tlsClientRandom": "peg7xsTIn6maYwQOk0vy0Q7TzfFR0sQPQkJlEk+7Vxw=",
              "tlsExportedAuthenticator": [
                {
                  "clientFinished": "8e21445ebc36d53d397420af6d70e0c0e4b40be11f48b1c9ce48dfb686206e301d2bf30b7139ccb75370103c32a594e2",
                  "clientHandshake": "2ec10b9acd7b7d467072a9c9fb78ffc20a59ebacc596ea4fdcf762428152429f268fe1d1d365e0c72c8339f1353eb91a",
                  "serverFinished": "65e3a495ca53b0186f54dfc5a2abafd83fa8a9271c55e33f39f237149ff7b7821ba9b3d8eb028b6190d091eef2778797",
                  "serverHandshake": "1a53d989f9fa546b53cb24dd64a566e148e60d589f1e223edff94f166c139e587100823ed8313e6cef2b99d4c6a31931"
                }
              ],
              "tlsVersion": "TLSv1.3",
              "verifiedBotCategory": null
            }
          ],
          "headers": [
            {
              "accept": "*/*",
              "cf_cache_status": null,
              "cf_connecting_ip": "************",
              "cf_ipcountry": "US",
              "cf_ray": "9688c8a2fdb74743",
              "content_length": "197",
              "content_location": null,
              "content_range": null,
              "content_type": "application/json;charset=UTF-8",
              "date": null,
              "host": "izoutrnsxaaoueljiimu.supabase.co",
              "prefer": null,
              "range": null,
              "referer": null,
              "sb_gateway_mode": null,
              "sb_gateway_version": null,
              "user_agent": "node",
              "x_client_info": "supabase-js-node/2.49.4",
              "x_forwarded_host": null,
              "x_forwarded_proto": "https",
              "x_forwarded_user_agent": null,
              "x_kong_proxy_latency": null,
              "x_kong_upstream_latency": null,
              "x_real_ip": "************"
            }
          ],
          "host": "izoutrnsxaaoueljiimu.supabase.co",
          "method": "POST",
          "path": "/auth/v1/admin/users",
          "port": null,
          "protocol": "https:",
          "sb": [
            {
              "apikey": [
                {
                  "authorization": [
                    {
                      "error": null,
                      "prefix": "sb_secret_pL6hG"
                    }
                  ]
                }
              ],
              "auth_user": null,
              "jwt": []
            }
          ],
          "search": null,
          "url": "https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users"
        }
      ],
      "response": [
        {
          "headers": [
            {
              "cf_cache_status": "DYNAMIC",
              "cf_ray": "9688c8a317964743-DFW",
              "content_length": "74",
              "content_location": null,
              "content_range": null,
              "content_type": "application/json",
              "date": "Fri, 01 Aug 2025 22:31:05 GMT",
              "sb_gateway_mode": null,
              "sb_gateway_version": "1",
              "transfer_encoding": null,
              "x_kong_proxy_latency": null,
              "x_kong_upstream_latency": null,
              "x_sb_error_code": "unexpected_failure"
            }
          ],
          "origin_time": 232,
          "status_code": 500
        }
      ]
    }
  ],
  "timestamp": ****************
}

#500 error 3:
{
  "event_message": "POST | 500 | ************ | 9688c89f28954743 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users | node",
  "id": "ece308ac-4d09-476e-ad13-c23a9cb20ac1",
  "metadata": [
    {
      "load_balancer_experimental_routing": null,
      "load_balancer_geo_aware_info": [],
      "load_balancer_redirect_identifier": null,
      "logflare_worker": [
        {
          "worker_id": "Q3BJF8"
        }
      ],
      "request": [
        {
          "cf": [
            {
              "asOrganization": "Microsoft Corporation",
              "asn": 8075,
              "botManagement": [
                {
                  "corporateProxy": false,
                  "detectionIds": [],
                  "ja3Hash": "70cb5ca646080902703ffda87036a5ea",
                  "ja4": "t13d5912h1_a33745022dd6_dbd39dd1d406",
                  "ja4Signals": [
                    {
                      "browser_ratio_1h": 0.***************,
                      "cache_ratio_1h": 0.***************,
                      "h2h3_ratio_1h": 0.**************,
                      "heuristic_ratio_1h": 0.***************,
                      "ips_quantile_1h": 0.**************,
                      "ips_rank_1h": 207,
                      "paths_rank_1h": 87,
                      "reqs_quantile_1h": 0.99982273578644,
                      "reqs_rank_1h": 64,
                      "uas_rank_1h": 142
                    }
                  ],
                  "jsDetection": [
                    {
                      "passed": false
                    }
                  ],
                  "score": 5,
                  "staticResource": false,
                  "verifiedBot": false
                }
              ],
              "city": "Des Moines",
              "clientAcceptEncoding": "gzip, deflate, br",
              "clientTcpRtt": 18,
              "clientTrustScore": 5,
              "colo": "DFW",
              "continent": "NA",
              "country": "US",
              "edgeRequestKeepAliveStatus": 1,
              "httpProtocol": "HTTP/1.1",
              "isEUCountry": null,
              "latitude": "41.60054",
              "longitude": "-93.60911",
              "metroCode": null,
              "postalCode": "50307",
              "region": "Iowa",
              "regionCode": "IA",
              "requestPriority": null,
              "timezone": "America/Chicago",
              "tlsCipher": "AEAD-AES256-GCM-SHA384",
              "tlsClientAuth": [
                {
                  "certPresented": "0",
                  "certRevoked": "0",
                  "certVerified": "NONE"
                }
              ],
              "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=",
              "tlsClientExtensionsSha1": "GWeb1cCR2UBICwtIDbeP9YjL/PU=",
              "tlsClientExtensionsSha1Le": "LddRTv85Gcz7xx7AQg+t+GZR5bs=",
              "tlsClientHelloLength": "667",
              "tlsClientRandom": "peg7xsTIn6maYwQOk0vy0Q7TzfFR0sQPQkJlEk+7Vxw=",
              "tlsExportedAuthenticator": [
                {
                  "clientFinished": "8e21445ebc36d53d397420af6d70e0c0e4b40be11f48b1c9ce48dfb686206e301d2bf30b7139ccb75370103c32a594e2",
                  "clientHandshake": "2ec10b9acd7b7d467072a9c9fb78ffc20a59ebacc596ea4fdcf762428152429f268fe1d1d365e0c72c8339f1353eb91a",
                  "serverFinished": "65e3a495ca53b0186f54dfc5a2abafd83fa8a9271c55e33f39f237149ff7b7821ba9b3d8eb028b6190d091eef2778797",
                  "serverHandshake": "1a53d989f9fa546b53cb24dd64a566e148e60d589f1e223edff94f166c139e587100823ed8313e6cef2b99d4c6a31931"
                }
              ],
              "tlsVersion": "TLSv1.3",
              "verifiedBotCategory": null
            }
          ],
          "headers": [
            {
              "accept": "*/*",
              "cf_cache_status": null,
              "cf_connecting_ip": "************",
              "cf_ipcountry": "US",
              "cf_ray": "9688c89f28954743",
              "content_length": "197",
              "content_location": null,
              "content_range": null,
              "content_type": "application/json;charset=UTF-8",
              "date": null,
              "host": "izoutrnsxaaoueljiimu.supabase.co",
              "prefer": null,
              "range": null,
              "referer": null,
              "sb_gateway_mode": null,
              "sb_gateway_version": null,
              "user_agent": "node",
              "x_client_info": "supabase-js-node/2.49.4",
              "x_forwarded_host": null,
              "x_forwarded_proto": "https",
              "x_forwarded_user_agent": null,
              "x_kong_proxy_latency": null,
              "x_kong_upstream_latency": null,
              "x_real_ip": "************"
            }
          ],
          "host": "izoutrnsxaaoueljiimu.supabase.co",
          "method": "POST",
          "path": "/auth/v1/admin/users",
          "port": null,
          "protocol": "https:",
          "sb": [
            {
              "apikey": [
                {
                  "authorization": [
                    {
                      "error": null,
                      "prefix": "sb_secret_pL6hG"
                    }
                  ]
                }
              ],
              "auth_user": null,
              "jwt": []
            }
          ],
          "search": null,
          "url": "https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users"
        }
      ],
      "response": [
        {
          "headers": [
            {
              "cf_cache_status": "DYNAMIC",
              "cf_ray": "9688c89f55f74743-DFW",
              "content_length": "74",
              "content_location": null,
              "content_range": null,
              "content_type": "application/json",
              "date": "Fri, 01 Aug 2025 22:31:05 GMT",
              "sb_gateway_mode": null,
              "sb_gateway_version": "1",
              "transfer_encoding": null,
              "x_kong_proxy_latency": null,
              "x_kong_upstream_latency": null,
              "x_sb_error_code": "unexpected_failure"
            }
          ],
          "origin_time": 225,
          "status_code": 500
        }
      ]
    }
  ],
  "timestamp": ****************
}

#500 error 4:

{
  "event_message": "POST | 500 | ************ | 9688c8989fde4743 | https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users | node",
  "id": "27ddeff9-38a5-4401-8b27-5efa3077834f",
  "metadata": [
    {
      "load_balancer_experimental_routing": null,
      "load_balancer_geo_aware_info": [],
      "load_balancer_redirect_identifier": null,
      "logflare_worker": [
        {
          "worker_id": "Q3BJF8"
        }
      ],
      "request": [
        {
          "cf": [
            {
              "asOrganization": "Microsoft Corporation",
              "asn": 8075,
              "botManagement": [
                {
                  "corporateProxy": false,
                  "detectionIds": [],
                  "ja3Hash": "70cb5ca646080902703ffda87036a5ea",
                  "ja4": "t13d5912h1_a33745022dd6_dbd39dd1d406",
                  "ja4Signals": [
                    {
                      "browser_ratio_1h": 0.***************,
                      "cache_ratio_1h": 0.***************,
                      "h2h3_ratio_1h": 0.**************,
                      "heuristic_ratio_1h": 0.***************,
                      "ips_quantile_1h": 0.**************,
                      "ips_rank_1h": 207,
                      "paths_rank_1h": 87,
                      "reqs_quantile_1h": 0.99982273578644,
                      "reqs_rank_1h": 64,
                      "uas_rank_1h": 142
                    }
                  ],
                  "jsDetection": [
                    {
                      "passed": false
                    }
                  ],
                  "score": 2,
                  "staticResource": false,
                  "verifiedBot": false
                }
              ],
              "city": "Des Moines",
              "clientAcceptEncoding": "gzip, deflate, br",
              "clientTcpRtt": 14,
              "clientTrustScore": 2,
              "colo": "DFW",
              "continent": "NA",
              "country": "US",
              "edgeRequestKeepAliveStatus": 1,
              "httpProtocol": "HTTP/1.1",
              "isEUCountry": null,
              "latitude": "41.60054",
              "longitude": "-93.60911",
              "metroCode": null,
              "postalCode": "50307",
              "region": "Iowa",
              "regionCode": "IA",
              "requestPriority": null,
              "timezone": "America/Chicago",
              "tlsCipher": "AEAD-AES256-GCM-SHA384",
              "tlsClientAuth": [
                {
                  "certPresented": "0",
                  "certRevoked": "0",
                  "certVerified": "NONE"
                }
              ],
              "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=",
              "tlsClientExtensionsSha1": "GWeb1cCR2UBICwtIDbeP9YjL/PU=",
              "tlsClientExtensionsSha1Le": "LddRTv85Gcz7xx7AQg+t+GZR5bs=",
              "tlsClientHelloLength": "667",
              "tlsClientRandom": "peg7xsTIn6maYwQOk0vy0Q7TzfFR0sQPQkJlEk+7Vxw=",
              "tlsExportedAuthenticator": [
                {
                  "clientFinished": "8e21445ebc36d53d397420af6d70e0c0e4b40be11f48b1c9ce48dfb686206e301d2bf30b7139ccb75370103c32a594e2",
                  "clientHandshake": "2ec10b9acd7b7d467072a9c9fb78ffc20a59ebacc596ea4fdcf762428152429f268fe1d1d365e0c72c8339f1353eb91a",
                  "serverFinished": "65e3a495ca53b0186f54dfc5a2abafd83fa8a9271c55e33f39f237149ff7b7821ba9b3d8eb028b6190d091eef2778797",
                  "serverHandshake": "1a53d989f9fa546b53cb24dd64a566e148e60d589f1e223edff94f166c139e587100823ed8313e6cef2b99d4c6a31931"
                }
              ],
              "tlsVersion": "TLSv1.3",
              "verifiedBotCategory": null
            }
          ],
          "headers": [
            {
              "accept": "*/*",
              "cf_cache_status": null,
              "cf_connecting_ip": "************",
              "cf_ipcountry": "US",
              "cf_ray": "9688c8989fde4743",
              "content_length": "197",
              "content_location": null,
              "content_range": null,
              "content_type": "application/json;charset=UTF-8",
              "date": null,
              "host": "izoutrnsxaaoueljiimu.supabase.co",
              "prefer": null,
              "range": null,
              "referer": null,
              "sb_gateway_mode": null,
              "sb_gateway_version": null,
              "user_agent": "node",
              "x_client_info": "supabase-js-node/2.49.4",
              "x_forwarded_host": null,
              "x_forwarded_proto": "https",
              "x_forwarded_user_agent": null,
              "x_kong_proxy_latency": null,
              "x_kong_upstream_latency": null,
              "x_real_ip": "************"
            }
          ],
          "host": "izoutrnsxaaoueljiimu.supabase.co",
          "method": "POST",
          "path": "/auth/v1/admin/users",
          "port": null,
          "protocol": "https:",
          "sb": [
            {
              "apikey": [
                {
                  "authorization": [
                    {
                      "error": null,
                      "prefix": "sb_secret_pL6hG"
                    }
                  ]
                }
              ],
              "auth_user": null,
              "jwt": []
            }
          ],
          "search": null,
          "url": "https://izoutrnsxaaoueljiimu.supabase.co/auth/v1/admin/users"
        }
      ],
      "response": [
        {
          "headers": [
            {
              "cf_cache_status": "DYNAMIC",
              "cf_ray": "9688c898b3ce4743-DFW",
              "content_length": "74",
              "content_location": null,
              "content_range": null,
              "content_type": "application/json",
              "date": "Fri, 01 Aug 2025 22:31:04 GMT",
              "sb_gateway_mode": null,
              "sb_gateway_version": "1",
              "transfer_encoding": null,
              "x_kong_proxy_latency": null,
              "x_kong_upstream_latency": null,
              "x_sb_error_code": "unexpected_failure"
            }
          ],
          "origin_time": 259,
          "status_code": 500
        }
      ]
    }
  ],
  "timestamp": 1754087463787000
}