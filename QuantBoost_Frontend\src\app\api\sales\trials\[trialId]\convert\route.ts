import { NextRequest } from 'next/server';
import { getSupabaseAdmin, getAppBaseUrl } from '@/lib/supabaseServer';
import { convertSchema } from '@/lib/salesTrialsValidation';
import { getStripe, createCheckoutSession } from '@/lib/stripe/checkout';

export async function POST(req: NextRequest, ctx: { params: Promise<{ trialId: string }> }) {
  const body = await req.json();
  const parsed = convertSchema.safeParse(body);
  if (!parsed.success) return Response.json({ error: parsed.error.flatten() }, { status: 400 });

  const { priceId, quantity } = parsed.data;
  const supa = getSupabaseAdmin();
  const { trialId } = await ctx.params;

  // Load trial + customer
  const { data: trial, error } = await supa
    .from('enterprise_trials')
    .select('*, enterprise_customers:customer_id ( id, company_name, contact_email, stripe_customer_id )')
    .eq('id', trialId)
    .single();
  if (error) return Response.json({ error: 'Trial not found' }, { status: 404 });

  const customer = trial.enterprise_customers;
  if (!customer) return Response.json({ error: 'Customer missing' }, { status: 400 });

  // Ensure Stripe customer exists
  let stripeCustomerId: string | null = customer.stripe_customer_id || null;
  const stripe = getStripe();
  if (!stripeCustomerId) {
    const created = await stripe.customers.create({
      email: customer.contact_email || undefined,
      name: customer.company_name,
      metadata: { enterprise_customer_id: customer.id }
    });
    stripeCustomerId = created.id;
    await supa.from('enterprise_customers').update({ stripe_customer_id: stripeCustomerId }).eq('id', customer.id);
  }

  const base = getAppBaseUrl();
  const checkoutUrl = await createCheckoutSession({
    customer: stripeCustomerId,
    customerEmail: customer.contact_email || undefined,
    priceId,
    quantity: quantity ?? (trial.seats_used || 1),
    successUrl: `${base}/dashboard/sales?convert=success`,
    cancelUrl: `${base}/dashboard/sales?convert=cancel`,
    metadata: { trialId }
  });

  // Optionally update trial status to converted on webhook completion; here just return URL
  return Response.json({ checkoutUrl });
}
