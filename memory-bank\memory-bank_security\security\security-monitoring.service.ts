import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter } from 'events';

export interface SecurityIncident {
  id: string;
  type: 'stripe_security_incident' | 'authentication_failure' | 'rate_limit_exceeded' | 'suspicious_activity' | 'data_breach' | 'system_compromise';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  details: string;
  timestamp: string;
  source: string;
  customerId?: string;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  actions_taken: string[];
  metadata?: Record<string, any>;
}

export interface AlertChannel {
  type: 'webhook' | 'email' | 'slack' | 'teams' | 'pagerduty';
  url: string;
  enabled: boolean;
  severityFilter: ('low' | 'medium' | 'high' | 'critical')[];
}

@Injectable()
export class SecurityMonitoringService extends EventEmitter {
  private readonly logger = new Logger(SecurityMonitoringService.name);
  private readonly alertChannels: AlertChannel[] = [];
  private readonly incidentHistory: SecurityIncident[] = [];
  private readonly maxHistorySize = 10000;

  // Rate limiting for alerts to prevent spam
  private readonly alertRateLimit = new Map<string, number[]>();
  private readonly alertRateLimitWindow = 300000; // 5 minutes
  private readonly alertRateLimitMax = 5; // max 5 alerts per window

  constructor(private configService: ConfigService) {
    super();
    this.initializeAlertChannels();
    this.setupEventListeners();
  }

  /**
   * Initialize alert channels from configuration
   */
  private initializeAlertChannels(): void {
    // Security webhook
    const securityWebhookUrl = this.configService.get<string>('SECURITY_WEBHOOK_URL');
    if (securityWebhookUrl) {
      this.alertChannels.push({
        type: 'webhook',
        url: securityWebhookUrl,
        enabled: true,
        severityFilter: ['medium', 'high', 'critical'],
      });
    }

    // Slack webhook
    const slackWebhookUrl = this.configService.get<string>('SLACK_WEBHOOK_URL');
    if (slackWebhookUrl) {
      this.alertChannels.push({
        type: 'slack',
        url: slackWebhookUrl,
        enabled: true,
        severityFilter: ['high', 'critical'],
      });
    }

    // PagerDuty for critical incidents
    const pagerDutyUrl = this.configService.get<string>('PAGERDUTY_WEBHOOK_URL');
    if (pagerDutyUrl) {
      this.alertChannels.push({
        type: 'pagerduty',
        url: pagerDutyUrl,
        enabled: true,
        severityFilter: ['critical'],
      });
    }

    this.logger.log(`Initialized ${this.alertChannels.length} alert channels`);
  }

  /**
   * Setup event listeners for security monitoring
   */
  private setupEventListeners(): void {
    this.on('security_incident', this.handleSecurityIncident.bind(this));
    this.on('stripe_anomaly', this.handleStripeAnomaly.bind(this));
    this.on('authentication_failure', this.handleAuthenticationFailure.bind(this));
  }

  /**
   * Report a security incident
   */
  async reportIncident(incident: Omit<SecurityIncident, 'id' | 'timestamp'>): Promise<string> {
    const incidentId = this.generateIncidentId();
    const fullIncident: SecurityIncident = {
      ...incident,
      id: incidentId,
      timestamp: new Date().toISOString(),
    };

    // Store in history
    this.incidentHistory.push(fullIncident);
    if (this.incidentHistory.length > this.maxHistorySize) {
      this.incidentHistory.shift(); // Remove oldest
    }

    this.logger.warn(`Security incident reported: ${incidentId} - ${incident.type} (${incident.severity})`);

    // Emit event for processing
    this.emit('security_incident', fullIncident);

    return incidentId;
  }

  /**
   * Handle security incident processing
   */
  private async handleSecurityIncident(incident: SecurityIncident): Promise<void> {
    try {
      // Check rate limiting
      if (!this.checkAlertRateLimit(incident.type)) {
        this.logger.warn(`Alert rate limit exceeded for ${incident.type}, skipping notifications`);
        return;
      }

      // Send alerts to configured channels
      await this.sendAlerts(incident);

      // Automatic response actions based on severity
      await this.executeAutomaticResponse(incident);

      // Log to security audit trail
      this.logSecurityEvent(incident);

    } catch (error) {
      this.logger.error(`Failed to handle security incident ${incident.id}: ${error.message}`);
    }
  }

  /**
   * Send alerts to configured channels
   */
  private async sendAlerts(incident: SecurityIncident): Promise<void> {
    const promises = this.alertChannels
      .filter(channel => 
        channel.enabled && 
        channel.severityFilter.includes(incident.severity)
      )
      .map(channel => this.sendAlert(channel, incident));

    await Promise.allSettled(promises);
  }

  /**
   * Send alert to specific channel
   */
  private async sendAlert(channel: AlertChannel, incident: SecurityIncident): Promise<void> {
    try {
      let payload: any;

      switch (channel.type) {
        case 'webhook':
          payload = this.formatWebhookPayload(incident);
          break;
        case 'slack':
          payload = this.formatSlackPayload(incident);
          break;
        case 'teams':
          payload = this.formatTeamsPayload(incident);
          break;
        case 'pagerduty':
          payload = this.formatPagerDutyPayload(incident);
          break;
        default:
          payload = this.formatGenericPayload(incident);
      }

      const response = await fetch(channel.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'QuantBoost-Security-Monitor/1.0',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      this.logger.log(`Alert sent to ${channel.type} for incident ${incident.id}`);
    } catch (error) {
      this.logger.error(`Failed to send alert to ${channel.type}: ${error.message}`);
    }
  }

  /**
   * Format webhook payload
   */
  private formatWebhookPayload(incident: SecurityIncident): any {
    return {
      event_type: 'security_incident',
      incident,
      environment: process.env.NODE_ENV || 'unknown',
      service: 'quantboost-security-monitor',
    };
  }

  /**
   * Format Slack payload
   */
  private formatSlackPayload(incident: SecurityIncident): any {
    const severityColor = {
      low: '#36a64f',
      medium: '#ff9900',
      high: '#ff6600',
      critical: '#ff0000',
    };

    return {
      text: `🚨 Security Incident: ${incident.title}`,
      attachments: [
        {
          color: severityColor[incident.severity],
          fields: [
            { title: 'Incident ID', value: incident.id, short: true },
            { title: 'Severity', value: incident.severity.toUpperCase(), short: true },
            { title: 'Type', value: incident.type, short: true },
            { title: 'Source', value: incident.source, short: true },
            { title: 'Details', value: incident.details, short: false },
            { title: 'Actions Taken', value: incident.actions_taken.join(', '), short: false },
          ],
          ts: Math.floor(new Date(incident.timestamp).getTime() / 1000),
        },
      ],
    };
  }

  /**
   * Format Teams payload
   */
  private formatTeamsPayload(incident: SecurityIncident): any {
    return {
      "@type": "MessageCard",
      "@context": "http://schema.org/extensions",
      "themeColor": incident.severity === 'critical' ? 'FF0000' : '0076D7',
      "summary": `Security Incident: ${incident.title}`,
      "sections": [
        {
          "activityTitle": "🚨 Security Incident",
          "activitySubtitle": incident.title,
          "facts": [
            { "name": "Incident ID", "value": incident.id },
            { "name": "Severity", "value": incident.severity.toUpperCase() },
            { "name": "Type", "value": incident.type },
            { "name": "Source", "value": incident.source },
            { "name": "Details", "value": incident.details },
          ],
        },
      ],
    };
  }

  /**
   * Format PagerDuty payload
   */
  private formatPagerDutyPayload(incident: SecurityIncident): any {
    return {
      routing_key: this.configService.get<string>('PAGERDUTY_ROUTING_KEY'),
      event_action: "trigger",
      payload: {
        summary: `Security Incident: ${incident.title}`,
        severity: incident.severity,
        source: incident.source,
        custom_details: {
          incident_id: incident.id,
          type: incident.type,
          details: incident.details,
          actions_taken: incident.actions_taken,
          timestamp: incident.timestamp,
        },
      },
    };
  }

  /**
   * Format generic payload
   */
  private formatGenericPayload(incident: SecurityIncident): any {
    return incident;
  }

  /**
   * Execute automatic response actions
   */
  private async executeAutomaticResponse(incident: SecurityIncident): Promise<void> {
    try {
      switch (incident.severity) {
        case 'critical':
          await this.handleCriticalIncident(incident);
          break;
        case 'high':
          await this.handleHighSeverityIncident(incident);
          break;
        case 'medium':
          await this.handleMediumSeverityIncident(incident);
          break;
        case 'low':
          await this.handleLowSeverityIncident(incident);
          break;
      }
    } catch (error) {
      this.logger.error(`Failed to execute automatic response for ${incident.id}: ${error.message}`);
    }
  }

  /**
   * Handle critical incidents
   */
  private async handleCriticalIncident(incident: SecurityIncident): Promise<void> {
    this.logger.error(`CRITICAL INCIDENT: ${incident.id}`);
    
    // Immediate actions for critical incidents
    if (incident.type === 'data_breach' || incident.type === 'system_compromise') {
      // In a real implementation, you might:
      // - Disable affected user accounts
      // - Revoke API keys
      // - Enable maintenance mode
      // - Trigger automated backups
      
      this.logger.error('Critical incident requires immediate manual intervention');
    }
  }

  /**
   * Handle high severity incidents
   */
  private async handleHighSeverityIncident(incident: SecurityIncident): Promise<void> {
    this.logger.warn(`HIGH SEVERITY INCIDENT: ${incident.id}`);
    
    // Enhanced monitoring and potential automated responses
    if (incident.type === 'stripe_security_incident') {
      // Could trigger additional Stripe monitoring
      this.logger.warn('Enhanced Stripe monitoring activated');
    }
  }

  /**
   * Handle medium severity incidents
   */
  private async handleMediumSeverityIncident(incident: SecurityIncident): Promise<void> {
    this.logger.warn(`MEDIUM SEVERITY INCIDENT: ${incident.id}`);
    // Standard monitoring and logging
  }

  /**
   * Handle low severity incidents
   */
  private async handleLowSeverityIncident(incident: SecurityIncident): Promise<void> {
    this.logger.log(`LOW SEVERITY INCIDENT: ${incident.id}`);
    // Basic logging and monitoring
  }

  /**
   * Handle Stripe anomalies
   */
  private async handleStripeAnomaly(data: any): Promise<void> {
    await this.reportIncident({
      type: 'stripe_security_incident',
      severity: 'medium',
      title: 'Stripe Anomaly Detected',
      details: `Anomaly detected in Stripe operations: ${JSON.stringify(data)}`,
      source: 'stripe-monitoring',
      actions_taken: ['Logged anomaly', 'Enhanced monitoring activated'],
      metadata: data,
    });
  }

  /**
   * Handle authentication failures
   */
  private async handleAuthenticationFailure(data: any): Promise<void> {
    await this.reportIncident({
      type: 'authentication_failure',
      severity: 'low',
      title: 'Authentication Failure',
      details: `Authentication failed for user: ${data.userId || 'unknown'}`,
      source: 'authentication-service',
      userId: data.userId,
      ipAddress: data.ipAddress,
      actions_taken: ['Logged failure', 'Rate limiting applied'],
      metadata: data,
    });
  }

  /**
   * Check alert rate limiting
   */
  private checkAlertRateLimit(alertType: string): boolean {
    const now = Date.now();
    const windowStart = now - this.alertRateLimitWindow;
    
    if (!this.alertRateLimit.has(alertType)) {
      this.alertRateLimit.set(alertType, []);
    }
    
    const alerts = this.alertRateLimit.get(alertType)!;
    const recentAlerts = alerts.filter(time => time > windowStart);
    
    if (recentAlerts.length >= this.alertRateLimitMax) {
      return false;
    }
    
    recentAlerts.push(now);
    this.alertRateLimit.set(alertType, recentAlerts);
    return true;
  }

  /**
   * Generate unique incident ID
   */
  private generateIncidentId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `INC-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Log security event to audit trail
   */
  private logSecurityEvent(incident: SecurityIncident): void {
    const auditLog = {
      event_type: 'security_incident',
      incident_id: incident.id,
      severity: incident.severity,
      type: incident.type,
      timestamp: incident.timestamp,
      source: incident.source,
      details: incident.details,
    };

    this.logger.error(`SECURITY_AUDIT: ${JSON.stringify(auditLog)}`);
  }

  /**
   * Get incident statistics
   */
  getIncidentStatistics(): {
    total: number;
    by_severity: Record<string, number>;
    by_type: Record<string, number>;
    recent_24h: number;
  } {
    const now = Date.now();
    const last24h = now - (24 * 60 * 60 * 1000);

    const stats = {
      total: this.incidentHistory.length,
      by_severity: { low: 0, medium: 0, high: 0, critical: 0 },
      by_type: {},
      recent_24h: 0,
    };

    this.incidentHistory.forEach(incident => {
      // Count by severity
      stats.by_severity[incident.severity]++;
      
      // Count by type
      stats.by_type[incident.type] = (stats.by_type[incident.type] || 0) + 1;
      
      // Count recent incidents
      if (new Date(incident.timestamp).getTime() > last24h) {
        stats.recent_24h++;
      }
    });

    return stats;
  }

  /**
   * Get recent incidents
   */
  getRecentIncidents(limit = 50): SecurityIncident[] {
    return this.incidentHistory
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }
}