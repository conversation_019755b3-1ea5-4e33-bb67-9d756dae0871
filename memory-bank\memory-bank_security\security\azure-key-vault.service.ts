import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SecretClient } from '@azure/keyvault-secrets';
import { DefaultAzureCredential } from '@azure/identity';

@Injectable()
export class AzureKeyVaultService {
  private readonly logger = new Logger(AzureKeyVaultService.name);
  private readonly secretClient: SecretClient;
  private readonly vaultName: string;

  constructor(private configService: ConfigService) {
    this.vaultName = this.configService.get<string>('AZURE_KEYVAULT_NAME') || 'quantboost-keyvault';
    
    try {
      const credential = new DefaultAzureCredential();
      const vaultUrl = `https://${this.vaultName}.vault.azure.net`;
      this.secretClient = new SecretClient(vaultUrl, credential);
      
      this.logger.log(`Azure Key Vault initialized: ${vaultUrl}`);
    } catch (error) {
      this.logger.error(`Failed to initialize Azure Key Vault: ${error.message}`);
      throw error;
    }
  }

  /**
   * Retrieve Stripe secret key from Azure Key Vault
   */
  async getStripeSecretKey(): Promise<string> {
    try {
      const secret = await this.secretClient.getSecret('stripe-secret-key');
      
      if (!secret.value) {
        throw new Error('Stripe secret key not found in Key Vault');
      }

      this.logger.log('Successfully retrieved Stripe secret key from Key Vault');
      return secret.value;
    } catch (error) {
      this.logger.error(`Failed to retrieve Stripe secret key: ${error.message}`);
      
      // Fallback to environment variable in development
      const fallbackKey = this.configService.get<string>('STRIPE_SECRET_KEY');
      if (fallbackKey && process.env.NODE_ENV === 'development') {
        this.logger.warn('Using fallback Stripe key from environment variable');
        return fallbackKey;
      }
      
      throw error;
    }
  }

  /**
   * Retrieve Stripe webhook secret from Azure Key Vault
   */
  async getStripeWebhookSecret(): Promise<string> {
    try {
      const secret = await this.secretClient.getSecret('stripe-webhook-signing-secret');
      
      if (!secret.value) {
        throw new Error('Stripe webhook secret not found in Key Vault');
      }

      this.logger.log('Successfully retrieved Stripe webhook secret from Key Vault');
      return secret.value;
    } catch (error) {
      this.logger.error(`Failed to retrieve Stripe webhook secret: ${error.message}`);
      
      // Fallback to environment variable in development
      const fallbackSecret = this.configService.get<string>('STRIPE_WEBHOOK_SECRET');
      if (fallbackSecret && process.env.NODE_ENV === 'development') {
        this.logger.warn('Using fallback webhook secret from environment variable');
        return fallbackSecret;
      }
      
      throw error;
    }
  }

  /**
   * Retrieve JWT secret from Azure Key Vault
   */
  async getJwtSecret(): Promise<string> {
    try {
      const secret = await this.secretClient.getSecret('jwt-secret');
      
      if (!secret.value) {
        throw new Error('JWT secret not found in Key Vault');
      }

      this.logger.log('Successfully retrieved JWT secret from Key Vault');
      return secret.value;
    } catch (error) {
      this.logger.error(`Failed to retrieve JWT secret: ${error.message}`);
      
      // Fallback to environment variable in development
      const fallbackSecret = this.configService.get<string>('JWT_SECRET');
      if (fallbackSecret && process.env.NODE_ENV === 'development') {
        this.logger.warn('Using fallback JWT secret from environment variable');
        return fallbackSecret;
      }
      
      throw error;
    }
  }

  /**
   * Store a secret in Azure Key Vault
   */
  async setSecret(secretName: string, secretValue: string): Promise<void> {
    try {
      await this.secretClient.setSecret(secretName, secretValue);
      this.logger.log(`Successfully stored secret: ${secretName}`);
    } catch (error) {
      this.logger.error(`Failed to store secret ${secretName}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Rotate Stripe API keys
   */
  async rotateStripeKeys(environment: string): Promise<void> {
    this.logger.log(`Starting Stripe key rotation for ${environment}`);
    
    try {
      // Step 1: Generate new keys (this would typically be done via Stripe Dashboard API)
      // For now, this is a placeholder for the rotation process
      const rotationTimestamp = new Date().toISOString();
      
      // Step 2: Store rotation metadata
      await this.setSecret(`stripe-key-rotation-${environment}`, JSON.stringify({
        rotatedAt: rotationTimestamp,
        environment,
        status: 'in-progress'
      }));

      // Step 3: In a real implementation, you would:
      // - Generate new keys via Stripe API
      // - Test the new keys
      // - Update the Key Vault with new keys
      // - Deploy the changes
      // - Verify functionality
      // - Revoke old keys
      
      this.logger.log(`Stripe key rotation initiated for ${environment}`);
      
      // Step 4: Update rotation status
      await this.setSecret(`stripe-key-rotation-${environment}`, JSON.stringify({
        rotatedAt: rotationTimestamp,
        environment,
        status: 'pending-deployment'
      }));

    } catch (error) {
      this.logger.error(`Stripe key rotation failed for ${environment}: ${error.message}`);
      
      // Store failure information
      await this.setSecret(`stripe-key-rotation-${environment}`, JSON.stringify({
        rotatedAt: new Date().toISOString(),
        environment,
        status: 'failed',
        error: error.message
      }));
      
      throw error;
    }
  }

  /**
   * Get all secrets for backup/audit purposes
   */
  async getAllSecretNames(): Promise<string[]> {
    try {
      const secretNames: string[] = [];
      
      for await (const secretProperties of this.secretClient.listPropertiesOfSecrets()) {
        secretNames.push(secretProperties.name);
      }
      
      this.logger.log(`Retrieved ${secretNames.length} secret names from Key Vault`);
      return secretNames;
    } catch (error) {
      this.logger.error(`Failed to list secrets: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check if a secret exists
   */
  async secretExists(secretName: string): Promise<boolean> {
    try {
      await this.secretClient.getSecret(secretName);
      return true;
    } catch (error) {
      if (error.code === 'SecretNotFound') {
        return false;
      }
      throw error;
    }
  }

  /**
   * Delete a secret (use with extreme caution)
   */
  async deleteSecret(secretName: string): Promise<void> {
    try {
      await this.secretClient.beginDeleteSecret(secretName);
      this.logger.warn(`Secret deleted: ${secretName}`);
    } catch (error) {
      this.logger.error(`Failed to delete secret ${secretName}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get secret with version information
   */
  async getSecretWithVersion(secretName: string, version?: string): Promise<{
    value: string;
    version: string;
    createdOn: Date;
    updatedOn: Date;
  }> {
    try {
      const secret = version 
        ? await this.secretClient.getSecret(secretName, { version })
        : await this.secretClient.getSecret(secretName);
      
      if (!secret.value) {
        throw new Error(`Secret ${secretName} has no value`);
      }

      return {
        value: secret.value,
        version: secret.properties.version,
        createdOn: secret.properties.createdOn,
        updatedOn: secret.properties.updatedOn,
      };
    } catch (error) {
      this.logger.error(`Failed to retrieve secret ${secretName}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Health check for Key Vault connectivity
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    vaultUrl: string;
    timestamp: string;
    error?: string;
  }> {
    const vaultUrl = `https://${this.vaultName}.vault.azure.net`;
    
    try {
      // Try to list secrets as a connectivity test
      const iterator = this.secretClient.listPropertiesOfSecrets();
      await iterator.next();
      
      return {
        status: 'healthy',
        vaultUrl,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Key Vault health check failed: ${error.message}`);
      
      return {
        status: 'unhealthy',
        vaultUrl,
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  /**
   * Audit log for secret access
   */
  async auditSecretAccess(secretName: string, operation: string, userId?: string): Promise<void> {
    const auditEntry = {
      secretName,
      operation,
      userId: userId || 'system',
      timestamp: new Date().toISOString(),
      vaultName: this.vaultName,
    };

    this.logger.log(`AUDIT: ${JSON.stringify(auditEntry)}`);
    
    try {
      // In a production environment, you might want to store audit logs
      // in a separate secure location like Azure Monitor or Application Insights
      await this.setSecret(`audit-${Date.now()}`, JSON.stringify(auditEntry));
    } catch (error) {
      this.logger.error(`Failed to store audit log: ${error.message}`);
      // Don't throw here as audit logging failure shouldn't break the main operation
    }
  }
}