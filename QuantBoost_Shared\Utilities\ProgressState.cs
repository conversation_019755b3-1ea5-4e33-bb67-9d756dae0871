// --- START OF FILE ProgressState.cs ---

using System;

// Define the namespace clearly
namespace QuantBoost_Shared.Utilities
{
    /// <summary>
    /// Represents the current state of a progress operation with detailed information.
    /// This class acts as a data carrier for progress updates.
    /// </summary>
    public class ProgressState
    {
        // Properties remain the same with private setters
        public string CurrentStep { get; private set; }
        public int PercentComplete { get; private set; }
        public string DetailMessage { get; private set; }
        public bool IsComplete { get; private set; }
        public bool HasError { get; private set; }
        public int? CurrentSlideIndex { get; private set; }
        public int? TotalSlides { get; private set; }

        /// <summary>
        /// Private constructor - use factory methods to create instances.
        /// </summary>
        private ProgressState() { }

        /// <summary>
        /// Creates a new progress state for a specific step.
        /// </summary>
        public static ProgressState ForStep(string stepName, int percentComplete, string detailMessage = null)
        {
            // --- Use Explicit Variable and Assignments ---
            var newState = new ProgressState(); // Create instance first
            newState.CurrentStep = stepName;                 // Assign properties one by one
            newState.PercentComplete = Math.Min(100, Math.Max(0, percentComplete));
            newState.DetailMessage = detailMessage;
            newState.IsComplete = false;
            newState.HasError = false;
            return newState;
        }

        /// <summary>
        /// Creates a new progress state for processing a slide.
        /// </summary>
        public static ProgressState ForSlide(int currentSlideIndex, int totalSlides, string detailMessage = null)
        {
            int percentComplete = totalSlides > 0
                ? (int)Math.Min(95, Math.Max(5, (currentSlideIndex * 90.0 / totalSlides) + 5))
                : 0;

            // --- Use Explicit Variable and Assignments ---
            var newState = new ProgressState(); // Create instance first
            newState.CurrentStep = $"Processing slide {currentSlideIndex} of {totalSlides}"; // Assign properties one by one
            newState.PercentComplete = percentComplete;
            newState.DetailMessage = detailMessage;
            newState.CurrentSlideIndex = currentSlideIndex;
            newState.TotalSlides = totalSlides;
            newState.IsComplete = false;
            newState.HasError = false;
            return newState;
        }

        /// <summary>
        /// Creates a progress state for a completed operation.
        /// </summary>
        public static ProgressState Completed(string message = "Operation completed successfully")
        {
            // --- Use Explicit Variable and Assignments ---
            var newState = new ProgressState(); // Create instance first
            newState.CurrentStep = "Completed";              // Assign properties one by one
            newState.PercentComplete = 100;
            newState.DetailMessage = message;
            newState.IsComplete = true;
            newState.HasError = false;
            return newState;
        }

        /// <summary>
        /// Creates a progress state for an operation that encountered an error.
        /// </summary>
        public static ProgressState Error(string errorMessage)
        {
            // --- Use Explicit Variable and Assignments ---
            var newState = new ProgressState(); // Create instance first
            newState.CurrentStep = "Error";                  // Assign properties one by one
            newState.PercentComplete = 0;
            newState.DetailMessage = errorMessage;
            newState.IsComplete = true;
            newState.HasError = true;
            return newState;
        }

        /// <summary>
        /// Calculates a percentage within a specific range of a larger operation.
        /// </summary>
        public static int CalculatePercentage(int current, int total, double portionWeight, int basePercentage = 0)
        {
            if (total <= 0) return basePercentage;
            double portion = Math.Min(1.0, Math.Max(0.0, portionWeight));
            double progress = (double)current / total * portion * 100.0;
            return Math.Min(100, Math.Max(0, basePercentage + (int)progress));
        }
    }
}
// --- END OF FILE ProgressState.cs ---