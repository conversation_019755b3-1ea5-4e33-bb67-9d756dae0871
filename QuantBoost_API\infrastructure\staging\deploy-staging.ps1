# QuantBoost Staging Infrastructure Deployment Script
# This script deploys ONLY staging resources and will NOT affect production

param(
    [Parameter(Mandatory=$false)]
    [switch]$PlanOnly,
    
    [Parameter(Mandatory=$false)]
    [switch]$DestroyStaging
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🚀 QuantBoost STAGING Infrastructure Deployment" -ForegroundColor Green
Write-Host "⚠️  This will create NEW staging resources and NOT affect production" -ForegroundColor Yellow

# Check if terraform.tfvars exists
if (-not (Test-Path "terraform.tfvars")) {
    Write-Host "❌ terraform.tfvars not found!" -ForegroundColor Red
    Write-Host "The staging terraform.tfvars file should already exist in this directory" -ForegroundColor Yellow
    exit 1
}

# Check if Azure CLI is logged in
try {
    $azAccount = az account show --query "name" -o tsv 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Not logged into Azure CLI" -ForegroundColor Red
        Write-Host "Please run: az login" -ForegroundColor Yellow
        exit 1
    }
    Write-Host "✅ Azure CLI logged in as: $azAccount" -ForegroundColor Green
} catch {
    Write-Host "❌ Azure CLI not found or not logged in" -ForegroundColor Red
    exit 1
}

# Initialize Terraform
Write-Host "📦 Initializing Terraform for staging..." -ForegroundColor Blue
..\terraform.exe init
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Terraform init failed" -ForegroundColor Red
    exit 1
}

# Create Terraform plan
Write-Host "📋 Creating Terraform plan for staging..." -ForegroundColor Blue
..\terraform.exe plan -out=staging.tfplan
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Terraform plan failed" -ForegroundColor Red
    exit 1
}

if ($PlanOnly) {
    Write-Host "✅ Staging plan completed. Review the plan above." -ForegroundColor Green
    Write-Host "To apply: ..\terraform.exe apply staging.tfplan" -ForegroundColor Yellow
    exit 0
}

if ($DestroyStaging) {
    Write-Host "⚠️  DESTROYING STAGING INFRASTRUCTURE" -ForegroundColor Red
    $confirmation = Read-Host "Are you sure you want to destroy staging infrastructure? Type 'yes' to confirm"
    if ($confirmation -eq "yes") {
        ..\terraform.exe destroy -auto-approve
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Staging infrastructure destroyed successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Staging infrastructure destruction failed" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "❌ Destruction cancelled" -ForegroundColor Yellow
    }
    exit 0
}

# Apply Terraform plan
Write-Host "🚀 Applying Terraform plan for staging..." -ForegroundColor Blue
..\terraform.exe apply -auto-approve staging.tfplan
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Terraform apply failed" -ForegroundColor Red
    exit 1
}

# Get outputs
Write-Host "📊 Getting staging deployment outputs..." -ForegroundColor Blue
$staticWebAppName = ..\terraform.exe output -raw static_web_app_default_hostname
$containerAppFqdn = ..\terraform.exe output -raw container_app_fqdn
$resourceGroupName = ..\terraform.exe output -raw resource_group_name
$containerRegistryName = ..\terraform.exe output -raw container_registry_name

Write-Host "✅ STAGING infrastructure deployed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Staging Deployment Summary:" -ForegroundColor Cyan
Write-Host "  Resource Group: $resourceGroupName" -ForegroundColor White
Write-Host "  Frontend URL: https://$staticWebAppName" -ForegroundColor White
Write-Host "  Backend API URL: https://$containerAppFqdn" -ForegroundColor White
Write-Host "  Container Registry: $containerRegistryName" -ForegroundColor White
Write-Host ""
Write-Host "🔒 PRODUCTION SAFETY:" -ForegroundColor Green
Write-Host "  ✅ Production resources in rg-quantboost-api-prod are UNTOUCHED" -ForegroundColor White
Write-Host "  ✅ This staging environment is completely separate" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Next Steps:" -ForegroundColor Cyan
Write-Host "  1. Build and push your API container to the new staging registry" -ForegroundColor White
Write-Host "  2. Deploy your frontend code to the staging Static Web App" -ForegroundColor White
Write-Host "  3. Test the complete staging environment" -ForegroundColor White
Write-Host "  4. Configure staging webhook URL in Stripe Dashboard" -ForegroundColor White
