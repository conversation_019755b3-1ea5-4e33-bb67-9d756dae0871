import { describe, it, expect } from 'vitest';
import { createTrialSchema, bulkInviteSchema, extractEmailsFromCsv } from './libImportShim';

describe('salesTrialsValidation', () => {
  it('validates createTrialSchema', () => {
    const ok = createTrialSchema.safeParse({ company: 'Acme Corp', contactEmail: '<EMAIL>' });
    expect(ok.success).toBe(true);

    const bad = createTrialSchema.safeParse({ company: 'A', contactEmail: 'nope' });
    expect(bad.success).toBe(false);
  });

  it('accepts bulk invites via emails[] or csv', () => {
    const viaEmails = bulkInviteSchema.safeParse({ emails: ['<EMAIL>', '<EMAIL>'] });
    expect(viaEmails.success).toBe(true);

    const viaCsv = bulkInviteSchema.safeParse({ csv: '<EMAIL>\n <EMAIL>' });
    expect(viaCsv.success).toBe(true);

    const noData = bulkInviteSchema.safeParse({ });
    expect(noData.success).toBe(false);
  });

  it('extracts emails from CSV-ish input', () => {
    const arr = extractEmailsFromCsv('<EMAIL>, <EMAIL>\n <EMAIL>;<EMAIL>');
    expect(arr).toEqual(['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']);
  });
});
