// --- START OF FILE AnalysisService.cs ---

using System; // Added for IProgress
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading; // Added for CancellationToken
using System.Threading.Tasks;
using QuantBoost_Shared.Utilities; // Added to reference ProgressState etc.
using PowerPoint = Microsoft.Office.Interop.PowerPoint;

namespace QuantBoost_Powerpoint_Addin.Analysis
{
    public class SlideAnalysisSummary
    {
        public int SlideIndex { get; set; }
        public string Title { get; set; }
        public int ImageCount { get; set; }
        public int ChartCount { get; set; }
        public int MediaCount { get; set; }
        public int EmbeddedObjectCount { get; set; }
        public int ExcelLinkCount { get; set; }
        public long TotalAssetSizeBytes { get; set; }

        // New properties for save & measure approach
        /// <summary>
        /// The actual file size in bytes of this slide when isolated in a temporary presentation.
        /// This represents the raw measured size before proportional allocation.
        /// </summary>
        public long SizeBytes { get; set; }

        /// <summary>
        /// The percentage this slide represents of the total presentation file size.
        /// Calculated after proportional allocation.
        /// </summary>
        public double PercentageOfTotal { get; set; }

        /// <summary>
        /// Indicates whether this slide has any content that affects file size.
        /// Used to distinguish between empty slides and content-bearing slides.
        /// </summary>
        public bool HasContent { get; set; }
    }

    public class OverallAnalysisSummary
    {
        public int SlideCount { get; set; }
        public int TotalImages { get; set; }
        public int TotalCharts { get; set; }
        public int TotalMedia { get; set; }
        public int TotalEmbeddedObjects { get; set; }
        public int TotalExcelLinks { get; set; }
        public long TotalAssetSizeBytes { get; set; }
        public string LargestAssetType { get; set; }
        public long LargestAssetSizeBytes { get; set; }
        public string LargestAssetDescription { get; set; }
    }

    /// <summary>
    /// Service class for analyzing PowerPoint presentation file sizes using the "Save & Measure" method.
    /// This approach creates temporary presentations for each slide to get accurate size measurements.
    /// </summary>
    public class AnalysisService
    {
        private readonly string _tempFolderPath;

        /// <summary>
        /// Initializes a new instance of the AnalysisService class.
        /// </summary>
        public AnalysisService()
        {
            _tempFolderPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "QuantBoost", "TempAnalysis");
        }

        /// <summary>
        /// Analyzes the presentation asynchronously using the "Save & Measure" method with proportional allocation.
        /// This method creates temporary presentations for each slide to get accurate relative weights,
        /// then applies those weights to the actual total presentation file size for perfect breakdown.
        /// </summary>
        /// <param name="pptxPath">Path to the PowerPoint file.</param>
        /// <param name="progress">Optional progress reporter.</param>
        /// <param name="cancellationToken">Optional cancellation token.</param>
        /// <returns>A tuple containing lists of slide summaries and an overall summary.</returns>
        public async Task<(List<SlideAnalysisSummary> slideSummaries, OverallAnalysisSummary overallSummary)>
            AnalyzePresentationAsync(
                string pptxPath,
                IProgress<ProgressState> progress = null,
                CancellationToken cancellationToken = default)
        {
            List<SlideAnalysisSummary> results = new List<SlideAnalysisSummary>();

            try
            {
                // Step 1: Pre-flight checks
                System.Diagnostics.Debug.WriteLine("=== STARTING POWERPOINT SAVE & MEASURE ANALYSIS ===");
                progress?.Report(ProgressState.ForStep("Initializing analysis...", 0));
                cancellationToken.ThrowIfCancellationRequested();

                // Step 2: Prepare temp directory
                System.Diagnostics.Debug.WriteLine("Step 2: Preparing temporary directory");
                progress?.Report(ProgressState.ForStep("Preparing temporary workspace...", 5));
                PrepareTempDirectory();

                // Step 3: Get PowerPoint application and active presentation
                System.Diagnostics.Debug.WriteLine("Step 3: Getting PowerPoint application and presentation");
                progress?.Report(ProgressState.ForStep("Accessing presentation...", 10));
                var pptApp = GetPowerPointApplication();
                var activePresentation = GetActivePresentation(pptApp);

                // Disable PowerPoint alerts during analysis
                var originalDisplayAlerts = pptApp.DisplayAlerts;
                pptApp.DisplayAlerts = PowerPoint.PpAlertLevel.ppAlertsNone;
                // Note: PowerPoint Application doesn't have ScreenUpdating property like Excel

                try
                {
                    // Step 4: Get actual presentation file size for proportional allocation
                    System.Diagnostics.Debug.WriteLine("Step 4: Getting actual presentation file size");
                    progress?.Report(ProgressState.ForStep("Getting presentation file size...", 15));
                    long actualPresentationSize = GetActualPresentationSize(activePresentation);
                    System.Diagnostics.Debug.WriteLine($"Actual presentation size obtained: {actualPresentationSize} bytes");

                    // Step 5: Iterate through each slide and analyze using Save & Measure
                    System.Diagnostics.Debug.WriteLine("Step 5: Starting slide iteration");
                    int slideCount = activePresentation.Slides.Count;
                    System.Diagnostics.Debug.WriteLine($"Total slides to process: {slideCount}");
                    int processedCount = 0;
                    var rawResults = new List<SlideAnalysisSummary>();

                    progress?.Report(ProgressState.ForStep("Starting slide analysis...", 20));

                    foreach (PowerPoint.Slide slide in activePresentation.Slides)
                    {
                        try
                        {
                            processedCount++;
                            int progressPercent = 20 + (processedCount * 60) / slideCount;
                            string slideTitle = GetSlideTitle(slide);
                            progress?.Report(ProgressState.ForStep($"Processing '{slideTitle}'...", progressPercent));
                            cancellationToken.ThrowIfCancellationRequested();

                            // Analyze this slide using Save & Measure method to get raw size
                            var analysis = await AnalyzeSlideAsync(slide, pptApp, cancellationToken);
                            rawResults.Add(analysis);

                            // Give PowerPoint a moment to breathe
                            await Task.Delay(50, cancellationToken);
                        }
                        finally
                        {
                            // Always release COM objects
                            if (slide != null)
                            {
                                object slideObj = slide;
                                ReleaseComObject(ref slideObj);
                            }
                        }
                    }

                    // Step 6: Apply proportional allocation based on relative weights
                    progress?.Report(ProgressState.ForStep("Calculating proportional allocation...", 85));
                    results = ApplyProportionalAllocation(rawResults, actualPresentationSize);
                }
                finally
                {
                    // Step 7: Restore PowerPoint settings and clean up
                    pptApp.DisplayAlerts = originalDisplayAlerts;
                    CleanupTempDirectory();
                }

                progress?.Report(ProgressState.ForStep("Analysis complete!", 100));


                // Calculate overall summary from the analyzed slides
                var overallSummary = new OverallAnalysisSummary
                {
                    SlideCount = results.Count,
                    TotalImages = results.Sum(s => s.ImageCount),
                    TotalCharts = results.Sum(s => s.ChartCount),
                    TotalMedia = results.Sum(s => s.MediaCount),
                    TotalEmbeddedObjects = results.Sum(s => s.EmbeddedObjectCount),
                    TotalExcelLinks = results.Sum(s => s.ExcelLinkCount),
                    TotalAssetSizeBytes = results.Sum(s => s.SizeBytes),
                    LargestAssetType = "Slide",
                    LargestAssetSizeBytes = results.Count > 0 ? results.Max(s => s.SizeBytes) : 0L,
                    LargestAssetDescription = results.Count > 0 ? results.OrderByDescending(s => s.SizeBytes).First().Title : ""
                };

                return (results, overallSummary);
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("=== ANALYSIS CANCELLED BY USER ===");
                progress?.Report(ProgressState.ForStep("Analysis cancelled", 0));
                CleanupTempDirectory();
                throw;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"=== ANALYSIS ERROR ===");
                System.Diagnostics.Debug.WriteLine($"Error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                CleanupTempDirectory();
                throw;
            }
        }

        #region Temporary Directory Management

        /// <summary>
        /// Prepares the temporary directory for analysis operations.
        /// Creates the directory if it doesn't exist and cleans up any existing files.
        /// </summary>
        private void PrepareTempDirectory()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Preparing temporary directory: {_tempFolderPath}");

                // Create directory if it doesn't exist
                if (!Directory.Exists(_tempFolderPath))
                {
                    Directory.CreateDirectory(_tempFolderPath);
                    System.Diagnostics.Debug.WriteLine("Temporary directory created");
                }
                else
                {
                    // Clean up any existing files from previous runs
                    CleanupTempDirectory();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error preparing temporary directory: {ex.Message}");
                throw new InvalidOperationException($"Failed to prepare temporary directory: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Cleans up the temporary directory by removing all files.
        /// Keeps the directory structure but removes temporary presentation files.
        /// </summary>
        private void CleanupTempDirectory()
        {
            try
            {
                if (!Directory.Exists(_tempFolderPath))
                {
                    System.Diagnostics.Debug.WriteLine("Temporary directory does not exist, nothing to clean up");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"Cleaning up temporary directory: {_tempFolderPath}");

                var files = Directory.GetFiles(_tempFolderPath, "*.*");
                int deletedCount = 0;

                foreach (string file in files)
                {
                    try
                    {
                        File.Delete(file);
                        deletedCount++;
                    }
                    catch (Exception fileEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"Could not delete temporary file {file}: {fileEx.Message}");
                        // Continue with other files
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Cleanup complete. Deleted {deletedCount} temporary files");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during temporary directory cleanup: {ex.Message}");
                // Don't throw here as cleanup is best-effort
            }
        }

        /// <summary>
        /// Sanitizes a file name by removing or replacing invalid characters.
        /// Used when creating temporary presentation files based on slide titles.
        /// </summary>
        /// <param name="fileName">The original file name to sanitize.</param>
        /// <returns>A sanitized file name safe for use in the file system.</returns>
        private static string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "UnknownSlide";

            // Get invalid characters for file names
            char[] invalidChars = Path.GetInvalidFileNameChars();

            // Replace invalid characters with underscores
            string sanitized = fileName;
            foreach (char invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // Limit length to avoid path length issues
            if (sanitized.Length > 50)
            {
                sanitized = sanitized.Substring(0, 50);
            }

            return sanitized;
        }

        #endregion

        #region File Size Measurement

        /// <summary>
        /// Gets the actual file size of the presentation.
        /// For SharePoint/OneDrive files, saves a temporary copy to get the size.
        /// </summary>
        /// <param name="presentation">The PowerPoint presentation.</param>
        /// <returns>The actual file size in bytes.</returns>
        private static long GetActualPresentationSize(PowerPoint.Presentation presentation)
        {
            try
            {
                string filePath = presentation.FullName;
                System.Diagnostics.Debug.WriteLine($"Getting actual presentation size for: {filePath}");

                // Try to access the file directly first (for local files)
                if (!string.IsNullOrEmpty(filePath) && !filePath.StartsWith("http") && File.Exists(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    long localSize = fileInfo.Length;
                    System.Diagnostics.Debug.WriteLine($"Local file size: {localSize:N0} bytes ({localSize / 1024.0:F1} KB, {localSize / (1024.0 * 1024.0):F1} MB)");
                    return localSize;
                }

                // For SharePoint/OneDrive files OR any case where we can't access the file directly,
                // save a temporary copy using the same approach as individual slides
                System.Diagnostics.Debug.WriteLine("File is not locally accessible, creating temporary copy to measure size...");

                string tempPath = Path.Combine(Path.GetTempPath(), $"QuantBoost_PresentationSizeCheck_{Guid.NewGuid()}.pptx");
                System.Diagnostics.Debug.WriteLine($"Saving temporary presentation copy to: {tempPath}");

                // Save a copy to get the actual file size (same approach as slide analysis)
                presentation.SaveCopyAs(tempPath);
                System.Diagnostics.Debug.WriteLine("Temporary presentation copy saved successfully");

                var tempFileInfo = new FileInfo(tempPath);
                long tempSize = tempFileInfo.Length;
                System.Diagnostics.Debug.WriteLine($"Presentation file size from temporary copy: {tempSize:N0} bytes ({tempSize / 1024.0:F1} KB, {tempSize / (1024.0 * 1024.0):F1} MB)");

                // Clean up the temporary file
                try
                {
                    File.Delete(tempPath);
                    System.Diagnostics.Debug.WriteLine("Temporary presentation size check file deleted");
                }
                catch (Exception cleanupEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Could not delete temporary presentation size check file: {cleanupEx.Message}");
                }

                return tempSize;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Could not get actual presentation size: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Exception details: {ex}");
            }

            // Fallback: return 0 if we can't determine the size
            // The proportional allocation will handle this gracefully
            System.Diagnostics.Debug.WriteLine("Returning 0 for actual presentation size - proportional allocation will be skipped");
            return 0;
        }

        #endregion

        #region Proportional Allocation

        /// <summary>
        /// Applies proportional allocation to slide sizes based on their relative weights.
        /// </summary>
        /// <param name="rawResults">Raw analysis results with measured sizes.</param>
        /// <param name="actualPresentationSize">The actual total presentation file size.</param>
        /// <returns>Results with proportionally allocated sizes.</returns>
        private static List<SlideAnalysisSummary> ApplyProportionalAllocation(
            List<SlideAnalysisSummary> rawResults,
            long actualPresentationSize)
        {
            if (rawResults == null || rawResults.Count == 0)
                return rawResults;

            // Calculate total of raw measured sizes to determine relative weights
            long totalRawSize = rawResults.Sum(r => r.SizeBytes);

            // Debug logging
            System.Diagnostics.Debug.WriteLine($"Proportional Allocation: Actual presentation size = {actualPresentationSize:N0} bytes ({actualPresentationSize / 1024.0:F1} KB)");
            System.Diagnostics.Debug.WriteLine($"Proportional Allocation: Total raw temp files = {totalRawSize:N0} bytes ({totalRawSize / 1024.0:F1} KB)");

            if (totalRawSize == 0 || actualPresentationSize == 0)
            {
                // If we can't calculate proportions, return raw results
                System.Diagnostics.Debug.WriteLine("Proportional Allocation: Cannot calculate proportions, returning raw results");
                return rawResults;
            }

            // Apply proportional allocation
            var allocatedResults = new List<SlideAnalysisSummary>();
            long allocatedSoFar = 0;

            for (int i = 0; i < rawResults.Count; i++)
            {
                var result = rawResults[i];
                long allocatedSize;

                if (i == rawResults.Count - 1)
                {
                    // For the last slide, allocate remaining size to ensure perfect total
                    allocatedSize = actualPresentationSize - allocatedSoFar;
                }
                else
                {
                    // Calculate proportional size based on relative weight
                    double weight = (double)result.SizeBytes / totalRawSize;
                    allocatedSize = (long)(actualPresentationSize * weight);
                    allocatedSoFar += allocatedSize;
                }

                // Create new result with allocated size
                var allocatedResult = new SlideAnalysisSummary
                {
                    SlideIndex = result.SlideIndex,
                    Title = result.Title,
                    ImageCount = result.ImageCount,
                    ChartCount = result.ChartCount,
                    MediaCount = result.MediaCount,
                    EmbeddedObjectCount = result.EmbeddedObjectCount,
                    ExcelLinkCount = result.ExcelLinkCount,
                    TotalAssetSizeBytes = result.TotalAssetSizeBytes,
                    HasContent = result.HasContent,
                    SizeBytes = allocatedSize,
                    PercentageOfTotal = actualPresentationSize > 0 ? (double)allocatedSize / actualPresentationSize * 100.0 : 0.0
                };

                allocatedResults.Add(allocatedResult);

                System.Diagnostics.Debug.WriteLine($"Slide '{result.Title}': Raw={result.SizeBytes:N0} bytes, Allocated={allocatedSize:N0} bytes ({allocatedResult.PercentageOfTotal:F1}%)");
            }

            // Final verification
            long totalAllocated = allocatedResults.Sum(r => r.SizeBytes);
            System.Diagnostics.Debug.WriteLine($"Proportional Allocation Complete: Total allocated = {totalAllocated:N0} bytes ({totalAllocated / 1024.0:F1} KB)");
            System.Diagnostics.Debug.WriteLine($"Difference from actual presentation size: {Math.Abs(actualPresentationSize - totalAllocated)} bytes");

            return allocatedResults;
        }

        #endregion

        #region PowerPoint COM Interop Helpers

        /// <summary>
        /// Creates a new temporary presentation for slide isolation.
        /// </summary>
        /// <param name="pptApp">The PowerPoint application instance.</param>
        /// <returns>A new presentation object.</returns>
        private static PowerPoint.Presentation CreateTempPresentation(PowerPoint.Application pptApp)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Creating new temporary presentation...");
                var tempPresentation = pptApp.Presentations.Add();
                System.Diagnostics.Debug.WriteLine("Temporary presentation created successfully");
                return tempPresentation;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating temporary presentation: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Copies a slide from the source presentation to the target presentation.
        /// Handles slide layouts, masters, and all content preservation.
        /// </summary>
        /// <param name="sourceSlide">The slide to copy.</param>
        /// <param name="targetPresentation">The target presentation.</param>
        /// <returns>The copied slide in the target presentation.</returns>
        private static PowerPoint.Slide CopySlideToPresentation(PowerPoint.Slide sourceSlide, PowerPoint.Presentation targetPresentation)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Copying slide {sourceSlide.SlideIndex} to temporary presentation...");

                // Copy the slide to the target presentation
                // This preserves all content, layouts, and formatting
                sourceSlide.Copy();

                // Remove any default slides from the target presentation first
                while (targetPresentation.Slides.Count > 0)
                {
                    targetPresentation.Slides[1].Delete();
                }

                // Paste the copied slide
                targetPresentation.Slides.Paste();

                var copiedSlide = targetPresentation.Slides[1];
                System.Diagnostics.Debug.WriteLine($"Slide copied successfully. Target presentation now has {targetPresentation.Slides.Count} slide(s)");

                return copiedSlide;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error copying slide: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Safely releases a COM object and sets the reference to null.
        /// </summary>
        /// <param name="comObject">The COM object to release.</param>
        private static void ReleaseComObject(ref object comObject)
        {
            try
            {
                if (comObject != null)
                {
                    Marshal.ReleaseComObject(comObject);
                    comObject = null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error releasing COM object: {ex.Message}");
                // Don't throw here as this is cleanup code
            }
        }

        /// <summary>
        /// Gets the PowerPoint application instance from the current add-in.
        /// </summary>
        /// <returns>The PowerPoint application instance.</returns>
        private static PowerPoint.Application GetPowerPointApplication()
        {
            try
            {
                // Get the application from the add-in
                var pptApp = Globals.ThisAddIn.Application;
                if (pptApp == null)
                {
                    throw new InvalidOperationException("PowerPoint application is not available");
                }
                return pptApp;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting PowerPoint application: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets the active presentation from PowerPoint.
        /// </summary>
        /// <param name="pptApp">The PowerPoint application instance.</param>
        /// <returns>The active presentation.</returns>
        private static PowerPoint.Presentation GetActivePresentation(PowerPoint.Application pptApp)
        {
            try
            {
                var activePresentation = pptApp.ActivePresentation;
                if (activePresentation == null)
                {
                    throw new InvalidOperationException("No active presentation found");
                }
                return activePresentation;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting active presentation: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Slide Analysis

        /// <summary>
        /// Analyzes a single slide using the Save & Measure method.
        /// Creates temporary presentations without disrupting the user's main PowerPoint window.
        /// </summary>
        /// <param name="slide">The slide to analyze.</param>
        /// <param name="pptApp">The PowerPoint application instance.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>Analysis summary for the slide.</returns>
        private async Task<SlideAnalysisSummary> AnalyzeSlideAsync(
            PowerPoint.Slide slide,
            PowerPoint.Application pptApp,
            CancellationToken cancellationToken)
        {
            System.Diagnostics.Debug.WriteLine($"--- Analyzing slide: {slide?.SlideIndex ?? -1} ---");
            PowerPoint.Presentation tempPresentation = null;
            string tempFilePath = null;

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                // Create a new temporary presentation
                System.Diagnostics.Debug.WriteLine("Creating temporary presentation for slide isolation...");
                tempPresentation = CreateTempPresentation(pptApp);
                System.Diagnostics.Debug.WriteLine("Temporary presentation created");

                // Copy the slide to the temporary presentation
                System.Diagnostics.Debug.WriteLine($"Copying slide {slide.SlideIndex} to temporary presentation...");
                var copiedSlide = CopySlideToPresentation(slide, tempPresentation);
                System.Diagnostics.Debug.WriteLine($"Slide copied successfully. Temporary presentation has {tempPresentation.Slides.Count} slide(s)");

                cancellationToken.ThrowIfCancellationRequested();

                // Save the temporary presentation (containing only our slide)
                string slideTitle = GetSlideTitle(slide);
                tempFilePath = Path.Combine(_tempFolderPath, $"{Guid.NewGuid()}_{SanitizeFileName(slideTitle)}.pptx");
                System.Diagnostics.Debug.WriteLine($"Saving temporary presentation to: {tempFilePath}");

                tempPresentation.SaveAs(tempFilePath, PowerPoint.PpSaveAsFileType.ppSaveAsOpenXMLPresentation);
                System.Diagnostics.Debug.WriteLine("Temporary presentation saved successfully");
                cancellationToken.ThrowIfCancellationRequested();

                // Get the file size and other metadata
                System.Diagnostics.Debug.WriteLine("Getting file size and analyzing slide metadata...");
                var fileInfo = new FileInfo(tempFilePath);
                System.Diagnostics.Debug.WriteLine($"Temporary file size: {fileInfo.Length} bytes ({fileInfo.Length / 1024.0:F1} KB)");

                var analysis = AnalyzeSlideWithInterop(slide);
                System.Diagnostics.Debug.WriteLine("Slide metadata analysis completed");

                // Set the accurate size from the isolated slide
                analysis.SizeBytes = fileInfo.Length;
                analysis.HasContent = fileInfo.Length > 0 && HasSlideContent(slide);
                System.Diagnostics.Debug.WriteLine($"Analysis complete for slide {slide.SlideIndex}: {analysis.SizeBytes} bytes");

                return analysis;
            }
            catch (System.Runtime.InteropServices.COMException comEx)
            {
                System.Diagnostics.Debug.WriteLine($"COM Exception in AnalyzeSlideAsync for slide {slide?.SlideIndex ?? -1}:");
                System.Diagnostics.Debug.WriteLine($"  HRESULT: 0x{comEx.HResult:X8}");
                System.Diagnostics.Debug.WriteLine($"  Message: {comEx.Message}");
                System.Diagnostics.Debug.WriteLine($"  Source: {comEx.Source}");
                System.Diagnostics.Debug.WriteLine($"  Stack Trace: {comEx.StackTrace}");

                // Return a minimal analysis result for this slide
                return new SlideAnalysisSummary
                {
                    SlideIndex = slide?.SlideIndex ?? -1,
                    Title = GetSlideTitle(slide) ?? "Error",
                    SizeBytes = 0,
                    HasContent = false
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"General Exception in AnalyzeSlideAsync for slide {slide?.SlideIndex ?? -1}:");
                System.Diagnostics.Debug.WriteLine($"  Type: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"  Message: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"  Stack Trace: {ex.StackTrace}");
                throw;
            }
            finally
            {
                System.Diagnostics.Debug.WriteLine($"Cleaning up resources for slide {slide?.SlideIndex ?? -1}...");

                // Clean up temporary file
                if (!string.IsNullOrEmpty(tempFilePath) && File.Exists(tempFilePath))
                {
                    try
                    {
                        File.Delete(tempFilePath);
                        System.Diagnostics.Debug.WriteLine("Temporary presentation file deleted");
                    }
                    catch (Exception fileEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"Could not delete temporary file {tempFilePath}: {fileEx.Message}");
                    }
                }

                // Clean up COM objects
                if (tempPresentation != null)
                {
                    try
                    {
                        tempPresentation.Close();
                        System.Diagnostics.Debug.WriteLine("Temporary presentation closed");
                    }
                    catch (Exception closeEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error closing temporary presentation: {closeEx.Message}");
                    }
                    finally
                    {
                        object tempPres = tempPresentation;
                        ReleaseComObject(ref tempPres);
                        tempPresentation = null;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Resource cleanup complete for slide {slide?.SlideIndex ?? -1}");
            }
        }

        /// <summary>
        /// Analyzes a single slide using PowerPoint Interop for live metadata.
        /// This method gathers slide details similar to the original PptxFileParser approach.
        /// </summary>
        private static SlideAnalysisSummary AnalyzeSlideWithInterop(PowerPoint.Slide slide)
        {
            var analysis = new SlideAnalysisSummary
            {
                SlideIndex = slide.SlideIndex,
                Title = GetSlideTitle(slide),
                ImageCount = 0,
                ChartCount = 0,
                MediaCount = 0,
                EmbeddedObjectCount = 0,
                ExcelLinkCount = 0,
                TotalAssetSizeBytes = 0,
                SizeBytes = 0,
                PercentageOfTotal = 0,
                HasContent = false
            };

            try
            {
                // Count different types of shapes on the slide
                if (slide.Shapes != null)
                {
                    foreach (PowerPoint.Shape shape in slide.Shapes)
                    {
                        try
                        {
                            switch (shape.Type)
                            {
                                case Microsoft.Office.Core.MsoShapeType.msoPicture:
                                    analysis.ImageCount++;
                                    break;
                                case Microsoft.Office.Core.MsoShapeType.msoChart:
                                    analysis.ChartCount++;
                                    break;
                                case Microsoft.Office.Core.MsoShapeType.msoMedia:
                                    analysis.MediaCount++;
                                    break;
                                case Microsoft.Office.Core.MsoShapeType.msoOLEControlObject:
                                case Microsoft.Office.Core.MsoShapeType.msoEmbeddedOLEObject:
                                case Microsoft.Office.Core.MsoShapeType.msoLinkedOLEObject:
                                    analysis.EmbeddedObjectCount++;
                                    break;
                            }
                        }
                        catch (Exception shapeEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error analyzing shape: {shapeEx.Message}");
                            // Continue with other shapes
                        }
                    }
                }

                // Determine if slide has content
                analysis.HasContent = analysis.ImageCount > 0 || analysis.ChartCount > 0 ||
                                    analysis.MediaCount > 0 || analysis.EmbeddedObjectCount > 0 ||
                                    HasTextContent(slide);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error analyzing slide {slide.SlideIndex}: {ex.Message}");
            }

            return analysis;
        }

        /// <summary>
        /// Gets the title of a slide, handling various title scenarios.
        /// </summary>
        private static string GetSlideTitle(PowerPoint.Slide slide)
        {
            try
            {
                if (slide?.Shapes != null)
                {
                    foreach (PowerPoint.Shape shape in slide.Shapes)
                    {
                        try
                        {
                            if (shape.Type == Microsoft.Office.Core.MsoShapeType.msoPlaceholder)
                            {
                                var placeholder = shape.PlaceholderFormat;
                                if (placeholder.Type == PowerPoint.PpPlaceholderType.ppPlaceholderTitle ||
                                    placeholder.Type == PowerPoint.PpPlaceholderType.ppPlaceholderCenterTitle)
                                {
                                    if (shape.HasTextFrame == Microsoft.Office.Core.MsoTriState.msoTrue &&
                                        shape.TextFrame.HasText == Microsoft.Office.Core.MsoTriState.msoTrue)
                                    {
                                        return shape.TextFrame.TextRange.Text?.Trim();
                                    }
                                }
                            }
                        }
                        catch
                        {
                            // Continue checking other shapes
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting slide title: {ex.Message}");
            }

            return $"Slide {slide?.SlideIndex ?? 0}";
        }

        /// <summary>
        /// Determines if a slide has meaningful content beyond just layout elements.
        /// </summary>
        private static bool HasSlideContent(PowerPoint.Slide slide)
        {
            try
            {
                if (slide?.Shapes == null || slide.Shapes.Count == 0)
                    return false;

                // Check for non-placeholder content
                foreach (PowerPoint.Shape shape in slide.Shapes)
                {
                    try
                    {
                        // Images, charts, media, and embedded objects are always content
                        if (shape.Type == Microsoft.Office.Core.MsoShapeType.msoPicture ||
                            shape.Type == Microsoft.Office.Core.MsoShapeType.msoChart ||
                            shape.Type == Microsoft.Office.Core.MsoShapeType.msoMedia ||
                            shape.Type == Microsoft.Office.Core.MsoShapeType.msoEmbeddedOLEObject ||
                            shape.Type == Microsoft.Office.Core.MsoShapeType.msoLinkedOLEObject)
                        {
                            return true;
                        }

                        // Check for text content
                        if (shape.HasTextFrame == Microsoft.Office.Core.MsoTriState.msoTrue &&
                            shape.TextFrame.HasText == Microsoft.Office.Core.MsoTriState.msoTrue)
                        {
                            string text = shape.TextFrame.TextRange.Text?.Trim();
                            if (!string.IsNullOrEmpty(text))
                            {
                                return true;
                            }
                        }
                    }
                    catch
                    {
                        // Continue checking other shapes
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking slide content: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// Checks if a slide has text content.
        /// </summary>
        private static bool HasTextContent(PowerPoint.Slide slide)
        {
            try
            {
                if (slide?.Shapes != null)
                {
                    foreach (PowerPoint.Shape shape in slide.Shapes)
                    {
                        try
                        {
                            if (shape.HasTextFrame == Microsoft.Office.Core.MsoTriState.msoTrue &&
                                shape.TextFrame.HasText == Microsoft.Office.Core.MsoTriState.msoTrue)
                            {
                                string text = shape.TextFrame.TextRange.Text?.Trim();
                                if (!string.IsNullOrEmpty(text))
                                {
                                    return true;
                                }
                            }
                        }
                        catch
                        {
                            // Continue checking other shapes
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking text content: {ex.Message}");
            }

            return false;
        }

        #endregion


        // --- STATIC WRAPPER METHODS FOR BACKWARD COMPATIBILITY ---

        /// <summary>
        /// Static wrapper method for backward compatibility.
        /// Creates a new AnalysisService instance and calls the instance method.
        /// </summary>
        public static async Task<(List<SlideAnalysisSummary> slideSummaries, OverallAnalysisSummary overallSummary)>
            AnalyzePresentationAsync(string pptxPath)
        {
            var service = new AnalysisService();
            return await service.AnalyzePresentationAsync(pptxPath, null, CancellationToken.None).ConfigureAwait(false);
        }

        /// <summary>
        /// Static wrapper method for backward compatibility with progress and cancellation.
        /// Creates a new AnalysisService instance and calls the instance method.
        /// </summary>
        public static async Task<(List<SlideAnalysisSummary> slideSummaries, OverallAnalysisSummary overallSummary)>
            AnalyzePresentationWithProgressAsync(
                string pptxPath,
                IProgress<ProgressState> progress,
                CancellationToken cancellationToken = default)
        {
            var service = new AnalysisService();
            return await service.AnalyzePresentationAsync(pptxPath, progress, cancellationToken).ConfigureAwait(false);
        }

    }
}
// --- END OF FILE AnalysisService.cs ---