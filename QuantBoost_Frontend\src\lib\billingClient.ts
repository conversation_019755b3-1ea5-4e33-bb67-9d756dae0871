import { authJson } from './authFetch';

// Route response shapes (align with backend expectations; adjust as backend evolves)
export interface ReceiptsResponse { receipts: Receipt[] };
export interface Receipt { id: string; amount: number; created_at: string; status: string; receipt_url?: string }
export interface UpcomingInvoiceResponse { amount_due: number; billing_interval: string }
export interface SubscriptionDetailsResponse { billing_interval: string }
export interface CreateSetupIntentResponse { client_secret: string }
export interface SetDefaultPaymentMethodResponse { success?: boolean }
export interface CancelSubscriptionResponse { success?: boolean }
export interface UndoCancellationResponse { success?: boolean }
export interface PortalSessionResponse { url: string }

export class BillingClient {
  async getReceipts(userId: string) {
    return authJson<Receipt[] | ReceiptsResponse>('/api/billing/receipts', { method: 'POST', body: JSON.stringify({ userId }) });
  }
  async getUpcomingInvoice(subscriptionId: string) {
    return authJson<UpcomingInvoiceResponse>('/api/billing/upcoming-invoice', { method: 'POST', body: JSON.stringify({ subscriptionId }) });
  }
  async getSubscriptionDetails(subscriptionId: string) {
    return authJson<SubscriptionDetailsResponse>('/api/billing/subscription-details', { method: 'POST', body: JSON.stringify({ subscriptionId }) });
  }
  async createSetupIntent(subscriptionId: string) {
    return authJson<CreateSetupIntentResponse>('/api/billing/create-setup-intent', { method: 'POST', body: JSON.stringify({ subscriptionId }) });
  }
  async setDefaultPaymentMethod(subscriptionId: string, paymentMethodId: string) {
    return authJson<SetDefaultPaymentMethodResponse>('/api/billing/set-default-payment-method', { method: 'POST', body: JSON.stringify({ subscriptionId, paymentMethodId }) });
  }
  async cancelSubscription(subscriptionId: string) {
    return authJson<CancelSubscriptionResponse>('/api/billing/cancel-subscription', { method: 'POST', body: JSON.stringify({ subscriptionId, cancelAtPeriodEnd: true }) });
  }
  async undoCancellation(subscriptionId: string) {
    return authJson<UndoCancellationResponse>('/api/billing/undo-cancellation', { method: 'POST', body: JSON.stringify({ subscriptionId }) });
  }
  async createPortalSession(customerId: string) {
    return authJson<PortalSessionResponse>('/api/billing/create-portal-session', { method: 'POST', body: JSON.stringify({ customerId }) });
  }
}

export const billingClient = new BillingClient();
