# IDE & Editor specific
.vscode/

# Environment variables
# Ignore all .env files to prevent accidental commitment of secrets.
# An exception is made for template files.
.env
.env.*
!.env.example

# Node.js dependencies
node_modules/

# Frontend build artifacts
QuantBoost_Frontend/.next/

# Terraform state and variables
.terraform/
*.tfstate
*.tfstate.backup
*.tfvars

# Executables
*.exe

# OS-specific
.DS_Store
Thumbs.db
QuantBoost_Testing/.env.example
