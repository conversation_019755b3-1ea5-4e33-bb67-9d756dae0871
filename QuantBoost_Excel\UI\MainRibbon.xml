<?xml version="1.0" encoding="UTF-8"?>
<customUI xmlns="http://schemas.microsoft.com/office/2009/07/customui" onLoad="OnLoad">
  <ribbon>
    <tabs>
      <tab id="tabQuantBoost" label="QuantBoost">

        <group id="groupFormulaAuditing" label="Audit">
          <button id="btnTracePrecedents"
                  label="Excel Trace"
                  getImage="GetExcelTraceImage"
                  size="large"
                  screentip="Excel Trace"
                  supertip="Trace precedents for the selected cell to analyze formula dependencies"
                  onAction="OnTracePrecedents_Click"
                  getEnabled="IsPremiumFeatureEnabled"/>
          <button id="btnSizeAnalyzer"
                  label="Size Analyzer"
                  getImage="GetSizeAnalyzerImage"
                  size="large"
                  screentip="Workbook Size Analyzer"
                  supertip="Analyze the file size breakdown of your Excel workbook by worksheet and content type"
                  onAction="OnSizeAnalyzer_Click"
                  getEnabled="IsPremiumFeatureEnabled"/>
        </group>

        <group id="groupAccount" label="Account">
          <button id="btnManageAccount"
                  getImage="GetLoginImage"
                  size="large"
                  getLabel="GetManageAccountLabel"
                  onAction="OnManageAccountClick"
                  getVisible="GetManageAccountVisible" />
          <button id="btnLogout"
                  label="Logout"
                  getImage="GetLogoutImage"
                  size="large"
                  onAction="OnLogoutClick"
                  getVisible="GetLogoutButtonVisible" />
        </group>

      </tab>
    </tabs>
  </ribbon>
</customUI>
