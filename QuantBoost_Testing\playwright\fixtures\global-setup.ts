import { FullConfig } from '@playwright/test';
import * as fs from 'fs';

export default async function globalSetup(config: FullConfig) {
  const required = ['BASE_URL', 'SUPABASE_URL', 'SUPABASE_ANON_KEY'];
  const missing = required.filter(k => !process.env[k]);
  if (missing.length) {
    console.warn(`[globalSetup] Warning: missing env vars: ${missing.join(', ')} - related tests may be skipped.`);
  }
  // Ensure reports dir exists
  if (!fs.existsSync('playwright/reports')) fs.mkdirSync('playwright/reports', { recursive: true });
}
