// Simple telemetry wrapper; in future route to external service (App Insights, OpenTelemetry, etc.)
interface TelemetryEventBase { event: string; ts: string; }
export interface PartialSuccessPaymentEvent extends TelemetryEventBase {
  event: 'billing.partial_success_payment_method';
  subscriptionId: string;
  paymentMethodId?: string;
  attempt: number;
}

let partialSuccessAttempts: Record<string, number> = {};

export function recordPartialSuccess(opts: { subscriptionId: string; paymentMethodId?: string }) {
  const key = `${opts.subscriptionId}|${opts.paymentMethodId || 'unknown'}`;
  partialSuccessAttempts[key] = (partialSuccessAttempts[key] || 0) + 1;
  const payload: PartialSuccessPaymentEvent = {
    event: 'billing.partial_success_payment_method',
    ts: new Date().toISOString(),
    subscriptionId: opts.subscriptionId,
    paymentMethodId: opts.paymentMethodId,
    attempt: partialSuccessAttempts[key]
  };
  // For now log to console; replace with remote ingestion later
  // eslint-disable-next-line no-console
  console.warn('[telemetry]', payload);
  return payload;
}
