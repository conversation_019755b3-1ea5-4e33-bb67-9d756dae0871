// --- START OF NEW telemetry.js FILE ---

const { useAzureMonitor } = require("@azure/monitor-opentelemetry");
const logger = require('./logger');

class TelemetryService {
  constructor() {
    // TEMPORARILY DISABLED: Only configure telemetry when specifically needed
    // Re-enable for production monitoring once quota is configured properly
    const telemetryEnabled = process.env.ENABLE_TELEMETRY === 'true' && 
                             process.env.NODE_ENV === 'production' && 
                             process.env.APPLICATIONINSIGHTS_CONNECTION_STRING;
    
    if (telemetryEnabled) {
      try {
        // This single line initializes and starts everything:
        // - Dependency Correlation
        // - Request Collection
        // - Performance Counters
        // - Exception Tracking
        // - Console Log Collection
        // - Live Metrics
        useAzureMonitor();
        
        // We don't need a client instance anymore. The magic is automatic.
        this.enabled = true;
        logger.info('Azure Monitor (Application Insights) initialized successfully with OpenTelemetry.');

      } catch (error) {
        logger.error('Failed to initialize Azure Monitor', { error: error.message, stack: error.stack });
        this.enabled = false;
      }
    } else {
      this.enabled = false;
      if (process.env.NODE_ENV === 'production') {
        logger.info('Azure Monitor disabled - set ENABLE_TELEMETRY=true to enable monitoring');
      }
    }
  }

  // Backward compatibility methods for existing code
  trackMetric(name, value, properties = {}) {
    if (this.enabled) {
      // In development, just log the metric
      logger.debug('Metric tracked', { name, value, properties });
    }
  }

  trackEvent(name, properties = {}) {
    if (this.enabled) {
      // In development, just log the event
      logger.debug('Event tracked', { name, properties });
    }
  }
}

// Export a single instance so it runs once on startup
module.exports = new TelemetryService();

// --- END OF NEW telemetry.js FILE ---