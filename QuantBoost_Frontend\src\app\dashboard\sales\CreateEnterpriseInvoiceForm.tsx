"use client";

import { useState } from 'react';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface EnterpriseCustomer {
  id: string;
  company_name: string;
  contact_email: string;
  license_quantity: number;
  license_tier: string;
  annual_contract_value: number;
}

interface CreateEnterpriseInvoiceFormProps {
  customer: EnterpriseCustomer;
  onSuccess: () => void;
}

interface LineItem {
  description: string;
  amount: number;
  quantity: number;
}

export default function CreateEnterpriseInvoiceForm({ customer, onSuccess }: CreateEnterpriseInvoiceFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabaseClient();

  const [formData, setFormData] = useState({
    description: `${customer.license_tier} License Subscription - Annual`,
    amount_due: customer.annual_contract_value || 0,
    currency: 'USD',
    due_days: 30,
    line_items: [
      {
        description: `${customer.license_tier} License Subscription - ${customer.license_quantity} seats`,
        amount: customer.annual_contract_value || 0,
        quantity: 1,
      }
    ] as LineItem[],
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError(null);

      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/sales/enterprise-invoices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          ...formData,
          enterprise_customer_id: customer.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create invoice');
      }

      const result = await response.json();
      console.log('Invoice created:', result);

      onSuccess();
    } catch (error) {
      console.error('Error creating invoice:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const updateLineItem = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      line_items: prev.line_items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const addLineItem = () => {
    setFormData(prev => ({
      ...prev,
      line_items: [
        ...prev.line_items,
        { description: '', amount: 0, quantity: 1 }
      ],
    }));
  };

  const removeLineItem = (index: number) => {
    if (formData.line_items.length > 1) {
      setFormData(prev => ({
        ...prev,
        line_items: prev.line_items.filter((_, i) => i !== index),
      }));
    }
  };

  const calculateTotal = () => {
    return formData.line_items.reduce((sum, item) => sum + (item.amount * item.quantity), 0);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md">
          {error}
        </div>
      )}

      {/* Customer Info */}
      <div className="bg-gray-50 p-4 rounded-md">
        <h4 className="text-sm font-medium mb-2">Customer: {customer.company_name}</h4>
        <p className="text-sm text-gray-600">{customer.contact_email}</p>
        <p className="text-sm text-gray-600">{customer.license_quantity} {customer.license_tier} licenses</p>
      </div>

      {/* Invoice Details */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">Invoice Details</h4>
        
        <div>
          <label htmlFor="description" className="block text-sm font-medium mb-1">
            Description
          </label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => updateFormData('description', e.target.value)}
            rows={2}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="due_days" className="block text-sm font-medium mb-1">
              Payment Terms (Days)
            </label>
            <Input
              id="due_days"
              type="number"
              value={formData.due_days}
              onChange={(e) => updateFormData('due_days', parseInt(e.target.value))}
            />
          </div>
          
          <div>
            <label htmlFor="currency" className="block text-sm font-medium mb-1">
              Currency
            </label>
            <Input
              id="currency"
              value={formData.currency}
              onChange={(e) => updateFormData('currency', e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Line Items */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium">Line Items</h4>
        
        {formData.line_items.map((item, index) => (
          <div key={index} className="border p-4 rounded-md space-y-4">
            <div className="flex justify-between items-center">
              <h5 className="text-sm font-medium">Item {index + 1}</h5>
              {formData.line_items.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeLineItem(index)}
                >
                  Remove
                </Button>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">
                Description
              </label>
              <Input
                value={item.description}
                onChange={(e) => updateLineItem(index, 'description', e.target.value)}
                required
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  Amount ($)
                </label>
                <Input
                  type="number"
                  step="0.01"
                  value={item.amount}
                  onChange={(e) => updateLineItem(index, 'amount', parseFloat(e.target.value))}
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">
                  Quantity
                </label>
                <Input
                  type="number"
                  min="1"
                  value={item.quantity}
                  onChange={(e) => updateLineItem(index, 'quantity', parseInt(e.target.value))}
                  required
                />
              </div>
            </div>
            
            <div className="text-right">
              <span className="text-sm font-medium">
                Subtotal: ${(item.amount * item.quantity).toFixed(2)}
              </span>
            </div>
          </div>
        ))}
        
        <Button type="button" variant="outline" onClick={addLineItem}>
          Add Line Item
        </Button>
      </div>

      {/* Total */}
      <div className="border-t pt-4">
        <div className="text-right">
          <span className="text-lg font-medium">
            Total: ${calculateTotal().toFixed(2)} {formData.currency}
          </span>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="submit" disabled={loading}>
          {loading ? 'Creating Invoice...' : 'Create & Send Invoice'}
        </Button>
      </div>
    </form>
  );
}
