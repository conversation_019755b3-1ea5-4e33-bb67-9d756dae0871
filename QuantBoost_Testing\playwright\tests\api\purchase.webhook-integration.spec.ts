import { test, expect } from '../../fixtures/combined.fixture';
import <PERSON><PERSON> from 'stripe';

/**
 * Working example of the complete webhook integration test solution.
 * This test demonstrates the full purchase flow with webhook simulation.
 * 
 * Requirements:
 * - Running local development server (npm run dev) 
 * - BASE_URL=http://localhost:3000
 * - All environment variables configured
 * 
 * This test will work once the webhook handler updates are deployed.
 */

const PRICE_ID = 'price_1RC3HTE6FvhUKV1bE9D6zf6e';

const requireEnv = () => {
  if (!process.env.BASE_URL) test.skip(true, 'BASE_URL not set');
  if (!process.env.STRIPE_SECRET_KEY_TEST) test.skip(true, 'STRIPE_SECRET_KEY_TEST missing');
  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) test.skip(true, 'SUPABASE_SERVICE_ROLE_KEY missing');
};

async function postJSON(path: string, body: any) {
  const baseUrl = process.env.BASE_URL;
  console.log(`🔧 Making request to: ${baseUrl}${path}`);
  const res = await fetch(`${baseUrl}${path}`, {
    method: 'POST', 
    headers: { 'Content-Type': 'application/json' }, 
    body: JSON.stringify(body)
  });
  let json: any = {};
  try { 
    json = await res.json(); 
  } catch (e) {
    console.log(`⚠️ Failed to parse JSON response: ${e}`);
  }
  return { status: res.status, json };
}

async function poll<T>(label: string, fn: () => Promise<T | null | undefined>, predicate: (v: T) => boolean, attempts = 20, delayMs = 2000) {
  console.log(`🔄 Starting to poll for: ${label}`);
  for (let i = 0; i < attempts; i++) {
    const val = await fn();
    if (val && predicate(val)) {
      console.log(`✅ Found ${label} after ${i + 1} attempts`);
      return val;
    }
    console.log(`⏳ ${label} attempt ${i + 1}/${attempts} - waiting ${delayMs}ms...`);
    if (i < attempts - 1) await new Promise(r => setTimeout(r, delayMs));
  }
  console.log(`❌ Failed to find ${label} after ${attempts} attempts`);
  return null;
}

test.describe('Webhook Integration Test (Local Development)', () => {
  test('complete purchase flow with webhook simulation - LOCAL ONLY', async ({ stripe, supabase }) => {
    test.setTimeout(120_000);
    requireEnv();
    if (!stripe) test.skip(true, 'Stripe fixture not initialized');
    if (!supabase) test.skip(true, 'Supabase client not available');
    
    // Skip if not running against localhost (this test requires local dev server)
    if (!process.env.BASE_URL?.includes('localhost')) {
      test.skip(true, 'This test requires local development server');
    }

    const email = `webhook_test_${Date.now()}@example.com`;
    console.log(`🧪 Testing with email: ${email}`);

    // Step 1: Create PaymentIntent via API
    console.log('📝 Step 1: Creating PaymentIntent...');
    const createPI = await postJSON('/api/checkout/create-payment-intent', { 
      priceId: PRICE_ID, 
      email, 
      quantity: 1 
    });
    
    expect(createPI.status).toBe(200);
    const { paymentIntentId, subscriptionId, customerId } = createPI.json;
    
    console.log(`✅ PaymentIntent created:`, {
      paymentIntentId,
      subscriptionId, 
      customerId
    });

    // Step 2: Confirm payment with Stripe
    console.log('💳 Step 2: Confirming payment...');
    const confirmed = await stripe!.paymentIntents.confirm(paymentIntentId, {
      payment_method: 'pm_card_visa',
      return_url: 'https://example.com/success'
    });
    
    expect(['succeeded', 'requires_capture'].includes(confirmed.status)).toBeTruthy();
    console.log(`✅ Payment confirmed with status: ${confirmed.status}`);

    // Step 3: Get full subscription details for webhook simulation
    const subscription = await stripe!.subscriptions.retrieve(subscriptionId);
    console.log(`📋 Retrieved subscription details: ${subscription.id}`);

    // Step 4: Simulate webhook events
    console.log('🎭 Step 4: Simulating webhook events...');
    
    // Simulate customer.subscription.created
    const subWebhook = await postJSON('/api/test/simulate-stripe-webhook', {
      eventType: 'customer.subscription.created',
      payload: subscription
    });
    console.log(`🎯 Subscription webhook: ${subWebhook.status}`);
    
    // Simulate payment_intent.succeeded  
    const payWebhook = await postJSON('/api/test/simulate-stripe-webhook', {
      eventType: 'payment_intent.succeeded', 
      payload: confirmed
    });
    console.log(`🎯 Payment webhook: ${payWebhook.status}`);

    // Small delay for webhook processing
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 5: Verify database records
    console.log('🔍 Step 5: Verifying database records...');

    // Check profile
    const profile = await poll('profile', async () => {
      const { data } = await supabase!.from('profiles')
        .select('id,email,stripe_customer_id')
        .eq('stripe_customer_id', customerId)
        .maybeSingle();
      return data || null;
    }, d => !!d?.id);
    
    expect(profile, 'Profile should be created').toBeTruthy();
    expect(profile?.email).toBe(email);
    console.log(`✅ Profile verified: ${profile?.id}`);

    // Check subscription
    const subRow = await poll('subscription', async () => {
      const { data } = await supabase!.from('subscriptions')
        .select('*')
        .eq('stripe_subscription_id', subscriptionId)
        .maybeSingle();
      return data || null;
    }, d => !!d?.status);
    
    expect(subRow, 'Subscription should be created').toBeTruthy();
    expect(subRow?.status).toBe('active');
    console.log(`✅ Subscription verified: ${subRow?.id}`);

    // Check license
    const license = await poll('license', async () => {
      const { data } = await supabase!.from('licenses')
        .select('*')
        .eq('subscription_id', subRow!.id)
        .limit(1);
      return (data && data.length ? data[0] : null);
    }, d => !!d?.id);
    
    expect(license, 'License should be created').toBeTruthy();
    expect(license?.status).toBe('active');
    console.log(`✅ License verified: ${license?.id}`);

    // Check webhook events
    const webhookEvent = await poll('webhook_events', async () => {
      const { data } = await supabase!.from('webhook_events')
        .select('id,event_type')
        .in('event_type', ['customer.subscription.created', 'payment_intent.succeeded'])
        .order('processed_at', { ascending: false })
        .limit(1);
      return (data && data.length ? data[0] : null);
    }, d => !!d?.id);
    
    expect(webhookEvent, 'Webhook event should be logged').toBeTruthy();
    console.log(`✅ Webhook event verified: ${webhookEvent?.event_type}`);

    console.log('🎉 All verification steps completed successfully!');
    
    // Final assertions
    expect(profile?.email).toBe(email);
    expect(subRow?.user_id).toBe(profile?.id);
    expect(license?.user_id).toBe(profile?.id);
    expect(license?.subscription_id).toBe(subRow?.id);
  });
});