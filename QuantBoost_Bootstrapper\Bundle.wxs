<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi"
     xmlns:util="http://schemas.microsoft.com/wix/UtilExtension"
     xmlns:bal="http://schemas.microsoft.com/wix/BalExtension">

  <Bundle Name="QuantBoost Installer"
      Version="0.1.0"
      Manufacturer="QuantBoost.ai"
      UpgradeCode="1B4C9B2B-4FD1-4B97-9A8D-8C2D5E8B9A33">

    <BootstrapperApplicationRef Id="WixStandardBootstrapperApplication.RtfLicense">
      <bal:WixStandardBootstrapperApplication
        LicenseFile="License.rtf"
        ShowVersion="yes" />
    </BootstrapperApplicationRef>

    <!-- This variable tells the bootstrapper to install per-machine -->
    <Variable Name="InstallScope" Type="string" Value="perMachine" />

    <Chain>
      <PackageGroupRef Id="NetFx481" />
      <PackageGroupRef Id="VSTORuntime" />
      <MsiPackage Id="QuantBoostMsi" SourceFile="$(var.QuantBoost.WixInstaller.TargetPath)" DisplayInternalUI="no" Visible="no"/>
    </Chain>
  </Bundle>

  <Fragment>
    <PackageGroup Id="NetFx481">
      <ExePackage Id="NetFx481Web"
                  DisplayName="Microsoft .NET Framework 4.8.1"
                  Name="ndp481-web.exe"
				  SourceFile="ndp481-web.exe" 
                  Compressed="no"
                  PerMachine="yes"
                  Vital="yes"
                  DetectCondition="(NetFxRelease32 &gt;= 533320) OR (NetFxRelease64 &gt;= 533320)"
                  DownloadUrl="https://dotnet.microsoft.com/en-us/download/dotnet-framework/thank-you/net481-web-installer"
                  InstallCommand="/passive /norestart"
                  RepairCommand="/passive /norestart"
                  UninstallCommand="/uninstall /passive /norestart">
        <!-- Corrected ExitCode elements for v3 -->
        <ExitCode Value="0" Behavior="success" />
        <ExitCode Value="3010" Behavior="success" />
      </ExePackage>
    </PackageGroup>

    <PackageGroup Id="VSTORuntime">
      <ExePackage Id="VSTORedist"
                  DisplayName="Microsoft Visual Studio Tools for Office Runtime"
                  Name="vstor_redist.exe"
				  SourceFile="vstor_redist.exe"
                  Compressed="no"
                  PerMachine="yes"
                  Vital="yes"
                  DetectCondition="(VstoInstall32 &gt;= 1) OR (VstoInstall64 &gt;= 1)"
                  DownloadUrl="https://download.microsoft.com/download/8/6/4/8641e164-7796-4b34-81c7-30d24a5bd533/vstor_redist.exe"
                  InstallCommand="/passive /norestart">
        <!-- Corrected ExitCode elements for v3 -->
        <ExitCode Value="0" Behavior="success" />
        <ExitCode Value="3010" Behavior="success" />
      </ExePackage>
    </PackageGroup>
  </Fragment>

  <Fragment>
    <util:RegistrySearch Id="NetFxReleaseSearch32"
                         Variable="NetFxRelease32"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full"
                         Value="Release"
                         Result="value" />
    <util:RegistrySearch Id="NetFxReleaseSearch64"
                         Variable="NetFxRelease64"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full"
                         Value="Release"
                         Win64="yes"
                         Result="value" />
    <util:RegistrySearch Id="VstoInstallSearch32"
                         Variable="VstoInstall32"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\VSTO Runtime Setup\v4"
                         Value="Install"
                         Result="value" />
    <util:RegistrySearch Id="VstoInstallSearch64"
                         Variable="VstoInstall64"
                         Root="HKLM"
                         Key="SOFTWARE\Microsoft\VSTO Runtime Setup\v4"
                         Value="Install"
                         Win64="yes"
                         Result="value" />
  </Fragment>
</Wix>