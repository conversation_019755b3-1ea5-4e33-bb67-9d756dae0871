# Azure Container Apps Deployment Guide

## Overview
Deploy Next.js application as a containerized solution using Azure Container Apps for maximum control and reliability.

## Step 1: Docker Configuration

### Create Dockerfile
```dockerfile
# QuantBoost_Frontend/Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build arguments for environment variables
ARG NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
ARG NEXT_PUBLIC_SUPABASE_URL
ARG NEXT_PUBLIC_SUPABASE_ANON_KEY

ENV NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=$NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
ENV NEXT_PUBLIC_SUPABASE_URL=$NEXT_PUBLIC_SUPABASE_URL
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=$NEXT_PUBLIC_SUPABASE_ANON_KEY

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Update next.config.js for Container
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable standalone output for container deployment
  output: 'standalone',
  
  // Configure for container environment
  experimental: {
    // Enable if needed
  },
  
  // Environment variable handling
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // Headers for API routes
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

### .dockerignore
```
node_modules
.next
.git
.gitignore
README.md
Dockerfile
.dockerignore
npm-debug.log
.nyc_output
.coverage
.env.local
.env.development.local
.env.test.local
.env.production.local
```

## Step 2: Azure Container Apps Setup

### Create Container Apps Environment
```bash
# Create resource group if not exists
az group create --name quantboost-rg --location eastus

# Create Container Apps environment
az containerapp env create \
  --name quantboost-env \
  --resource-group quantboost-rg \
  --location eastus
```

### Create Azure Container Registry
```bash
# Create ACR
az acr create \
  --resource-group quantboost-rg \
  --name quantboostregistry \
  --sku Basic \
  --admin-enabled true

# Get login server
az acr show --name quantboostregistry --query loginServer --output tsv
```

## Step 3: Container App Configuration

### Create Container App with Environment Variables
```bash
# Create container app
az containerapp create \
  --name quantboost-frontend \
  --resource-group quantboost-rg \
  --environment quantboost-env \
  --image quantboostregistry.azurecr.io/quantboost-frontend:latest \
  --target-port 3000 \
  --ingress 'external' \
  --registry-server quantboostregistry.azurecr.io \
  --registry-username quantboostregistry \
  --registry-password $(az acr credential show --name quantboostregistry --query passwords[0].value --output tsv) \
  --env-vars \
    STRIPE_SECRET_KEY="sk_test_51R..." \
    STRIPE_WEBHOOK_SECRET="whsec_..." \
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51R..." \
    NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co" \
    NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
    SUPABASE_SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
    NODE_ENV="production" \
  --cpu 0.5 \
  --memory 1Gi \
  --min-replicas 1 \
  --max-replicas 3
```

## Step 4: GitHub Actions for Container Deployment

### .github/workflows/azure-container-apps.yml
```yaml
name: Deploy to Azure Container Apps

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY_NAME: quantboostregistry
  IMAGE_NAME: quantboost-frontend
  RESOURCE_GROUP: quantboost-rg
  CONTAINER_APP_NAME: quantboost-frontend
  CONTAINER_APP_ENVIRONMENT: quantboost-env

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Azure Container Registry
      uses: azure/docker-login@v1
      with:
        login-server: ${{ env.REGISTRY_NAME }}.azurecr.io
        username: ${{ secrets.ACR_USERNAME }}
        password: ${{ secrets.ACR_PASSWORD }}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./QuantBoost_Frontend
        file: ./QuantBoost_Frontend/Dockerfile
        push: true
        tags: ${{ env.REGISTRY_NAME }}.azurecr.io/${{ env.IMAGE_NAME }}:${{ github.sha }}
        build-args: |
          NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${{ secrets.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY }}
          NEXT_PUBLIC_SUPABASE_URL=${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
    
    - name: Log in to Azure
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
    
    - name: Deploy to Azure Container Apps
      uses: azure/CLI@v1
      with:
        inlineScript: |
          az containerapp update \
            --name ${{ env.CONTAINER_APP_NAME }} \
            --resource-group ${{ env.RESOURCE_GROUP }} \
            --image ${{ env.REGISTRY_NAME }}.azurecr.io/${{ env.IMAGE_NAME }}:${{ github.sha }}
```

## Step 5: Environment Variable Management

### Using Azure Key Vault (Recommended for Production)
```bash
# Create Key Vault
az keyvault create \
  --name quantboost-keyvault \
  --resource-group quantboost-rg \
  --location eastus

# Add secrets
az keyvault secret set \
  --vault-name quantboost-keyvault \
  --name "stripe-secret-key" \
  --value "sk_test_51R..."

az keyvault secret set \
  --vault-name quantboost-keyvault \
  --name "supabase-service-key" \
  --value "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# Update container app to use Key Vault
az containerapp update \
  --name quantboost-frontend \
  --resource-group quantboost-rg \
  --set-env-vars \
    STRIPE_SECRET_KEY="secretref:stripe-secret-key" \
    SUPABASE_SERVICE_KEY="secretref:supabase-service-key"
```

## Step 6: Cost Analysis

### Container Apps Pricing:
- **Consumption Plan**: Pay per vCPU-second and memory GB-second
- **Estimated Monthly Cost**: $15-50/month (depending on traffic)
- **Scaling**: Automatic scaling from 0 to N instances

### Cost Comparison:
| Service | Monthly Cost | Pros | Cons |
|---------|-------------|------|------|
| Static Web Apps | $9 | Cheapest | API issues |
| App Service B1 | $13 | Simple | Always-on |
| Container Apps | $15-50 | Flexible | Complex setup |
| Functions Hybrid | $9-29 | Cost-effective | Multi-service |

## Step 7: Migration Timeline

### Phase 1 (Day 1-2): Container Setup
- Create Dockerfile and container configuration
- Set up Azure Container Registry
- Create Container Apps environment
- Test local container build

### Phase 2 (Day 3-4): Deployment Pipeline
- Configure GitHub Actions workflow
- Set up environment variables and secrets
- Deploy to staging environment
- Test payment processing functionality

### Phase 3 (Day 5): Production Deployment
- Deploy to production Container Apps
- Configure custom domain and SSL
- Monitor performance and scaling
- Validate all functionality

## Step 8: Advantages of Container Apps

### Benefits:
- **Reliable Environment Variables**: Full control over container environment
- **Automatic Scaling**: Scale to zero when not in use
- **Container Consistency**: Same environment locally and in production
- **Advanced Networking**: VNet integration, private endpoints
- **Monitoring**: Built-in Application Insights integration

### Considerations:
- **Complexity**: More setup than App Service
- **Container Management**: Need to maintain Docker images
- **Cold Starts**: Potential latency when scaling from zero
- **Learning Curve**: Container and Kubernetes concepts

## Step 9: Breaking Changes Assessment

### Required Changes:
- Add Dockerfile to project
- Update next.config.js for standalone output
- Modify GitHub Actions workflow
- Set up container registry

### No Changes Needed:
- API routes code (works as-is)
- Environment variable usage in code
- Stripe integration logic
- Supabase integration

## Step 10: Monitoring and Troubleshooting

### Container Apps Monitoring:
```bash
# View container logs
az containerapp logs show \
  --name quantboost-frontend \
  --resource-group quantboost-rg \
  --follow

# Check container app status
az containerapp show \
  --name quantboost-frontend \
  --resource-group quantboost-rg \
  --query properties.provisioningState
```

### Health Checks:
```javascript
// Add to your Next.js app
// pages/api/health.js
export default function handler(req, res) {
  res.status(200).json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV 
  });
}
```
