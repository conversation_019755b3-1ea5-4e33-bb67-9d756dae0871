import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { requireBillingAccess } from '@/lib/userTypeMiddleware';

export async function POST(req: NextRequest) {
  // Check user access before processing
  const accessDenied = await requireBillingAccess(req);
  if (accessDenied) return accessDenied;

  // Create Stripe client inside the function to avoid build-time issues
  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-08-27.basil' as any,
  });
  try {
    const { customerId } = await req.json();

    if (!customerId) {
      return NextResponse.json({ error: 'Missing customerId' }, { status: 400 });
    }

    const invoices = await stripe.invoices.list({
      customer: customerId,
      limit: 10, // Or any other limit
    });

    return NextResponse.json(invoices.data);
  } catch (error) {
    console.error('Error fetching Stripe invoices:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}