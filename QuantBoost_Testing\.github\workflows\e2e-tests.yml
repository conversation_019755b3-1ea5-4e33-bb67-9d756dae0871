name: E2E Checkout Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    env:
      BASE_URL: https://app-quantboost-frontend-staging.azurewebsites.net
      ENABLE_SECURITY_TESTS: true
      ENABLE_LOAD_TESTS: false
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'
          cache-dependency-path: QuantBoost_Testing/package.json

      - name: Install dependencies
        working-directory: QuantBoost_Testing
        run: |
          npm install
          npx playwright install --with-deps chromium

      - name: Run Tests
        working-directory: QuantBoost_Testing
        env:
          STRIPE_SECRET_KEY_TEST: ${{ secrets.STRIPE_SECRET_KEY_TEST }}
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
          S<PERSON>ABAS<PERSON>_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        run: |
          npm run ci

      - name: Upload HTML Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-html-report
          path: QuantBoost_Testing/playwright/reports/html

      - name: Upload Summary
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-summary
          path: QuantBoost_Testing/playwright/reports/summary.md
