# QuantBoost Infrastructure - Terraform Variables
# Copy this file to terraform.tfvars and fill in your actual values

# Supabase Configuration
supabase_url              = "https://izoutrnsxaaoueljiimu.supabase.co"
supabase_anon_key         = "your_supabase_anon_key_here"
supabase_service_role_key = "your_supabase_service_role_key_here"

# Stripe Configuration
stripe_publishable_key        = "pk_test_your_publishable_key_here"
stripe_secret_key            = "sk_test_your_secret_key_here"
stripe_webhook_signing_secret = "whsec_your_webhook_secret_here"

# Security
jwt_secret = "your_very_secure_jwt_secret_at_least_32_characters_long"

# Azure Configuration
environment = "staging"  # or "prod"
location    = "West US 3"

# Notes:
# - Frontend will be deployed to East US 2 (Static Web Apps limitation)
# - Backend API will be deployed to the location specified above
# - For production, set environment = "prod" and use live Stripe keys