import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

/*
  POST /api/auth/exchange-session
  Body: { code: string }
  code format: email|expEpoch|sig  (sig = base64url(hmac_sha256(secret, `${email}|${exp}`)))
  Validates signature + expiry (5 min window) then generates magic link for silent login.
  Requires EXCHANGE_SESSION_SECRET env var.
*/

function verifyCode(code: string): { email: string } | null {
  try {
    const secret = process.env.EXCHANGE_SESSION_SECRET;
    if (!secret) throw new Error('Missing EXCHANGE_SESSION_SECRET');
    const parts = code.split('|');
    if (parts.length !== 3) return null;
    const [emailRaw, expRaw, sig] = parts;
    const payload = `${emailRaw}|${expRaw}`;
    const expected = crypto.createHmac('sha256', secret).update(payload).digest('base64url');
    if (!crypto.timingSafeEqual(Buffer.from(sig), Buffer.from(expected))) return null;
    const exp = parseInt(expRaw, 10);
    if (isNaN(exp) || exp < Math.floor(Date.now() / 1000)) return null; // expired
    return { email: emailRaw.toLowerCase() };
  } catch (e) {
    console.error('verifyCode error', e);
    return null;
  }
}

export async function POST(req: NextRequest) {
  try {
    const { code } = await req.json();
    if (!code) {
      return NextResponse.json({ error: 'Missing code' }, { status: 400 });
    }

    const verified = verifyCode(code);
    if (!verified) {
      return NextResponse.json({ error: 'Invalid or expired code' }, { status: 401 });
    }

    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!,
      { auth: { persistSession: false } }
    );

    const { data: { users }, error: listError } = await supabaseAdmin.auth.admin.listUsers();
    if (listError) {
      console.error('List users error:', listError);
      return NextResponse.json({ error: 'Auth service unavailable' }, { status: 503 });
    }

    const target = users.find(u => u.email?.toLowerCase() === verified.email);
    if (!target) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { data: linkData, error: linkError } = await supabaseAdmin.auth.admin.generateLink({
      type: 'magiclink',
      email: verified.email,
      options: { redirectTo: `/v1/auth/callback?auto_login=true&email=${encodeURIComponent(verified.email)}&from=exchange-session` }
    });

    if (linkError) {
      console.error('generateLink error:', linkError);
      return NextResponse.json({ error: 'Failed to generate link' }, { status: 500 });
    }

    const actionLink = linkData.properties?.action_link;
    let accessToken: string | undefined; let refreshToken: string | undefined;
    if (actionLink) {
      try {
        const urlObj = new URL(actionLink);
        const at = urlObj.searchParams.get('access_token');
        const rt = urlObj.searchParams.get('refresh_token');
        if (at && rt) { accessToken = at; refreshToken = rt; }
      } catch {}
    }

    return NextResponse.json({ success: true, access_token: accessToken, refresh_token: refreshToken, action_link: actionLink });
  } catch (err) {
    console.error('exchange-session error:', err);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
