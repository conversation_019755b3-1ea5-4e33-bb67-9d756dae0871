<!DOCTYPE html>
<html>
<head>
    <title>Authenticating...</title>
    <script>
        function processAuth() {
            try {
                const params = new URLSearchParams(window.location.search);
                const finalRedirectUri = params.get('final_redirect_uri');
                const state = params.get('state'); // Optional

                if (!finalRedirectUri) {
                    document.body.innerHTML = "<h1>Error: Missing redirect information.</h1>";
                    console.error("Error: final_redirect_uri is missing from query parameters.");
                    return;
                }

                const hash = window.location.hash.substring(1); // Remove #
                if (!hash) {
                    document.body.innerHTML = "<h1>Authentication Error</h1><p>No token information found in the URL fragment. Please ensure you clicked the link from your email correctly.</p>";
                    console.error("Error: URL fragment (hash) is missing.");
                    return;
                }
                
                const hashParams = new URLSearchParams(hash);
                const accessToken = hashParams.get('access_token');
                const refreshToken = hashParams.get('refresh_token');
                const expiresIn = hashParams.get('expires_in');
                const tokenType = hashParams.get('token_type');
                // any other params like 'provider_token', 'provider_refresh_token' if needed

                if (accessToken && refreshToken) {
                    let targetUrl = finalRedirectUri;
                    targetUrl += (targetUrl.includes('?') ? '&' : '?') + 'access_token=' + encodeURIComponent(accessToken);
                    targetUrl += '&refresh_token=' + encodeURIComponent(refreshToken);
                    if (expiresIn) {
                        targetUrl += '&expires_in=' + encodeURIComponent(expiresIn);
                    }
                    if (tokenType) {
                        targetUrl += '&token_type=' + encodeURIComponent(tokenType);
                    }
                    if (state) {
                        targetUrl += '&state=' + encodeURIComponent(state);
                    }
                    
                    document.body.innerHTML = "<h1>Processing...</h1><p>Please wait, redirecting back to the application. This window will close automatically if the application receives the authentication details.</p>";
                    // Try to close the window after a short delay, assuming the redirect to localhost will be fast.
                    // Some browsers might block this, but it's a good UX attempt.
                    setTimeout(() => {
                        // Check if the target is localhost, if so, it's likely the desktop app.
                        if (targetUrl.startsWith("http://localhost")) {
                            window.close();
                        }
                    }, 1500); // 1.5 seconds delay
                    
                    window.location.replace(targetUrl); // Use replace to avoid back button issues
                } else {
                    let errorMessage = "<h1>Authentication Error</h1><p>Could not retrieve necessary tokens (access_token or refresh_token) from Supabase redirect. Please try the login process again.</p>";
                    if (!accessToken) errorMessage += "<p>Missing: access_token</p>";
                    if (!refreshToken) errorMessage += "<p>Missing: refresh_token</p>";
                    document.body.innerHTML = errorMessage;
                    console.error("Tokens not found in hash:", hash, "Access Token:", accessToken, "Refresh Token:", refreshToken);
                }
            } catch (e) {
                document.body.innerHTML = "<h1>Error</h1><p>An unexpected error occurred during authentication relay. Please see console for details.</p>";
                console.error("Error processing auth relay:", e);
            }
        }
        window.onload = processAuth;
    </script>
</head>
<body>
    <h1>Authenticating, please wait...</h1>
    <p>If this page does not redirect or close automatically, please check for error messages or try returning to the QuantBoost add-in.</p>
</body>
</html>
