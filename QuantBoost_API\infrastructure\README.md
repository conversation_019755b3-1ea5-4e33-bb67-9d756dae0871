# QuantBoost Infrastructure

This directory contains the Terraform configuration for deploying the complete QuantBoost application infrastructure to Azure, including both the backend API and frontend Static Web App.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Azure Resource Group                     │
│                   rg-quantboost-{env}                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  Static Web App │    │  Container App  │                │
│  │   (Frontend)    │◄──►│     (API)       │                │
│  │                 │    │                 │                │
│  │ • Next.js App   │    │ • Node.js API   │                │
│  │ • Stripe Webhooks│    │ • Supabase      │                │
│  │ • Custom Domain │    │ • Auto-scaling  │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           │              ┌─────────────────┐               │
│           │              │   Key Vault     │               │
│           └──────────────┤   (Secrets)     │               │
│                          │                 │               │
│                          │ • Stripe Keys   │               │
│                          │ • Supabase Keys │               │
│                          │ • JWT Secret    │               │
│                          └─────────────────┘               │
│                                   │                        │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │Container Registry│    │App Insights     │               │
│  │     (ACR)       │    │  (Monitoring)   │               │
│  └─────────────────┘    └─────────────────┘               │
└─────────────────────────────────────────────────────────────┘
```

## 📋 Resources Deployed

### Core Infrastructure
- **Resource Group**: `rg-quantboost-{environment}`
- **Key Vault**: Secure storage for secrets and API keys
- **Application Insights**: Monitoring and logging for both frontend and backend

### Backend (API)
- **Container App**: Scalable Node.js API hosting
- **Container Registry**: Docker image storage
- **Container App Environment**: Managed container hosting environment

### Frontend
- **Static Web App**: Next.js application hosting with:
  - Global CDN
  - Custom domain support
  - Automatic HTTPS
  - API routes support

## 🚀 Quick Start

### Prerequisites
1. **Azure CLI** installed and logged in (`az login`)
2. **Terraform** v1.9.8+ installed
3. **PowerShell** (for Windows deployment script)

### 1. Configure Variables
```bash
# Copy the example file
cp terraform.tfvars.example terraform.tfvars

# Edit with your actual values
# - Supabase URL and keys
# - Stripe keys (test for staging, live for prod)
# - JWT secret
```

### 2. Deploy Infrastructure

**Option A: Using PowerShell Script (Recommended)**
- update changes to Github and use Github Actions to push to Azure.

## 🔧 Configuration

### Environment Variables
The Static Web App is automatically configured with these environment variables:

| Variable | Source | Description |
|----------|--------|-------------|
| `NEXT_PUBLIC_SUPABASE_URL` | terraform.tfvars | Supabase project URL |
| `SUPABASE_SERVICE_KEY` | terraform.tfvars | Supabase service role key |
| `STRIPE_PUBLISHABLE_KEY` | terraform.tfvars | Stripe publishable key |
| `STRIPE_SECRET_KEY` | terraform.tfvars | Stripe secret key |
| `STRIPE_WEBHOOK_SECRET` | terraform.tfvars | Stripe webhook signing secret |
| `NEXT_PUBLIC_BASE_URL` | Auto-generated | Frontend URL |
| `NEXT_PUBLIC_AZURE_API_URL` | Auto-generated | Backend API URL |

### Environments
- **Staging**: `environment = "staging"`
  - Uses test Stripe keys
  - Deployed to staging subdomain
- **Production**: `environment = "prod"`
  - Uses live Stripe keys
  - Deployed to quantboost.ai

## 📦 Deployment Workflow

### 1. Infrastructure Deployment
```bash
# Initialize Terraform
terraform init

# Review the plan
terraform plan

# Deploy infrastructure
terraform apply
```

### 2. Frontend Deployment
The Static Web App is configured for automatic deployment via GitHub Actions or manual deployment:

```bash
# Manual deployment (from QuantBoost_Frontend directory)
npm run build
az staticwebapp deploy --name swa-quantboost-frontend-staging --source ./out
```

### 3. Backend Deployment
```bash
# Build and push container (from QuantBoost_API directory)
az acr build --registry acrquantboost{suffix} --image quantboost-api:latest .
```

## 🔒 Security

### Secrets Management
- All sensitive values stored in Azure Key Vault
- Environment variables injected at runtime
- No secrets in Terraform state (uses variables)

### Access Control
- Container App uses system-assigned managed identity
- Key Vault access policies restrict secret access
- Static Web App environment variables are encrypted

## 🌐 Custom Domain Setup

### For Production (quantboost.ai)
1. Deploy infrastructure with `environment = "prod"`
2. Get Static Web App URL from Terraform output
3. Configure DNS:
   ```
   Type: CNAME
   Name: @
   Value: {static-web-app-url}
   ```
4. Add custom domain in Azure Portal

### For Staging (dev.quantboost.ai)
1. Deploy infrastructure with `environment = "staging"`
2. Configure DNS:
   ```
   Type: CNAME
   Name: dev
   Value: {static-web-app-url}
   ```

## 📊 Monitoring

### Application Insights
- Automatic logging for both frontend and backend
- Performance monitoring
- Error tracking
- Custom telemetry

### Access Logs
```bash
# View Container App logs
az containerapp logs show --name ca-quantboost-api --resource-group rg-quantboost-staging

# View Static Web App logs
az staticwebapp show --name swa-quantboost-frontend-staging --resource-group rg-quantboost-staging
```

## 🔧 Troubleshooting

### Common Issues

**1. Terraform State Lock**
```bash
# If state is locked, force unlock (use carefully)
terraform force-unlock {lock-id}
```

**2. Container App Not Starting**
```bash
# Check container logs
az containerapp logs show --name ca-quantboost-api --resource-group rg-quantboost-staging --follow
```

**3. Static Web App Build Failures**
- Check GitHub Actions logs
- Verify Node.js version compatibility
- Check environment variables configuration

### Support
For infrastructure issues, check:
1. Azure Portal resource health
2. Terraform state consistency
3. Environment variable configuration
4. Network connectivity between resources
