// /utils/eventLogger.js

// Get the admin client, which has permission to bypass RLS for logging
const { supabaseAdmin } = require('../supabaseClient');

// The 'supabase' argument is now ignored, but we keep it for compatibility
// with existing calls until you refactor them.
async function logLicenseEvent(supabase_ignored, event_type, license_id, machine_id, user_identifier, status_code) {
    
    // ... (your existing validation logic is fine) ...
    const safe_event_type = event_type ? String(event_type).substring(0, 255) : 'unknown_event';
    const safe_license_id = license_id ? String(license_id).substring(0, 255) : null;
    const safe_machine_id = machine_id ? String(machine_id).substring(0, 255) : 'unknown_machine';
    const safe_user_identifier = user_identifier ? String(user_identifier).substring(0, 255) : 'unknown_user';
    const safe_status_code = status_code ? String(status_code).substring(0, 100) : 'unknown_status';

    let eventToInsert = {
        event_type: safe_event_type,
        license_id: safe_license_id,
        machine_id: safe_machine_id,
        email: safe_user_identifier,
        status: safe_status_code,
    };

    try {
        // Use the admin client to insert the event. This will succeed.
        const { error } = await supabaseAdmin.from('license_events').insert(eventToInsert);
        
        if (error) {
            console.error(`Error logging license event [${safe_event_type}]...:`, error.message);
        }
    } catch (e) {
        console.error(`Exception during license event logging...:`, e.message);
    }
}

module.exports = {
    logLicenseEvent,
};
