// Analytics and dispute tracking types
export interface DisputeMetrics {
  total_disputes: number;
  open_disputes: number;
  overdue_disputes: number;
  disputes_won: number;
  disputes_lost: number;
  total_disputed_amount: number;
  total_fees_paid: number;
  avg_response_time_hours: number;
  dispute_rate_percentage: number;
}

export interface PaymentHealthMetrics {
  total_payment_attempts: number;
  successful_payments: number;
  failed_payments: number;
  success_rate_percentage: number;
  high_risk_payments: number;
  avg_risk_score: number;
  total_amount_processed: number;
  total_refund_amount: number;
  refund_rate_percentage: number;
}

export interface DisputeAnalytics {
  id: string;
  stripe_dispute_id: string;
  stripe_charge_id: string;
  amount: number;
  currency: string;
  reason: string;
  status: string;
  created: string;
  evidence_due_by?: string;
  closed_at?: string;
  fee_amount: number;
  fee_refunded: boolean;
  priority: string;
  customer_email: string;
  first_name?: string;
  last_name?: string;
  plan_id?: string;
  product_name?: string;
  subscription_amount?: number;
  interval?: string;
  is_overdue: boolean;
  is_closed: boolean;
  days_to_resolution?: number;
  time_until_due?: string;
}

export interface RefundAnalytics {
  id: string;
  stripe_refund_id: string;
  stripe_charge_id: string;
  amount: number;
  currency: string;
  reason?: string;
  status: string;
  refund_type?: string;
  internal_reason?: string;
  created_at: string;
  customer_email: string;
  first_name?: string;
  last_name?: string;
  plan_id?: string;
  product_name?: string;
  subscription_amount?: number;
  original_charge_amount: number;
  charge_date: string;
  refund_percentage: number;
}

export interface CustomerLifecycleAnalytics {
  user_id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  stripe_customer_id?: string;
  customer_since: string;
  total_subscriptions: number;
  active_subscriptions: number;
  canceled_subscriptions: number;
  total_charges: number;
  total_revenue: number;
  avg_charge_amount: number;
  last_payment?: string;
  next_billing_date?: string;
  total_disputes: number;
  total_refunds: number;
  total_refunded: number;
  customer_health_score: number;
}

export interface SystemAlert {
  id: string;
  alert_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  user_id?: string;
  dispute_id?: string;
  refund_id?: string;
  subscription_id?: string;
  data?: any;
  is_read: boolean;
  is_resolved: boolean;
  resolved_by?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
  // Relationships
  profiles?: {
    email: string;
    first_name?: string;
    last_name?: string;
  };
  disputes?: {
    stripe_dispute_id: string;
    amount: number;
    reason: string;
  };
  refunds?: {
    stripe_refund_id: string;
    amount: number;
    reason?: string;
  };
}

export interface AlertSummary {
  total: number;
  unread: number;
  unresolved: number;
  critical: number;
  high: number;
  by_type: Record<string, number>;
}

export interface PaymentHealthCustomer {
  user_id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  stripe_customer_id?: string;
  total_payment_events: number;
  successful_payments: number;
  failed_payments: number;
  avg_risk_score: number;
  high_risk_events: number;
  last_payment_attempt?: string;
  recent_events: number;
  success_rate_percentage: number;
}

export interface PaymentEvent {
  id: string;
  stripe_event_id: string;
  event_type: string;
  stripe_payment_intent_id?: string;
  stripe_charge_id?: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  user_id?: string;
  subscription_id?: string;
  amount?: number;
  currency: string;
  status?: string;
  failure_code?: string;
  failure_message?: string;
  risk_level?: string;
  risk_score?: number;
  fraud_outcome?: string;
  payment_method_type?: string;
  payment_method_brand?: string;
  payment_method_last4?: string;
  payment_method_country?: string;
  event_data?: any;
  processed_successfully: boolean;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface DisputeReasonStats {
  [reason: string]: {
    count: number;
    total_amount: number;
    won: number;
    lost: number;
    pending: number;
  };
}

export interface RiskAnalysis {
  high_risk_count: number;
  elevated_risk_count: number;
  normal_risk_count: number;
  avg_risk_score: number;
  high_risk_success_rate: number;
}
