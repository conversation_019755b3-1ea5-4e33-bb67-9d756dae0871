C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Debug\QuantBoost_Licensing.dll
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Debug\QuantBoost_Licensing.pdb
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Debug\Newtonsoft.Json.Bson.dll
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Debug\Newtonsoft.Json.dll
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Debug\Newtonsoft.Json.xml
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Debug\Newtonsoft.Json.Bson.pdb
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Debug\Newtonsoft.Json.Bson.xml
C:\VS projects\QuantBoost\QuantBoost_Licensing\obj\Debug\QuantBoost_Licensing.csproj.AssemblyReference.cache
C:\VS projects\QuantBoost\QuantBoost_Licensing\obj\Debug\QuantBoost_Licensing.csproj.CoreCompileInputs.cache
C:\VS projects\QuantBoost\QuantBoost_Licensing\obj\Debug\QuantBoo.8B3DD63D.Up2Date
C:\VS projects\QuantBoost\QuantBoost_Licensing\obj\Debug\QuantBoost_Licensing.dll
C:\VS projects\QuantBoost\QuantBoost_Licensing\obj\Debug\QuantBoost_Licensing.pdb
