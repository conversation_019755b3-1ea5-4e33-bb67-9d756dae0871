# QuantBoost WiX Installer

This folder contains the WiX authoring for:
- Product.wxs (MSI)
- TestReg.wxs (Office VSTO registry keys)
- Bundle.wxs (Burn bootstrapper EXE)

## Prerequisites
- Install WiX Toolset build tools 3.11 (or WiX v4 if you plan to migrate) and the Visual Studio extension.
- Add the WiX extensions to the project references (VS: right-click project → Add Reference → WixUIExtension, WixUtilExtension, WixNetFxExtension, WixBalExtension).

## Build order
1. Build your VSTO add-ins (QuantBoost_Excel and QuantBoost_PPTX) in Release.
2. Harvest the release outputs with Heat into Excel.Harvested.wxs and PowerPoint.Harvested.wxs.
3. Add harvested .wxs to this project and include the component groups in Product.wxs.
4. Build MSI.
5. Build a separate WiX Bootstrapper project referencing Bundle.wxs to produce QuantBoost.exe.

## Heat sample (run from this folder)
```
heat dir "..\QuantBoost_Excel\bin\Release" -cg ExcelFiles -gg -sfrag -srd -dr EXCELDIR -var var.ExcelSourceDir -out Excel.Harvested.wxs
heat dir "..\QuantBoost_PPTX\bin\Release"  -cg PowerPointFiles -gg -sfrag -srd -dr POWERPOINTDIR -var var.PowerPointSourceDir -out PowerPoint.Harvested.wxs
```
Then define preprocessor variables in the project properties:
- ExcelSourceDir = ..\QuantBoost_Excel\bin\Release
- PowerPointSourceDir = ..\QuantBoost_PPTX\bin\Release

## Signing
You can sign the MSI and the EXE with AzureSignTool in CI once your certificate profile is validated.

## Notes
- Registry keys are authored per-user (HKCU) to keep VSTO activation simple.
- If you need per-machine, consider Office policy keys and deployment via Intune/ConfigMgr.
