import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { ENV, validateStripeConfig, getEnvironmentDebugInfo } from '@/lib/env';

// Configurable polling intervals (ms) for resolving the invoice PaymentIntent.
// In production we use a graduated backoff; in tests we can override with QB_PI_WAIT_OVERRIDES="10,10,10" etc.
const WAIT_INTERVALS: number[] = (process.env.QB_PI_WAIT_OVERRIDES
  ? process.env.QB_PI_WAIT_OVERRIDES.split(',').map(v => parseInt(v.trim(), 10)).filter(n => !isNaN(n) && n > 0)
  : [200, 400, 600, 800, 1000, 1200, 1500]); // total ~5.7s max wait

// Maximum overall wait derived from intervals (used for logging only)
const MAX_WAIT_MS = WAIT_INTERVALS.reduce((a, b) => a + b, 0);

// Valid price IDs for QuantBoost subscriptions - Individual and Team use same price IDs
const VALID_PRICE_IDS = {
  'price_1RC3HTE6FvhUKV1bE9D6zf6e': { // Annual (Individual or Team based on quantity)
    name: 'Annual Plan',
    description: 'Full access, billed annually.',
    productId: 'prod_S6Fn893jGxRhKk'
  },
  'price_1RyhsAE6FvhUKV1bImD5Ft34': { // Monthly (Individual or Team based on quantity)
    name: 'Monthly Plan',
    description: 'Full access, billed monthly.',
    productId: 'prod_S6Fn893jGxRhKk'
  }
};

export async function POST(req: NextRequest) {
  const started = Date.now();
  const debugInfo = getEnvironmentDebugInfo();
  const stripeValidation = validateStripeConfig();
  if (!stripeValidation.isValid) {
    console.error('stripe.config.invalid', stripeValidation);
    return NextResponse.json({ error: 'Payment service unavailable' }, { status: 500 });
  }

  const stripe = new Stripe(ENV.STRIPE_SECRET_KEY!, { apiVersion: '2025-08-27.basil' as any });

  try {
    const body = await req.json();
  const { priceId, userId, email, quantity = 1, customerInfo } = body || {};
    console.log(JSON.stringify({ msg: 'subscription.request.start', priceId, qty: quantity, hasEmail: !!email }));

    // Basic validation
    if (!priceId) return NextResponse.json({ error: 'Missing priceId' }, { status: 400 });
    const priceDetails = VALID_PRICE_IDS[priceId as keyof typeof VALID_PRICE_IDS];
    if (!priceDetails) return NextResponse.json({ error: 'Invalid priceId' }, { status: 400 });
    // Email required (captured via LinkAuthenticationElement before this call)
    if (!email || typeof email !== 'string' || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email) || email === '<EMAIL>') {
      return NextResponse.json({ error: 'Valid email required' }, { status: 400 });
    }

    // 1. Find or create customer by email (idempotent via search)
  let customer: Stripe.Customer | null = null;
  const existingCustomers = await stripe.customers.list({ email, limit: 1 });
    if (existingCustomers.data.length > 0) {
      customer = existingCustomers.data[0];
      // Optional lightweight update if new info supplied
      const updatePayload: Stripe.CustomerUpdateParams = {};
      if (customerInfo?.firstName && customerInfo?.lastName) {
        updatePayload.name = `${customerInfo.firstName} ${customerInfo.lastName}`;
      }
      if (customerInfo?.phone) updatePayload.phone = customerInfo.phone;
      if (customerInfo?.addressLine1 && customerInfo?.city && customerInfo?.country) {
        updatePayload.address = {
          line1: customerInfo.addressLine1,
          line2: customerInfo.addressLine2 || undefined,
          city: customerInfo.city,
          state: customerInfo.state || undefined,
          postal_code: customerInfo.postalCode || undefined,
          country: customerInfo.country,
        };
      }
      if (customer && Object.keys(updatePayload).length > 0) {
        try { customer = await stripe.customers.update(customer.id, updatePayload); } catch { /* swallow minor update failures */ }
      }
    } else {
      customer = await stripe.customers.create({
        email,
        name: customerInfo?.firstName && customerInfo?.lastName ? `${customerInfo.firstName} ${customerInfo.lastName}` : undefined,
        phone: customerInfo?.phone || undefined,
        address: customerInfo?.addressLine1 && customerInfo?.city && customerInfo?.country ? {
          line1: customerInfo.addressLine1,
          line2: customerInfo.addressLine2 || undefined,
          city: customerInfo.city,
          state: customerInfo.state || undefined,
          postal_code: customerInfo.postalCode || undefined,
          country: customerInfo.country,
        } : undefined,
        metadata: { app: 'quantboost', userId: userId || '' }
      });
    }

    // 2. Attempt to find a reusable active/incomplete subscription for same customer+price (avoid duplicates on rapid retries)
    // We can't filter server-side by metadata priceId directly; list few recent subs.
  if (!customer) return NextResponse.json({ error: 'Customer creation failed' }, { status: 500 });
  const existingSubs = await stripe.subscriptions.list({ customer: customer.id, status: 'all', limit: 10, expand: ['data.latest_invoice.payment_intent'] });
    let reusableSub: Stripe.Subscription | null = null;
    for (const sub of existingSubs.data) {
      if (sub.items.data.some(i => (i.price.id === priceId)) && ['incomplete', 'trialing', 'active', 'past_due'].includes(sub.status)) {
        // If incomplete, ensure we have a payment_intent client_secret to return (latest invoice)
        reusableSub = sub;
        break;
      }
    }

    let subscription: Stripe.Subscription;
    if (reusableSub && reusableSub.latest_invoice) {
      subscription = reusableSub;
      console.log(JSON.stringify({ msg: 'subscription.reuse', subId: subscription.id, status: subscription.status, customerId: customer.id }));
      
      // For incomplete subscriptions, we need to finalize the invoice to create a PaymentIntent
      if (subscription.status === 'incomplete' && subscription.latest_invoice) {
        const invoiceId = typeof subscription.latest_invoice === 'string' 
          ? subscription.latest_invoice 
          : subscription.latest_invoice.id;
        
        try {
          // Check if invoice needs finalization
          const existingInvoice = await stripe.invoices.retrieve(invoiceId);
          if (existingInvoice.status === 'draft') {
            console.log(JSON.stringify({ msg: 'invoice.finalizing', invoiceId }));
            await stripe.invoices.finalizeInvoice(invoiceId);
          }
          
          // Re-fetch subscription with expanded invoice and payment_intent
          subscription = await stripe.subscriptions.retrieve(subscription.id, {
            expand: ['latest_invoice.payment_intent']
          });
          console.log(JSON.stringify({ msg: 'subscription.refreshed', subId: subscription.id }));
        } catch (e: any) {
          console.error('invoice.finalization.error', { invoiceId, error: e.message });
          // If finalization fails, create a new subscription instead
          reusableSub = null;
        }
      }
    }
    
    if (!reusableSub || !reusableSub.latest_invoice) {
      // Idempotency key to avoid duplicate creates on fast double-submit (customer+price+qty)
      const idemKey = `sub:${customer.id}:${priceId}:${quantity}`;
      subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: priceId, quantity }],
        payment_behavior: 'default_incomplete',
        payment_settings: {
          save_default_payment_method: 'on_subscription',
          payment_method_types: ['card', 'link']
        },
        metadata: {
          priceId,
          email,
          userId: userId || '',
          productName: priceDetails.name,
          quantity: String(quantity),
          app: 'quantboost'
        },
        expand: ['latest_invoice.payment_intent']
      }, { idempotencyKey: idemKey });
      console.log(JSON.stringify({ msg: 'subscription.create', subId: subscription.id, customerId: customer.id, idemKey }));
    }

    // 3. Resolve PaymentIntent from latest invoice (expand above ensures object where possible)
    let invoice: Stripe.Invoice | null = null;
    if (typeof subscription.latest_invoice === 'string') {
      invoice = await stripe.invoices.retrieve(subscription.latest_invoice, { expand: ['payment_intent'] });
    } else {
      invoice = subscription.latest_invoice as Stripe.Invoice | null;
    }
    if (!invoice) return NextResponse.json({ error: 'Invoice missing' }, { status: 500 });

    // Helper to resolve PI reference (string | object | null)
    const derefPaymentIntent = async (inv: Stripe.Invoice | null): Promise<Stripe.PaymentIntent | null> => {
      if (!inv) return null;
      const raw = (inv as any).payment_intent;
      if (!raw) return null;
      if (typeof raw === 'string') return await stripe.paymentIntents.retrieve(raw);
      return raw as Stripe.PaymentIntent;
    };

    let pi: Stripe.PaymentIntent | null = await derefPaymentIntent(invoice);
    let attempt = 0;
    // Extended polling strategy: Stripe occasionally returns invoice without hydrated payment_intent during propagation.
    // We poll with increasing backoff up to ~MAX_WAIT_MS.
    while (!pi && attempt < WAIT_INTERVALS.length) {
      const wait = WAIT_INTERVALS[attempt];
      await new Promise(r => setTimeout(r, wait));
      attempt += 1;
      try {
        invoice = await stripe.invoices.retrieve(invoice.id as string, { expand: ['payment_intent'] });
        pi = await derefPaymentIntent(invoice);
        if (pi) {
          console.log(JSON.stringify({ msg: 'payment_intent.resolve.success', attempt, waitedMs: WAIT_INTERVALS.slice(0, attempt).reduce((a,b)=>a+b,0), id: pi.id }));
          break;
        }
      } catch (e: any) {
        console.warn('payment_intent.poll.error', { attempt, wait, message: e?.message });
      }
    }

    if (!pi) {
      const duration = Date.now() - started;
      const payload = {
        pending: true,
        subscriptionId: subscription.id,
        invoiceId: invoice.id,
        attempts: attempt,
        maxAttempts: WAIT_INTERVALS.length,
        waitedTotalMs: WAIT_INTERVALS.slice(0, attempt).reduce((a,b)=>a+b,0),
        durationMs: duration,
        maxPlannedWaitMs: MAX_WAIT_MS,
        hint: 'payment_intent_pending'
      };
      console.warn('payment_intent.pending.return', payload);
      // 202 Accepted signals asynchronous processing – client should poll.
      return NextResponse.json(payload, { status: 202 });
    }

    const responsePayload = {
      clientSecret: pi.client_secret,
      subscriptionId: subscription.id,
      paymentIntentId: pi.id,
  customerId: customer.id,
  productName: priceDetails.name,
      description: priceDetails.description,
      reusedSubscription: reusableSub ? true : false,
      durationMs: Date.now() - started
    };
    console.log(JSON.stringify({ msg: 'subscription.request.success', subId: subscription.id, paymentIntentId: pi.id, reused: !!reusableSub, durationMs: responsePayload.durationMs }));
    return NextResponse.json(responsePayload);
  } catch (err: any) {
    console.error('subscription.route.error', { message: err?.message, stack: err?.stack });
    return NextResponse.json({ error: 'Failed to create subscription' }, { status: 500 });
  }
}