# Deploy Azure App Service for QuantBoost Frontend (Staging)
# This script replaces the Static Web Apps deployment with App Service B1

param(
    [Parameter(Mandatory=$false)]
    [switch]$PlanOnly = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$DestroyStaticWebApp = $false,
    
    [Parameter(Mandatory=$false)]
    [string]$SubscriptionId = ""
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🚀 QuantBoost Frontend - Azure App Service Deployment (Staging)" -ForegroundColor Green
Write-Host "=================================================================" -ForegroundColor Green

# Check if Azure CLI is installed
try {
    $azVersion = az version --output json | ConvertFrom-Json
    Write-Host "✅ Azure CLI version: $($azVersion.'azure-cli')" -ForegroundColor Green
} catch {
    Write-Error "❌ Azure CLI is not installed or not in PATH. Please install Azure CLI first."
    exit 1
}

# Check if Terraform is installed
try {
    $tfVersion = terraform version -json | ConvertFrom-Json
    Write-Host "✅ Terraform version: $($tfVersion.terraform_version)" -ForegroundColor Green
} catch {
    Write-Error "❌ Terraform is not installed or not in PATH. Please install Terraform first."
    exit 1
}

# Set subscription if provided
if ($SubscriptionId) {
    Write-Host "🔧 Setting Azure subscription to: $SubscriptionId" -ForegroundColor Yellow
    az account set --subscription $SubscriptionId
}

# Get current subscription
$currentSub = az account show --output json | ConvertFrom-Json
Write-Host "📋 Current subscription: $($currentSub.name) ($($currentSub.id))" -ForegroundColor Cyan

# Change to the staging infrastructure directory
$stagingDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $stagingDir

Write-Host "📁 Working directory: $stagingDir" -ForegroundColor Cyan

# Initialize Terraform
Write-Host "🔧 Initializing Terraform..." -ForegroundColor Yellow
terraform init

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Terraform initialization failed"
    exit 1
}

# Validate Terraform configuration
Write-Host "✅ Validating Terraform configuration..." -ForegroundColor Yellow
terraform validate

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Terraform validation failed"
    exit 1
}

# Plan the deployment
Write-Host "📋 Planning Terraform deployment..." -ForegroundColor Yellow
terraform plan -out=tfplan

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Terraform planning failed"
    exit 1
}

if ($PlanOnly) {
    Write-Host "✅ Plan completed successfully. Use -PlanOnly:$false to apply changes." -ForegroundColor Green
    exit 0
}

# Confirm deployment
Write-Host ""
Write-Host "⚠️  IMPORTANT: This will replace Static Web Apps with App Service B1" -ForegroundColor Yellow
Write-Host "   - Monthly cost will increase by ~$4.58 (from ~$11 to ~$15.88)" -ForegroundColor Yellow
Write-Host "   - Environment variables will work reliably in API routes" -ForegroundColor Yellow
Write-Host "   - Payment processing issues will be resolved" -ForegroundColor Yellow
Write-Host ""

$confirmation = Read-Host "Do you want to proceed with the deployment? (yes/no)"
if ($confirmation -ne "yes") {
    Write-Host "❌ Deployment cancelled by user" -ForegroundColor Red
    exit 0
}

# Apply the deployment
Write-Host "🚀 Applying Terraform deployment..." -ForegroundColor Green
terraform apply tfplan

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Terraform deployment failed"
    exit 1
}

# Get outputs
Write-Host "📋 Getting deployment outputs..." -ForegroundColor Yellow
$outputs = terraform output -json | ConvertFrom-Json

# Display important information
Write-Host ""
Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 App Service Details:" -ForegroundColor Cyan
Write-Host "   Name: $($outputs.app_service_name.value)" -ForegroundColor White
Write-Host "   URL: https://$($outputs.app_service_default_hostname.value)" -ForegroundColor White
Write-Host "   Plan: $($outputs.app_service_plan_name.value) (B1 Basic)" -ForegroundColor White
Write-Host ""
Write-Host "🔑 Key Vault:" -ForegroundColor Cyan
Write-Host "   Name: $($outputs.key_vault_name.value)" -ForegroundColor White
Write-Host ""
Write-Host "📊 Monitoring:" -ForegroundColor Cyan
Write-Host "   Application Insights configured and enabled" -ForegroundColor White
Write-Host ""

# Optionally destroy Static Web App
if ($DestroyStaticWebApp) {
    Write-Host "⚠️  Destroying Static Web App..." -ForegroundColor Yellow
    Write-Host "   Note: This should be done after confirming App Service is working" -ForegroundColor Yellow
    
    $destroyConfirmation = Read-Host "Are you sure you want to destroy the Static Web App? (yes/no)"
    if ($destroyConfirmation -eq "yes") {
        # This would require modifying the Terraform to remove the Static Web App resource
        Write-Host "ℹ️  To destroy Static Web App, comment out the azurerm_static_web_app resource in main.tf and run terraform apply" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Green
Write-Host "1. Update GitHub Actions workflow to deploy to App Service" -ForegroundColor White
Write-Host "2. Update Next.js configuration (remove standalone output)" -ForegroundColor White
Write-Host "3. Test the deployment with a sample payment" -ForegroundColor White
Write-Host "4. Configure custom domain if needed" -ForegroundColor White
Write-Host "5. Monitor Application Insights for any issues" -ForegroundColor White
Write-Host ""
Write-Host "📖 See app-service-deployment.md for detailed instructions" -ForegroundColor Cyan
Write-Host ""

# Test the App Service endpoint
Write-Host "🧪 Testing App Service endpoint..." -ForegroundColor Yellow
$appServiceUrl = "https://$($outputs.app_service_default_hostname.value)"

try {
    $response = Invoke-WebRequest -Uri $appServiceUrl -Method GET -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ App Service is responding (HTTP $($response.StatusCode))" -ForegroundColor Green
    } else {
        Write-Host "⚠️  App Service responded with HTTP $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  App Service is not yet responding (this is normal for new deployments)" -ForegroundColor Yellow
    Write-Host "   Wait a few minutes and try accessing: $appServiceUrl" -ForegroundColor White
}

Write-Host ""
Write-Host "🎉 App Service deployment completed!" -ForegroundColor Green
Write-Host "   Your Next.js app with reliable environment variables is ready!" -ForegroundColor Green
