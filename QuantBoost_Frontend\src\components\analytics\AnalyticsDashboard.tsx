'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RefreshCw, Calendar, TrendingUp, Shield, Bell } from 'lucide-react';
import { useAnalytics } from '@/hooks/useAnalytics';
import { DisputeAnalytics } from './DisputeAnalytics';
import { PaymentHealth } from './PaymentHealth';
import { AlertsManager } from './AlertsManager';

interface AnalyticsDashboardProps {
  className?: string;
}

export function AnalyticsDashboard({ className }: AnalyticsDashboardProps) {
  const [dateRange, setDateRange] = useState(30);
  const [autoRefresh, setAutoRefresh] = useState(false);
  
  const {
    disputeMetrics,
    paymentHealthMetrics,
    alertSummary,
    isLoading,
    error,
    refreshData,
  } = useAnalytics({ 
    dateRange, 
    autoRefresh, 
    refreshInterval: 60000 // 1 minute
  });

  const handleRefresh = async () => {
    await refreshData();
  };

  const handleDateRangeChange = (days: number) => {
    setDateRange(days);
  };

  const getOverallHealthScore = () => {
    if (!disputeMetrics || !paymentHealthMetrics) return 0;
    
    // Simple health score calculation
    const disputeScore = Math.max(0, 100 - disputeMetrics.dispute_rate_percentage * 10);
    const paymentScore = paymentHealthMetrics.success_rate_percentage;
    const riskScore = Math.max(0, 100 - paymentHealthMetrics.avg_risk_score);
    
    return Math.round((disputeScore + paymentScore + riskScore) / 3);
  };

  const healthScore = getOverallHealthScore();
  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-500';
    return 'text-red-600';
  };

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Business Analytics</h1>
          <p className="text-muted-foreground">
            Monitor payment health, disputes, and system alerts
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            <span>Date Range:</span>
            <Button
              variant={dateRange === 7 ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleDateRangeChange(7)}
            >
              7d
            </Button>
            <Button
              variant={dateRange === 30 ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleDateRangeChange(30)}
            >
              30d
            </Button>
            <Button
              variant={dateRange === 90 ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleDateRangeChange(90)}
            >
              90d
            </Button>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            variant={autoRefresh ? 'default' : 'outline'}
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Calendar className="h-4 w-4 mr-2" />
            Auto Refresh
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <Shield className="h-5 w-5" />
              <span className="font-medium">Error loading analytics data:</span>
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overall Health Score */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Overall Business Health
          </CardTitle>
          <CardDescription>
            Combined score based on payment success, dispute rates, and risk metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <div className={`text-4xl font-bold ${getHealthScoreColor(healthScore)}`}>
                {healthScore}
              </div>
              <div className="text-sm text-muted-foreground">
                <div>Health Score</div>
                <div className="text-xs">
                  {healthScore >= 80 ? 'Excellent' :
                   healthScore >= 60 ? 'Good' : 'Needs Attention'}
                </div>
              </div>
            </div>
            
            {alertSummary && (
              <div className="flex items-center gap-4 ml-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{alertSummary.critical}</div>
                  <div className="text-xs text-muted-foreground">Critical</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-500">{alertSummary.high}</div>
                  <div className="text-xs text-muted-foreground">High</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{alertSummary.unread}</div>
                  <div className="text-xs text-muted-foreground">Unread</div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Analytics Tabs */}
      <Tabs defaultValue="disputes" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="disputes" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Disputes
          </TabsTrigger>
          <TabsTrigger value="payments" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Payments
          </TabsTrigger>
          <TabsTrigger value="alerts" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Alerts
          </TabsTrigger>
        </TabsList>

        <TabsContent value="disputes" className="space-y-4">
          <DisputeAnalytics 
            metrics={disputeMetrics} 
            isLoading={isLoading} 
          />
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <PaymentHealth 
            metrics={paymentHealthMetrics} 
            isLoading={isLoading} 
          />
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <AlertsManager 
            alertSummary={alertSummary} 
            isLoading={isLoading} 
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
