using System;
using System.Runtime.InteropServices;
using QuantBoost_Shared.Utilities; // For ErrorHandlingService
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using Office = Microsoft.Office.Core;

namespace QuantBoost_Powerpoint_Addin.ExcelLink
{
    /// <summary>
    /// Provides safe wrappers for interacting with the PowerPoint COM object model.
    /// Handles common operations like accessing presentations, slides, shapes, and inserting content.
    /// Implements IDisposable to assist with managing COM object references held by the wrapper instance.
    /// Note: The Application object itself is typically managed by the Add-in's lifetime.
    /// </summary>
    public class PowerPointComWrapper : IDisposable
    {
        #region Fields

        private readonly PowerPoint.Application _pptApp; // Reference to the main PowerPoint Application object
        private PowerPoint.Presentation _presentation; // Cached reference to the active presentation
        private PowerPoint.Slide _slide; // Cached reference to a slide (e.g., last accessed by index)
        private bool _disposed = false;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the PowerPointComWrapper class.
        /// </summary>
        /// <param name="pptApp">The running PowerPoint Application instance, typically obtained from Globals.ThisAddIn.Application.</param>
        /// <exception cref="ArgumentNullException">Thrown if pptApp is null.</exception>
        public PowerPointComWrapper(PowerPoint.Application pptApp)
        {
            if (pptApp == null)
            {
                // Log critical error, as wrapper cannot function without the application object.
                ErrorHandlingService.LogException(new ArgumentNullException("pptApp"), "PowerPointComWrapper Initialization Failed: Application object is null.");
                throw new ArgumentNullException("pptApp", "PowerPoint Application object cannot be null.");
            }
            _pptApp = pptApp;
        }

        #endregion

        #region Public Methods (Presentation & Slide Access)

        /// <summary>
        /// Gets the currently active PowerPoint presentation.
        /// Releases any previously cached presentation reference.
        /// </summary>
        /// <returns>The active Presentation object, or null if no presentation is active or an error occurs.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        public PowerPoint.Presentation GetActivePresentation()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(PowerPointComWrapper));
            if (_pptApp == null)
            {
                ErrorHandlingService.LogException(new InvalidOperationException("PowerPoint Application object is null."), "GetActivePresentation Error");
                return null;
            }

            // Release the previously cached presentation object before getting a new one
            ReleaseComObjectInternal(_presentation);
            _presentation = null; // Reset field

            try
            {
                // Accessing ActivePresentation throws if no presentation is open/active
                _presentation = _pptApp.ActivePresentation;
                return _presentation;
            }
            catch (COMException ex)
            {
                // Log common errors like "No presentation open" without flooding logs if expected
                ErrorHandlingService.LogException(ex, "Failed to get active presentation (potentially none open).");
                return null;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Unexpected error getting active presentation.");
                return null;
            }
        }

        /// <summary>
        /// Gets a specific slide from the active presentation by its 1-based index.
        /// Releases any previously cached slide reference.
        /// </summary>
        /// <param name="slideIndex">The 1-based index of the slide.</param>
        /// <returns>The Slide object, or null if the index is out of range, no presentation is active, or an error occurs.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        public PowerPoint.Slide GetSlideByIndex(int slideIndex)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(PowerPointComWrapper));

            // Ensure we have an active presentation reference first
            PowerPoint.Presentation pres = GetActivePresentation(); // Use the wrapper method
            if (pres == null)
            {
                // GetActivePresentation already logged potential errors
                return null;
            }

            PowerPoint.Slides slides = null;
            try
            {
                 slides = pres.Slides;
                 if (slides == null) // Defensive check
                 {
                     throw new InvalidOperationException("Could not retrieve Slides collection.");
                 }
                 // Check index bounds
                 if (slideIndex <= 0 || slideIndex > slides.Count)
                 {
                     ErrorHandlingService.LogException(null, string.Format("Slide index {0} is out of range (1-{1}).", slideIndex, slides.Count));
                     return null;
                 }

                 // Release the previously cached slide object before getting a new one
                 ReleaseComObjectInternal(_slide);
                 _slide = null; // Reset field

                 // Accessing by index throws if out of range (already checked, but belt-and-suspenders)
                 _slide = slides[slideIndex];
                 return _slide;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, string.Format("Failed to get slide by index: {0}", slideIndex));
                ReleaseComObjectInternal(_slide); // Clean up if partially obtained
                _slide = null;
                return null;
            }
            finally
            {
                 // Release the Slides collection object obtained within this method's scope
                 ReleaseComObjectInternal(slides);
                 // Do not release 'pres' here, it's managed by the wrapper's _presentation field.
            }
        }

        /// <summary>
        /// Gets the slide currently active in the main application window view.
        /// Releases any previously cached slide reference.
        /// </summary>
        /// <returns>The active Slide object, or null if no slide view is active or an error occurs.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        public PowerPoint.Slide GetActiveSlide()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(PowerPointComWrapper));
            if (_pptApp == null)
            {
                 ErrorHandlingService.LogException(new InvalidOperationException("PowerPoint Application object is null."), "GetActiveSlide Error");
                 return null;
            }

            PowerPoint.DocumentWindow window = null;
            PowerPoint.View view = null;

            // Release the previously cached slide object before getting a new one
            ReleaseComObjectInternal(_slide);
            _slide = null; // Reset field

            try
            {
                // Get the active window, then the view, then the slide from the view
                window = _pptApp.ActiveWindow;
                if (window != null)
                {
                    view = window.View;
                    if (view?.Slide != null) // Check if the view actually has a slide
                    {
                        // Attempt cast to Slide (should succeed if View.Slide is not null)
                        _slide = view.Slide as PowerPoint.Slide;
                        return _slide;
                    }
                    else
                    {
                         ErrorHandlingService.LogException(null, "Active window view does not contain a slide (e.g., backstage view, sorter view).");
                         return null; // Not an error, just no slide active in the view
                    }
                }
                else
                {
                     ErrorHandlingService.LogException(null, "No active document window found.");
                     return null; // No active window
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Failed to get active slide.");
                ReleaseComObjectInternal(_slide); // Clean up if partially obtained
                _slide = null;
                return null;
            }
            finally
            {
                // Release intermediate COM objects obtained within this method's scope
                ReleaseComObjectInternal(view);
                ReleaseComObjectInternal(window);
            }
        }

        #endregion

        #region Public Methods (Shape Manipulation)

        /// <summary>
        /// Inserts a picture from a file onto a specified slide.
        /// </summary>
        /// <param name="imagePath">Full path to the image file.</param>
        /// <param name="targetSlide">The slide object where the picture should be inserted.</param>
        /// <param name="left">Position from the left edge of the slide.</param>
        /// <param name="top">Position from the top edge of the slide.</param>
        /// <param name="width">Optional width of the picture (-1 for original).</param>
        /// <param name="height">Optional height of the picture (-1 for original).</param>
        /// <returns>The inserted Shape object, or null if insertion fails.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        /// <exception cref="ArgumentNullException">Thrown if targetSlide or imagePath is null/empty.</exception>
        public PowerPoint.Shape InsertPicture(string imagePath, PowerPoint.Slide targetSlide, float left, float top, float width = -1, float height = -1)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(PowerPointComWrapper));
            if (targetSlide == null) throw new ArgumentNullException("targetSlide");
            if (string.IsNullOrEmpty(imagePath)) throw new ArgumentNullException("imagePath");
            if (!System.IO.File.Exists(imagePath))
            {
                ErrorHandlingService.LogException(new System.IO.FileNotFoundException("Image file not found.", imagePath), "InsertPicture Error");
                return null;
            }


            PowerPoint.Shapes shapes = null;
            PowerPoint.Shape picture = null;
            try
            {
                shapes = targetSlide.Shapes;
                if (shapes == null) throw new InvalidOperationException("Could not retrieve Shapes collection from target slide.");

                // Add picture, linking to file=false, saving with document=true
                picture = shapes.AddPicture(imagePath, Office.MsoTriState.msoFalse, Office.MsoTriState.msoTrue, left, top, width, height);
                return picture; // Caller is responsible for releasing this object
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, string.Format("Failed to insert picture '{0}' onto slide {1}.", imagePath, targetSlide.SlideIndex));
                ReleaseComObjectInternal(picture); // Clean up if partially created
                return null;
            }
            finally
            {
                // Release the intermediate Shapes collection COM object
                ReleaseComObjectInternal(shapes);
            }
        }

        /// <summary>
        /// Gets a shape on a specific slide by its Name property.
        /// Note: Shape Name is more reliable than ID for retrieval via indexer.
        /// </summary>
        /// <param name="slide">The slide object containing the shape.</param>
        /// <param name="shapeName">The Name property of the shape to find.</param>
        /// <returns>The Shape object, or null if not found or an error occurs.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        /// <exception cref="ArgumentNullException">Thrown if slide or shapeName is null/empty.</exception>
        public PowerPoint.Shape GetShapeById(PowerPoint.Slide slide, string shapeName) // Renamed parameter for clarity
        {
            if (_disposed) throw new ObjectDisposedException(nameof(PowerPointComWrapper));
            if (slide == null) throw new ArgumentNullException("slide");
            if (string.IsNullOrEmpty(shapeName)) throw new ArgumentNullException("shapeName");

            PowerPoint.Shapes shapes = null;
            PowerPoint.Shape shape = null;
            try
            {
                shapes = slide.Shapes;
                 if (shapes == null) throw new InvalidOperationException("Could not retrieve Shapes collection from slide.");

                // Accessing by name (which is what the indexer often uses) throws if not found.
                shape = shapes[shapeName];
                return shape; // Caller is responsible for releasing this object
            }
            catch (COMException comEx) when ((uint)comEx.ErrorCode == 0x800A01A8) // Object not found HRESULT (common for Shapes indexer)
            {
                ErrorHandlingService.LogException(null, string.Format("Shape with Name '{0}' not found on slide {1}.", shapeName, slide.SlideIndex));
                return null;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, string.Format("Error getting shape by Name '{0}' on slide {1}.", shapeName, slide.SlideIndex));
                ReleaseComObjectInternal(shape); // Clean up if partially obtained
                return null;
            }
            finally
            {
                // Release the intermediate Shapes collection COM object
                ReleaseComObjectInternal(shapes);
            }
        }

        /// <summary>
        /// Gets a shape on a specific slide by its AlternativeText property.
        /// Iterates through shapes, so potentially less efficient than GetShapeById if Name is known.
        /// </summary>
        /// <param name="slide">The slide object containing the shape.</param>
        /// <param name="altText">The AlternativeText property value to match.</param>
        /// <returns>The first matching Shape object, or null if not found or an error occurs.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        /// <exception cref="ArgumentNullException">Thrown if slide or altText is null/empty.</exception>
        public PowerPoint.Shape GetShapeByAltText(PowerPoint.Slide slide, string altText)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(PowerPointComWrapper));
            if (slide == null) throw new ArgumentNullException("slide");
            if (string.IsNullOrEmpty(altText)) throw new ArgumentNullException("altText");

            PowerPoint.Shapes shapes = null;
            PowerPoint.Shape currentShape = null; // Variable for the shape in the loop
            PowerPoint.Shape foundShape = null; // Variable to hold the matched shape
            try
            {
                shapes = slide.Shapes;
                if (shapes == null) throw new InvalidOperationException("Could not retrieve Shapes collection from slide.");

                // Iterate through shapes collection
                foreach (PowerPoint.Shape shape in shapes)
                {
                    currentShape = shape; // Assign to loop variable
                    bool hasAltText = false;
                    string currentAltText = null;

                    try
                    {
                        // Accessing AlternativeText can sometimes throw on certain shape types (e.g., placeholders without text)
                        // Check properties defensively. Avoid checking HasTable/Type if possible, focus on AltText.
                        currentAltText = currentShape.AlternativeText;
                        hasAltText = !string.IsNullOrEmpty(currentAltText);
                    }
                    catch (Exception altEx)
                    {
                        // Log error accessing AlternativeText but continue checking other shapes
                        string shapeName = "Unknown"; try { shapeName = currentShape.Name; } catch { }
                        ErrorHandlingService.LogException(altEx, string.Format("Error accessing AlternativeText for shape '{0}' on slide {1}.", shapeName, slide.SlideIndex));
                        hasAltText = false;
                    }

                    if (hasAltText && currentAltText == altText)
                    {
                        foundShape = currentShape; // Found the shape
                        currentShape = null; // Prevent release in finally block for the found shape
                        break; // Exit loop once found
                    }

                    // Release the current shape if it wasn't the one we were looking for
                    ReleaseComObjectInternal(currentShape);
                    currentShape = null; // Reset loop variable
                }

                // Return the found shape (caller must release) or null if loop finished without match
                return foundShape;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, string.Format("Error iterating shapes on slide {0} to find AltText '{1}'.", slide.SlideIndex, altText));
                ReleaseComObjectInternal(foundShape); // Clean up foundShape if error occurred after finding it
                ReleaseComObjectInternal(currentShape); // Clean up current loop shape if error occurred mid-loop
                return null;
            }
            finally
            {
                // Release the intermediate Shapes collection COM object
                ReleaseComObjectInternal(shapes);
                // Release the currentShape if the loop exited normally without finding a match
                // or if an exception occurred before the loop finished processing it.
                ReleaseComObjectInternal(currentShape);
            }
        }

        /// <summary>
        /// Gets the currently selected single shape in the active window.
        /// </summary>
        /// <returns>The selected Shape object, or null if the selection is not a single shape or an error occurs.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        public PowerPoint.Shape GetSelectedShape()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(PowerPointComWrapper));
            if (_pptApp == null)
            {
                 ErrorHandlingService.LogException(new InvalidOperationException("PowerPoint Application object is null."), "GetSelectedShape Error");
                 return null;
            }

            PowerPoint.Selection selection = null;
            PowerPoint.ShapeRange shapeRange = null;
            PowerPoint.Shape shape = null;
            try
            {
                selection = _pptApp.ActiveWindow?.Selection; // Use null-conditional

                // Check if selection is valid and contains shapes
                if (selection != null && selection.Type == PowerPoint.PpSelectionType.ppSelectionShapes)
                {
                    shapeRange = selection.ShapeRange;
                    // Check if ShapeRange is valid and contains exactly one shape
                    if (shapeRange != null && shapeRange.Count == 1)
                    {
                        // Get the single shape
                        shape = shapeRange[1];
                        // IMPORTANT: The caller is responsible for releasing the returned shape object.
                        // We release the intermediate Selection and ShapeRange objects here.
                        return shape;
                    }
                    else if (shapeRange != null) // Log if multiple shapes selected
                    {
                         ErrorHandlingService.LogException(null, string.Format("Multiple shapes selected ({0}). GetSelectedShape requires a single selection.", shapeRange.Count));
                    }
                }
                // No shape selected or selection type is different
                return null;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error getting selected shape.");
                ReleaseComObjectInternal(shape); // Clean up shape if partially obtained
                return null;
            }
            finally
            {
                // Release intermediate COM objects obtained within this method's scope
                ReleaseComObjectInternal(shapeRange);
                ReleaseComObjectInternal(selection);
            }
        }

        /// <summary>
        /// Inserts an OLE object from an Excel file onto a slide.
        /// </summary>
        /// <param name="sourceFilePath">Path to the source Excel file.</param>
        /// <param name="targetSlide">The slide to insert the object onto.</param>
        /// <param name="left">Position from left.</param>
        /// <param name="top">Position from top.</param>
        /// <param name="width">Optional width.</param>
        /// <param name="height">Optional height.</param>
        /// <param name="linkToFile">True to create a linked OLE object, False to embed.</param>
        /// <returns>The inserted OLE shape, or null on failure.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        /// <exception cref="ArgumentNullException">Thrown if targetSlide or sourceFilePath is null/empty.</exception>
        public PowerPoint.Shape InsertOleObject(string sourceFilePath, PowerPoint.Slide targetSlide, float left, float top, float width = -1, float height = -1, bool linkToFile = true)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(PowerPointComWrapper));
            if (targetSlide == null) throw new ArgumentNullException("targetSlide");
            if (string.IsNullOrEmpty(sourceFilePath)) throw new ArgumentNullException("sourceFilePath");
            if (!System.IO.File.Exists(sourceFilePath))
            {
                ErrorHandlingService.LogException(new System.IO.FileNotFoundException("Source file not found for OLE object.", sourceFilePath), "InsertOleObject Error");
                return null;
            }

            PowerPoint.Shapes shapes = null;
            PowerPoint.Shape oleShape = null;
            try
            {
                shapes = targetSlide.Shapes;
                if (shapes == null) throw new InvalidOperationException("Could not retrieve Shapes collection from target slide.");

                // Construct ClassName for Excel objects if needed, or let PowerPoint infer from FileName
                // string className = "Excel.Sheet.12"; // For .xlsx, or "Excel.Sheet.8" for .xls

                // Add OLE object. DisplayAsIcon=False, Link=True/False
                oleShape = shapes.AddOLEObject(
                    Left: left, Top: top, Width: width, Height: height,
                    FileName: sourceFilePath,
                    // ClassName: className, // Usually inferred from FileName extension
                    DisplayAsIcon: Office.MsoTriState.msoFalse,
                    Link: linkToFile ? Office.MsoTriState.msoTrue : Office.MsoTriState.msoFalse);

                // Note: Activating the OLE object or setting the specific worksheet/range
                // to display initially often requires complex OLE verb execution or manipulation
                // of the embedded object data, which is beyond simple AddOLEObject parameters.

                return oleShape; // Caller is responsible for releasing this object
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, string.Format("Failed to insert OLE object from '{0}' onto slide {1}.", sourceFilePath, targetSlide.SlideIndex));
                ReleaseComObjectInternal(oleShape); // Clean up if partially created
                return null;
            }
            finally
            {
                // Release the intermediate Shapes collection COM object
                ReleaseComObjectInternal(shapes);
            }
        }

        /// <summary>
        /// Pastes content from the clipboard onto a specified slide using a specific data format.
        /// </summary>
        /// <param name="targetSlide">The slide object where the content should be pasted.</param>
        /// <param name="dataType">The desired paste format (e.g., ppPasteEnhancedMetafile, ppPasteBitmap).</param>
        /// <returns>A ShapeRange containing the pasted shapes, or null if pasting fails.</returns>
        /// <exception cref="ObjectDisposedException">Thrown if the wrapper has been disposed.</exception>
        /// <exception cref="ArgumentNullException">Thrown if targetSlide is null.</exception>
        public PowerPoint.ShapeRange PasteSpecial(PowerPoint.Slide targetSlide, PowerPoint.PpPasteDataType dataType = PowerPoint.PpPasteDataType.ppPasteDefault)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(PowerPointComWrapper));
            if (targetSlide == null) throw new ArgumentNullException("targetSlide");

            PowerPoint.Shapes shapes = null;
            PowerPoint.ShapeRange pastedRange = null;
            try
            {
                shapes = targetSlide.Shapes;
                if (shapes == null) throw new InvalidOperationException("Could not retrieve Shapes collection from target slide.");

                // Ensure the target slide's view is active before pasting, if possible.
                try { targetSlide.Select(); }
                catch (Exception selectEx)
                {
                    ErrorHandlingService.LogException(selectEx, string.Format("Could not select slide {0} before PasteSpecial. Paste might fail or occur on wrong slide.", targetSlide.SlideIndex));
                }

                // Execute paste special operation
                pastedRange = shapes.PasteSpecial(DataType: dataType);
                return pastedRange; // Caller is responsible for releasing this object
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, string.Format("PasteSpecial failed on slide {0} with format {1}.", targetSlide.SlideIndex, dataType));
                ReleaseComObjectInternal(pastedRange); // Clean up if partially created
                return null;
            }
            finally
            {
                // Release the intermediate Shapes collection COM object
                ReleaseComObjectInternal(shapes);
            }
        }

        #endregion

        #region COM Object Release Helpers

        /// <summary>
        /// Public static helper method to allow external code to safely release COM objects
        /// obtained from this wrapper (e.g., Slide, Shape, ShapeRange) if their lifetime
        /// needs to be managed explicitly by the caller. Uses Marshal.ReleaseComObject.
        /// </summary>
        /// <param name="obj">The COM object to release.</param>
        public static void ReleaseComObject(object obj)
        {
            if (obj != null && Marshal.IsComObject(obj))
            {
                try
                {
                    // Use ReleaseComObject for external calls, decrementing the count.
                    Marshal.ReleaseComObject(obj);
                }
                catch (Exception ex)
                {
                     // Log cautiously or use Debug.WriteLine
                     System.Diagnostics.Debug.WriteLine($"PowerPointComWrapper.ReleaseComObject: Ignoring error: {ex.Message}");
                     // Consider logging to ErrorHandlingService if appropriate, but avoid flooding logs.
                     // ErrorHandlingService.LogException(ex, "Error in public static ReleaseComObject.", LogLevel.Debug); // Example
                }
                finally
                {
                    // Can't set obj to null here as it's a copy of the reference.
                }
            }
        }

        /// <summary>
        /// Internal private static helper to safely release a COM object using Marshal.ReleaseComObject.
        /// Used internally by the wrapper (e.g., in finally blocks, Dispose).
        /// </summary>
        /// <param name="obj">The COM object to release.</param>
        private static void ReleaseComObjectInternal(object obj)
        {
            if (obj != null && Marshal.IsComObject(obj))
            {
                try
                {
                    // Use standard ReleaseComObject for internal cleanup too for consistency.
                    // FinalReleaseComObject can be too aggressive if the object is still legitimately referenced elsewhere.
                    Marshal.ReleaseComObject(obj);
                }
                catch (Exception ex)
                {
                    // Log internal release errors, potentially with more detail or at a lower level.
                    System.Diagnostics.Debug.WriteLine($"PowerPointComWrapper: Error during internal ReleaseComObject: {ex.Message}");
                    // ErrorHandlingService.LogException(ex, "Error releasing COM object internally.", LogLevel.Debug); // Example
                }
                finally
                {
                     // Can't set obj to null here as it's a copy of the reference.
                }
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Releases COM objects held by this wrapper instance.
        /// Does not release the Application object passed in the constructor.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            // Prevent the finalizer from running.
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Performs the actual cleanup logic.
        /// </summary>
        /// <param name="disposing">True if called from Dispose(), false if called from the finalizer.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;

            if (disposing)
            {
                // Dispose managed state (managed objects) if any were held.
                // Currently none.
            }

            // Release COM objects held as instance fields (_slide, _presentation)
            // Use the internal helper.
            ReleaseComObjectInternal(_slide);
            _slide = null;
            ReleaseComObjectInternal(_presentation);
            _presentation = null;

            // Do NOT release _pptApp here. Its lifetime is managed by the Add-in itself.
            // Null the reference to prevent use after dispose.
            // _pptApp = null; // Keep reference as it was passed in? Or null it? Nulling seems safer. Let's null it.
            // Reconsidering: Keep the reference. If the wrapper is disposed but the Add-in is still running,
            // nulling _pptApp here prevents recreation of the wrapper if needed later, forcing Add-in restart.
            // The _disposed flag prevents methods from using _pptApp anyway.

            _disposed = true;
        }

        /// <summary>
        /// Finalizer to ensure COM objects held by the instance are released even if Dispose() is not called.
        /// </summary>
        ~PowerPointComWrapper()
        {
            // Call the dispose logic, indicating it's from the finalizer.
            // Use Debug.WriteLine for logging here as ErrorHandlingService might not be safe.
            System.Diagnostics.Debug.WriteLine("PowerPointComWrapper finalizer called. Disposing resources.");
            Dispose(false);
        }

        #endregion
    }
}