﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO</name>
  </assembly>
  <members>
    <member name="T:System.IO.BinaryReader">
      <summary>Liest primitive Datentypen als Binärwerte in einer spezifischen Codierung.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.#ctor(System.IO.Stream)">
      <summary>Initialisiert eine neue Instanz der auf dem bereitgestellten Stream basierenden <see cref="T:System.IO.BinaryReader" />-Klasse mit einer spezifischen Stream- und Zeichencodierung unter Verwendung von UTF-8.</summary>
      <param name="input">Der Eingabestream. </param>
      <exception cref="T:System.ArgumentException">Der Stream unterstützt keine Lesevorgänge, ist null oder er wurde bereits geschlossen. </exception>
    </member>
    <member name="M:System.IO.BinaryReader.#ctor(System.IO.Stream,System.Text.Encoding)">
      <summary>Initialisiert eine neue Instanz der auf dem bereitgestellten Stream basierenden <see cref="T:System.IO.BinaryReader" />-Klasse mit einer spezifischen Stream- und Zeichencodierung.</summary>
      <param name="input">Der Eingabestream. </param>
      <param name="encoding">Die zu verwendende Zeichencodierung. </param>
      <exception cref="T:System.ArgumentException">Der Stream unterstützt keine Lesevorgänge, ist null oder er wurde bereits geschlossen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="encoding" /> ist null. </exception>
    </member>
    <member name="M:System.IO.BinaryReader.#ctor(System.IO.Stream,System.Text.Encoding,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.BinaryReader" />-Klasse auf Grundlage des angegebenen Streams und der Zeichencodierung und lässt den Stream optional geöffnet.</summary>
      <param name="input">Der Eingabestream.</param>
      <param name="encoding">Die zu verwendende Zeichencodierung.</param>
      <param name="leaveOpen">true, um den Datenstrom geöffnet zu lassen, nach dem das <see cref="T:System.IO.BinaryReader" />-Objekt freigegeben wurde; andernfalls false.</param>
      <exception cref="T:System.ArgumentException">Der Stream unterstützt keine Lesevorgänge, ist null oder er wurde bereits geschlossen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="encoding" /> oder <paramref name="input" /> ist null. </exception>
    </member>
    <member name="P:System.IO.BinaryReader.BaseStream">
      <summary>Gewährt Zugriff auf den zugrunde liegenden Stream von <see cref="T:System.IO.BinaryReader" />.</summary>
      <returns>Der BinaryReader zugeordnete, zugrunde liegende Stream.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.IO.BinaryReader" />-Klasse verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.IO.BinaryReader.Dispose(System.Boolean)">
      <summary>Gibt die von der <see cref="T:System.IO.BinaryReader" />-Klasse verwendeten nicht verwalteten Ressourcen frei und gibt (optional) auch die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben. </param>
    </member>
    <member name="M:System.IO.BinaryReader.FillBuffer(System.Int32)">
      <summary>Füllt den internen Puffer mit der angegebenen Anzahl von Bytes, die aus dem Stream gelesen wurden.</summary>
      <param name="numBytes">Die Anzahl der zu lesenden Bytes. </param>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht, bevor <paramref name="numBytes" /> gelesen werden konnte. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die angeforderten <paramref name="numBytes" /> überschreiten die interne Puffergröße.</exception>
    </member>
    <member name="M:System.IO.BinaryReader.PeekChar">
      <summary>Gibt das nächste verfügbare Zeichen zurück, ohne die Byte- oder Zeichenposition zu erhöhen.</summary>
      <returns>Das nächste verfügbare Zeichen oder -1, wenn keine weiteren Zeichen verfügbar sind oder der Stream keine Suchvorgänge unterstützt.</returns>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ArgumentException">Das aktuelle Zeichen kann nicht mit dem für den Stream ausgewählten <see cref="T:System.Text.Encoding" /> in den internen Zeichenpuffer decodiert werden.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.Read">
      <summary>Liest Zeichen aus dem zugrunde liegenden Stream und erhöht die aktuelle Position im Stream in Abhängigkeit von der verwendeten Encoding und dem aus dem Stream gelesenen Zeichen.</summary>
      <returns>Das nächste Zeichen aus dem Eingabestream bzw. -1, wenn derzeit keine Zeichen verfügbar sind.</returns>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest die angegebene Anzahl von Bytes beginnend bei einem angegebenen Punkt im Bytearray aus dem Stream. </summary>
      <returns>Die Anzahl von Bytes, die in <paramref name="buffer" /> gelesen wurden.Diese kann kleiner sein als die Anzahl der angeforderten Bytes, wenn gegenwärtig keine entsprechende Anzahl von Bytes verfügbar ist, oder null, wenn das Ende des Streams erreicht ist.</returns>
      <param name="buffer">Der Puffer, in den Daten gelesen werden sollen. </param>
      <param name="index">Der Anfangspunkt für das Lesen in den Puffer. </param>
      <param name="count">Die Anzahl der zu lesenden Bytes. </param>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. - oder - Die Anzahl der zu lesenden decodierten Zeichen ist größer als <paramref name="count" />.Dies kann geschehen, wenn ein Unicode-Decoder Fallbackzeichen oder ein Ersatzzeichenpaar zurückgibt.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.Read(System.Char[],System.Int32,System.Int32)">
      <summary>Liest die angegebene Anzahl von Zeichen beginnend bei einem angegebenen Punkt im Zeichenarray aus dem Stream.</summary>
      <returns>Die Gesamtanzahl der in den Puffer gelesenen Zeichen.Diese kann kleiner sein als die Anzahl der angeforderten Zeichen, wenn gegenwärtig keine entsprechende Anzahl von Bytes verfügbar ist, oder null, wenn das Ende des Streams erreicht ist.</returns>
      <param name="buffer">Der Puffer, in den Daten gelesen werden sollen. </param>
      <param name="index">Der Anfangspunkt für das Lesen in den Puffer. </param>
      <param name="count">Die Anzahl der zu lesenden Zeichen. </param>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. - oder - Die Anzahl der zu lesenden decodierten Zeichen ist größer als <paramref name="count" />.Dies kann geschehen, wenn ein Unicode-Decoder Fallbackzeichen oder ein Ersatzzeichenpaar zurückgibt.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.Read7BitEncodedInt">
      <summary>Liest eine 32-Bit-Ganzzahl in komprimiertem Format.</summary>
      <returns>Eine 32-Bit-Ganzzahl in komprimiertem Format.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.FormatException">Der Datenstrom ist beschädigt.</exception>
    </member>
    <member name="M:System.IO.BinaryReader.ReadBoolean">
      <summary>Liest einen Boolean-Wert aus dem aktuellen Stream und erhöht die aktuelle Position im Stream um 1 Byte.</summary>
      <returns>true, wenn das Byte ungleich 0 (null) ist, andernfalls false.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadByte">
      <summary>Liest das nächste Byte aus dem aktuellen Stream und erhöht die aktuelle Position im Stream um 1 Byte.</summary>
      <returns>Das nächste aus dem aktuellen Stream gelesene Byte.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadBytes(System.Int32)">
      <summary>Liest die angegebene Anzahl von Bytes aus dem aktuellen Stream in ein Bytearray und erhöht die aktuelle Position um diese Anzahl von Bytes.</summary>
      <returns>Ein Bytearray mit Daten aus dem zugrunde liegenden Stream.Dies kann kleiner sein als die Anzahl der angeforderten Bytes, wenn das Ende des Streams erreicht ist.</returns>
      <param name="count">Die Anzahl der zu lesenden Bytes.Dieser Wert muss 0 oder eine nicht negative Zahl sein; andernfalls tritt eine Ausnahme auf.</param>
      <exception cref="T:System.ArgumentException">Die Anzahl der zu lesenden decodierten Zeichen ist größer als <paramref name="count" />.Dies kann geschehen, wenn ein Unicode-Decoder Fallbackzeichen oder ein Ersatzzeichenpaar zurückgibt.</exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> ist negativ. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadChar">
      <summary>Liest das nächste Zeichen aus dem aktuellen Stream und erhöht die aktuelle Position im Stream in Abhängigkeit von der verwendeten Encoding und dem aus dem Stream gelesenen Zeichen.</summary>
      <returns>Ein aus dem aktuellen Stream gelesenes Zeichen.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ArgumentException">Ein Ersatzzeichenzeichen wurde gelesen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadChars(System.Int32)">
      <summary>Liest die angegebene Anzahl von Zeichen aus dem aktuellen Stream, gibt die Daten in einem Zeichenarray zurück und erhöht die aktuelle Position in Abhängigkeit von der verwendeten Encoding und dem aus dem Stream gelesenen Zeichen.</summary>
      <returns>Ein Zeichenarray mit Daten aus dem zugrunde liegenden Stream.Dies kann kleiner sein als die Anzahl der angeforderten Zeichen, wenn das Ende des Streams erreicht ist.</returns>
      <param name="count">Die Anzahl der zu lesenden Zeichen. </param>
      <exception cref="T:System.ArgumentException">Die Anzahl der zu lesenden decodierten Zeichen ist größer als <paramref name="count" />.Dies kann geschehen, wenn ein Unicode-Decoder Fallbackzeichen oder ein Ersatzzeichenpaar zurückgibt.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> ist negativ. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadDecimal">
      <summary>Liest einen Dezimalwert aus dem aktuellen Stream und erhöht die aktuelle Position im Stream um 16 Bytes.</summary>
      <returns>Ein aus dem aktuellen Stream gelesener Dezimalwert.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadDouble">
      <summary>Liest einen 8-Byte-Gleitkommawert aus dem aktuellen Stream und erhöht die aktuelle Position im Stream um 8 Bytes.</summary>
      <returns>Ein aus dem aktuellen Stream gelesener 8-Byte-Gleitkommawert.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadInt16">
      <summary>Liest eine 2-Byte-Ganzzahl mit Vorzeichen aus dem aktuellen Stream und erhöht die aktuelle Position im Stream um 2 Bytes.</summary>
      <returns>Eine aus dem aktuellen Stream gelesene 2-Byte-Ganzzahl mit Vorzeichen.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadInt32">
      <summary>Liest eine 4-Byte-Ganzzahl mit Vorzeichen aus dem aktuellen Stream und erhöht die aktuelle Position im Stream um 4 Bytes.</summary>
      <returns>Eine aus dem aktuellen Stream gelesene 4-Byte-Ganzzahl mit Vorzeichen.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadInt64">
      <summary>Liest eine 8-Byte-Ganzzahl mit Vorzeichen aus dem aktuellen Stream und erhöht die aktuelle Position im Stream um 8 Bytes.</summary>
      <returns>Eine aus dem aktuellen Stream gelesene 8-Byte-Ganzzahl mit Vorzeichen.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadSByte">
      <summary>Liest ein Byte mit Vorzeichen aus dem aktuellen Stream und erhöht die aktuelle Position im Stream um ein Byte.</summary>
      <returns>Ein aus dem aktuellen Stream gelesenes Byte mit Vorzeichen.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadSingle">
      <summary>Liest einen 4-Byte-Gleitkommawert aus dem aktuellen Stream und erhöht die aktuelle Position im Stream um 4 Bytes.</summary>
      <returns>Ein aus dem aktuellen Stream gelesener 4-Byte-Gleitkommawert.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadString">
      <summary>Liest eine Zeichenfolge aus dem aktuellen Stream.Die Zeichenfolge weist ein Präfix mit der Länge auf, die als Ganzzahl mit jeweils 7 Bits codiert ist.</summary>
      <returns>Die gelesene Zeichenfolge.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadUInt16">
      <summary>Liest eine 2-Byte-Ganzzahl ohne Vorzeichen mithilfe einer Little-Endian-Codierung aus dem aktuellen Stream und erhöht die aktuelle Position im Stream um 2 Bytes.</summary>
      <returns>Eine aus diesem Stream gelesene 2-Byte-Ganzzahl ohne Vorzeichen.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadUInt32">
      <summary>Liest eine 4-Byte-Ganzzahl ohne Vorzeichen aus dem aktuellen Stream und erhöht die Position des Streams um 4 Bytes.</summary>
      <returns>Eine aus diesem Stream gelesene 4-Byte-Ganzzahl ohne Vorzeichen.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryReader.ReadUInt64">
      <summary>Liest eine 8-Byte-Ganzzahl ohne Vorzeichen aus dem aktuellen Stream und erhöht die Position des Streams um 8 Bytes.</summary>
      <returns>Eine aus diesem Stream gelesene 8-Byte-Ganzzahl ohne Vorzeichen.</returns>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.BinaryWriter">
      <summary>Schreibt primitive Typen binär in einen Stream und unterstützt das Schreiben von Zeichenfolgen in einer bestimmten Codierung.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.BinaryWriter" />-Klasse zum Schreiben in einen Stream.</summary>
    </member>
    <member name="M:System.IO.BinaryWriter.#ctor(System.IO.Stream)">
      <summary>Initialisiert eine neue Instanz der auf dem spezifizierten Stream basierenden <see cref="T:System.IO.BinaryWriter" />-Klasse unter Verwendung von UTF-8-Verschlüsselung.</summary>
      <param name="output">Der Ausgabestream. </param>
      <exception cref="T:System.ArgumentException">Der Stream unterstützt keine Schreibvorgänge, oder er wurde bereits geschlossen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="output" /> ist null. </exception>
    </member>
    <member name="M:System.IO.BinaryWriter.#ctor(System.IO.Stream,System.Text.Encoding)">
      <summary>Initialisiert eine neue Instanz der auf den bereitgestellten Stream basierenden <see cref="T:System.IO.BinaryWriter" />-Klasse mit einer spezifischen Stream- und Zeichencodierung.</summary>
      <param name="output">Der Ausgabestream. </param>
      <param name="encoding">Die zu verwendende Zeichencodierung. </param>
      <exception cref="T:System.ArgumentException">Der Stream unterstützt keine Schreibvorgänge, oder er wurde bereits geschlossen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="output" /> oder <paramref name="encoding" /> ist null. </exception>
    </member>
    <member name="M:System.IO.BinaryWriter.#ctor(System.IO.Stream,System.Text.Encoding,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.BinaryWriter" />-Klasse auf Grundlage des angegebenen Streams und der Zeichencodierung und lässt den Stream optional geöffnet.</summary>
      <param name="output">Der Ausgabestream.</param>
      <param name="encoding">Die zu verwendende Zeichencodierung.</param>
      <param name="leaveOpen">true, um den Datenstrom geöffnet zu lassen, nach dem das <see cref="T:System.IO.BinaryWriter" />-Objekt freigegeben wurde; andernfalls false.</param>
      <exception cref="T:System.ArgumentException">Der Stream unterstützt keine Schreibvorgänge, oder er wurde bereits geschlossen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="output" /> oder <paramref name="encoding" /> ist null. </exception>
    </member>
    <member name="P:System.IO.BinaryWriter.BaseStream">
      <summary>Ruft den zugrunde liegenden Stream von <see cref="T:System.IO.BinaryWriter" /> ab.</summary>
      <returns>Der BinaryWriter zugeordnete, zugrunde liegende Stream.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.IO.BinaryWriter" />-Klasse verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.IO.BinaryWriter.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.IO.BinaryWriter" /> verwendeten nicht verwalteten Ressourcen und optional auch die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben. </param>
    </member>
    <member name="M:System.IO.BinaryWriter.Flush">
      <summary>Löscht sämtliche Puffer für den aktuellen Writer und veranlasst die Ausgabe aller gepufferten Daten an das zugrunde liegende Gerät.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.BinaryWriter.Null">
      <summary>Gibt eine <see cref="T:System.IO.BinaryWriter" />-Klasse ohne Sicherungsspeicher an.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.BinaryWriter.OutStream">
      <summary>Enthält den zugrunde liegenden Stream.</summary>
    </member>
    <member name="M:System.IO.BinaryWriter.Seek(System.Int32,System.IO.SeekOrigin)">
      <summary>Legt die Position im aktuellen Stream fest.</summary>
      <returns>Die Position im aktuellen Stream.</returns>
      <param name="offset">Ein Byteoffset im Verhältnis zu <paramref name="origin" />. </param>
      <param name="origin">Ein Feld von <see cref="T:System.IO.SeekOrigin" />, das den Bezugspunkt angibt, von dem aus die neue Position ermittelt werden soll. </param>
      <exception cref="T:System.IO.IOException">Der Dateizeiger wurde an eine ungültige Position verschoben. </exception>
      <exception cref="T:System.ArgumentException">Der <see cref="T:System.IO.SeekOrigin" />-Wert ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Boolean)">
      <summary>Schreibt einen 1-Byte-Boolean-Wert in den aktuellen Stream, wobei 0 (null) false und 1 true darstellt.</summary>
      <param name="value">Der zu schreibende Boolean-Wert (0 (null) oder 1). </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Byte)">
      <summary>Schreibt ein Byte ohne Vorzeichen in den aktuellen Stream und erhöht die aktuelle Position im Stream um ein Byte.</summary>
      <param name="value">Das zu schreibende Byte ohne Vorzeichen. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Byte[])">
      <summary>Schreibt ein Bytearray in den zugrunde liegenden Stream.</summary>
      <param name="buffer">Ein Bytearray, das die zu schreibenden Daten enthält. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Schreibt einen Bereich eines Bytearrays in den aktuellen Stream.</summary>
      <param name="buffer">Ein Bytearray, das die zu schreibenden Daten enthält. </param>
      <param name="index">Der Anfangspunkt im <paramref name="buffer" />, an dem mit dem Schreiben begonnen wird. </param>
      <param name="count">Die Anzahl der zu schreibenden Bytes. </param>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Char)">
      <summary>Schreibt ein Unicode-Zeichen in den aktuellen Stream und erhöht die aktuelle Position im Stream in Abhängigkeit von der verwendeten Encoding und der in den Stream geschriebenen Zeichen.</summary>
      <param name="ch">Das zu schreibende Unicode-Zeichen (nicht-Ersatzzeichen). </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ch" /> ist ein einzelnes Ersatzzeichen.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Char[])">
      <summary>Schreibt ein Zeichenarray in den aktuellen Stream und erhöht die aktuelle Position im Stream in Abhängigkeit von der verwendeten Encoding und der in den Stream geschriebenen Zeichen.</summary>
      <param name="chars">Ein Zeichenarray mit den zu schreibenden Daten. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> ist null. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt einen Bereich eines Zeichenarrays in den aktuellen Stream und erhöht die aktuelle Position im Stream in Abhängigkeit von der verwendeten Encoding und ggf. der in den Stream geschriebenen Zeichen.</summary>
      <param name="chars">Ein Zeichenarray mit den zu schreibenden Daten. </param>
      <param name="index">Der Anfangspunkt im <paramref name="chars" />, an dem mit dem Schreiben begonnen wird. </param>
      <param name="count">Die Anzahl der zu schreibenden Zeichen. </param>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Decimal)">
      <summary>Schreibt einen Dezimalwert in den aktuellen Stream und erhöht die Position im Stream um 16 Bytes.</summary>
      <param name="value">Der zu schreibende Dezimalwert. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Double)">
      <summary>Schreibt einen 8-Byte-Gleitkommawert in den aktuellen Stream und erhöht die Position im Stream um 8 Bytes.</summary>
      <param name="value">Der zu schreibende 8-Byte-Gleitkommawert. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Int16)">
      <summary>Schreibt eine 2-Byte-Ganzzahl mit Vorzeichen in den aktuellen Stream und erhöht die Position im Stream um 2 Bytes.</summary>
      <param name="value">Die zu schreibende 2-Byte-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Int32)">
      <summary>Schreibt eine 4-Byte-Ganzzahl mit Vorzeichen in den aktuellen Stream und erhöht die Position im Stream um 4 Bytes.</summary>
      <param name="value">Die zu schreibende 4-Byte-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Int64)">
      <summary>Schreibt eine 8-Byte-Ganzzahl mit Vorzeichen in den aktuellen Stream und erhöht die Position im Stream um 8 Bytes.</summary>
      <param name="value">Die zu schreibende 8-Byte-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.SByte)">
      <summary>Schreibt ein Byte mit Vorzeichen in den aktuellen Stream und erhöht die aktuelle Position im Stream um ein Byte.</summary>
      <param name="value">Das zu schreibende Byte mit Vorzeichen. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.Single)">
      <summary>Schreibt einen 4-Byte-Gleitkommawert in den aktuellen Stream und erhöht die Position im Stream um 4 Bytes.</summary>
      <param name="value">Der zu schreibende 4-Byte-Gleitkommawert. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.String)">
      <summary>Schreibt eine Zeichenfolge mit Längenpräfix in der aktuellen Codierung von <see cref="T:System.IO.BinaryWriter" /> in diesen Stream und erhöht die aktuelle Position im Stream in Abhängigkeit von der verwendeten Codierung und der in den Stream geschriebenen Zeichen.</summary>
      <param name="value">Der zu schreibende Wert. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> ist null. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.UInt16)">
      <summary>Schreibt eine 2-Byte-Ganzzahl ohne Vorzeichen in den aktuellen Stream und erhöht die Position im Stream um 2 Bytes.</summary>
      <param name="value">Die zu schreibende 2-Byte-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.UInt32)">
      <summary>Schreibt eine 4-Byte-Ganzzahl ohne Vorzeichen in den aktuellen Stream und erhöht die Position im Stream um 4 Bytes.</summary>
      <param name="value">Die zu schreibende 4-Byte-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write(System.UInt64)">
      <summary>Schreibt eine 8-Byte-Ganzzahl ohne Vorzeichen in den aktuellen Stream und erhöht die Position im Stream um 8 Bytes.</summary>
      <param name="value">Die zu schreibende 8-Byte-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.BinaryWriter.Write7BitEncodedInt(System.Int32)">
      <summary>Schreibt eine 32-Bit-Ganzzahl in einem komprimierten Format.</summary>
      <param name="value">Die zu schreibende 32-Bit-Ganzzahl. </param>
      <exception cref="T:System.IO.EndOfStreamException">Das Ende des Streams ist erreicht. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">Der Stream ist geschlossen. </exception>
    </member>
    <member name="T:System.IO.EndOfStreamException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn hinter dem Ende eines Streams ein Leseversuch erfolgt.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.EndOfStreamException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.EndOfStreamException" />-Klasse, bei der die Meldungszeichenfolge auf eine vom System gelieferte Meldung und HRESULT auf COR_E_ENDOFSTREAM festgelegt ist.</summary>
    </member>
    <member name="M:System.IO.EndOfStreamException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.EndOfStreamException" />-Klasse, bei der die Nachrichtenzeichenfolge auf <paramref name="message" /> und HRESULT auf COR_E_ENDOFSTREAM festgelegt wurde.</summary>
      <param name="message">Eine Zeichenfolge, die den Fehler beschreibt.Der Inhalt der <paramref name="message" /> soll in verständlicher Sprache gehalten sein.Der Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
    </member>
    <member name="M:System.IO.EndOfStreamException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.EndOfStreamException" />-Klasse mit einer angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Eine Zeichenfolge, die den Fehler beschreibt.Der Inhalt der <paramref name="message" /> soll in verständlicher Sprache gehalten sein.Der Aufrufer dieses Konstruktors muss sicherstellen, dass diese Zeichenfolge für die aktuelle Systemkultur lokalisiert wurde.</param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.IO.InvalidDataException">
      <summary>Die Ausnahme, die ausgelöst wird, wenn ein Datenstream ein ungültiges Format hat.</summary>
    </member>
    <member name="M:System.IO.InvalidDataException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.InvalidDataException" />-Klasse.</summary>
    </member>
    <member name="M:System.IO.InvalidDataException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.InvalidDataException" />-Klasse mit einer angegebenen Fehlermeldung.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
    </member>
    <member name="M:System.IO.InvalidDataException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.InvalidDataException" />-Klasse mit einem Verweis auf die innere Ausnahme, die die Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Fehlermeldung, in der die Ursache der Ausnahme erklärt wird.</param>
      <param name="innerException">Die Ausnahme, die die Ursache der aktuellen Ausnahme ist.Wenn der <paramref name="innerException" />-Parameter nicht null ist, wird die aktuelle Ausnahme in einem catch-Block ausgelöst, der die innere Ausnahme behandelt.</param>
    </member>
    <member name="T:System.IO.MemoryStream">
      <summary>Erstellt einen Stream, der den Arbeitsspeicher als Sicherungsspeicher verwendet.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, finden Sie unter der Reference Source.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.MemoryStream" />-Klasse mit einer erweiterbaren Kapazität, die mit 0 (null) initialisiert wurde.</summary>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Byte[])">
      <summary>Initialisiert anhand des angegebenen Bytearrays eine neue Instanz der <see cref="T:System.IO.MemoryStream" />-Klasse, deren Größe nicht geändert werden kann.</summary>
      <param name="buffer">Das Array vorzeichenloser Bytes, aus dem der aktuelle Stream erstellt werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Byte[],System.Boolean)">
      <summary>Initialisiert anhand des angegebenen Bytearrays eine neue Instanz der <see cref="T:System.IO.MemoryStream" />-Klasse, deren Größe nicht geändert werden kann, wobei die <see cref="P:System.IO.MemoryStream.CanWrite" />-Eigenschaft wie angegeben festgelegt wird.</summary>
      <param name="buffer">Das Array vorzeichenloser Bytes, aus dem dieser Stream erstellt werden soll. </param>
      <param name="writable">Die Einstellung der <see cref="P:System.IO.MemoryStream.CanWrite" />-Eigenschaft, mit der bestimmt wird, ob der Stream Schreibvorgänge unterstützt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Byte[],System.Int32,System.Int32)">
      <summary>Initialisiert anhand des angegebenen Bereichs (Indexes) eines Bytearrays eine neue Instanz der <see cref="T:System.IO.MemoryStream" />-Klasse, deren Größe nicht geändert werden kann.</summary>
      <param name="buffer">Das Array vorzeichenloser Bytes, aus dem dieser Stream erstellt werden soll. </param>
      <param name="index">Der Index in <paramref name="buffer" />, an dem der Stream beginnt. </param>
      <param name="count">Die Länge des Streams in Bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist kleiner als 0. </exception>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />.</exception>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Byte[],System.Int32,System.Int32,System.Boolean)">
      <summary>Initialisiert anhand des angegebenen Bereichs eines Bytearrays eine neue Instanz der <see cref="T:System.IO.MemoryStream" />-Klasse, deren Größe nicht geändert werden kann, wobei die <see cref="P:System.IO.MemoryStream.CanWrite" />-Eigenschaft wie angegeben festgelegt wurde.</summary>
      <param name="buffer">Das Array vorzeichenloser Bytes, aus dem dieser Stream erstellt werden soll. </param>
      <param name="index">Der Index in <paramref name="buffer" />, bei dem der Stream beginnt. </param>
      <param name="count">Die Länge des Streams in Bytes. </param>
      <param name="writable">Die Einstellung der <see cref="P:System.IO.MemoryStream.CanWrite" />-Eigenschaft, mit der bestimmt wird, ob der Stream Schreibvorgänge unterstützt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />.</exception>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Byte[],System.Int32,System.Int32,System.Boolean,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.MemoryStream" />-Klasse auf der Grundlage des angegebenen Bereichs eines Bytearrays, wobei die <see cref="P:System.IO.MemoryStream.CanWrite" />-Eigenschaft und die Möglichkeit zum Aufruf von <see cref="M:System.IO.MemoryStream.GetBuffer" /> wie angegeben festgelegt werden.</summary>
      <param name="buffer">Das Array vorzeichenloser Bytes, aus dem dieser Stream erstellt werden soll. </param>
      <param name="index">Der Index in <paramref name="buffer" />, an dem der Stream beginnt. </param>
      <param name="count">Die Länge des Streams in Bytes. </param>
      <param name="writable">Die Einstellung der <see cref="P:System.IO.MemoryStream.CanWrite" />-Eigenschaft, mit der bestimmt wird, ob der Stream Schreibvorgänge unterstützt. </param>
      <param name="publiclyVisible">true, um <see cref="M:System.IO.MemoryStream.GetBuffer" /> zu aktivieren, wodurch das Array vorzeichenloser Bytes zurückgegeben wird, aus dem der Stream erstellt wurde, andernfalls false. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. </exception>
    </member>
    <member name="M:System.IO.MemoryStream.#ctor(System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.MemoryStream" />-Klasse mit einer erweiterbaren Kapazität, die mit dem angegebenen Wert initialisiert wird.</summary>
      <param name="capacity">Die Anfangsgröße des internen Arrays in Bytes. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> ist negativ. </exception>
    </member>
    <member name="P:System.IO.MemoryStream.CanRead">
      <summary>Ruft einen Wert ab, der angibt, ob der aktuelle Stream Lesevorgänge unterstützt.</summary>
      <returns>true, wenn der Stream geöffnet ist.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.MemoryStream.CanSeek">
      <summary>Ruft einen Wert ab, der angibt, ob der aktuelle Stream Suchvorgänge unterstützt.</summary>
      <returns>true, wenn der Stream geöffnet ist.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.MemoryStream.CanWrite">
      <summary>Ruft einen Wert ab, der angibt, ob der aktuelle Stream Schreibvorgänge unterstützt.</summary>
      <returns>true, wenn der Stream Schreibvorgänge unterstützt, andernfalls false.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.MemoryStream.Capacity">
      <summary>Ruft die Anzahl der für diesen Stream reservierten Bytes ab oder legt diese fest.</summary>
      <returns>Die Länge des Bereichs, der für den Stream im Puffer verwendet werden kann.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Es wurde eine Kapazität festgelegt, die negativ oder kleiner als die derzeitige Länge des Streams ist. </exception>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Stream ist geschlossen. </exception>
      <exception cref="T:System.NotSupportedException">set wird für einen Stream aufgerufen, dessen Kapazität nicht geändert werden kann. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.CopyToAsync(System.IO.Stream,System.Int32,System.Threading.CancellationToken)">
      <summary>Liest alle Bytes asynchron aus dem aktuellen Stream und schreibt sie unter Verwendung einer angegebenen Puffergröße und eines Abbruchtokens in einen anderen Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Kopiervorgang darstellt.</returns>
      <param name="destination">Der Stream, in den der Inhalt des aktuellen Stream kopiert wird.</param>
      <param name="bufferSize">Die Größe des Cookies in Bytes.Dieser Wert muss größer als 0 sein.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="buffersize" /> ist negativ oder 0 (null).</exception>
      <exception cref="T:System.ObjectDisposedException">Entweder der aktuelle Stream oder der Zielstream wird freigegeben.</exception>
      <exception cref="T:System.NotSupportedException">Der aktuelle Stream unterstützt kein Lesen oder Zielstream unterstützt keine Schreibvorgänge.</exception>
    </member>
    <member name="M:System.IO.MemoryStream.Dispose(System.Boolean)">
      <summary>Gibt die von der <see cref="T:System.IO.MemoryStream" />-Klasse verwendeten nicht verwalteten Ressourcen frei und gibt (optional) auch die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="M:System.IO.MemoryStream.Flush">
      <summary>Überschreibt die <see cref="M:System.IO.Stream.Flush" />-Methode, sodass keine Aktion durchgeführt wird.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Löscht sämtliche Puffer für diesen Stream asynchron und überwacht Abbruchanforderungen.</summary>
      <returns>Eine Aufgabe, die die asynchrone Leerung darstellt.</returns>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .</param>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
    </member>
    <member name="P:System.IO.MemoryStream.Length">
      <summary>Ruft die Länge des Streams in Bytes ab.</summary>
      <returns>Die Länge des Streams in Bytes.</returns>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.MemoryStream.Position">
      <summary>Ruft die aktuelle Position im Stream ab oder legt diese fest.</summary>
      <returns>Die aktuelle Position innerhalb des Streams.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Position ist auf einen negativen Wert oder einen größeren Wert als <see cref="F:System.Int32.MaxValue" /> festgelegt. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest einen Byteblock aus dem aktuellen Stream und schreibt die Daten in einen Puffer.</summary>
      <returns>Die Gesamtanzahl der in den Puffer geschriebenen Bytes.Dies kann weniger als die Anzahl der angeforderten Bytes sein, wenn diese Anzahl an Bytes derzeit nicht verfügbar ist, oder null, wenn das Ende des Streams erreicht ist, bevor mindestens ein Byte gelesen wurde.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Bytearray mit den Werten zwischen <paramref name="offset" /> und (<paramref name="offset" /> + <paramref name="count" /> - 1), die durch aus dem aktuellen Stream gelesene Zeichen ersetzt wurden. </param>
      <param name="offset">Der nullbasierte Byteoffset im <paramref name="buffer" />, ab dem die Daten aus dem aktuellen Stream gespeichert werden.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> subtrahiert von der Pufferlänge ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Instanz des Streams ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Liest eine Folge von Bytes asynchron aus dem aktuellen Stream, erhöht die Position im Stream um die Anzahl der gelesenen Bytes und überwacht Abbruchanfragen.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die Gesamtzahl der Bytes, die in den Puffer gelesen werden.Der Ergebniswert kann niedriger als die Anzahl der angeforderten Bytes sein, wenn die Anzahl an derzeit verfügbaren Bytes kleiner ist als die angeforderte Anzahl, oder sie kann 0 (null) sein, wenn das Datenstromende erreicht ist.</returns>
      <param name="buffer">Der Puffer, in den die Daten geschrieben werden sollen.</param>
      <param name="offset">Der Byteoffset im <paramref name="buffer" />, ab dem Daten aus dem Stream geschrieben werden.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Bytes.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .Der Standardwert ist <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="offset" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.NotSupportedException">Lesevorgänge werden vom Stream nicht unterstützt.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Stream wird gerade durch einen früheren Lesevorgang. </exception>
    </member>
    <member name="M:System.IO.MemoryStream.ReadByte">
      <summary>Liest ein Byte aus dem aktuellen Stream.</summary>
      <returns>Das Byte, das in <see cref="T:System.Int32" /> umgewandelt wurde, oder -1, wenn das Ende des Streams erreicht wurde.</returns>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Instanz des Streams ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Legt die Position im aktuellen Stream auf den angegebenen Wert fest.</summary>
      <returns>Die neue Position im Stream, die durch Kombinieren des anfänglichen Verweispunktes und des Offsets berechnet wird.</returns>
      <param name="offset">Die neue Position innerhalb des Streams.Diese ist relativ zum <paramref name="loc" />-Parameter und kann positiv oder negativ sein.</param>
      <param name="loc">Ein Wert vom Typ <see cref="T:System.IO.SeekOrigin" />, der den Bezugspunkt für die Suche darstellt. </param>
      <exception cref="T:System.IO.IOException">Es wurde versucht, eine Suche vor dem Anfang des Streams auszuführen. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> ist größer als <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">Ein <see cref="T:System.IO.SeekOrigin" /> ist ungültig. - oder - <paramref name="offset" /> hat einen arithmetischen Überlauf verursacht.</exception>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Instanz des Streams ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.SetLength(System.Int64)">
      <summary>Legt die Länge des aktuellen Streams auf den angegebenen Wert fest.</summary>
      <param name="value">Der Wert, auf den die Länge festgelegt werden soll. </param>
      <exception cref="T:System.NotSupportedException">Die Größe des aktuellen Streams kann nicht geändert werden, und <paramref name="value" /> übersteigt die aktuelle Kapazität.- oder -  Der aktuelle Stream unterstützt keine Schreibvorgänge. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="value" /> ist negativ oder größer als die maximale Länge von <see cref="T:System.IO.MemoryStream" />, wobei die maximale Länge (<see cref="F:System.Int32.MaxValue" /> - Ursprung) ist, und der Ursprung der Index des zugrunde liegenden Puffers ist, an dem der Stream beginnt. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.ToArray">
      <summary>Schreibt den Inhalt des Streams in ein Bytearray, unabhängig von der <see cref="P:System.IO.MemoryStream.Position" />-Eigenschaft.</summary>
      <returns>Ein neues Bytearray.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.TryGetBuffer(System.ArraySegment{System.Byte}@)">
      <summary>Gibt das Array vorzeichenloser Bytes zurück, aus dem dieser Stream erstellt wurde.Der Rückgabewert gibt an, ob die Konvertierung erfolgreich abgeschlossen wurde.</summary>
      <returns>true, wenn die Konvertierung erfolgreich war, andernfalls false.</returns>
      <param name="buffer">Das Bytearraysegment, aus dem der Stream erstellt wurde.</param>
    </member>
    <member name="M:System.IO.MemoryStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Schreibt einen Byteblock mit den aus einem Puffer gelesenen Daten in den aktuellen Stream.</summary>
      <param name="buffer">Der Puffer, aus dem Daten geschrieben werden sollen. </param>
      <param name="offset">Der nullbasierte Byteoffset im <paramref name="buffer" />, ab dem Bytes in den aktuellen Stream kopiert werden.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Bytes. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt das Schreiben nicht.Weitere Informationen finden Sie unter <see cref="P:System.IO.Stream.CanWrite" />.- oder -  Der Abstand der aktuellen Position zum Ende des Streams beträgt weniger als <paramref name="count" /> Bytes, und die Kapazität kann nicht geändert werden. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> subtrahiert von der Pufferlänge ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Instanz des Streams ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse eine Folge von Bytes asynchron in den aktuellen Stream und erhöht die aktuelle Position im Stream um die Anzahl der geschriebenen Bytes und überwacht Abbruchanforderungen.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Der Puffer, aus dem Daten geschrieben werden sollen.</param>
      <param name="offset">Der nullbasierte Byteoffset im <paramref name="buffer" />, ab dem Bytes in den Stream kopiert werden.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Bytes.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .Der Standardwert ist <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="offset" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt das Schreiben nicht.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Stream wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.MemoryStream.WriteByte(System.Byte)">
      <summary>Schreibt ein Byte an die aktuelle Position im aktuellen Stream.</summary>
      <param name="value">Das zu schreibende Byte. </param>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt das Schreiben nicht.Weitere Informationen finden Sie unter <see cref="P:System.IO.Stream.CanWrite" />.- oder -  Die aktuelle Position befindet sich am Ende des Streams, und die Kapazität kann nicht geändert werden. </exception>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Stream ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.MemoryStream.WriteTo(System.IO.Stream)">
      <summary>Schreibt den gesamten Inhalt dieses Arbeitsspeicherstreams in einen anderen Stream.</summary>
      <param name="stream">Der Stream, in den dieser Arbeitsspeicherstream geschrieben werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null. </exception>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Stream oder der Zielstream ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.SeekOrigin">
      <summary>Gibt die Position in einem Stream an, der für die Suche verwendet werden soll.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.IO.SeekOrigin.Begin">
      <summary>Gibt den Anfang eines Streams an.</summary>
    </member>
    <member name="F:System.IO.SeekOrigin.Current">
      <summary>Gibt die aktuelle Position innerhalb eines Streams an.</summary>
    </member>
    <member name="F:System.IO.SeekOrigin.End">
      <summary>Gibt das Ende eines Streams an.</summary>
    </member>
    <member name="T:System.IO.Stream">
      <summary>Stellt eine allgemeine Ansicht einer Folge von Bytes bereit.Dies ist eine abstrakte Klasse.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, finden Sie unter der Reference Source.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Stream.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Stream" />-Klasse. </summary>
    </member>
    <member name="P:System.IO.Stream.CanRead">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse einen Wert ab, der angibt, ob der aktuelle Stream Lesevorgänge unterstützt.</summary>
      <returns>true, wenn der Stream Lesevorgänge unterstützt, andernfalls false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.Stream.CanSeek">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse einen Wert ab, der angibt, ob der aktuelle Stream Suchvorgänge unterstützt.</summary>
      <returns>true, wenn der Stream Suchvorgänge unterstützt, andernfalls false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.Stream.CanTimeout">
      <summary>Ruft einen Wert ab, der bestimmt, ob für den aktuellen Stream ein Timeout möglich ist.</summary>
      <returns>Ein Wert, der bestimmt, ob für den aktuellen Stream ein Timeout möglich ist.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Stream.CanWrite">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse einen Wert ab, der angibt, ob der aktuelle Stream Schreibvorgänge unterstützt.</summary>
      <returns>true, wenn der Stream Schreibvorgänge unterstützt, andernfalls false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Stream.CopyTo(System.IO.Stream)">
      <summary>Liest alle Bytes aus dem aktuellen Stream und schreibt sie in einen anderen Datenstrom.</summary>
      <param name="destination">Der Stream, in den der Inhalt des aktuellen Stream kopiert wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> ist null.</exception>
      <exception cref="T:System.NotSupportedException">Der aktuelle Stream unterstützt keine Lesevorgänge.- oder - <paramref name="destination" /> unterstützt das Schreiben nicht.</exception>
      <exception cref="T:System.ObjectDisposedException">Entweder der aktuelle Stream oder <paramref name="destination" /> wurde geschlossen, bevor die <see cref="M:System.IO.Stream.CopyTo(System.IO.Stream)" />-Methode aufgerufen wurde.</exception>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten.</exception>
    </member>
    <member name="M:System.IO.Stream.CopyTo(System.IO.Stream,System.Int32)">
      <summary>Liest alles Bytes aus dem aktuellen Datenstrom und schreibt sie unter Verwendung einer angegebenen Puffergröße in einen anderen Datenstrom.</summary>
      <param name="destination">Der Stream, in den der Inhalt des aktuellen Stream kopiert wird.</param>
      <param name="bufferSize">Die Größe des Puffers.Dieser Wert muss größer als 0 sein.Die Standardgröße ist 81920.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> ist negativ oder 0 (null).</exception>
      <exception cref="T:System.NotSupportedException">Der aktuelle Stream unterstützt keine Lesevorgänge.- oder - <paramref name="destination" /> unterstützt das Schreiben nicht.</exception>
      <exception cref="T:System.ObjectDisposedException">Entweder der aktuelle Stream oder <paramref name="destination" /> wurde geschlossen, bevor die <see cref="M:System.IO.Stream.CopyTo(System.IO.Stream)" />-Methode aufgerufen wurde.</exception>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten.</exception>
    </member>
    <member name="M:System.IO.Stream.CopyToAsync(System.IO.Stream)">
      <summary>Liest die Bytes asynchron aus dem aktuellen Stream und schreibt sie in einen anderen Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Kopiervorgang darstellt.</returns>
      <param name="destination">Der Stream, in den der Inhalt des aktuellen Stream kopiert wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> ist null.</exception>
      <exception cref="T:System.ObjectDisposedException">Entweder der aktuelle Stream oder der Zielstream wird freigegeben.</exception>
      <exception cref="T:System.NotSupportedException">Der aktuelle Stream unterstützt kein Lesen oder Zielstream unterstützt keine Schreibvorgänge.</exception>
    </member>
    <member name="M:System.IO.Stream.CopyToAsync(System.IO.Stream,System.Int32)">
      <summary>Liest die Bytes asynchron aus dem aktuellen Stream und schreibt sie unter Verwendung einer angegebenen Puffergröße in einen anderen Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Kopiervorgang darstellt.</returns>
      <param name="destination">Der Stream, in den der Inhalt des aktuellen Stream kopiert wird.</param>
      <param name="bufferSize">Die Größe des Cookies in Bytes.Dieser Wert muss größer als 0 sein.Die Standardgröße ist 81920.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="buffersize" /> ist negativ oder 0 (null).</exception>
      <exception cref="T:System.ObjectDisposedException">Entweder der aktuelle Stream oder der Zielstream wird freigegeben.</exception>
      <exception cref="T:System.NotSupportedException">Der aktuelle Stream unterstützt kein Lesen oder Zielstream unterstützt keine Schreibvorgänge.</exception>
    </member>
    <member name="M:System.IO.Stream.CopyToAsync(System.IO.Stream,System.Int32,System.Threading.CancellationToken)">
      <summary>Liest die Bytes asynchron aus dem aktuellen Stream und schreibt sie unter Verwendung einer angegebenen Puffergröße und eines Abbruchtokens in einen anderen Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Kopiervorgang darstellt.</returns>
      <param name="destination">Der Stream, in den der Inhalt des aktuellen Stream kopiert wird.</param>
      <param name="bufferSize">Die Größe des Cookies in Bytes.Dieser Wert muss größer als 0 sein.Die Standardgröße ist 81920.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .Der Standardwert ist <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destination" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="buffersize" /> ist negativ oder 0 (null).</exception>
      <exception cref="T:System.ObjectDisposedException">Entweder der aktuelle Stream oder der Zielstream wird freigegeben.</exception>
      <exception cref="T:System.NotSupportedException">Der aktuelle Stream unterstützt kein Lesen oder Zielstream unterstützt keine Schreibvorgänge.</exception>
    </member>
    <member name="M:System.IO.Stream.Dispose">
      <summary>Gibt alle vom <see cref="T:System.IO.Stream" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.IO.Stream.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.IO.Stream" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="M:System.IO.Stream.Flush">
      <summary>Löscht beim Überschreiben in einer abgeleiteten Klasse alle Puffer für diesen Stream und veranlasst die Ausgabe aller gepufferten Daten an das zugrunde liegende Gerät.</summary>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Stream.FlushAsync">
      <summary>Löscht sämtliche Puffer für diesen Stream asynchron und veranlasst die Ausgabe aller gepufferten Daten an das zugrunde liegende Gerät.</summary>
      <returns>Eine Aufgabe, die die asynchrone Leerung darstellt.</returns>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.Stream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Löscht alle Puffer für diesen Stream asynchron, veranlasst die Ausgabe aller gepufferten Daten an das zugrunde liegende Gerät und überwacht Abbruchanforderungen.</summary>
      <returns>Eine Aufgabe, die die asynchrone Leerung darstellt.</returns>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .Der Standardwert ist <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
    </member>
    <member name="P:System.IO.Stream.Length">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse die Länge des Streams in Bytes ab.</summary>
      <returns>Ein Long-Wert, der die Länge des Streams in Bytes darstellt.</returns>
      <exception cref="T:System.NotSupportedException">Eine aus Stream abgeleitete Klasse unterstützt keine Suchvorgänge. </exception>
      <exception cref="T:System.ObjectDisposedException">Es wurden Methoden aufgerufen, nachdem der Stream geschlossen wurde. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.Stream.Null">
      <summary>Ein Stream ohne Sicherungsspeicher.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.Stream.Position">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse die Position im aktuellen Stream ab oder legt diese fest.</summary>
      <returns>Die aktuelle Position innerhalb des Streams.</returns>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt keine Suchvorgänge. </exception>
      <exception cref="T:System.ObjectDisposedException">Es wurden Methoden aufgerufen, nachdem der Stream geschlossen wurde. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Stream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest beim Überschreiben in einer abgeleiteten Klasse eine Folge von Bytes aus dem aktuellen Stream und erhöht die Position im Stream um die Anzahl der gelesenen Bytes.</summary>
      <returns>Die Gesamtanzahl der in den Puffer gelesenen Bytes.Dies kann weniger als die Anzahl der angeforderten Bytes sein, wenn diese Anzahl an Bytes derzeit nicht verfügbar ist, oder 0, wenn das Ende des Streams erreicht ist.</returns>
      <param name="buffer">Ein Bytearray.Nach dem Beenden dieser Methode enthält der Puffer das angegebene Bytearray mit den Werten zwischen <paramref name="offset" /> und (<paramref name="offset" /> + <paramref name="count" /> - 1), die durch aus der aktuellen Quelle gelesene Bytes ersetzt wurden.</param>
      <param name="offset">Der nullbasierte Byteoffset im <paramref name="buffer" />, ab dem die aus dem aktuellen Stream gelesenen Daten gespeichert werden. </param>
      <param name="count">Die maximale Anzahl an Bytes, die aus dem aktuellen Stream gelesen werden sollen. </param>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="offset" /> und <paramref name="count" /> ist größer als die Pufferlänge. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.NotSupportedException">Lesevorgänge werden vom Stream nicht unterstützt. </exception>
      <exception cref="T:System.ObjectDisposedException">Es wurden Methoden aufgerufen, nachdem der Stream geschlossen wurde. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Stream.ReadAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest eine Bytesequenz asynchron aus dem aktuellen Stream und setzt die Position in diesem Stream um die Anzahl der gelesenen Bytes nach vorn.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die Gesamtzahl der Bytes, die in den Puffer gelesen werden.Der Ergebniswert kann niedriger als die Anzahl der angeforderten Bytes sein, wenn die Anzahl an derzeit verfügbaren Bytes kleiner ist als die angeforderte Anzahl, oder sie kann 0 (null) sein, wenn das Datenstromende erreicht ist.</returns>
      <param name="buffer">Der Puffer, in den die Daten geschrieben werden sollen.</param>
      <param name="offset">Der Byteoffset im <paramref name="buffer" />, ab dem Daten aus dem Stream geschrieben werden.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Bytes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="offset" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.NotSupportedException">Lesevorgänge werden vom Stream nicht unterstützt.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Stream wird gerade durch einen früheren Lesevorgang. </exception>
    </member>
    <member name="M:System.IO.Stream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Liest eine Folge von Bytes asynchron aus dem aktuellen Stream, erhöht die Position im Stream um die Anzahl der gelesenen Bytes und überwacht Abbruchanfragen.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die Gesamtzahl der Bytes, die in den Puffer gelesen werden.Der Ergebniswert kann niedriger als die Anzahl der angeforderten Bytes sein, wenn die Anzahl an derzeit verfügbaren Bytes kleiner ist als die angeforderte Anzahl, oder sie kann 0 (null) sein, wenn das Datenstromende erreicht ist.</returns>
      <param name="buffer">Der Puffer, in den die Daten geschrieben werden sollen.</param>
      <param name="offset">Der Byteoffset im <paramref name="buffer" />, ab dem Daten aus dem Stream geschrieben werden.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Bytes.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .Der Standardwert ist <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="offset" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.NotSupportedException">Lesevorgänge werden vom Stream nicht unterstützt.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Stream wird gerade durch einen früheren Lesevorgang. </exception>
    </member>
    <member name="M:System.IO.Stream.ReadByte">
      <summary>Liest ein Byte aus dem Stream und erhöht die Position im Stream um ein Byte, oder gibt -1 zurück, wenn das Ende des Streams erreicht ist.</summary>
      <returns>Das Byte ohne Vorzeichen, umgewandelt in Int32, oder -1, wenn das Ende des Streams erreicht ist.</returns>
      <exception cref="T:System.NotSupportedException">Lesevorgänge werden vom Stream nicht unterstützt. </exception>
      <exception cref="T:System.ObjectDisposedException">Es wurden Methoden aufgerufen, nachdem der Stream geschlossen wurde. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Stream.ReadTimeout">
      <summary>Ruft einen Wert in Millisekunden ab, der bestimmt, wie lange der Stream versucht, Lesevorgänge durchzuführen, bevor ein Timeout auftritt, oder legt diesen fest. </summary>
      <returns>Ein Wert in Millisekunden, der bestimmt, wie lange der Stream versucht, Lesevorgänge durchzuführen, bevor ein Timeout auftritt.</returns>
      <exception cref="T:System.InvalidOperationException">Die <see cref="P:System.IO.Stream.ReadTimeout" />-Methode löst immer eine <see cref="T:System.InvalidOperationException" /> aus. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Stream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Legt beim Überschreiben in einer abgeleiteten Klasse die Position im aktuellen Stream fest.</summary>
      <returns>Die neue Position innerhalb des aktuellen Streams.</returns>
      <param name="offset">Ein Byteoffset relativ zum <paramref name="origin" />-Parameter. </param>
      <param name="origin">Ein Wert vom Typ <see cref="T:System.IO.SeekOrigin" />, der den Bezugspunkt angibt, von dem aus die neue Position ermittelt wird. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt keine Suchvorgänge. Dies ist beispielsweise der Fall, wenn der Stream aus einer Pipe- oder Konsolenausgabe erstellt wird. </exception>
      <exception cref="T:System.ObjectDisposedException">Es wurden Methoden aufgerufen, nachdem der Stream geschlossen wurde. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Stream.SetLength(System.Int64)">
      <summary>Legt beim Überschreiben in einer abgeleiteten Klasse die Länge des aktuellen Streams fest.</summary>
      <param name="value">Die gewünschte Länge des aktuellen Streams in Bytes. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt nicht sowohl Lese- als auch Schreibvorgänge. Dies ist beispielsweise der Fall, wenn der Stream aus einer Pipe- oder Konsolenausgabe erstellt wird. </exception>
      <exception cref="T:System.ObjectDisposedException">Es wurden Methoden aufgerufen, nachdem der Stream geschlossen wurde. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.Stream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse eine Folge von Bytes in den aktuellen Stream und erhöht die aktuelle Position im Stream um die Anzahl der geschriebenen Bytes.</summary>
      <param name="buffer">Ein Bytearray.Diese Methode kopiert <paramref name="count" /> Bytes aus dem <paramref name="buffer" /> in den aktuellen Stream.</param>
      <param name="offset">Der nullbasierte Byteoffset im <paramref name="buffer" />, ab dem Bytes in den aktuellen Stream kopiert werden. </param>
      <param name="count">Die Anzahl an Bytes, die in den aktuellen Stream geschrieben werden sollen. </param>
      <exception cref="T:System.ArgumentException">Die Summe der <paramref name="offset" /> und <paramref name="count" /> ist größer als die Länge des Puffers.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" />  ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.IO.IOException">E/a-Fehler, wie z. B. die angegebene Datei nicht gefunden werden kann.</exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt das Schreiben nicht.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="M:System.IO.Stream.Write(System.Byte[],System.Int32,System.Int32)" /> wurde aufgerufen, nachdem der Stream geschlossen wurde.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.Stream.WriteAsync(System.Byte[],System.Int32,System.Int32)">
      <summary>Schreibt eine Bytesequenz asynchron in den aktuellen Stream und setzt die aktuelle Position in diesem Stream um die Anzahl der geschriebenen Bytes nach vorn.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Der Puffer, aus dem Daten geschrieben werden sollen.</param>
      <param name="offset">Der nullbasierte Byteoffset im <paramref name="buffer" />, ab dem Bytes in den Stream kopiert werden.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Bytes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="offset" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt das Schreiben nicht.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Stream wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.Stream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse eine Folge von Bytes asynchron in den aktuellen Stream und erhöht die aktuelle Position im Stream um die Anzahl der geschriebenen Bytes und überwacht Abbruchanforderungen.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Der Puffer, aus dem Daten geschrieben werden sollen.</param>
      <param name="offset">Der nullbasierte Byteoffset im <paramref name="buffer" />, ab dem Bytes in den Stream kopiert werden.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Bytes.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .Der Standardwert ist <see cref="P:System.Threading.CancellationToken.None" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="offset" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt das Schreiben nicht.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Stream wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.Stream.WriteByte(System.Byte)">
      <summary>Schreibt ein Byte an die aktuellen Position im Stream und erhöht die aktuelle Position im Stream um ein Byte.</summary>
      <param name="value">Das Byte, das in den Stream geschrieben werden soll. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.NotSupportedException">Der Stream unterstützt keine Schreibvorgänge, oder er wurde bereits geschlossen. </exception>
      <exception cref="T:System.ObjectDisposedException">Es wurden Methoden aufgerufen, nachdem der Stream geschlossen wurde. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.Stream.WriteTimeout">
      <summary>Ruft einen Wert in Millisekunden ab, der bestimmt, wie lange der Stream versucht, Schreibvorgänge durchzuführen, bevor ein Timeout auftritt, oder legt diesen fest. </summary>
      <returns>Ein Wert in Millisekunden, der bestimmt, wie lange der Stream versucht, Schreibvorgänge durchzuführen, bevor ein Timeout auftritt.</returns>
      <exception cref="T:System.InvalidOperationException">Die <see cref="P:System.IO.Stream.WriteTimeout" />-Methode löst immer eine <see cref="T:System.InvalidOperationException" /> aus. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.IO.StreamReader">
      <summary>Implementiert einen <see cref="T:System.IO.TextReader" />, der Zeichen aus einem Bytestream in einer bestimmten Codierung liest.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, finden Sie unter der Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StreamReader" />-Klasse für den angegebenen Stream.</summary>
      <param name="stream">Der zu lesende Stream. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> unterstützt keine Lesevorgänge. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null. </exception>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StreamReader" />-Klasse für den angegebenen Stream mit der angegebenen Option zur Erkennung der Bytereihenfolgemarken.</summary>
      <param name="stream">Der zu lesende Stream. </param>
      <param name="detectEncodingFromByteOrderMarks">Gibt an, ob am Anfang der Datei nach Bytereihenfolgemarken gesucht werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> unterstützt keine Lesevorgänge. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null. </exception>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream,System.Text.Encoding)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StreamReader" />-Klasse für den angegebenen Stream und mit der angegebenen Zeichencodierung.</summary>
      <param name="stream">Der zu lesende Stream. </param>
      <param name="encoding">Die zu verwendende Zeichencodierung. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> unterstützt keine Lesevorgänge. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> oder <paramref name="encoding" /> ist null. </exception>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream,System.Text.Encoding,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StreamReader" />-Klasse für den angegebenen Stream mit der angegebenen Zeichencodierung und Option zur Erkennung der Bytereihenfolgemarken.</summary>
      <param name="stream">Der zu lesende Stream. </param>
      <param name="encoding">Die zu verwendende Zeichencodierung. </param>
      <param name="detectEncodingFromByteOrderMarks">Gibt an, ob am Anfang der Datei nach Bytereihenfolgemarken gesucht werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> unterstützt keine Lesevorgänge. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> oder <paramref name="encoding" /> ist null. </exception>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream,System.Text.Encoding,System.Boolean,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StreamReader" />-Klasse für den angegebenen Stream mit der angegebenen Zeichencodierung, Option zur Erkennung der Bytereihenfolgemarken und Puffergröße.</summary>
      <param name="stream">Der zu lesende Stream. </param>
      <param name="encoding">Die zu verwendende Zeichencodierung. </param>
      <param name="detectEncodingFromByteOrderMarks">Gibt an, ob am Anfang der Datei nach Bytereihenfolgemarken gesucht werden soll. </param>
      <param name="bufferSize">Die Mindestgröße des Puffers. </param>
      <exception cref="T:System.ArgumentException">Lesevorgänge werden vom Stream nicht unterstützt. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> oder <paramref name="encoding" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> ist kleiner oder gleich 0 (null). </exception>
    </member>
    <member name="M:System.IO.StreamReader.#ctor(System.IO.Stream,System.Text.Encoding,System.Boolean,System.Int32,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StreamReader" />-Klasse für den angegebenen Stream auf Grundlage der angegebenen Zeichencodierung, der Option zur Erkennung der Bytereihenfolgenmarken, der Puffergröße und lässt optional den Stream geöffnet.</summary>
      <param name="stream">Der zu lesende Stream.</param>
      <param name="encoding">Die zu verwendende Zeichencodierung.</param>
      <param name="detectEncodingFromByteOrderMarks">true, um am Anfang der Datei nach Bytereihenfolgemarken zu suchen, andernfalls false.</param>
      <param name="bufferSize">Die Mindestgröße des Puffers.</param>
      <param name="leaveOpen">true, um den Datenstrom geöffnet zu lassen, nach dem das <see cref="T:System.IO.StreamReader" />-Objekt freigegeben wurde; andernfalls false.</param>
    </member>
    <member name="P:System.IO.StreamReader.BaseStream">
      <summary>Gibt den zugrunde liegenden Stream zurück.</summary>
      <returns>Der zugrunde liegende Stream.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.StreamReader.CurrentEncoding">
      <summary>Ruft die aktuelle Zeichencodierung ab, die das aktuelle <see cref="T:System.IO.StreamReader" />-Objekt verwendet.</summary>
      <returns>Die durch das aktuelle Leseprogramm verwendete aktuelle Zeichencodierung.Der Wert kann sich nach dem ersten Aufruf einer <see cref="Overload:System.IO.StreamReader.Read" />-Methode von <see cref="T:System.IO.StreamReader" /> ändern, da die automatische Erkennung der Codierung erst nach dem ersten Aufruf einer <see cref="Overload:System.IO.StreamReader.Read" />-Methode erfolgt.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.DiscardBufferedData">
      <summary>Löscht den internen Puffer.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.Dispose(System.Boolean)">
      <summary>Schließt den zugrunde liegenden Stream und gibt die vom <see cref="T:System.IO.StreamReader" /> verwendeten nicht verwalteten Ressourcen sowie optional auch die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben. </param>
    </member>
    <member name="P:System.IO.StreamReader.EndOfStream">
      <summary>Ruft einen Wert ab, der angibt, ob sich die aktuelle Streamposition am Ende des Streams befindet.</summary>
      <returns>true, wenn sich die aktuelle Streamposition am Ende des Streams befindet, andernfalls false.</returns>
      <exception cref="T:System.ObjectDisposedException">Der zugrunde liegende Stream wurde verworfen.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.IO.StreamReader.Null">
      <summary>Ein <see cref="T:System.IO.StreamReader" />-Objekt um einen leeren Stream.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.Peek">
      <summary>Gibt das nächste verfügbare Zeichen zurück, ohne es zu verarbeiten.</summary>
      <returns>Eine ganze Zahl, die das nächste zu lesende Zeichen darstellt, oder -1, wenn keine zu lesenden Zeichen vorhanden sind oder der Stream keine Suchvorgänge unterstützt.</returns>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.Read">
      <summary>Liest das nächste Zeichen aus dem Eingabestream und verschiebt die Zeichenposition um ein Zeichen nach vorn.</summary>
      <returns>Das nächste Zeichen im Eingabestream wird als <see cref="T:System.Int32" />-Objekt dargestellt, oder -1, wenn keine weiteren Zeichen verfügbar sind.</returns>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.Read(System.Char[],System.Int32,System.Int32)">
      <summary>Liest vom aktuellen Stream ein angegebenes Maximum von Zeichen in einen Puffer ein und beginnt dabei am angegebenen Index.</summary>
      <returns>Die Anzahl der gelesenen Zeichen, bzw. 0 (null), wenn das Ende des Streams erreicht ist und keine Daten gelesen wurden.Die Anzahl ist gleich dem <paramref name="count" />-Parameter oder kleiner, abhängig davon, ob die Daten im Stream verfügbar sind.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Zeichenarray mit den Werten zwischen <paramref name="index" /> und (<paramref name="index + count - 1" />), die durch die aus der aktuellen Quelle gelesenen Zeichen ersetzt wurden. </param>
      <param name="index">Der Index von <paramref name="buffer" />, bei dem mit dem Schreiben begonnen wird. </param>
      <param name="count">Die maximale Anzahl der zu lesenden Zeichen. </param>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.IO.IOException">Es ist ein E/A-Fehler aufgetreten, beispielsweise das Schließen eines Streams. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.ReadAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Liest eine angegebene Höchstanzahl von Zeichen asynchron aus dem aktuellen Stream und schreibt die Daten in einen Puffer, wobei am angegebenen Index begonnen wird. </summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die Gesamtzahl der Bytes, die in den Puffer gelesen werden.Der Ergebniswert kann niedriger als die Anzahl der angeforderten Bytes sein, wenn die Anzahl an derzeit verfügbaren Bytes kleiner ist als die angeforderte Anzahl, oder sie kann 0 (null) sein, wenn das Datenstromende erreicht ist.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Zeichenarray mit den Werten zwischen <paramref name="index" /> und (<paramref name="index" /> + <paramref name="count" /> - 1), die durch die aus der aktuellen Quelle gelesenen Zeichen ersetzt wurden.</param>
      <param name="index">Die Position in <paramref name="buffer" />, an der mit dem Schreiben begonnen wird.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Zeichen.Wenn das Ende des Streams erreicht ist, bevor die angegebene Anzahl von Zeichen in den Puffer geschrieben wurde, erfolgt die aktuelle Methodenrückgabe.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StreamReader.ReadBlock(System.Char[],System.Int32,System.Int32)">
      <summary>Liest eine angegebene Höchstanzahl von Zeichen aus dem aktuellen Stream und schreibt die Daten in einen Puffer, wobei am angegebenen Index begonnen wird.</summary>
      <returns>Die Anzahl der gelesenen Zeichen.Die Anzahl ist kleiner oder gleich <paramref name="count" />, je nachdem, ob alle Eingabezeichen gelesen wurden.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Zeichenarray mit den Werten zwischen <paramref name="index" /> und (<paramref name="index + count - 1" />), die durch die aus der aktuellen Quelle gelesenen Zeichen ersetzt wurden.</param>
      <param name="index">Die Position in <paramref name="buffer" />, an der mit dem Schreiben begonnen wird.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Zeichen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.StreamReader" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten. </exception>
    </member>
    <member name="M:System.IO.StreamReader.ReadBlockAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Liest eine angegebene Höchstanzahl von Zeichen asynchron aus dem aktuellen Stream und schreibt die Daten in einen Puffer, wobei am angegebenen Index begonnen wird.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die Gesamtzahl der Bytes, die in den Puffer gelesen werden.Der Ergebniswert kann niedriger als die Anzahl der angeforderten Bytes sein, wenn die Anzahl an derzeit verfügbaren Bytes kleiner ist als die angeforderte Anzahl, oder sie kann 0 (null) sein, wenn das Datenstromende erreicht ist.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Zeichenarray mit den Werten zwischen <paramref name="index" /> und (<paramref name="index" /> + <paramref name="count" /> - 1), die durch die aus der aktuellen Quelle gelesenen Zeichen ersetzt wurden.</param>
      <param name="index">Die Position in <paramref name="buffer" />, an der mit dem Schreiben begonnen wird.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Zeichen.Wenn das Ende des Streams erreicht ist, bevor die angegebene Anzahl von Zeichen in den Puffer geschrieben wurde, erfolgt die Methodenrückgabe.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StreamReader.ReadLine">
      <summary>Liest eine Zeile von Zeichen aus dem aktuellen Stream und gibt die Daten als Zeichenfolge zurück.</summary>
      <returns>Die nächste Zeile des Eingabestreams bzw. null, wenn das Ende des Eingabestreams erreicht ist.</returns>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden, um einen Puffer für die zurückgegebene Zeichenfolge zu reservieren. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.ReadLineAsync">
      <summary>Liest eine Zeile von Zeichen asynchron aus dem aktuellen Stream und gibt die Daten als Zeichenfolge zurück.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die nächste Zeile aus dem Stream oder ist null, wenn alle Zeichen gelesen wurden.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Anzahl der Zeichen in der nächsten Zeile ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StreamReader.ReadToEnd">
      <summary>Liest alle Zeichen von der aktuellen Position bis zum Ende des Streams.</summary>
      <returns>Der Rest des Streams als Zeichenfolge, von der aktuellen Position bis zum Ende.Wenn die aktuelle Position am Ende des Streams ist, wird eine leere Zeichenfolge ("") zurückgegeben.</returns>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden, um einen Puffer für die zurückgegebene Zeichenfolge zu reservieren. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamReader.ReadToEndAsync">
      <summary>Liest alle Zeichen asynchron von der aktuellen Position bis zum Ende des Streams und gibt diese als einzelne Zeichenfolge zurück.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält eine Zeichenfolge mit den Zeichen von der aktuellen Position bis zum Ende des Streams.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Anzahl der Zeichen ist niedriger als <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="T:System.IO.StreamWriter">
      <summary>Implementiert einen <see cref="T:System.IO.TextWriter" /> zum Schreiben von Zeichen in einen Stream in einer bestimmten Codierung.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, rufen Sie die Verweisquelle auf.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.#ctor(System.IO.Stream)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StreamWriter" />-Klasse für den angegebenen Stream unter Verwendung der UTF-8-Codierung und der Standardpuffergröße.</summary>
      <param name="stream">Der Stream, in den geschrieben werden soll. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> ist schreibgeschützt. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null. </exception>
    </member>
    <member name="M:System.IO.StreamWriter.#ctor(System.IO.Stream,System.Text.Encoding)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StreamWriter" />-Klasse für den angegebenen Stream unter Verwendung der angegebenen Codierung und der Standardpuffergröße.</summary>
      <param name="stream">Der Stream, in den geschrieben werden soll. </param>
      <param name="encoding">Die zu verwendende Zeichencodierung. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> oder <paramref name="encoding" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> ist schreibgeschützt. </exception>
    </member>
    <member name="M:System.IO.StreamWriter.#ctor(System.IO.Stream,System.Text.Encoding,System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StreamWriter" />-Klasse für den angegebenen Stream unter Verwendung der angegebenen Codierung und Puffergröße.</summary>
      <param name="stream">Der Stream, in den geschrieben werden soll. </param>
      <param name="encoding">Die zu verwendende Zeichencodierung. </param>
      <param name="bufferSize">Die Puffergröße in Byte. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> oder <paramref name="encoding" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> ist negativ. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> ist schreibgeschützt. </exception>
    </member>
    <member name="M:System.IO.StreamWriter.#ctor(System.IO.Stream,System.Text.Encoding,System.Int32,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StreamWriter" />-Klasse für den angegebenen Stream unter Verwendung der angegebenen Codierung und Puffergröße und lässt optional den Stream geöffnet.</summary>
      <param name="stream">Der Stream, in den geschrieben werden soll.</param>
      <param name="encoding">Die zu verwendende Zeichencodierung.</param>
      <param name="bufferSize">Die Puffergröße in Byte.</param>
      <param name="leaveOpen">true, um den Datenstrom geöffnet zu lassen, nach dem das <see cref="T:System.IO.StreamWriter" />-Objekt freigegeben wurde; andernfalls false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> oder <paramref name="encoding" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="bufferSize" /> ist negativ. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="stream" /> ist schreibgeschützt. </exception>
    </member>
    <member name="P:System.IO.StreamWriter.AutoFlush">
      <summary>Ruft einen Wert ab, der angibt, ob der <see cref="T:System.IO.StreamWriter" /> nach jedem Aufruf von <see cref="M:System.IO.StreamWriter.Write(System.Char)" /> den Puffer in den zugrunde liegenden Stream wegschreibt, oder legt diesen Wert fest.</summary>
      <returns>true, um <see cref="T:System.IO.StreamWriter" /> zum Entleeren des Puffers zu zwingen, andernfalls false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.IO.StreamWriter.BaseStream">
      <summary>Ruft den zugrunde liegenden Stream ab, der eine Schnittstelle zu einem Sicherungsspeicher bildet.</summary>
      <returns>Der Stream, in den dieser StreamWriter schreibt.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.IO.StreamWriter" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben. </param>
      <exception cref="T:System.Text.EncoderFallbackException">Die aktuelle Codierung unterstützt das Anzeigen einer Hälfte eines Unicode-Ersatzzeichenpaars nicht.</exception>
    </member>
    <member name="P:System.IO.StreamWriter.Encoding">
      <summary>Ruft die <see cref="T:System.Text.Encoding" /> ab, in der die Ausgabe geschrieben wird.</summary>
      <returns>Die im Konstruktor für die aktuelle Instanz angegebene <see cref="T:System.Text.Encoding" /> oder <see cref="T:System.Text.UTF8Encoding" />, sofern keine Codierung angegeben wurde.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Flush">
      <summary>Löscht sämtliche Puffer für den aktuellen Writer und veranlasst die Ausgabe aller gepufferten Daten an den zugrunde liegenden Stream.</summary>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Writer wird geschlossen. </exception>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten. </exception>
      <exception cref="T:System.Text.EncoderFallbackException">Die aktuelle Codierung unterstützt das Anzeigen einer Hälfte eines Unicode-Ersatzzeichenpaars nicht. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.FlushAsync">
      <summary>Löscht alle Puffer für diesen Stream asynchron und veranlasst die Ausgabe aller gepufferten Daten an das zugrunde liegende Gerät.</summary>
      <returns>Eine Aufgabe, die die asynchrone Leerung darstellt.</returns>
      <exception cref="T:System.ObjectDisposedException">Der Stream wurde freigegeben.</exception>
    </member>
    <member name="F:System.IO.StreamWriter.Null">
      <summary>Stellt einen StreamWriter ohne Sicherungsspeicher bereit, in den zwar geschrieben, aus dem jedoch nicht gelesen werden kann.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Write(System.Char)">
      <summary>Schreibt ein Zeichen in den Stream.</summary>
      <param name="value">Das in den Stream zu schreibende Zeichen. </param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> ist true, oder der <see cref="T:System.IO.StreamWriter" />-Puffer ist voll, und der aktuelle Writer ist geschlossen. </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> ist true, oder der <see cref="T:System.IO.StreamWriter" />-Puffer ist voll, und der Inhalt des Puffers kann nicht in den zugrunde liegenden Stream fester Größe geschrieben werden, da der <see cref="T:System.IO.StreamWriter" /> sich am Ende des Streams befindet. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Write(System.Char[])">
      <summary>Schreibt ein Zeichenarray in den Stream.</summary>
      <param name="buffer">Ein Zeichenarray mit den zu schreibenden Daten.Wenn <paramref name="buffer" /> den Wert null ist, wird nichts geschrieben.</param>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> ist true, oder der <see cref="T:System.IO.StreamWriter" />-Puffer ist voll, und der aktuelle Writer ist geschlossen. </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> ist true, oder der <see cref="T:System.IO.StreamWriter" />-Puffer ist voll, und der Inhalt des Puffers kann nicht in den zugrunde liegenden Stream fester Größe geschrieben werden, da der <see cref="T:System.IO.StreamWriter" /> sich am Ende des Streams befindet. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Write(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt ein Teilarray von Zeichen in den Stream.</summary>
      <param name="buffer">Ein Zeichenarray, das die zu schreibenden Daten enthält. </param>
      <param name="index">Die Zeichenposition im Puffer, an der mit dem Lesen von Daten begonnen werden soll. </param>
      <param name="count">Die maximale Anzahl der zu schreibenden Zeichen. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> ist true, oder der <see cref="T:System.IO.StreamWriter" />-Puffer ist voll, und der aktuelle Writer ist geschlossen. </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> ist true, oder der <see cref="T:System.IO.StreamWriter" />-Puffer ist voll, und der Inhalt des Puffers kann nicht in den zugrunde liegenden Stream fester Größe geschrieben werden, da der <see cref="T:System.IO.StreamWriter" /> sich am Ende des Streams befindet. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.Write(System.String)">
      <summary>Schreibt eine Zeichenfolge in den Stream.</summary>
      <param name="value">Die Zeichenfolge, die in den Stream geschrieben werden soll.Wenn <paramref name="value" /> den Wert NULL hat, wird nichts geschrieben.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> ist true, oder der <see cref="T:System.IO.StreamWriter" />-Puffer ist voll, und der aktuelle Writer ist geschlossen. </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="P:System.IO.StreamWriter.AutoFlush" /> ist true, oder der <see cref="T:System.IO.StreamWriter" />-Puffer ist voll, und der Inhalt des Puffers kann nicht in den zugrunde liegenden Stream fester Größe geschrieben werden, da der <see cref="T:System.IO.StreamWriter" /> sich am Ende des Streams befindet. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StreamWriter.WriteAsync(System.Char)">
      <summary>Schreibt ein Zeichen asynchron in den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="value">Das in den Stream zu schreibende Zeichen.</param>
      <exception cref="T:System.ObjectDisposedException">Der Streamwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Streamwriter wird derzeit von einem vorherigen Schreibvorgang verwendet.</exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt ein Teilarray von Zeichen asynchron in den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Ein Zeichenarray, das die zu schreibenden Daten enthält.</param>
      <param name="index">Die Zeichenposition im Puffer, an der mit dem Lesen von Daten begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Zeichen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Streamwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Streamwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteAsync(System.String)">
      <summary>Schreibt eine Zeichenfolge asynchron in den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="value">Die Zeichenfolge, die in den Stream geschrieben werden soll.Wenn <paramref name="value" /> den Wert null ist, wird nichts geschrieben.</param>
      <exception cref="T:System.ObjectDisposedException">Der Streamwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Streamwriter wird derzeit von einem vorherigen Schreibvorgang verwendet.</exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteLineAsync">
      <summary>Schreibt ein Zeichen für den Zeilenabschluss asynchron in den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <exception cref="T:System.ObjectDisposedException">Der Streamwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Streamwriter wird derzeit von einem vorherigen Schreibvorgang verwendet.</exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteLineAsync(System.Char)">
      <summary>Schreibt ein Zeichen, gefolgt von einem Zeichen für den Zeilenabschluss asynchron in den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="value">Das in den Stream zu schreibende Zeichen.</param>
      <exception cref="T:System.ObjectDisposedException">Der Streamwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Streamwriter wird derzeit von einem vorherigen Schreibvorgang verwendet.</exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteLineAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt ein Teilarray von Zeichen, gefolgt von einem Zeichen für den Zeilenabschluss, asynchron in den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Das Zeichenarray, aus dem Daten geschrieben werden sollen.</param>
      <param name="index">Die Zeichenposition im Puffer, an der mit dem Lesen von Daten begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Zeichen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Streamwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Streamwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StreamWriter.WriteLineAsync(System.String)">
      <summary>Schreibt eine Zeichenfolge, gefolgt von einem Zeichen für den Zeilenabschluss, asynchron in den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="value">Die zu schreibende Zeichenfolge.Wenn der Wert null ist, wird nur ein Zeichen für den Zeilenabschluss geschrieben.</param>
      <exception cref="T:System.ObjectDisposedException">Der Streamwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Streamwriter wird derzeit von einem vorherigen Schreibvorgang verwendet.</exception>
    </member>
    <member name="T:System.IO.StringReader">
      <summary>Implementiert einen <see cref="T:System.IO.TextReader" />, der aus einer Zeichenfolge liest.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StringReader" />-Klasse, die aus der angegebenen Zeichenfolge liest.</summary>
      <param name="s">Die Zeichenfolge, mit der der <see cref="T:System.IO.StringReader" /> initialisiert werden soll. </param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="s" />-Parameter ist null. </exception>
    </member>
    <member name="M:System.IO.StringReader.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.IO.StringReader" /> verwendeten nicht verwalteten Ressourcen und optional auch die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben. </param>
    </member>
    <member name="M:System.IO.StringReader.Peek">
      <summary>Gibt das nächste verfügbare Zeichen zurück, ohne es zu verarbeiten.</summary>
      <returns>Eine ganze Zahl, die das nächste zu lesende Zeichen darstellt, oder auch -1, wenn keine weiteren Zeichen verfügbar sind oder der Stream keine Suchvorgänge unterstützt.</returns>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Reader ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.Read">
      <summary>Liest das nächste Zeichen aus der Eingabezeichenfolge und verschiebt die Zeichenposition um ein Zeichen nach vorn.</summary>
      <returns>Das nächste Zeichen in der zugrunde liegenden Zeichenfolge oder -1, wenn keine weiteren Zeichen verfügbar sind.</returns>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Reader ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.Read(System.Char[],System.Int32,System.Int32)">
      <summary>Liest einen Zeichenblock aus der Eingabezeichenfolge und verschiebt die Zeichenposition um die durch <paramref name="count" /> angegebene Anzahl von Zeichen nach vorn.</summary>
      <returns>Die Gesamtanzahl der in den Puffer gelesenen Zeichen.Diese kann kleiner als die Anzahl der angeforderten Zeichen sein, wenn diese Anzahl von Zeichen derzeit nicht verfügbar ist, oder 0, wenn das Ende der zugrunde liegenden Zeichenfolge erreicht ist.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Zeichenarray mit den Werten zwischen <paramref name="index" /> und (<paramref name="index" /> + <paramref name="count" /> - 1), die durch die aus der aktuellen Quelle gelesenen Zeichen ersetzt wurden. </param>
      <param name="index">Der Anfangsindex im Puffer. </param>
      <param name="count">Die Anzahl der zu lesenden Zeichen. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Reader ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.ReadAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Liest eine angegebene Höchstanzahl von Zeichen asynchron aus der aktuellen Zeichenkette und schreibt die Daten in einen Puffer, wobei am angegebenen Index begonnen wird. </summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die Gesamtzahl der Bytes, die in den Puffer gelesen werden.Der Ergebniswert kann niedriger als die Anzahl der angeforderten Bytes sein, wenn die Anzahl an derzeit verfügbaren Bytes kleiner ist als die angeforderte Anzahl, oder sie kann 0 (null) sein, wenn das Zeichenfolgenende erreicht ist.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Zeichenarray mit den Werten zwischen <paramref name="index" /> und (<paramref name="index" /> + <paramref name="count" /> - 1), die durch die aus der aktuellen Quelle gelesenen Zeichen ersetzt wurden.</param>
      <param name="index">Die Position in <paramref name="buffer" />, an der mit dem Schreiben begonnen wird.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Zeichen.Wenn das Ende der Zeichenfolge erreicht ist, bevor die angegebene Anzahl von Zeichen in den Puffer geschrieben wurde, erfolgt die Methodenrückgabe.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Zeichenfolgen-Reader wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StringReader.ReadBlockAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Liest eine angegebene Höchstanzahl von Zeichen asynchron aus der aktuellen Zeichenkette und schreibt die Daten in einen Puffer, wobei am angegebenen Index begonnen wird.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die Gesamtzahl der Bytes, die in den Puffer gelesen werden.Der Ergebniswert kann niedriger als die Anzahl der angeforderten Bytes sein, wenn die Anzahl an derzeit verfügbaren Bytes kleiner ist als die angeforderte Anzahl, oder sie kann 0 (null) sein, wenn das Zeichenfolgenende erreicht ist.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Zeichenarray mit den Werten zwischen <paramref name="index" /> und (<paramref name="index" /> + <paramref name="count" /> - 1), die durch die aus der aktuellen Quelle gelesenen Zeichen ersetzt wurden.</param>
      <param name="index">Die Position in <paramref name="buffer" />, an der mit dem Schreiben begonnen wird.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Zeichen.Wenn das Ende der Zeichenfolge erreicht ist, bevor die angegebene Anzahl von Zeichen in den Puffer geschrieben wurde, erfolgt die Methodenrückgabe.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Zeichenfolgen-Reader wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StringReader.ReadLine">
      <summary>Liest eine Zeile von Zeichen aus der aktuellen Zeichenkette und gibt die Daten als Zeichenfolge zurück.</summary>
      <returns>Die nächste Zeile der aktuellen Zeichenfolge oder null, wenn das Ende der Zeichenfolge erreicht ist.</returns>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Reader ist geschlossen. </exception>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden, um einen Puffer für die zurückgegebene Zeichenfolge zu reservieren. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.ReadLineAsync">
      <summary>Liest eine Zeile von Zeichen asynchron aus der aktuellen Zeichenkette und gibt die Daten als Zeichenfolge zurück.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die nächste Zeile aus dem Zeichenfolgenreader oder null, wenn alle Zeichen gelesen wurden.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Anzahl der Zeichen in der nächsten Zeile ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Zeichenfolgen-Reader wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StringReader.ReadToEnd">
      <summary>Liest alle Zeichen von der aktuellen Position bis zum Ende des Streams und gibt diese als einzelne Zeichenfolge zurück.</summary>
      <returns>Der Inhalt ab der aktuellen Position bis zum Ende der zugrunde liegenden Zeichenfolge.</returns>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden, um einen Puffer für die zurückgegebene Zeichenfolge zu reservieren. </exception>
      <exception cref="T:System.ObjectDisposedException">Der aktuelle Reader ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringReader.ReadToEndAsync">
      <summary>Liest alle Zeichen asynchron von der aktuellen Position bis zum Ende des Streams und gibt diese als einzelne Zeichenfolge zurück.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält eine Zeichenfolge mit den Zeichen von der aktuellen Position bis zum Ende der Zeichenfolge.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Anzahl der Zeichen ist niedriger als <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Zeichenfolgen-Reader wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="T:System.IO.StringWriter">
      <summary>Implementiert einen <see cref="T:System.IO.TextWriter" /> zum Schreiben von Informationen in eine Zeichenfolge.Die Informationen werden in einem zugrunde liegenden <see cref="T:System.Text.StringBuilder" /> gespeichert.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StringWriter" />-Klasse.</summary>
    </member>
    <member name="M:System.IO.StringWriter.#ctor(System.IFormatProvider)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StringWriter" />-Klasse mit dem angegebenen Formatierungssteuerelement.</summary>
      <param name="formatProvider">Ein <see cref="T:System.IFormatProvider" />-Objekt zum Steuern der Formatierung. </param>
    </member>
    <member name="M:System.IO.StringWriter.#ctor(System.Text.StringBuilder)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StringWriter" />-Klasse zum Schreiben in den angegebenen <see cref="T:System.Text.StringBuilder" />.</summary>
      <param name="sb">Der StringBuilder, in den geschrieben werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sb" /> ist null. </exception>
    </member>
    <member name="M:System.IO.StringWriter.#ctor(System.Text.StringBuilder,System.IFormatProvider)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.StringWriter" />-Klasse zum Schreiben in den angegebenen <see cref="T:System.Text.StringBuilder" /> und mit dem angegebenen Formatanbieter.</summary>
      <param name="sb">Der StringBuilder, in den geschrieben werden soll. </param>
      <param name="formatProvider">Ein <see cref="T:System.IFormatProvider" />-Objekt zum Steuern der Formatierung. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sb" /> ist null. </exception>
    </member>
    <member name="M:System.IO.StringWriter.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.IO.StringWriter" /> verwendeten nicht verwalteten Ressourcen und optional auch die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben. </param>
    </member>
    <member name="P:System.IO.StringWriter.Encoding">
      <summary>Ruft die <see cref="T:System.Text.Encoding" /> ab, in der die Ausgabe geschrieben wird.</summary>
      <returns>Die Encoding, in der die Ausgabe geschrieben wird.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.FlushAsync">
      <summary>Löscht sämtliche Puffer für den aktuellen Writer asynchron und veranlasst die Ausgabe aller gepufferten Daten an das zugrunde liegende Gerät. </summary>
      <returns>Eine Aufgabe, die die asynchrone Leerung darstellt.</returns>
    </member>
    <member name="M:System.IO.StringWriter.GetStringBuilder">
      <summary>Gibt den zugrunde liegenden <see cref="T:System.Text.StringBuilder" /> zurück.</summary>
      <returns>Der zugrunde liegende StringBuilder.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.ToString">
      <summary>Gibt eine Zeichenfolge zurück, die die bisher in den aktuellen StringWriter geschriebenen Zeichen enthält.</summary>
      <returns>Die Zeichenfolge, die die bisher in den aktuellen StringWriter geschriebenen Zeichen enthält.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.Write(System.Char)">
      <summary>Schreibt ein Zeichen in die Zeichenfolge.</summary>
      <param name="value">Das zu schreibende Zeichen. </param>
      <exception cref="T:System.ObjectDisposedException">Der Writer ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.Write(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt ein Teilarray von Zeichen in die Zeichenfolge.</summary>
      <param name="buffer">Das Zeichenarray, aus dem Daten geschrieben werden sollen. </param>
      <param name="index">Die Position im Puffer, an der mit dem Lesen von Daten begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Zeichen. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ArgumentException">(<paramref name="index" /> + <paramref name="count" />)&gt; <paramref name="buffer" />.Length.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Writer ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.Write(System.String)">
      <summary>Schreibt eine Zeichenfolge in die aktuelle Zeichenfolge.</summary>
      <param name="value">Die zu schreibende Zeichenfolge. </param>
      <exception cref="T:System.ObjectDisposedException">Der Writer ist geschlossen. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.StringWriter.WriteAsync(System.Char)">
      <summary>Schreibt ein Zeichen asynchron in die Zeichenfolge.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="value">Das in die Zeichenfolge zu schreibende Zeichen.</param>
      <exception cref="T:System.ObjectDisposedException">Der Zeichenfolgenwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Zeichenfolgenwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StringWriter.WriteAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt ein Teilarray von Zeichen asynchron in die Zeichenfolge.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Das Zeichenarray, aus dem Daten geschrieben werden sollen.</param>
      <param name="index">Die Position im Puffer, an der mit dem Lesen von Daten begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Zeichen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Zeichenfolgenwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Zeichenfolgenwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StringWriter.WriteAsync(System.String)">
      <summary>Schreibt eine Zeichenfolge asynchron in die aktuelle Zeichenfolge.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="value">Die zu schreibende Zeichenfolge.Wenn <paramref name="value" /> den Wert null hat, wird nichts in den Stream geschrieben.</param>
      <exception cref="T:System.ObjectDisposedException">Der Zeichenfolgenwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Zeichenfolgenwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StringWriter.WriteLineAsync(System.Char)">
      <summary>Schreibt ein Zeichen, gefolgt von einem Zeichen für den Zeilenabschluss asynchron in die Zeichenfolge.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="value">Das in die Zeichenfolge zu schreibende Zeichen.</param>
      <exception cref="T:System.ObjectDisposedException">Der Zeichenfolgenwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Zeichenfolgenwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StringWriter.WriteLineAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt ein Teilarray von Zeichen, gefolgt von einem Zeichen für den Zeilenabschluss, asynchron in die Zeichenfolge.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Das Zeichenarray, aus dem Daten geschrieben werden sollen.</param>
      <param name="index">Die Position im Puffer, an der mit dem Lesen von Daten begonnen werden soll.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Zeichen. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Zeichenfolgenwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Zeichenfolgenwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.StringWriter.WriteLineAsync(System.String)">
      <summary>Schreibt eine Zeichenfolge gefolgt von einem Zeichen für den Zeilenabschluss asynchron in die aktuelle Zeichenfolge.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="value">Die zu schreibende Zeichenfolge.Wenn der Wert null  ist, wird nur ein Zeichen für den Zeilenabschluss geschrieben.</param>
      <exception cref="T:System.ObjectDisposedException">Der Zeichenfolgenwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Zeichenfolgenwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="T:System.IO.TextReader">
      <summary>Stellt ein Leseprogramm dar, das eine sequenzielle Serie von Zeichenfolgen lesen kann.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.TextReader" />-Klasse.</summary>
    </member>
    <member name="M:System.IO.TextReader.Dispose">
      <summary>Gibt alle vom <see cref="T:System.IO.TextReader" />-Objekt verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.IO.TextReader.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.IO.TextReader" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben. </param>
    </member>
    <member name="F:System.IO.TextReader.Null">
      <summary>Stellt einen TextReader ohne zu lesende Daten bereit.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.Peek">
      <summary>Liest das nächste Zeichen, ohne den Zustand des Readers oder der Zeichenquelle zu ändern.Gibt das nächste verfügbare Zeichen zurück, ohne es tatsächlich aus dem Reader zu lesen.</summary>
      <returns>Eine Ganzzahl, die das nächste zu lesende Zeichen darstellt, oder auch -1, wenn keine weiteren Zeichen verfügbar sind oder der Reader keine Suchvorgänge unterstützt.</returns>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextReader" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.Read">
      <summary>Liest das nächste Zeichen aus dem Text-Reader und verschiebt die Zeichenposition um ein Zeichen nach vorn.</summary>
      <returns>Das nächste Zeichen im Textreader, bzw. -1, wenn keine weiteren Zeichen verfügbar sind.Die Standardimplementierung gibt -1 zurück.</returns>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextReader" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.Read(System.Char[],System.Int32,System.Int32)">
      <summary>Liest eine angegebene Höchstanzahl von Zeichen aus dem aktuellen Reader und schreibt die Daten in einen Puffer, wobei am angegebenen Index begonnen wird.</summary>
      <returns>Die Anzahl der gelesenen Zeichen.Die Anzahl ist kleiner oder gleich <paramref name="count" />, abhängig davon, ob die Daten im Reader verfügbar sind.Diese Methode gibt 0 (null) zurück für den Fall, dass sie aufgerufen wird, wenn keine Zeichen mehr zu lesen sind.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Zeichenarray mit den Werten zwischen <paramref name="index" /> und (<paramref name="index" /> + <paramref name="count" /> - 1), die durch die aus der aktuellen Quelle gelesenen Zeichen ersetzt wurden. </param>
      <param name="index">Die Position in <paramref name="buffer" />, an der mit dem Schreiben begonnen wird. </param>
      <param name="count">Die maximale Anzahl der zu lesenden Zeichen.Wenn das Ende des Readers erreicht ist, bevor die angegebene Anzahl von Zeichen in den Puffer gelesen wurde, erfolgt die Methodenrückgabe.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextReader" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.ReadAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Liest asynchron eine angegebene Höchstanzahl von Zeichen aus dem aktuellen Textreader und schreibt die Daten in einen Puffer, wobei am angegebenen Index begonnen wird. </summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die Gesamtzahl der Bytes, die in den Puffer gelesen werden.Der Ergebniswert kann niedriger als die Anzahl der angeforderten Bytes sein, wenn die Anzahl an derzeit verfügbaren Bytes kleiner ist als die angeforderte Anzahl, oder sie kann 0 (null) sein, wenn das Textende erreicht ist.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Zeichenarray mit den Werten zwischen <paramref name="index" /> und (<paramref name="index" /> + <paramref name="count" /> - 1), die durch die aus der aktuellen Quelle gelesenen Zeichen ersetzt wurden.</param>
      <param name="index">Die Position in <paramref name="buffer" />, an der mit dem Schreiben begonnen wird.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Zeichen.Wenn das Ende des Texts erreicht ist, bevor die angegebene Anzahl von Zeichen in den Puffer gelesen wurde, erfolgt die aktuelle Methodenrückgabe.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Textreader wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.TextReader.ReadBlock(System.Char[],System.Int32,System.Int32)">
      <summary>Liest eine angegebene Höchstanzahl von Zeichen aus dem aktuellen Textreader und schreibt die Daten in einen Puffer, wobei am angegebenen Index begonnen wird.</summary>
      <returns>Die Anzahl der gelesenen Zeichen.Die Anzahl ist kleiner oder gleich <paramref name="count" />, je nachdem, ob alle Eingabezeichen gelesen wurden.</returns>
      <param name="buffer">Dieser Parameter enthält nach dem Beenden dieser Methode das angegebene Zeichenarray mit den Werten zwischen <paramref name="index" /> und (<paramref name="index" /> + <paramref name="count" /> -1), die durch die aus der aktuellen Quelle gelesenen Zeichen ersetzt wurden. </param>
      <param name="index">Die Position in <paramref name="buffer" />, an der mit dem Schreiben begonnen wird.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Zeichen. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null. </exception>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextReader" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.ReadBlockAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Liest asynchron eine angegebene Höchstanzahl von Zeichen aus dem aktuellen Textreader und schreibt die Daten in einen Puffer, wobei am angegebenen Index begonnen wird.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die Gesamtzahl der Bytes, die in den Puffer gelesen werden.Der Ergebniswert kann niedriger als die Anzahl der angeforderten Bytes sein, wenn die Anzahl an derzeit verfügbaren Bytes kleiner ist als die angeforderte Anzahl, oder sie kann 0 (null) sein, wenn das Textende erreicht ist.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Zeichenarray mit den Werten zwischen <paramref name="index" /> und (<paramref name="index" /> + <paramref name="count" /> - 1), die durch die aus der aktuellen Quelle gelesenen Zeichen ersetzt wurden.</param>
      <param name="index">Die Position in <paramref name="buffer" />, an der mit dem Schreiben begonnen wird.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Zeichen.Wenn das Ende des Texts erreicht ist, bevor die angegebene Anzahl von Zeichen in den Puffer gelesen wurde, erfolgt die aktuelle Methodenrückgabe.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ArgumentException">Die Summe aus <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Textreader wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.TextReader.ReadLine">
      <summary>Liest eine Zeile von Zeichen aus dem Textleser und gibt die Daten als Zeichenfolge zurück.</summary>
      <returns>Die nächste Zeile des Readers oder null, wenn alle Zeichen gelesen wurden.</returns>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden, um einen Puffer für die zurückgegebene Zeichenfolge zu reservieren. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextReader" /> ist geschlossen. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Anzahl der Zeichen in der nächsten Zeile ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.ReadLineAsync">
      <summary>Liest eine Zeile von asynchron Zeichen aus dem aktuellen Stream und gibt die Daten als Zeichenfolge zurück. </summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die nächste Zeile aus dem Textreader oder null, wenn alle Zeichen gelesen wurden.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Anzahl der Zeichen in der nächsten Zeile ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Textreader wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.TextReader.ReadToEnd">
      <summary>Liest alle Zeichen von der aktuellen Position bis zum Ende des Text-Readers und gibt diese als eine Zeichenfolge zurück.</summary>
      <returns>Eine Zeichenfolge mit allen Zeichen von der aktuellen Position bis zum Ende des Textreaders.</returns>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextReader" /> ist geschlossen. </exception>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Speicherplatz vorhanden, um einen Puffer für die zurückgegebene Zeichenfolge zu reservieren. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Anzahl der Zeichen in der nächsten Zeile ist größer als <see cref="F:System.Int32.MaxValue" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextReader.ReadToEndAsync">
      <summary>Liest alle Zeichen asynchron von der aktuellen Position bis zum Ende des Text-Readers und gibt diese als eine Zeichenfolge zurück.</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält eine Zeichenfolge mit den Zeichen von der aktuellen Position bis zum Ende des Textreaders.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Anzahl der Zeichen ist niedriger als <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Textreader wurde freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Reader wird gerade durch einen früheren Lesevorgang verwendet. </exception>
    </member>
    <member name="T:System.IO.TextWriter">
      <summary>Stellt einen Writer dar, der eine sequenzielle Serie von Zeichen schreiben kann.Diese Klasse ist abstrakt.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.TextWriter" />-Klasse.</summary>
    </member>
    <member name="M:System.IO.TextWriter.#ctor(System.IFormatProvider)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.TextWriter" />-Klasse mit dem angegebenen Formatanbieter.</summary>
      <param name="formatProvider">Ein <see cref="T:System.IFormatProvider" />-Objekt zum Steuern der Formatierung. </param>
    </member>
    <member name="F:System.IO.TextWriter.CoreNewLine">
      <summary>Speichert die für diesen TextWriter verwendeten Zeilenendemarken.</summary>
    </member>
    <member name="M:System.IO.TextWriter.Dispose">
      <summary>Gibt alle vom <see cref="T:System.IO.TextWriter" />-Objekt verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.IO.TextWriter.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.IO.TextWriter" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben. </param>
    </member>
    <member name="P:System.IO.TextWriter.Encoding">
      <summary>Gibt beim Überschreiben in einer abgeleiteten Klasse die Zeichencodierung zurück, in der die Ausgabe geschrieben ist.</summary>
      <returns>Die Zeichencodierung, in der die Ausgabe geschrieben wird.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Flush">
      <summary>Löscht sämtliche Puffer für den aktuellen Writer und veranlasst die Ausgabe aller gepufferten Daten an das zugrunde liegende Gerät.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.FlushAsync">
      <summary>Löscht sämtliche Puffer für den aktuellen Writer asynchron und veranlasst die Ausgabe aller gepufferten Daten an das zugrunde liegende Gerät. </summary>
      <returns>Eine Aufgabe, die die asynchrone Leerung darstellt. </returns>
      <exception cref="T:System.ObjectDisposedException">Der Textwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Writer wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="P:System.IO.TextWriter.FormatProvider">
      <summary>Ruft ein Objekt zum Steuern der Formatierung ab.</summary>
      <returns>Ein <see cref="T:System.IFormatProvider" />-Objekt für eine bestimmte Kultur oder die Formatierung der aktuellen Kultur, wenn keine andere Kultur angegeben wurde.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.TextWriter.NewLine">
      <summary>Ruft die Zeichenfolge für den Zeilenabschluss ab, die vom aktuellen TextWriter verwendet wird, oder legt diese fest.</summary>
      <returns>Die Zeichenfolge für den Zeilenabschluss für den aktuellen TextWriter.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.IO.TextWriter.Null">
      <summary>Stellt einen TextWriter ohne Sicherungsspeicher bereit, in den zwar geschrieben, aus dem jedoch nicht gelesen werden kann.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Boolean)">
      <summary>Schreibt die Textdarstellung eines Boolean-Werts in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Der zu schreibende Boolean-Wert. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Char)">
      <summary>Schreibt ein Zeichen in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Das in den Textstream zu schreibende Zeichen. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Char[])">
      <summary>Schreibt ein Zeichenarray in die Textzeichenfolge oder den Stream.</summary>
      <param name="buffer">Das in den Textstream zu schreibende Zeichenarray. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt ein Teilarray von Zeichen in die Textzeichenfolge oder den Stream.</summary>
      <param name="buffer">Das Zeichenarray, aus dem Daten geschrieben werden sollen. </param>
      <param name="index">Die Zeichenposition im Puffer, an der mit dem Abrufen von Daten begonnen werden soll. </param>
      <param name="count">Die Anzahl der zu schreibenden Zeichen. </param>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="buffer" />-Parameter ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Decimal)">
      <summary>Schreibt die Textdarstellung eines Dezimalwerts in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Der zu schreibende Dezimalwert. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Double)">
      <summary>Schreibt die Textdarstellung eines 8-Byte-Gleitkommawerts in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Der zu schreibende 8-Byte-Gleitkommawert. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Int32)">
      <summary>Schreibt die Textdarstellung einer 4-Byte-Ganzzahl mit Vorzeichen in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Die zu schreibende 4-Byte-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Int64)">
      <summary>Schreibt die Textdarstellung einer 8-Byte-Ganzzahl mit Vorzeichen in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Die zu schreibende 8-Byte-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Object)">
      <summary>Schreibt die Textdarstellung eines Objekts in die Textzeichenfolge oder den Stream, indem für das Objekt die ToString-Methode aufgerufen wird.</summary>
      <param name="value">Das zu schreibende Objekt. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.Single)">
      <summary>Schreibt die Textdarstellung eines 4-Byte-Gleitkommawerts in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Der zu schreibende 4-Byte-Gleitkommawert. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.String)">
      <summary>Schreibt eine Zeichenfolge in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Die zu schreibende Zeichenfolge. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.String,System.Object)">
      <summary>Schreibt eine formatierte Zeichenfolge in die Textzeichenfolge oder den Stream unter Verwendung der gleichen Semantik wie die <see cref="M:System.String.Format(System.String,System.Object)" />-Methode.</summary>
      <param name="format">Eine kombinierte Formatierungszeichenfolge (siehe Hinweise). </param>
      <param name="arg0">Das zu formatierende und zu schreibende Objekt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> ist null. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> ist keine gültige kombinierte Formatzeichenfolge.- oder -  Der Index eines Formatelements ist kleiner als 0 (null) oder größer/gleich der Anzahl der zu formatierenden Objekte (1 für diese Methodenüberladung). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.String,System.Object,System.Object)">
      <summary>Schreibt eine formatierte Zeichenfolge in die Textzeichenfolge oder den Stream unter Verwendung der gleichen Semantik wie die <see cref="M:System.String.Format(System.String,System.Object,System.Object)" />-Methode.</summary>
      <param name="format">Eine kombinierte Formatierungszeichenfolge (siehe Hinweise). </param>
      <param name="arg0">Das erste zu formatierende und zu schreibende Objekt. </param>
      <param name="arg1">Das zweite zu formatierende und zu schreibende Objekt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> ist null. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> ist keine gültige kombinierte Formatzeichenfolge.- oder -  Der Index eines Formatelements ist kleiner als 0 (null) oder größer/gleich der Anzahl der zu formatierenden Objekte (2 für diese Methodenüberladung). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.String,System.Object,System.Object,System.Object)">
      <summary>Schreibt eine formatierte Zeichenfolge in die Textzeichenfolge oder den Stream unter Verwendung der gleichen Semantik wie die <see cref="M:System.String.Format(System.String,System.Object,System.Object,System.Object)" />-Methode.</summary>
      <param name="format">Eine kombinierte Formatierungszeichenfolge (siehe Hinweise). </param>
      <param name="arg0">Das erste zu formatierende und zu schreibende Objekt. </param>
      <param name="arg1">Das zweite zu formatierende und zu schreibende Objekt. </param>
      <param name="arg2">Das dritte zu formatierende und zu schreibende Objekt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> ist null. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> ist keine gültige kombinierte Formatzeichenfolge.- oder -  Der Index eines Formatelements ist kleiner als 0 (null) oder größer/gleich der Anzahl der zu formatierenden Objekte (3 für diese Methodenüberladung). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.String,System.Object[])">
      <summary>Schreibt eine formatierte Zeichenfolge in die Textzeichenfolge oder den Stream unter Verwendung der gleichen Semantik wie die <see cref="M:System.String.Format(System.String,System.Object[])" />-Methode.</summary>
      <param name="format">Eine kombinierte Formatierungszeichenfolge (siehe Hinweise). </param>
      <param name="arg">Ein Objektarray mit 0 (null) oder mehr zu formatierenden und zu schreibenden Objekten. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> oder <paramref name="arg" /> ist null. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> ist keine gültige kombinierte Formatzeichenfolge.- oder -  Der Index eines Formatelements ist kleiner als 0 (null) oder größer oder gleich der Länge des <paramref name="arg" />-Arrays. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.UInt32)">
      <summary>Schreibt die Textdarstellung einer 4-Byte-Ganzzahl ohne Vorzeichen in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Die zu schreibende 4-Byte-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.Write(System.UInt64)">
      <summary>Schreibt die Textdarstellung einer 8-Byte-Ganzzahl ohne Vorzeichen in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Die zu schreibende 8-Byte-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteAsync(System.Char)">
      <summary>Schreibt ein Zeichen asynchron in die Textzeichenfolge oder den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="value">Das in den Textstream zu schreibende Zeichen.</param>
      <exception cref="T:System.ObjectDisposedException">Der Textwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Textwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteAsync(System.Char[])">
      <summary>Schreibt ein Zeichenarray asynchron in die Textzeichenfolge oder den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Das in den Textstream zu schreibende Zeichenarray.Wenn <paramref name="buffer" /> den Wert null ist, wird nichts geschrieben.</param>
      <exception cref="T:System.ObjectDisposedException">Der Textwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Textwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt ein Teilarray von Zeichen asynchron in die Textzeichenfolge oder den Stream. </summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Das Zeichenarray, aus dem Daten geschrieben werden sollen. </param>
      <param name="index">Die Zeichenposition im Puffer, an der mit dem Abrufen von Daten begonnen werden soll. </param>
      <param name="count">Die Anzahl der zu schreibenden Zeichen. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Textwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Textwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteAsync(System.String)">
      <summary>Schreibt eine Zeichenfolge asynchron in die Textzeichenfolge oder den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt. </returns>
      <param name="value">Die zu schreibende Zeichenfolge.Wenn <paramref name="value" /> den Wert null hat, wird nichts in den Stream geschrieben.</param>
      <exception cref="T:System.ObjectDisposedException">Der Textwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Textwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine">
      <summary>Schreibt ein Zeichen für den Zeilenabschluss in die Textzeichenfolge oder den Stream.</summary>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Boolean)">
      <summary>Schreibt die Textdarstellung eines Boolean-Werts, gefolgt von einem Zeichen für den Zeilenabschluss, in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Der zu schreibende Boolean-Wert. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Char)">
      <summary>Schreibt ein Zeichen, gefolgt von einem Zeichen für den Zeilenabschluss, in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Das in den Textstream zu schreibende Zeichen. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Char[])">
      <summary>Schreibt ein Array von Zeichen, gefolgt von einem Zeichen für den Zeilenabschluss, in die Textzeichenfolge oder den Stream.</summary>
      <param name="buffer">Das Zeichenarray, aus dem die Daten gelesen werden. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt ein Teilarray von Zeichen, gefolgt von einem Zeichen für den Zeilenabschluss, in die Textzeichenfolge oder den Stream.</summary>
      <param name="buffer">Das Zeichenarray, aus dem die Daten gelesen werden. </param>
      <param name="index">Die Zeichenposition im <paramref name="buffer" />, an der mit dem Lesen von Daten begonnen werden soll. </param>
      <param name="count">Die maximale Anzahl der zu schreibenden Zeichen. </param>
      <exception cref="T:System.ArgumentException">Die Länge des Puffers minus <paramref name="index" /> ist kleiner als <paramref name="count" />. </exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="buffer" />-Parameter ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Decimal)">
      <summary>Schreibt die Textdarstellung eines Dezimalwerts, gefolgt von einem Zeichen für den Zeilenabschluss, in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Der zu schreibende Dezimalwert. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Double)">
      <summary>Schreibt die Textdarstellung eines 8-Byte-Gleitkommawerts, gefolgt von einem Zeichen für den Zeilenabschluss, in die Zeichenfolge oder den Stream.</summary>
      <param name="value">Der zu schreibende 8-Byte-Gleitkommawert. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Int32)">
      <summary>Schreibt die Textdarstellung einer 4-Byte-Ganzzahl mit Vorzeichen, gefolgt von einem Zeichen für den Zeilenabschluss, in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Die zu schreibende 4-Byte-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Int64)">
      <summary>Schreibt die Textdarstellung einer 8-Byte-Ganzzahl mit Vorzeichen, gefolgt von einem Zeichen für den Zeilenabschluss, in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Die zu schreibende 8-Byte-Ganzzahl mit Vorzeichen. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Object)">
      <summary>Schreibt durch Aufrufen der ToString-Methode für ein Objekt die Textdarstellung dieses Objekts, gefolgt von einem Zeichen für den Zeilenabschluss, in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Das zu schreibende Objekt.Wenn <paramref name="value" />null ist, wird nur das Zeichen für den Zeilenabschluss geschrieben.</param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.Single)">
      <summary>Schreibt die Textdarstellung eines 4-Byte-Gleitkommawerts, gefolgt von einem Zeichen für den Zeilenabschluss, in die Zeichenfolge oder den Stream.</summary>
      <param name="value">Der zu schreibende 4-Byte-Gleitkommawert. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.String)">
      <summary>Schreibt eine Zeichenfolge, gefolgt von einem Zeichen für den Zeilenabschluss, in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Die zu schreibende Zeichenfolge.Wenn <paramref name="value" />null ist, wird nur das Zeichen für den Zeilenabschluss geschrieben.</param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.String,System.Object)">
      <summary>Schreibt eine formatierte Zeichenfolge und eine neue Zeile in die Textzeichenfolge oder den Stream unter Verwendung der gleichen Semantik wie die <see cref="M:System.String.Format(System.String,System.Object)" />-Methode.</summary>
      <param name="format">Eine kombinierte Formatierungszeichenfolge (siehe Hinweise).</param>
      <param name="arg0">Das zu formatierende und zu schreibende Objekt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> ist null. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> ist keine gültige kombinierte Formatzeichenfolge.- oder -  Der Index eines Formatelements ist kleiner als 0 (null) oder größer/gleich der Anzahl der zu formatierenden Objekte (1 für diese Methodenüberladung). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.String,System.Object,System.Object)">
      <summary>Schreibt eine formatierte Zeichenfolge und eine neue Zeile in die Textzeichenfolge oder den Stream unter Verwendung der gleichen Semantik wie die <see cref="M:System.String.Format(System.String,System.Object,System.Object)" />-Methode.</summary>
      <param name="format">Eine kombinierte Formatierungszeichenfolge (siehe Hinweise).</param>
      <param name="arg0">Das erste zu formatierende und zu schreibende Objekt. </param>
      <param name="arg1">Das zweite zu formatierende und zu schreibende Objekt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> ist null. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> ist keine gültige kombinierte Formatzeichenfolge.- oder -  Der Index eines Formatelements ist kleiner als 0 (null) oder größer/gleich der Anzahl der zu formatierenden Objekte (2 für diese Methodenüberladung). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.String,System.Object,System.Object,System.Object)">
      <summary>Schreibt eine formatierte Zeichenfolge und eine neue Zeile unter Verwendung der gleichen Semantik wie bei <see cref="M:System.String.Format(System.String,System.Object)" />.</summary>
      <param name="format">Eine kombinierte Formatierungszeichenfolge (siehe Hinweise).</param>
      <param name="arg0">Das erste zu formatierende und zu schreibende Objekt. </param>
      <param name="arg1">Das zweite zu formatierende und zu schreibende Objekt. </param>
      <param name="arg2">Das dritte zu formatierende und zu schreibende Objekt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> ist null. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> ist keine gültige kombinierte Formatzeichenfolge.- oder -  Der Index eines Formatelements ist kleiner als 0 (null) oder größer/gleich der Anzahl der zu formatierenden Objekte (3 für diese Methodenüberladung). </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.String,System.Object[])">
      <summary>Schreibt eine formatierte Zeichenfolge und eine neue Zeile unter Verwendung der gleichen Semantik wie bei <see cref="M:System.String.Format(System.String,System.Object)" />.</summary>
      <param name="format">Eine kombinierte Formatierungszeichenfolge (siehe Hinweise).</param>
      <param name="arg">Ein Objektarray mit 0 (null) oder mehr zu formatierenden und zu schreibenden Objekten. </param>
      <exception cref="T:System.ArgumentNullException">Eine Zeichenfolge oder ein Objekt wird als null übergeben. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" /> ist keine gültige kombinierte Formatzeichenfolge.- oder -  Der Index eines Formatelements ist kleiner als 0 (null) oder größer oder gleich der Länge des <paramref name="arg" />-Arrays. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.UInt32)">
      <summary>Schreibt die Textdarstellung einer 4-Byte-Ganzzahl ohne Vorzeichen, gefolgt von einem Zeichen für den Zeilenabschluss, in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Die zu schreibende 4-Byte-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLine(System.UInt64)">
      <summary>Schreibt die Textdarstellung einer 8-Byte-Ganzzahl ohne Vorzeichen, gefolgt von einem Zeichen für den Zeilenabschluss, in die Textzeichenfolge oder den Stream.</summary>
      <param name="value">Die zu schreibende 8-Byte-Ganzzahl ohne Vorzeichen. </param>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.IO.TextWriter" /> ist geschlossen. </exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.IO.TextWriter.WriteLineAsync">
      <summary>Schreibt ein Zeichen für den Zeilenabschluss asynchron in die Textzeichenfolge oder den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt. </returns>
      <exception cref="T:System.ObjectDisposedException">Der Textwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Textwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteLineAsync(System.Char)">
      <summary>Schreibt ein Zeichen, gefolgt von einem Zeichen für den Zeilenabschluss, asynchron in die Textzeichenfolge oder den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="value">Das in den Textstream zu schreibende Zeichen.</param>
      <exception cref="T:System.ObjectDisposedException">Der Textwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Textwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteLineAsync(System.Char[])">
      <summary>Schreibt ein Array von Zeichen, gefolgt von einem Zeichen für den Zeilenabschluss, asynchron in die Textzeichenfolge oder den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Das in den Textstream zu schreibende Zeichenarray.Wenn das Zeichenarray null ist, wird nur das Zeichen für den Zeilenabschluss geschrieben.</param>
      <exception cref="T:System.ObjectDisposedException">Der Textwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Textwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteLineAsync(System.Char[],System.Int32,System.Int32)">
      <summary>Schreibt ein Teilarray von Zeichen, gefolgt von einem Zeichen für den Zeilenabschluss, asynchron in die Textzeichenfolge oder den Stream.</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Das Zeichenarray, aus dem Daten geschrieben werden sollen. </param>
      <param name="index">Die Zeichenposition im Puffer, an der mit dem Abrufen von Daten begonnen werden soll. </param>
      <param name="count">Die Anzahl der zu schreibenden Zeichen. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="index" /> und <paramref name="count" /> ist größer als die Pufferlänge.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> oder <paramref name="count" /> ist negativ.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Textwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Textwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
    <member name="M:System.IO.TextWriter.WriteLineAsync(System.String)">
      <summary>Schreibt eine Zeichenfolge, gefolgt von einem Zeichen für den Zeilenabschluss, asynchron in die Textzeichenfolge oder den Stream. </summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="value">Die zu schreibende Zeichenfolge.Wenn der Wert null ist, wird nur ein Zeichen für den Zeilenabschluss geschrieben.</param>
      <exception cref="T:System.ObjectDisposedException">Der Textwriter wird freigegeben.</exception>
      <exception cref="T:System.InvalidOperationException">Der Textwriter wird derzeit von einem vorherigen Schreibvorgang verwendet. </exception>
    </member>
  </members>
</doc>