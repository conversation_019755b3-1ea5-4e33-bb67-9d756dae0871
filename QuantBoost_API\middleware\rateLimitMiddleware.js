const { sendError } = require('../utils/responseHelpers');

const rateLimitMap = {};
const RATE_LIMIT_WINDOW_MS = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX = 10; // Max 10 requests per license/device pair per window

function rateLimiter(req, res, next) {
    // Only apply to the validation endpoint
    if (req.method !== 'POST' || !req.path.endsWith('/licenses/validate')) return next();
    
    const { licenseKey, machineId } = req.body; 

    if (!licenseKey || !machineId) return next(); 

    const key = `${licenseKey}:${machineId}`;
    const now = Date.now();
    rateLimitMap[key] = (rateLimitMap[key] || []).filter(ts => now - ts < RATE_LIMIT_WINDOW_MS);

    if (rateLimitMap[key].length >= RATE_LIMIT_MAX) {
        console.warn(`Rate limit exceeded for key: ${key}`);
        return sendError(res, 'Rate limit exceeded. Please try again later.', 429);
    }
    rateLimitMap[key].push(now);
    next();
}

module.exports = {
    rateLimiter
};
