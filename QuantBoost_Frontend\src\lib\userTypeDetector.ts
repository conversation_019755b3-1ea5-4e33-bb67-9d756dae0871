import type { SupabaseClient } from '@supabase/supabase-js';
import type { License, Subscription } from '@/lib/api';

/**
 * User types for dashboard personalization and access control
 */
export enum UserType {
  TEAM_ADMIN = 'team_admin',
  INDIVIDUAL_SUBSCRIBER = 'individual_subscriber',
  TEAM_LICENSEE = 'team_licensee',
  NO_LICENSE = 'no_license'
}

/**
 * User type detection result
 */
export interface UserTypeResult {
  type: UserType;
  subscription?: Subscription;
  license?: License;
}

/**
 * Detects the user type based on their subscription and license status
 * 
 * @param userId - The current user's ID
 * @param supabase - Supabase client instance
 * @returns Promise<UserTypeResult> - The user type and associated data
 */
export async function detectUserType(
  userId: string,
  supabase: SupabaseClient
): Promise<UserTypeResult> {
  try {
    // First, check if user owns any subscriptions
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId);

    if (subscriptionsError) {
      console.error('Error fetching subscriptions:', subscriptionsError);
      throw new Error('Failed to detect user type: subscription check failed');
    }

    if (subscriptions && subscriptions.length > 0) {
      // User owns subscription(s)
      
      // Check if user owns a team subscription (quantity > 1)
      const teamSubscription = subscriptions.find((s: Subscription) => s.quantity > 1);
      if (teamSubscription) {
        return {
          type: UserType.TEAM_ADMIN,
          subscription: teamSubscription
        };
      }

      // User has individual subscription(s)
      return {
        type: UserType.INDIVIDUAL_SUBSCRIBER,
        subscription: subscriptions[0] // Use first subscription if multiple individual ones
      };
    }

    // User doesn't own subscriptions, check if they have assigned license
    const { data: license, error: licenseError } = await supabase
      .from('licenses')
      .select(`
        *,
        subscriptions (*)
      `)
      .eq('user_id', userId)
      .single();

    if (licenseError && licenseError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching license:', licenseError);
      throw new Error('Failed to detect user type: license check failed');
    }

    if (license) {
      return {
        type: UserType.TEAM_LICENSEE,
        license
      };
    }

    // User has no subscription or license
    return {
      type: UserType.NO_LICENSE
    };

  } catch (error) {
    console.error('Error in detectUserType:', error);
    // Return safe default on error
    return {
      type: UserType.NO_LICENSE
    };
  }
}

/**
 * Gets display name for user type
 * 
 * @param userType - The user type enum value
 * @returns Human-readable display name
 */
export function getUserTypeDisplayName(userType: UserType): string {
  switch (userType) {
    case UserType.TEAM_ADMIN:
      return 'Team Administrator';
    case UserType.INDIVIDUAL_SUBSCRIBER:
      return 'Individual Subscriber';
    case UserType.TEAM_LICENSEE:
      return 'Team Member';
    case UserType.NO_LICENSE:
      return 'No License';
    default:
      return 'Unknown';
  }
}

/**
 * Checks if user has access to team management features
 * 
 * @param userType - The user type enum value
 * @returns True if user can manage team
 */
export function canManageTeam(userType: UserType): boolean {
  return userType === UserType.TEAM_ADMIN;
}

/**
 * Checks if user has access to billing features
 * 
 * @param userType - The user type enum value
 * @returns True if user can access billing
 */
export function canAccessBilling(userType: UserType): boolean {
  return userType === UserType.TEAM_ADMIN || userType === UserType.INDIVIDUAL_SUBSCRIBER;
}

/**
 * Checks if user has access to subscription management
 * 
 * @param userType - The user type enum value
 * @returns True if user can manage subscriptions
 */
export function canManageSubscription(userType: UserType): boolean {
  return userType === UserType.TEAM_ADMIN || userType === UserType.INDIVIDUAL_SUBSCRIBER;
}

/**
 * Gets the license status color class for UI display
 * 
 * @param status - License status string
 * @returns Tailwind CSS color class
 */
export function getLicenseStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'inactive':
      return 'bg-gray-100 text-gray-800';
    case 'expired':
      return 'bg-red-100 text-red-800';
    case 'trial_active':
      return 'bg-blue-100 text-blue-800';
    case 'graceperiod':
      return 'bg-yellow-100 text-yellow-800';
    case 'revoked':
      return 'bg-red-100 text-red-800';
    case 'assigned':
      return 'bg-green-100 text-green-800';
    case 'unassigned':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * Formats a date string for display
 * 
 * @param dateString - ISO date string
 * @returns Formatted date string
 */
export function formatDate(dateString: string | undefined | null): string {
  if (!dateString) return 'N/A';
  
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    return 'Invalid Date';
  }
}