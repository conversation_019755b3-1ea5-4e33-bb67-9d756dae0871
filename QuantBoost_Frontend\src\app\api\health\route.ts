import { NextRequest, NextResponse } from 'next/server';

// Simple health endpoint. Supports optional GitHubActions bypass header for access restrictions.
// If a secret header value is configured (via NEXT_PUBLIC_GHA_HEALTH_SECRET or process env), include:
//   X-GHA-Bypass: <secret>
// to get a 204 response even if future logic gates other checks.
// Otherwise returns 200 with basic metadata.

const startedAt = Date.now();

export function GET(req: NextRequest) {
  const uptimeMs = Date.now() - startedAt;
  const hdr = req.headers.get('x-gha-bypass') || '';
  const secret = process.env.GHA_HEALTH_SECRET || process.env.NEXT_PUBLIC_GHA_HEALTH_SECRET || '';
  if (secret && hdr === secret) {
    return new NextResponse(null, { status: 204 });
  }
  return NextResponse.json(
    {
      status: 'ok',
      uptimeMs,
      timestamp: new Date().toISOString(),
      commit: process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA || process.env.GIT_COMMIT || process.env.COMMIT_SHA || 'unknown'
    },
    { status: 200 }
  );
}
