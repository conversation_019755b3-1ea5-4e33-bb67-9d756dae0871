"use client";

import { createBrowserClient } from '@supabase/ssr';
import { useMemo } from 'react';
import type { SupabaseClient } from '@supabase/supabase-js';

// Global Supabase client instance - shared with API client
let globalSupabaseClient: SupabaseClient | null = null;

function getGlobalSupabaseClient(): SupabaseClient {
  if (!globalSupabaseClient) {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      // During build time or SSR, create a placeholder client
      globalSupabaseClient = createBrowserClient('https://placeholder.supabase.co', 'placeholder-key');
      return globalSupabaseClient;
    }

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase environment variables');
      globalSupabaseClient = createBrowserClient('https://placeholder.supabase.co', 'placeholder-key');
      return globalSupabaseClient;
    }

    globalSupabaseClient = createBrowserClient(supabaseUrl, supabaseAnonKey);
  }
  
  return globalSupabaseClient;
}

/**
 * Custom hook to safely create Supabase client for client components
 * This returns the same global instance used by the API client
 */
export function useSupabaseClient() {
  const supabase = useMemo(() => {
    return getGlobalSupabaseClient();
  }, []);

  return supabase;
}
