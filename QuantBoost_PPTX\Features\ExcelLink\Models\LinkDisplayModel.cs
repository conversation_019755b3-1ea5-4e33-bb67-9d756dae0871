using System;
using System.ComponentModel;
using System.IO;
using QuantBoost_Powerpoint_Addin.ExcelLink;

namespace QuantBoost_Powerpoint_Addin.Features.ExcelLink.Models
{
    /// <summary>
    /// WPF-friendly display model for Excel links that implements INotifyPropertyChanged.
    /// This class wraps the ChartLink data object and provides formatted properties for UI binding.
    /// </summary>
    public class LinkDisplayModel : INotifyPropertyChanged
    {
        #region Fields
        private bool _isActive;
        private Action _onActiveChanged;
        #endregion

        #region Constructor
        /// <summary>
        /// Initializes a new instance of the LinkDisplayModel class.
        /// </summary>
        /// <param name="chartLink">The underlying ChartLink data object.</param>
        /// <param name="onActiveChanged">Optional callback when IsActive changes.</param>
        public LinkDisplayModel(ChartLink chartLink, Action onActiveChanged = null)
        {
            ChartLink = chartLink ?? throw new ArgumentNullException(nameof(chartLink));
            _isActive = chartLink.IsActive;
            _onActiveChanged = onActiveChanged;
        }
        #endregion

        #region Properties
        /// <summary>
        /// Gets the underlying ChartLink data object.
        /// </summary>
        public ChartLink ChartLink { get; }

        /// <summary>
        /// Gets or sets whether the link is active, with proper change notification.
        /// </summary>
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    ChartLink.IsActive = value;
                    ChartLink.ModifiedBy = Environment.UserName;
                    OnPropertyChanged(nameof(IsActive));
                    // Notify ViewModel that checkbox state has changed
                    _onActiveChanged?.Invoke();
                }
            }
        }

        /// <summary>
        /// Gets the friendly display name for the link type.
        /// </summary>
        public string LinkType
        {
            get
            {
                return ChartLink.LinkType switch
                {
                    "Chart" => "Embedded Chart",
                    "Range" => "Data Range",
                    "Shape" => "Excel Shape",
                    "OleObject" => "OLE Object",
                    _ => "Unknown Type"
                };
            }
        }

        /// <summary>
        /// Gets the slide index where the linked content is located.
        /// </summary>
        public int SlideIndex => ChartLink.SlideIndex;

        /// <summary>
        /// Gets the display string for the source identifier.
        /// Shows the file name for Chart links, or the full worksheet!range format for Range links.
        /// </summary>
        public string SourceIdentifierDisplay
        {
            get
            {
                if (ChartLink.LinkType == "Chart")
                {
                    if (string.IsNullOrEmpty(ChartLink.SourceFilePath))
                        return "";
                    
                    try
                    {
                        return Path.GetFileName(ChartLink.SourceFilePath);
                    }
                    catch (ArgumentException)
                    {
                        // Handle invalid path characters - return the original path or a safe version
                        return ChartLink.SourceFilePath ?? "";
                    }
                    catch (Exception)
                    {
                        // Handle any other path-related exceptions
                        return ChartLink.SourceFilePath ?? "";
                    }
                }
                else
                {
                    // For ranges, show full format: "Worksheet1!$J$58:$T$70"
                    if (!string.IsNullOrEmpty(ChartLink.WorksheetName) && !string.IsNullOrEmpty(ChartLink.SourceRange))
                    {
                        return $"{ChartLink.WorksheetName}!{ChartLink.SourceRange}";
                    }
                    return ChartLink.SourceRange ?? ChartLink.ChartNameOrId ?? "";
                }
            }
        }

        /// <summary>
        /// Gets the source file name for display.
        /// </summary>
        public string SourceFileName
        {
            get
            {
                if (string.IsNullOrEmpty(ChartLink.SourceFilePath))
                    return "";
                
                try
                {
                    return Path.GetFileName(ChartLink.SourceFilePath);
                }
                catch (ArgumentException)
                {
                    // Handle invalid path characters - return the original path or a safe version
                    return ChartLink.SourceFilePath ?? "";
                }
                catch (Exception)
                {
                    // Handle any other path-related exceptions
                    return ChartLink.SourceFilePath ?? "";
                }
            }
        }

        /// <summary>
        /// Gets the formatted last refresh timestamp.
        /// </summary>
        public string LastRefreshedDisplay
        {
            get
            {
                if (ChartLink.LastRefreshedUtc == null || ChartLink.LastRefreshedUtc == DateTime.MinValue)
                    return "Never";

                var refreshTime = ChartLink.LastRefreshedUtc.Value;
                var now = DateTime.UtcNow;
                var timeSpan = now - refreshTime;

                // Show friendly relative time for recent refreshes
                if (timeSpan.TotalMinutes < 1)
                    return "Just now";
                else if (timeSpan.TotalMinutes < 60)
                    return $"{(int)timeSpan.TotalMinutes}m ago";
                else if (timeSpan.TotalHours < 24)
                    return $"{(int)timeSpan.TotalHours}h ago";
                else if (timeSpan.TotalDays < 7)
                    return $"{(int)timeSpan.TotalDays}d ago";
                else
                    return refreshTime.ToString("MMM d, yyyy");
            }
        }

        /// <summary>
        /// Gets the user who last modified the link.
        /// </summary>
        public string ModifiedBy => ChartLink.ModifiedBy ?? Environment.UserName;

        /// <summary>
        /// Gets the link ID for identification purposes.
        /// </summary>
        public string LinkId => ChartLink.LinkId ?? "";

        /// <summary>
        /// Gets the PowerPoint shape ID for navigation.
        /// </summary>
        public string PowerPointShapeId => ChartLink.PowerPointShapeId ?? "";

        /// <summary>
        /// Gets the full source file path.
        /// </summary>
        public string SourceFilePath => ChartLink.SourceFilePath ?? "";

        /// <summary>
        /// Gets the worksheet name.
        /// </summary>
        public string WorksheetName => ChartLink.WorksheetName ?? "";

        /// <summary>
        /// Gets the chart name or ID.
        /// </summary>
        public string ChartNameOrId => ChartLink.ChartNameOrId ?? "";

        /// <summary>
        /// Gets the source range.
        /// </summary>
        public string SourceRange => ChartLink.SourceRange ?? "";

        /// <summary>
        /// Gets the error state if any.
        /// </summary>
        public string ErrorState => ChartLink.ErrorState ?? "";

        /// <summary>
        /// Gets whether the link has an error.
        /// </summary>
        public bool HasError => !string.IsNullOrEmpty(ChartLink.ErrorState);

        /// <summary>
        /// Gets the status display text.
        /// </summary>
        public string StatusDisplay
        {
            get
            {
                if (HasError)
                    return "Error";
                else if (IsActive)
                    return "Active";
                else
                    return "Inactive";
            }
        }
        #endregion

        #region INotifyPropertyChanged Implementation
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion

        #region Methods
        /// <summary>
        /// Refreshes all computed properties to reflect changes in the underlying ChartLink.
        /// </summary>
        public void RefreshDisplayProperties()
        {
            OnPropertyChanged(nameof(LinkType));
            OnPropertyChanged(nameof(SourceIdentifierDisplay));
            OnPropertyChanged(nameof(SourceFileName));
            OnPropertyChanged(nameof(LastRefreshedDisplay));
            OnPropertyChanged(nameof(ModifiedBy));
            OnPropertyChanged(nameof(ErrorState));
            OnPropertyChanged(nameof(HasError));
            OnPropertyChanged(nameof(StatusDisplay));
        }

        /// <summary>
        /// Returns a string representation of the link for debugging.
        /// </summary>
        public override string ToString()
        {
            return $"{LinkType} - {SourceIdentifierDisplay} (Slide {SlideIndex})";
        }
        #endregion
    }
}