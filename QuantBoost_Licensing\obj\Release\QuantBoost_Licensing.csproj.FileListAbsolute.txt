C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Release\QuantBoost_Licensing.dll
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Release\QuantBoost_Licensing.pdb
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Release\Newtonsoft.Json.Bson.dll
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Release\Newtonsoft.Json.dll
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Release\Newtonsoft.Json.xml
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Release\Newtonsoft.Json.Bson.pdb
C:\VS projects\QuantBoost\QuantBoost_Licensing\bin\Release\Newtonsoft.Json.Bson.xml
C:\VS projects\QuantBoost\QuantBoost_Licensing\obj\Release\QuantBoost_Licensing.csproj.AssemblyReference.cache
C:\VS projects\QuantBoost\QuantBoost_Licensing\obj\Release\QuantBoost_Licensing.csproj.CoreCompileInputs.cache
C:\VS projects\QuantBoost\QuantBoost_Licensing\obj\Release\QuantBoo.8B3DD63D.Up2Date
C:\VS projects\QuantBoost\QuantBoost_Licensing\obj\Release\QuantBoost_Licensing.dll
C:\VS projects\QuantBoost\QuantBoost_Licensing\obj\Release\QuantBoost_Licensing.pdb
