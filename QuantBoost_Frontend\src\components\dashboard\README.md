# User Type Detection & Dashboard Implementation

This document explains the user type detection system implemented for QuantBoost dashboard personalization.

## 📊 Overview

The system detects three user types and provides tailored dashboard experiences:

1. **Team Admin** - Owns team subscription (quantity > 1), full access
2. **Individual Subscriber** - Owns individual subscription, full access  
3. **Team Licensee** - License assigned by team admin, limited access
4. **No License** - No active subscription or license

## 🎯 User Experience Changes

### Team Licensees See:
- ✅ License status and activation instructions
- ✅ Download links for add-ins  
- ✅ Quick start guide
- ❌ No billing information
- ❌ No subscription management
- ❌ No team management features

### Team Admins & Individual Subscribers See:
- ✅ Full dashboard with all features
- ✅ Billing and payment management
- ✅ Subscription controls
- ✅ Team management (admins only)

## 🔧 Implementation Details

### Frontend Components

#### 1. User Type Detector (`src/lib/userTypeDetector.ts`)
```typescript
import { UserType, detectUserType } from '@/lib/userTypeDetector';

const userTypeResult = await detectUserType(userId, supabase);
// Returns: { type: UserType, subscription?, license? }
```

#### 2. Team Licensee Dashboard (`src/components/dashboard/TeamLicenseeDashboard.tsx`)
Specialized dashboard component for licensees with:
- License status display
- Activation instructions  
- Download links
- Help resources

#### 3. Main Dashboard (`src/app/dashboard/page.tsx`)
Updated with conditional rendering:
- Early user type detection
- Conditional data fetching (skips billing for licensees)
- Appropriate dashboard component rendering

### API Protection

#### 1. Express API Middleware (`middleware/userTypeMiddleware.js`)
```javascript
const { attachUserType, requireBillingAccess } = require('./middleware/userTypeMiddleware');

router.use(attachUserType);
router.use('/billing', requireBillingAccess);
```

#### 2. Next.js API Middleware (`src/lib/userTypeMiddleware.ts`)
```typescript
import { requireBillingAccess } from '@/lib/userTypeMiddleware';

export async function GET(req: NextRequest) {
  const accessDenied = await requireBillingAccess(req);
  if (accessDenied) return accessDenied;
  // ... continue with route logic
}
```

## 🔒 Security Features

### Access Control Matrix
| Feature | Team Admin | Individual | Team Licensee | No License |
|---------|------------|------------|---------------|------------|
| Dashboard | ✅ Full | ✅ Full | ✅ Limited | ❌ Message |
| Billing | ✅ | ✅ | ❌ | ❌ |
| Subscriptions | ✅ | ✅ | ❌ | ❌ |
| Team Management | ✅ | ❌ | ❌ | ❌ |
| License Info | ✅ | ✅ | ✅ Own Only | ❌ |

### Protected API Routes
All billing routes now check user access:
- `/api/billing/receipts` - Billing access required
- `/api/billing/cancel-subscription` - Subscription access required
- `/api/billing/update-payment-method` - Billing access required
- Team routes - Team management access required

## 🚀 Testing Different User Types

### Creating Test Users

1. **Team Admin**: Create user with subscription where `quantity > 1`
2. **Individual Subscriber**: Create user with subscription where `quantity = 1`
3. **Team Licensee**: Create license with `user_id` set but no owned subscription
4. **No License**: User with no subscriptions or licenses

### Test Scenarios

1. **Navigation**: Ensure licensees can't access billing URLs directly
2. **API Calls**: Verify API protection blocks unauthorized requests
3. **UI Elements**: Check that billing buttons/links are hidden for licensees
4. **Data Loading**: Confirm expensive billing queries are skipped for licensees

## 📈 Performance Benefits

- **Reduced API Calls**: Licensees skip billing/subscription data fetching
- **Faster Load Times**: Simplified dashboard renders immediately
- **Better UX**: No confusing billing information for non-owners
- **Clear Actions**: Licensees know exactly what they can do

## 🛠️ Future Enhancements

1. **Navigation Menu**: Update sidebar based on user type
2. **Role-Based Features**: Add more granular permissions
3. **Enterprise Users**: Support for enterprise customer roles
4. **Audit Trail**: Log access attempts and user type changes

## 🔧 Troubleshooting

### Common Issues

1. **User Type Not Detected**: Check Supabase RLS policies
2. **Access Denied Errors**: Verify middleware is applied correctly  
3. **UI Not Updating**: Ensure user type state is properly managed
4. **API 403 Errors**: Check user type middleware is running before routes

### Debug Helpers

```typescript
// Debug user type detection
console.log('User Type Result:', await detectUserType(userId, supabase));

// Check user permissions  
console.log('Can Access Billing:', canAccessBilling(userType));
console.log('Can Manage Team:', canManageTeam(userType));
```

## 📱 Mobile Considerations

The responsive design ensures the team licensee dashboard works well on mobile devices with:
- Stacked card layouts on small screens
- Touch-friendly buttons
- Readable license key display with copy functionality
- Collapsible sections for better mobile UX

---

This implementation provides a secure, user-friendly experience tailored to each user type while maintaining the existing functionality for admins and subscribers. 🎉