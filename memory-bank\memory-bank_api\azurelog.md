Connecting to stream...
2025-08-13T04:20:47.10256  Connecting to the container 'ca-quantboost-api'...

2025-08-13T04:20:47.12232  Successfully Connected to container: 'ca-quantboost-api' [Revision: 'ca-quantboost-api--0000071', Replica: 'ca-quantboost-api--0000071-7c8fb48f9b-49skw']
2025-08-13T01:39:30.333140087Z {"environment":"production","level":"info","message":"Azure Monitor disabled - set ENABLE_TELEMETRY=true to enable monitoring","service":"quantboost-api","timestamp":"2025-08-13 01:39:30.331","version":"1.0.0"}
2025-08-13T01:39:30.564187054Z Supabase clients (standard and admin) initialized successfully.
2025-08-13T01:39:30.638495039Z {"environment":"production","level":"info","message":"Supabase Client Initialized","service":"quantboost-api","timestamp":"2025-08-13 01:39:30.636","url":"https://izoutrnsxaao...","version":"1.0.0"}
2025-08-13T01:39:30.644918584Z {"environment":"production","level":"info","message":"QuantBoost API server started","nodeVersion":"v22.18.0","port":"3000","service":"quantboost-api","timestamp":"2025-08-13 01:39:30.644","version":"1.0.0"}
2025-08-13T04:18:54.350846710Z {"environment":"production","ip":"::ffff:*************","level":"info","message":"Incoming request","method":"GET","path":"/","query":{},"requestId":"082974f7-15ef-4018-af8d-f4d2784223f5","service":"quantboost-api","timestamp":"2025-08-13 04:18:54.350","userAgent":"python-requests/2.32.3","version":"1.0.0"}
2025-08-13T04:18:54.352040681Z Root route accessed: {
2025-08-13T04:18:54.352052940Z   url: '/',
2025-08-13T04:18:54.352058919Z   query: [Object: null prototype] {},
2025-08-13T04:18:54.352064226Z   headers: { referer: undefined, userAgent: 'python-requests/2.32.3' }
2025-08-13T04:18:54.352069014Z }
2025-08-13T04:18:54.352213791Z Auth detection result: {
2025-08-13T04:18:54.352221953Z   hasAuthParams: undefined,
2025-08-13T04:18:54.352226780Z   fromFrontend: undefined,
2025-08-13T04:18:54.352231427Z   queryKeys: [],
2025-08-13T04:18:54.352235844Z   hasReferer: false,
2025-08-13T04:18:54.352240331Z   refererUrl: undefined
2025-08-13T04:18:54.352244887Z }
2025-08-13T04:18:54.356871193Z {"contentLength":"111","duration":6,"environment":"production","level":"info","message":"Request completed","method":"GET","path":"/","requestId":"082974f7-15ef-4018-af8d-f4d2784223f5","service":"quantboost-api","statusCode":200,"timestamp":"2025-08-13 04:18:54.356","version":"1.0.0"}

2025-08-13T04:21:47.65430  No logs since last 60 seconds

2025-08-13T04:22:48.20460  No logs since last 60 seconds

2025-08-13T04:23:48.65583  No logs since last 60 seconds

2025-08-13T04:24:49.12828  No logs since last 60 seconds

2025-08-13T04:25:49.58752  No logs since last 60 seconds

2025-08-13T04:26:50.04002  No logs since last 60 seconds

2025-08-13T04:27:50.45735  No logs since last 60 seconds

2025-08-13T04:28:50.90801  No logs since last 60 seconds

2025-08-13T04:29:51.37573  No logs since last 60 seconds

2025-08-13T04:30:47.12224  Shutting down logstream. Max connection open time reached

Disconnected from stream
Reconnecting to stream...
2025-08-13T05:20:03.34186  Connecting to the container 'ca-quantboost-api'...

2025-08-13T05:20:03.36159  Successfully Connected to container: 'ca-quantboost-api' [Revision: 'ca-quantboost-api--0000071', Replica: 'ca-quantboost-api--0000071-7c8fb48f9b-49skw']
2025-08-13T01:39:30.333140087Z {"environment":"production","level":"info","message":"Azure Monitor disabled - set ENABLE_TELEMETRY=true to enable monitoring","service":"quantboost-api","timestamp":"2025-08-13 01:39:30.331","version":"1.0.0"}
2025-08-13T01:39:30.564187054Z Supabase clients (standard and admin) initialized successfully.
2025-08-13T01:39:30.638495039Z {"environment":"production","level":"info","message":"Supabase Client Initialized","service":"quantboost-api","timestamp":"2025-08-13 01:39:30.636","url":"https://izoutrnsxaao...","version":"1.0.0"}
2025-08-13T01:39:30.644918584Z {"environment":"production","level":"info","message":"QuantBoost API server started","nodeVersion":"v22.18.0","port":"3000","service":"quantboost-api","timestamp":"2025-08-13 01:39:30.644","version":"1.0.0"}
2025-08-13T04:18:54.350846710Z {"environment":"production","ip":"::ffff:*************","level":"info","message":"Incoming request","method":"GET","path":"/","query":{},"requestId":"082974f7-15ef-4018-af8d-f4d2784223f5","service":"quantboost-api","timestamp":"2025-08-13 04:18:54.350","userAgent":"python-requests/2.32.3","version":"1.0.0"}
2025-08-13T04:18:54.352040681Z Root route accessed: {
2025-08-13T04:18:54.352052940Z   url: '/',
2025-08-13T04:18:54.352058919Z   query: [Object: null prototype] {},
2025-08-13T04:18:54.352064226Z   headers: { referer: undefined, userAgent: 'python-requests/2.32.3' }
2025-08-13T04:18:54.352069014Z }
2025-08-13T04:18:54.352213791Z Auth detection result: {
2025-08-13T04:18:54.352221953Z   hasAuthParams: undefined,
2025-08-13T04:18:54.352226780Z   fromFrontend: undefined,
2025-08-13T04:18:54.352231427Z   queryKeys: [],
2025-08-13T04:18:54.352235844Z   hasReferer: false,
2025-08-13T04:18:54.352240331Z   refererUrl: undefined
2025-08-13T04:18:54.352244887Z }
2025-08-13T04:18:54.356871193Z {"contentLength":"111","duration":6,"environment":"production","level":"info","message":"Request completed","method":"GET","path":"/","requestId":"082974f7-15ef-4018-af8d-f4d2784223f5","service":"quantboost-api","statusCode":200,"timestamp":"2025-08-13 04:18:54.356","version":"1.0.0"}

Disconnected from stream
Connecting to stream...
2025-08-13T05:20:48.21062  Connecting to the container 'ca-quantboost-api'...

2025-08-13T05:20:48.23119  Successfully Connected to container: 'ca-quantboost-api' [Revision: 'ca-quantboost-api--0000071', Replica: 'ca-quantboost-api--0000071-7c8fb48f9b-49skw']
2025-08-13T01:39:30.333140087Z {"environment":"production","level":"info","message":"Azure Monitor disabled - set ENABLE_TELEMETRY=true to enable monitoring","service":"quantboost-api","timestamp":"2025-08-13 01:39:30.331","version":"1.0.0"}
2025-08-13T01:39:30.564187054Z Supabase clients (standard and admin) initialized successfully.
2025-08-13T01:39:30.638495039Z {"environment":"production","level":"info","message":"Supabase Client Initialized","service":"quantboost-api","timestamp":"2025-08-13 01:39:30.636","url":"https://izoutrnsxaao...","version":"1.0.0"}
2025-08-13T01:39:30.644918584Z {"environment":"production","level":"info","message":"QuantBoost API server started","nodeVersion":"v22.18.0","port":"3000","service":"quantboost-api","timestamp":"2025-08-13 01:39:30.644","version":"1.0.0"}
2025-08-13T04:18:54.350846710Z {"environment":"production","ip":"::ffff:*************","level":"info","message":"Incoming request","method":"GET","path":"/","query":{},"requestId":"082974f7-15ef-4018-af8d-f4d2784223f5","service":"quantboost-api","timestamp":"2025-08-13 04:18:54.350","userAgent":"python-requests/2.32.3","version":"1.0.0"}
2025-08-13T04:18:54.352040681Z Root route accessed: {
2025-08-13T04:18:54.352052940Z   url: '/',
2025-08-13T04:18:54.352058919Z   query: [Object: null prototype] {},
2025-08-13T04:18:54.352064226Z   headers: { referer: undefined, userAgent: 'python-requests/2.32.3' }
2025-08-13T04:18:54.352069014Z }
2025-08-13T04:18:54.352213791Z Auth detection result: {
2025-08-13T04:18:54.352221953Z   hasAuthParams: undefined,
2025-08-13T04:18:54.352226780Z   fromFrontend: undefined,
2025-08-13T04:18:54.352231427Z   queryKeys: [],
2025-08-13T04:18:54.352235844Z   hasReferer: false,
2025-08-13T04:18:54.352240331Z   refererUrl: undefined
2025-08-13T04:18:54.352244887Z }

2025-08-13T04:18:54.356871193Z {"contentLength":"111","duration":6,"environment":"production","level":"info","message":"Request completed","method":"GET","path":"/","requestId":"082974f7-15ef-4018-af8d-f4d2784223f5","service":"quantboost-api","statusCode":200,"timestamp":"2025-08-13 04:18:54.356","version":"1.0.0"}
