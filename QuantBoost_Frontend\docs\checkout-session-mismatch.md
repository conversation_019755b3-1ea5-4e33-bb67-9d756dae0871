# Checkout -> Dashboard Session Mismatch Handling

When a user completes checkout while already authenticated under a *different* account, we must ensure the subsequent dashboard session reflects the purchasing email, not the previously logged in user.

## Problem
Previously, if User A was logged in and completed a purchase for email `<EMAIL>`, the redirect to the dashboard would retain User A's session. Licenses / subscription provisioning would then appear under the wrong account.

## Solution
Implemented explicit mismatch detection on `auth/payment-success` page:

1. Retrieve current Supabase session (if any) and its `user.email`.
2. Compare with the `email` query parameter (purchase email) using normalized comparison.
3. If emails differ:
   - Sign out current session.
   - Attempt automatic post-payment authentication for the purchase email (direct token or magic link flow).
4. Proceed to `/dashboard?payment=success` only once session belongs to purchasing email.

## Diagnostics
A lightweight window object `window.__QB_PAYMENT_SUCCESS` is populated for QA/E2E assertions:
```
{
  currentSessionEmail,
  purchaseEmail,
  paymentIntentId,
  autoLoginAttempted,
  switched,
  normalizedCurrent,
  normalizedPurchase,
  mismatch
}
```

## Helper Module
`src/lib/auth/sessionEmailMismatch.ts` centralizes logic for:
- Normalizing emails
- Determining mismatch (`shouldSwitchAccount`)
- Building diagnostics object

## Tests
Added `src/__tests__/sessionEmailMismatch.test.ts` covering normalization and mismatch scenarios.

## Future Hardening Ideas
- Server-side webhook reconciliation to auto-link payment intents to user IDs if mismatch detected post-factum.
- Optional banner on dashboard if a mismatch remediation occurred.
- Rate limiting forced sign-outs to prevent abuse.

--
Maintainer Note: This logic purposely executes only on the payment success pathway to avoid interfering with normal authenticated navigation.
