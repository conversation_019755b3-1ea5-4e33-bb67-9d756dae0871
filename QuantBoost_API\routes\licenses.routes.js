const express = require('express');
const router = express.Router();
const { createClient } = require('@supabase/supabase-js');
const { supabase, supabaseAdmin } = require('../supabaseClient');
const crypto = require('crypto');
const { sendSuccess, sendError } = require('../utils/responseHelpers');
const { isValidEmail, getDeviceFingerprint } = require('../utils/validationHelpers'); // getDeviceFingerprint might be used if device tracking is part of trial logic
const { logLicenseEvent } = require('../utils/eventLogger');
const { authenticateApiKey, authenticateJWT } = require('../middleware/authMiddleware'); // Import both middleware functions
const { 
    evaluateLicenseStatus, 
    checkOrRecordActivation, 
    fetchLicenseFromSupabase, 
    getEmailFromUserProfile 
} = require('../utils/licenseLogic'); // For other license routes and potentially future enhancements to start-trial

// User-facing license operations

// +++ NEW ROUTE: Start Trial License +++
router.post('/start-trial', authenticateApiKey, async (req, res) => {
    const { email, productId, fullName } = req.body; // fullName is optional
    const TRIAL_DURATION_DAYS = parseInt(process.env.DEFAULT_TRIAL_DAYS || '7');
    // const MAGIC_LINK_REDIRECT_URL = process.env.APP_MAGIC_LINK_CALLBACK_URL; // Not used here, client handles redirect

    // if (!MAGIC_LINK_REDIRECT_URL) { // Client-side concern now
    //     console.error("Start Trial: CRITICAL - APP_MAGIC_LINK_CALLBACK_URL is not configured in environment variables.");
    //     return sendError(res, "Trial system configuration error. Please contact support.", 500);
    // }

    if (!email || !productId) {
        return sendError(res, 'Email and Product ID are required to start a trial.', 400);
    }
    if (!isValidEmail(email)) {
        return sendError(res, 'Invalid email format.', 400);
    }
    // Optional: Validate productId if you have a known list of product IDs
    // For example: if (!['QB_PPT_01', 'QB_XLS_01'].includes(productId)) { return sendError(res, 'Invalid Product ID.', 400); }

    try {
        // 1. Check for existing active/trial/grace licenses for this email and product
        const { data: existingLicenses, error: fetchError } = await supabase
            .from('licenses')
            .select('id, status, trial_expiry_date, expiry_date, user_id') // Added user_id for logging
            .eq('email', email)
            .eq('product_id', productId);

        if (fetchError) {
            console.error(`Start Trial: Error fetching existing licenses for ${email}, ${productId}: ${fetchError.message}`);
            return sendError(res, 'Error checking for existing trial.', 500);
        }

        if (existingLicenses && existingLicenses.length > 0) {
            const activeOrRecentTrial = existingLicenses.find(lic =>
                ['active', 'trial_active', 'graceperiod'].includes(lic.status) ||
                (lic.status === 'expired' && lic.trial_expiry_date) // Check if they had a trial that expired
            );
            if (activeOrRecentTrial) {
                console.warn(`Start Trial: User ${email} already has/had an active/trial/grace license for ${productId}. Status: ${activeOrRecentTrial.status}.`);
                // Instruct client to initiate Magic Link flow for login
                let userMessage = 'You already have an account or have previously started a trial for this product. Please use the login page to access your account.';
                if (activeOrRecentTrial.status === 'active') {
                    userMessage = 'You already have an active license for this product. Please use the login page to access your account.';
                } else if (activeOrRecentTrial.status === 'trial_active') {
                    userMessage = 'You already have an active trial for this product. Please use the login page to access your account.';
                }
                return sendError(res, userMessage, 409, { initiateMagicLink: true, email: email }); // 409 Conflict, signal client
            }
        }

        // 2. Create the trial license record.
        const now = new Date();
        const trialStartDate = now;
        const trialExpiryDate = new Date(now.getTime() + TRIAL_DURATION_DAYS * 24 * 60 * 60 * 1000);

        const newTrialLicenseData = {
            email: email, 
            user_id: null, 
            product_id: productId,
            status: 'trial_active',
            license_tier: 'trial',
            license_key: `TRIAL-${crypto.randomBytes(8).toString('hex').toUpperCase()}`, // crypto needs to be required
            trial_start_date: trialStartDate.toISOString(),
            trial_expiry_date: trialExpiryDate.toISOString(),
            max_activations: 1, 
            subscription_id: null,
            // metadata: fullName ? { fullName } : null // Optional: store fullName if provided
        };

        const { data: createdLicense, error: insertError } = await supabaseAdmin
            .from('licenses')
            .insert(newTrialLicenseData)
            .select('id, product_id, status, trial_expiry_date, email, license_key') 
            .single();

        if (insertError) {
            console.error(`Start Trial: Error creating trial license for ${email}, ${productId}: ${insertError.message}`);
            // Check for unique constraint violation on license_key if it happens, though unlikely with random generation
            if (insertError.code === '23505') {
                 return sendError(res, 'A unique identifier conflict occurred. Please try again.', 500);
            }
            return sendError(res, 'Could not start your trial at this time. Please try again later.', 500);
        }

        // 3. Instruct client to initiate Magic Link flow (REMOVED direct call to signInWithOtp)
        // The client should now call POST /v1/auth/magic-link with the user's email.

        // 4. Log event
        await logLicenseEvent(supabase, 'trial_started', createdLicense.id, null, email, 'trial_active');

        return sendSuccess(res, {
            message: `${TRIAL_DURATION_DAYS}-day trial started successfully for ${email}! Please proceed to login. If you are a new user, a magic link will be sent to your email after you attempt to login.`,
            license: createdLicense,
            initiateMagicLink: true, // Signal to client to call the magic-link auth route
            email: email // Provide email for the client to use in the next step
        }, 201);

    } catch (error) {
        console.error('Unexpected error in /start-trial (licenses.routes.js):', error);
        await logLicenseEvent(supabase, 'trial_creation_error', null, null, email, 'failure_unexpected_error');
        return sendError(res, 'An unexpected error occurred while starting your trial.', 500);
    }
});


// GET /v1/licenses/validate - This is the primary validation route for authenticated users.
// The authenticateJWT middleware has already run and placed the user on req.user.
router.get('/validate', authenticateJWT, async (req, res) => {
    // The user object is guaranteed to be here because of the middleware
    const user = req.user;
    const { productId, deviceId: machineId } = req.query; // For GET, params are in req.query
    
    // Extract the token from the Authorization header
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ') ? authHeader.split(' ')[1] : null;

    await logLicenseEvent(supabase, 'validation_attempt', null, machineId, user.email, "pending_token_auth");

    if (!productId || !machineId) {
        return sendError(res, 'Product ID and Machine ID are required.', 400);
    }

    try {
        // Create an authenticated Supabase client for this specific request
        const supabaseAuth = createClient(
            process.env.SUPABASE_URL,
            process.env.SUPABASE_ANON_KEY,
            {
                global: {
                    headers: {
                        Authorization: `Bearer ${token}`
                    }
                }
            }
        );

        console.log(`[GET /validate] Searching for license with user.id: ${user.id}, productId: ${productId}, email: ${user.email}`);
        
        // Now use the authenticated client for the query
        const { data: license, error: licenseError } = await supabaseAuth
            .from('licenses')
            .select('*')
            .eq('user_id', user.id)
            .eq('product_id', productId)
            .maybeSingle();

        if (licenseError) {
            console.error(`[GET /validate] Error fetching license for user ${user.id}:`, licenseError.message);
            return sendError(res, 'Error fetching license information.', 500);
        }

        if (!license) {
            // If we still can't find a license, then one truly doesn't exist for this user.
            console.log(`[GET /validate] No license found by user_id for user ${user.id} (${user.email}), product ${productId}`);
            await logLicenseEvent(supabase, 'token_validation_failure', null, machineId, user.email, 'failure_no_license_found');
            return sendError(res, 'No license found for your account for this product.', 404);
        }

        console.log(`[GET /validate] Found license: id=${license.id}, user_id=${license.user_id}, email=${license.email}, status=${license.status}`);

        // 2. Evaluate the license status (active, expired, etc.)
        const status = evaluateLicenseStatus(license);
        if (!status.isValid) {
            await logLicenseEvent(supabase, 'token_validation_failure', license.id, machineId, user.email, `failure_license_not_valid_${status.apiStatus}`);
            return sendError(res, `Your license is not active. Status: ${status.apiStatus}`, 403);
        }

        // 3. Check device activation
        const deviceFingerprint = getDeviceFingerprint(machineId);
        const activationResult = await checkOrRecordActivation(supabaseAdmin, license, deviceFingerprint);
        if (!activationResult.allowed) {
            await logLicenseEvent(supabase, 'token_validation_failure', license.id, machineId, user.email, `failure_activation_${activationResult.error.code}`);
            return sendError(res, activationResult.error.message, 409); // 409 Conflict for activation issues
        }

        // 4. If all checks pass, return success
        await logLicenseEvent(supabase, 'token_validation_success', license.id, machineId, user.email, `success_${status.apiStatus}`);
        
        return sendSuccess(res, {
            status: status.apiStatus,
            isValid: status.isValid,
            message: `License validation successful. Status: ${status.apiStatus}`,
            licenseTier: license.license_tier,
            trialDaysLeft: status.trialDaysLeft,
            activationId: activationResult.activationId,
            expiryDate: status.calculatedExpiryDate,
            email: user.email,
            licenseKey: license.license_key,
            // Add other details like manageUrl, upgradeUrl if they exist on the license
        });

    } catch (error) {
        console.error('[GET /validate] Unexpected error:', error);
        await logLicenseEvent(supabase, 'token_validation_error', null, machineId, user.email, 'failure_unexpected_error');
        return sendError(res, 'An unexpected error occurred during license validation.', 500);
    }
});

// POST /validate - Validate license key/device/product
router.post('/validate', async (req, res) => {
    const { licenseKey, productId, machineId } = req.body;

    // console.log(`[VALIDATE KEY] Received - LK: ${licenseKey}, ProdID: ${productId}, MID: ${machineId}`);

    // Initial log for key-based validation attempt
    await logLicenseEvent(supabase, 'validation_attempt', null, machineId || "unknown", licenseKey || "unknown_license_key", "pending_key_auth");    // --- Key-Based Validation Logic ---
    if (!licenseKey || !productId || !machineId) {
        await logLicenseEvent(supabase, 'key_validation_failure', null, machineId, licenseKey || "unknown_license_key", 'failure_missing_params_for_key_auth');
        return sendError(res, 'License key, product ID, and machine ID are required', 400);
    }    try {
        const license = await fetchLicenseFromSupabase(supabase, licenseKey);

        if (!license) {
            console.log(`[VALIDATE KEY] License key ${licenseKey} not found.`);
            await logLicenseEvent(supabase, 'key_validation_failure', null, machineId, licenseKey, 'failure_license_key_not_found');
            return sendError(res, 'License key not found or product ID mismatch.', 404);
        }

        // Check if product ID matches the license
        if (license.product_id !== productId) {
            console.log(`[VALIDATE KEY] License key ${licenseKey} found but product ID mismatch. Expected: ${productId}, Found: ${license.product_id}`);
            await logLicenseEvent(supabase, 'key_validation_failure', license.id, machineId, licenseKey, 'failure_product_id_mismatch');
            return sendError(res, 'Product ID does not match the license', 403);
        }

        const deviceFingerprint = getDeviceFingerprint(machineId);
        const activationResult = await checkOrRecordActivation(supabaseAdmin, license, deviceFingerprint);
        // Determine the best email for logging; user_id might be null for unlinked key-based licenses
        const userEmailForLog = license.profiles?.email || license.email || (license.user_id ? `user_id:${license.user_id}` : 'unlinked_key_holder');

        if (!activationResult.allowed) {
            const errorMessage = activationResult.error?.message || 'Device activation check failed.';
            const errorCode = activationResult.error?.code;
            console.log(`[VALIDATE KEY] Activation check failed for license ${license.id}. Reason: ${errorMessage}`);
            await logLicenseEvent(supabase, 'key_validation_failure', license.id, machineId, userEmailForLog, `failure_activation_${errorCode || 'check'}`);
            let httpStatusCode = 403;
            if (errorCode === "NO_LICENSE_EMAIL") httpStatusCode = 403; // Should not happen if license.email is populated for key-based
            else if (errorCode?.startsWith("MAX_ACTIVATIONS_REACHED")) httpStatusCode = 409; // Conflict
            return sendError(res, errorMessage, httpStatusCode);
        }

        const status = evaluateLicenseStatus(license, activationResult.activationId);

        if (!status.isValid) {
            console.log(`[VALIDATE KEY] License ${license.id} found but is not valid. API Status: ${status.apiStatus}`);
            await logLicenseEvent(supabase, 'key_validation_failure', license.id, machineId, userEmailForLog, `failure_license_not_valid_${status.apiStatus}`);
            return sendError(res, `License is not active. Status: ${status.apiStatus}`, 403);
        }

        console.log(`[VALIDATE KEY] Successful validation for license ${license.id}. API Status: ${status.apiStatus}`);
        await logLicenseEvent(supabase, 'key_validation_success', license.id, machineId, userEmailForLog, `success_${status.apiStatus}`);
        sendSuccess(res, {
            message: `License status (key): ${status.apiStatus}`,
            license_key: license.license_key,
            user_id: license.user_id, // Could be null
            product_id: license.product_id,
            status: status.apiStatus,
            expiry_date: status.calculatedExpiryDate,
            trial_days_left: status.trialDaysLeft,
            machine_id: machineId,
            activation_id: activationResult.activationId
        });    } catch (error) {
        console.error('[VALIDATE KEY] Unexpected error:', error);
        await logLicenseEvent(supabase, 'key_validation_error', null, machineId, licenseKey, 'failure_unexpected_error_key_auth');
        return sendError(res, 'An unexpected error occurred during license validation.', 500);
    }
});

// +++ NEW ROUTE: Activate License (Key-Based) +++
router.post('/activate', authenticateApiKey, async (req, res) => {
    const { licenseKey, productId } = req.body;

    if (!licenseKey || !productId) {
        return sendError(res, 'License key and product ID are required', 400);
    }

    try {        // Check if license already exists
        const { data: existingLicense, error: fetchError } = await supabaseAdmin
            .from('licenses')
            .select('*')
            .eq('license_key', licenseKey)
            .single();

        if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = no rows returned
            console.error('Error fetching license:', fetchError.message);
            return sendError(res, 'Error checking license', 500);
        }

        if (existingLicense) {
            if (existingLicense.status === 'active') {
                return sendError(res, 'License key already exists and is active', 409);
            }
              // Check if trying to reactivate with different product ID
            if (existingLicense.product_id !== productId) {
                return sendError(res, 'License key exists with a different product ID. Cannot reactivate with a new product ID.', 409);
            }

            // Reactivate existing license
            const { data: updatedLicense, error: updateError } = await supabaseAdmin
                .from('licenses')
                .update({ status: 'active' })
                .eq('license_key', licenseKey)
                .select()
                .single();

            if (updateError) {
                console.error('Error reactivating license:', updateError.message);
                return sendError(res, 'Error reactivating license', 500);
            }

            await logLicenseEvent(supabase, 'key_activation_success', updatedLicense.id, 'unknown_machine', licenseKey, 'success_reactivated');
            return sendSuccess(res, { 
                message: 'License reactivated successfully', 
                license_key: updatedLicense.license_key, 
                product_id: updatedLicense.product_id, 
                status: updatedLicense.status 
            }, 200);        }

        // Create new license
        const { data: createdLicense, error: insertError } = await supabaseAdmin
            .from('licenses')
            .insert({
                license_key: licenseKey,
                product_id: productId,
                status: 'active',
                max_activations: 1
            })
            .select()
            .single();

        if (insertError) {
            console.error('Error creating license:', insertError.message);
            if (insertError.code === '23505') { // Unique constraint violation
                return sendError(res, 'License key already exists', 409);
            }
            return sendError(res, 'Error creating license', 500);
        }

        await logLicenseEvent(supabase, 'key_activation_success', createdLicense.id, 'unknown_machine', licenseKey, 'success_newly_activated');
        return sendSuccess(res, {
            message: 'License activated successfully',
            license_key: createdLicense.license_key,
            product_id: createdLicense.product_id,
            status: createdLicense.status
        }, 201);

    } catch (error) {
        console.error('Unexpected error in license activation:', error);
        await logLicenseEvent(supabase, 'key_activation_error', null, 'unknown_machine', licenseKey, 'failure_unexpected_error_activate');
        return sendError(res, 'An unexpected error occurred during license activation', 500);
    }
});

// Duplicate route removed - using the main /validate route above



module.exports = router;
