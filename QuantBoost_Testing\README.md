# QuantBoost Testing Suite

End-to-end Playwright test harness targeting deployed (staging/production) QuantBoost frontend & API for checkout, billing, and subscription lifecycle validation.

## Key Features
- Checkout (individual & team) flows
- Stripe payment & webhook processing assertions
- Supabase data verification (profiles, subscriptions, licenses, receipts)
- Security & performance test scaffolding
- Report generation (Markdown + HTML dashboard)
- CI integration via GitHub Actions

## Environment Variables
Copy `.env.example` to `.env` and fill real values (never commit secrets).

| Variable | Description |
|----------|-------------|
| BASE_URL | Deployed frontend base URL (e.g. staging). |
| STRIPE_SECRET_KEY_TEST | Stripe test secret key. |
| STRIPE_PUBLISHABLE_KEY | Stripe publishable key for client-based requests if needed. |
| SUPABASE_URL | Supabase project URL. |
| SUPABASE_ANON_KEY | Public anon key for client-like queries. |
| SUPABASE_SERVICE_ROLE_KEY | Service role key (ONLY in CI secure secrets) for privileged verification. |
| ENABLE_LOAD_TESTS | Set true to enable performance tests. |
| ENABLE_SECURITY_TESTS | Toggle security test suite. |
| CHECKOUT_CONCURRENT_USERS | Number of simulated concurrent checkout browsers. |

## Install & Run

```powershell
npm install
npx playwright install
npm test
```

Run a subset:
```powershell
npm run test:checkout
npm run test:webhooks
npm run test:performance
npm run test:security
```

Generate standalone report (after run):
```powershell
npm run report
```

CI uses `npm run ci` to produce HTML + markdown.

## Test Data
Stripe & Supabase entries created by tests should be purged periodically in stealth phase. Long-term plan: ephemeral test accounts & data factories.

## Roadmap Enhancements
See PRP: visual regression, contract tests (Pact), chaos, accessibility, synthetic monitoring, analytics validation.

## Safety
- Skips suites gracefully if required env vars are missing.
- Never embed secrets in code or reports.

## License
Internal proprietary testing assets for QuantBoost.
