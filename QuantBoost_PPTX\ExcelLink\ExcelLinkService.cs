using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Presentation;
using System.Linq;
using System;
using System.Security.Cryptography;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using Excel = Microsoft.Office.Interop.Excel;
using QuantBoost_Shared.Utilities;
using System.Windows.Forms;
using System.Runtime.InteropServices; // Added for Marshal


namespace QuantBoost_Powerpoint_Addin.ExcelLink
{
    public class ExcelLinkService
    {
        private readonly PowerPointComWrapper _pptWrapper;
        private const string CustomXmlRelationshipType = "http://schemas.quantboost.com/excel-link/2025/relationships/linkmetadata"; // Updated RelType
        private const string CustomXmlNamespace = "http://schemas.quantboost.com/excel-link/2025/main";

        public ExcelLinkService(PowerPoint.Application pptApp)
        {
            _pptWrapper = new PowerPointComWrapper(pptApp);
        }

        /// <summary>
        /// Embeds selected Excel content (Chart, Range, Shape) into the target PowerPoint slide.
        /// </summary>
        public async Task<ExcelLinkMetadata> EmbedExcelContentAsync(string excelFilePath, string worksheetName, ExcelLinkType linkType, string sourceIdentifier, int targetSlideIndex)
        {
            string tempImagePath = null;
            PowerPoint.Shape insertedShape = null;
            string shapeId = null;
            ExcelLinkMetadata linkMetadata = null;

            // Capture PowerPoint window state before Excel operations
            var powerPointFocusManager = new PowerPointFocusManager();

            try
            {                // --- Create Metadata (Generate LinkId early) ---
                linkMetadata = new ExcelLinkMetadata
                {
                    LinkId = Guid.NewGuid().ToString(),
                    LinkType = linkType,
                    SourceFilePath = excelFilePath,
                    SourceFileHash = await GetFileHashAsync(excelFilePath),
                    SourceFileLastModifiedUtc = GetFileLastModifiedUtc(excelFilePath),
                    WorksheetName = worksheetName,
                    SourceIdentifier = sourceIdentifier,
                    SlideIndex = targetSlideIndex,
                    ModifiedBy = Environment.UserName,
                    IsActive = true
                };

                // --- Perform Embedding based on Link Type ---
                using (var pptWrapper = new PowerPointComWrapper(Globals.ThisAddIn.Application))
                {
                    var targetSlide = pptWrapper.GetSlideByIndex(targetSlideIndex);
                    if (targetSlide is null) throw new Exception($"Target slide index {targetSlideIndex} not found.");

                    using (var excelWrapper = new ExcelComWrapper())
                    {
                        var workbook = excelWrapper.OpenWorkbook(excelFilePath);
                        if (workbook is null) throw new Exception("Failed to open Excel workbook.");
                        var worksheet = excelWrapper.GetWorksheet(worksheetName);
                        if (worksheet is null) throw new Exception($"Worksheet '{worksheetName}' not found.");

                        if (linkType == ExcelLinkType.Chart)
                        {
                            var chartObject = excelWrapper.GetChartObject(worksheetName, sourceIdentifier);
                            if (chartObject is null) throw new Exception($"Chart '{sourceIdentifier}' not found.");
                            tempImagePath = Path.Combine(Path.GetTempPath(), $"QuantBoost_Chart_{linkMetadata.LinkId}.png");
                            if (!excelWrapper.ExportChartAsImage(chartObject, tempImagePath))
                                throw new Exception("Failed to export chart as image.");
                            insertedShape = pptWrapper.InsertPicture(tempImagePath, targetSlide, 100, 100);
                        }                        else if (linkType == ExcelLinkType.Range)
                        {
                            // Link the selected Range as a picture/metafile using enhanced clipboard management
                            Excel.Range range = excelWrapper.GetRange(worksheetName, sourceIdentifier);
                            if (range is null) throw new Exception($"Range '{sourceIdentifier}' not found.");

                            // Enhanced copy operation with multi-strategy fallback
                            bool copySuccess = await CopyRangeWithEnhancedStrategy(range, "xlPicture", excelFilePath);
                            if (!copySuccess)
                            {
                                throw new InvalidOperationException($"Failed to copy range '{sourceIdentifier}' using enhanced clipboard strategy");
                            }

                            // Determine if this is a SharePoint file for enhanced paste handling
                            bool isSharePointFile = excelFilePath?.Contains("sharepoint") == true ||
                                                   excelFilePath?.Contains("office365") == true ||
                                                   excelFilePath?.StartsWith("https://") == true;

                            // Enhanced paste operation with clipboard validation
                            var pastedRange = await PasteWithClipboardValidation(targetSlide, isSharePointFile, PowerPoint.PpPasteDataType.ppPasteEnhancedMetafile);
                            if (pastedRange != null && pastedRange.Count > 0)
                            {
                                insertedShape = pastedRange[1]; // Get first shape from the pasted range
                                insertedShape.Left = 100;
                                insertedShape.Top = 100; // Position
                                ReleaseComObject(pastedRange); // Clean up the ShapeRange
                            }
                            else
                            {
                                throw new InvalidOperationException($"Failed to paste range '{sourceIdentifier}' using enhanced clipboard strategy");
                            }
                            ReleaseComObject(range);
                        }                        else if (linkType == ExcelLinkType.Shape)
                        {
                            // Link the selected Shape as a picture/metafile using enhanced clipboard management
                            Excel.Shape shape = excelWrapper.GetShape(worksheetName, sourceIdentifier);
                            if (shape is null) throw new Exception($"Shape '{sourceIdentifier}' not found.");

                            // Enhanced shape copy operation with multi-strategy fallback
                            bool copySuccess = await CopyShapeWithEnhancedStrategy(shape, excelFilePath);
                            if (!copySuccess)
                            {
                                throw new InvalidOperationException($"Failed to copy shape '{sourceIdentifier}' using enhanced clipboard strategy");
                            }

                            // Determine if this is a SharePoint file for enhanced paste handling
                            bool isSharePointFile = excelFilePath?.Contains("sharepoint") == true ||
                                                   excelFilePath?.Contains("office365") == true ||
                                                   excelFilePath?.StartsWith("https://") == true;

                            // Enhanced paste operation with clipboard validation
                            var pastedRange = await PasteWithClipboardValidation(targetSlide, isSharePointFile, PowerPoint.PpPasteDataType.ppPasteEnhancedMetafile);
                            if (pastedRange != null && pastedRange.Count > 0)
                            {
                                insertedShape = pastedRange[1]; // Get first shape from the pasted range
                                insertedShape.Left = 100;
                                insertedShape.Top = 100; // Position
                                ReleaseComObject(pastedRange); // Clean up the ShapeRange
                            }
                            else
                            {
                                throw new InvalidOperationException($"Failed to paste shape '{sourceIdentifier}' using enhanced clipboard strategy");
                            }
                            ReleaseComObject(shape);
                        }
                        else if (linkType == ExcelLinkType.OleObject)
                        {
                            // This case is now reserved for explicitly embedding/linking whole files/sheets.
                            // The current selection logic doesn't trigger this for Range selection anymore.
                            // If triggered, use InsertOleObject (linked or embedded based on future needs).
                            // insertedShape = pptWrapper.InsertOleObject(excelFilePath, worksheetName, sourceIdentifier, targetSlide, 100, 100, linkToFile: true); // Example if needed later
                            throw new NotSupportedException("Direct OLE Object embedding from current selection type not implemented yet. Use Range/Chart/Shape selection.");
                        }
                        // TODO: Add cases for other LinkTypes (Text)
                        else
                        {
                            throw new NotSupportedException($"Embedding not supported for LinkType: {linkType}");
                        }
                    }

                    if (insertedShape is null) throw new Exception("Failed to insert content into PowerPoint.");

                    // Assign unique name and AltText identifier
                    shapeId = $"QuantBoostLinkedContent_{Guid.NewGuid()}";
                    insertedShape.Name = shapeId;                    insertedShape.AlternativeText = $"QuantBoostLink:{linkMetadata.LinkId}";
                    linkMetadata.PowerPointShapeId = shapeId; // Update metadata with actual shape ID/Name
                }

                // --- Write Metadata --- 
                var activePresentation = Globals.ThisAddIn.Application.ActivePresentation;
                string presentationPath = activePresentation.FullName;
                // Check if presentation is saved (has a file path)
                if (string.IsNullOrEmpty(presentationPath))
                {
                    throw new InvalidOperationException("Please save the PowerPoint presentation before creating Excel links. Unsaved presentations cannot store link metadata.");
                }
                
                // Store metadata using PowerPoint COM interface to avoid file locking issues
                await StoreMetadataViaCOMAsync(linkMetadata, activePresentation);

                // Restore PowerPoint window focus after Excel operations
                await powerPointFocusManager.RestorePowerPointFocusAsync();

                return linkMetadata;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, "Failed to embed Excel content");
                if (insertedShape != null) { try { insertedShape.Delete(); } catch { } }

                // Restore PowerPoint focus even on error
                try
                {
                    await powerPointFocusManager.RestorePowerPointFocusAsync();
                }
                catch (Exception focusEx)
                {
                    ErrorHandlingService.LogException(focusEx, "Failed to restore PowerPoint focus after error");
                }

                return null;
            }
            finally
            {
                if (!string.IsNullOrEmpty(tempImagePath) && File.Exists(tempImagePath)) { try { File.Delete(tempImagePath); } catch { } }
                ReleaseComObject(insertedShape); // Ensure COM object is released
                powerPointFocusManager?.Dispose(); // Clean up focus manager
            }
        }

        /// <summary>
        /// Refreshes a single linked item based on its LinkType.
        /// </summary>
        public async Task RefreshLinkAsync(ExcelLinkMetadata link)
        {
            if (link == null || string.IsNullOrEmpty(link.PowerPointShapeId) || link.SlideIndex <= 0)
                throw new ArgumentNullException(nameof(link), "Link metadata is incomplete.");

            // Capture PowerPoint window state before Excel operations
            var powerPointFocusManager = new PowerPointFocusManager();

            ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Starting refresh for link {link.LinkId} on slide {link.SlideIndex}");

            string altTextToFind = $"QuantBoostLink:{link.LinkId}";
            string tempImagePath = null;
            PowerPoint.Slide slide = null;
            PowerPoint.Shape targetShape = null;
            string specificError = null;

            try
            {
                ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Looking for shape with AltText: {altTextToFind}");

                // --- Find Target Shape ---
                using (var pptWrapper = new PowerPointComWrapper(Globals.ThisAddIn.Application))
                {
                    slide = pptWrapper.GetSlideByIndex(link.SlideIndex);
                    if (slide is null) throw new FileNotFoundException($"Slide index {link.SlideIndex} not found.");
                    
                    ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Found slide {link.SlideIndex}, now looking for target shape");
                    
                    targetShape = pptWrapper.GetShapeByAltText(slide, altTextToFind);
                    if (targetShape is null) throw new KeyNotFoundException($"Shape with LinkId '{link.LinkId}' not found on slide {link.SlideIndex}.");
                    
                    ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Found target shape: {targetShape.Name}");
                }

                // --- Check Source File ---
                ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Checking source file: {link.SourceFilePath}");
                
                // Check if source file exists (handle both local files and SharePoint URLs)
                if (!IsSourceFileAccessible(link.SourceFilePath))
                {
                    throw new FileNotFoundException($"Source file not found: {link.SourceFilePath}");
                }

                ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Source file accessible, proceeding with LinkType: {link.LinkType}");

                // --- Perform Refresh based on LinkType ---
                if (link.LinkType == ExcelLinkType.Chart)
                {
                    ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Processing Chart refresh");
                    
                    // Refresh logic for Chart (export image, replace shape)
                    using (var excelWrapper = new ExcelComWrapper())
                    {
                        var chartObject = excelWrapper.GetChartObject(link.WorksheetName, link.SourceIdentifier);
                        if (chartObject is null) throw new KeyNotFoundException($"Chart '{link.SourceIdentifier}' not found.");
                        tempImagePath = Path.Combine(Path.GetTempPath(), $"QuantBoost_Refresh_{link.LinkId}.png");
                        
                        ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Exporting chart to: {tempImagePath}");
                        
                        if (!excelWrapper.ExportChartAsImage(chartObject, tempImagePath))
                            throw new Exception("Failed to export updated chart image.");
                            
                        ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Chart exported successfully");
                    }
                    
                    // Replace shape logic (delete old, insert new picture)
                    float left = targetShape.Left; float top = targetShape.Top; float width = targetShape.Width; float height = targetShape.Height; string shapeName = targetShape.Name;
                    
                    ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Deleting old shape and inserting new one");
                    
                    targetShape.Delete(); ReleaseComObject(targetShape); targetShape = null;
                    using (var pptWrapper = new PowerPointComWrapper(Globals.ThisAddIn.Application))
                    {
                        slide = pptWrapper.GetSlideByIndex(link.SlideIndex); // Re-get slide
                        var newShape = pptWrapper.InsertPicture(tempImagePath, slide, left, top, width, height);
                        if (newShape == null) throw new Exception("Failed to insert refreshed chart image.");
                        newShape.Name = shapeName; newShape.AlternativeText = altTextToFind;
                        ReleaseComObject(newShape);
                    }
                    
                    ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Chart refresh completed successfully");
                }
                else if (link.LinkType == ExcelLinkType.Range || link.LinkType == ExcelLinkType.Shape)
                {
                    ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Processing {link.LinkType} refresh");
                    
                    // Refresh logic for Range/Shape (pasted as picture/metafile)
                    using (var excelWrapper = new ExcelComWrapper())
                    {
                        var workbook = excelWrapper.OpenWorkbook(link.SourceFilePath);
                        if (workbook is null) throw new FileLoadException("Failed to open source Excel workbook.");
                        var worksheet = excelWrapper.GetWorksheet(link.WorksheetName);
                        if (worksheet is null) throw new KeyNotFoundException($"Worksheet '{link.WorksheetName}' not found.");

                        ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Opened Excel workbook and worksheet");

                        // Get the source object (Range or Shape) and copy it using enhanced clipboard management
                        bool copySuccess = false;
                        if (link.LinkType == ExcelLinkType.Range)
                        {
                            Excel.Range range = excelWrapper.GetRange(link.WorksheetName, link.SourceIdentifier);
                            if (range is null) throw new KeyNotFoundException($"Range '{link.SourceIdentifier}' not found.");

                            ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Copying range to clipboard using enhanced strategy");

                            copySuccess = await CopyRangeWithEnhancedStrategy(range, "xlPicture", link.SourceFilePath);
                            ReleaseComObject(range);
                        }
                        else // Shape
                        {
                            Excel.Shape shape = excelWrapper.GetShape(link.WorksheetName, link.SourceIdentifier);
                            if (shape is null) throw new KeyNotFoundException($"Shape '{link.SourceIdentifier}' not found.");

                            ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Copying shape to clipboard using enhanced strategy");

                            copySuccess = await CopyShapeWithEnhancedStrategy(shape, link.SourceFilePath);
                            ReleaseComObject(shape);
                        }

                        if (!copySuccess)
                        {
                            throw new InvalidOperationException($"Failed to copy {link.LinkType} '{link.SourceIdentifier}' using enhanced clipboard strategy");
                        }
                    }

                    ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Content copied to clipboard, replacing shape");

                    // Replace shape logic (delete old, paste special new)
                    float left = targetShape.Left; float top = targetShape.Top; float width = targetShape.Width; float height = targetShape.Height; string shapeName = targetShape.Name;
                    targetShape.Delete(); ReleaseComObject(targetShape); targetShape = null;

                    using (var pptWrapper = new PowerPointComWrapper(Globals.ThisAddIn.Application))
                    {
                        slide = pptWrapper.GetSlideByIndex(link.SlideIndex); // Re-get slide
                        if (slide is null) throw new InvalidOperationException("Slide became unavailable during refresh.");

                        ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Attempting to paste from clipboard using enhanced strategy");

                        // Determine if this is a SharePoint file for enhanced paste handling
                        bool isSharePointFile = link.SourceFilePath?.Contains("sharepoint") == true ||
                                               link.SourceFilePath?.Contains("office365") == true ||
                                               link.SourceFilePath?.StartsWith("https://") == true;

                        // Use enhanced paste method with clipboard validation
                        var pastedShapeRange = await PasteWithClipboardValidation(slide, isSharePointFile, PowerPoint.PpPasteDataType.ppPasteEnhancedMetafile);
                        if (pastedShapeRange == null || pastedShapeRange.Count == 0)
                            throw new Exception("Failed to paste refreshed content using enhanced clipboard strategy after all retry attempts.");

                        var newShape = pastedShapeRange[1];
                        // Apply original position/size/name/alttext
                        newShape.Left = left; newShape.Top = top; newShape.Width = width; newShape.Height = height;
                        newShape.Name = shapeName;
                        newShape.AlternativeText = altTextToFind;

                        ReleaseComObject(newShape);
                        ReleaseComObject(pastedShapeRange);
                    }
                    specificError = null; // Clear error on success
                    
                    ErrorHandlingService.LogException(null, $"RefreshLinkAsync: {link.LinkType} refresh completed successfully");
                }
                // REMOVED: OleObject refresh logic using LinkFormat.Update() as it's not applicable
                // else if (link.LinkType == ExcelLinkType.OleObject)
                // {
                //    // ... code using LinkFormat.Update() ...
                // }
                else
                {
                    // Keep the fallback for any other unexpected/unsupported types
                    specificError = $"Refresh not supported for LinkType: {link.LinkType}";
                    ErrorHandlingService.LogException(null, $"RefreshLinkAsync: {specificError}");
                    // Throw exception here if we want to strictly enforce supported types
                    // throw new NotSupportedException($"Refresh not supported for LinkType: {link.LinkType}");
                }
                
                ErrorHandlingService.LogException(null, $"RefreshLinkAsync: Refresh operation completed successfully for link {link.LinkId}");

                // Restore PowerPoint focus after successful refresh
                await powerPointFocusManager.RestorePowerPointFocusAsync();
            }
            catch (Exception ex)
            {
                // Ensure specificError is set if it wasn't caught by specific handlers
                if (specificError == null) specificError = $"General Error: {ex.Message}";
                ErrorHandlingService.LogException(ex, $"RefreshLinkAsync FAILED for link: {link.SourceIdentifier ?? link.LinkId}");
                try { link.ErrorState = specificError; await Task.Yield(); /* placeholder for async save */ }
                catch (Exception saveEx) { ErrorHandlingService.LogException(saveEx, "Failed to save error state after refresh failure"); }

                // Restore PowerPoint focus even on error
                try
                {
                    await powerPointFocusManager.RestorePowerPointFocusAsync();
                }
                catch (Exception focusEx)
                {
                    ErrorHandlingService.LogException(focusEx, "Failed to restore PowerPoint focus after refresh error");
                }

                throw; // Re-throw to let caller handle the error
            }
            finally
            {
                if (!string.IsNullOrEmpty(tempImagePath) && File.Exists(tempImagePath)) { try { File.Delete(tempImagePath); } catch { } }
                ReleaseComObject(targetShape); // Ensure COM object is released
                powerPointFocusManager?.Dispose(); // Clean up focus manager
            }
        }

        private async Task<string> GetFileHashAsync(string filePath)
        {
            using (var sha256 = SHA256.Create())
            {
                // Check if this is a URL (SharePoint, OneDrive, etc.)
                if (Uri.TryCreate(filePath, UriKind.Absolute, out Uri uri) && 
                    (uri.Scheme == "http" || uri.Scheme == "https"))
                {
                    // For remote files, generate a hash based on the URL and current timestamp
                    // This provides a unique identifier while avoiding file access issues
                    var urlBytes = System.Text.Encoding.UTF8.GetBytes(filePath + DateTime.UtcNow.ToString("yyyy-MM-dd"));
                    var hash = await Task.Run(() => sha256.ComputeHash(urlBytes));
                    return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
                }
                else
                {
                    // Local file - use FileShare.ReadWrite to allow reading while file is open in Excel
                    using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                    {
                        // .NET Framework does not have ComputeHashAsync, use Task.Run
                        var hash = await Task.Run(() => sha256.ComputeHash(stream));
                        return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
                    }
                }
            }
        }

        private DateTime GetFileLastModifiedUtc(string filePath)
        {
            // Check if this is a URL (SharePoint, OneDrive, etc.)
            if (Uri.TryCreate(filePath, UriKind.Absolute, out Uri uri) && 
                (uri.Scheme == "http" || uri.Scheme == "https"))
            {
                // For remote files, return current time since we can't easily get the actual modified time
                // In a production scenario, you might want to use SharePoint API calls here
                return DateTime.UtcNow;
            }
            else
            {
                // Local file - use original implementation
                return File.GetLastWriteTimeUtc(filePath);
            }
        }

        /// <summary>
        /// Checks if a source file (local or SharePoint URL) is accessible.
        /// For local files, uses File.Exists(). For URLs, assumes accessible (Excel will handle the error).
        /// </summary>
        /// <param name="filePath">The file path or URL to check</param>
        /// <returns>True if the source appears to be accessible, false otherwise</returns>
        private bool IsSourceFileAccessible(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            // Check if this is a URL (SharePoint, OneDrive, etc.)
            if (Uri.TryCreate(filePath, UriKind.Absolute, out Uri uri) &&
                (uri.Scheme == "http" || uri.Scheme == "https"))
            {
                // For remote files, assume accessible - Excel COM will handle the actual connection
                // In a production scenario, you might want to add a lightweight HTTP HEAD request here
                ErrorHandlingService.LogException(null, $"IsSourceFileAccessible: Treating URL as accessible: {filePath}");
                return true;
            }
            else
            {
                // Local file - use original File.Exists() check
                bool exists = File.Exists(filePath);
                ErrorHandlingService.LogException(null, $"IsSourceFileAccessible: Local file exists = {exists}: {filePath}");
                return exists;
            }
        }

        private async Task WriteLinkMetadataAsync(ExcelLinkMetadata linkMetadata, CustomXmlPart customXmlPart)
        {
            var serializer = new System.Xml.Serialization.XmlSerializer(typeof(ExcelLinkMetadata));
            using (var memoryStream = new System.IO.MemoryStream())
            {
                serializer.Serialize(memoryStream, linkMetadata);
                memoryStream.Position = 0;
                using (var targetStream = customXmlPart.GetStream(FileMode.Create))
                {
                    await memoryStream.CopyToAsync(targetStream);
                }
            }
        }

        private void ReleaseComObject(object comObject)
        {
            if (comObject != null && Marshal.IsComObject(comObject))
            {
                Marshal.ReleaseComObject(comObject);
            }
        }

        /// <summary>
        /// Executes COM operations with retry logic for transient RPC_E_SERVERCALL_RETRYLATER errors
        /// </summary>
        private async Task<T> ExecuteWithComRetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3, int delayMs = 500)
        {
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    return await operation();
                }
                catch (System.Runtime.InteropServices.COMException comEx) when (comEx.HResult == unchecked((int)0x8001010A) && attempt < maxRetries)
                {
                    // RPC_E_SERVERCALL_RETRYLATER - PowerPoint is busy, retry after delay
                    ErrorHandlingService.LogException(comEx,
                        $"PowerPoint busy on attempt {attempt}/{maxRetries}. Retrying in {delayMs}ms...");
                    await Task.Delay(delayMs);
                    delayMs *= 2; // Exponential backoff
                }
            }
            
            // Final attempt without catching the exception
            return await operation();
        }

        /// <summary>
        /// Executes COM operations with retry logic for transient RPC_E_SERVERCALL_RETRYLATER errors (void version)
        /// </summary>
        private async Task ExecuteWithComRetryAsync(Func<Task> operation, int maxRetries = 3, int delayMs = 500)
        {
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    await operation();
                    return;
                }
                catch (System.Runtime.InteropServices.COMException comEx) when (comEx.HResult == unchecked((int)0x8001010A) && attempt < maxRetries)
                {
                    // RPC_E_SERVERCALL_RETRYLATER - PowerPoint is busy, retry after delay
                    ErrorHandlingService.LogException(comEx,
                        $"PowerPoint busy on attempt {attempt}/{maxRetries}. Retrying in {delayMs}ms...");
                    await Task.Delay(delayMs);
                    delayMs *= 2; // Exponential backoff
                }
            }
            
            // Final attempt without catching the exception
            await operation();
        }        // Get all links from presentation metadata
        public async Task<System.Collections.Generic.List<ChartLink>> GetAllLinksAsync()
        {
            var links = new System.Collections.Generic.List<ChartLink>();
            try
            {
                // Use COM retry logic to handle transient RPC_E_SERVERCALL_RETRYLATER errors
                return await ExecuteWithComRetryAsync(async () =>
                {
                    // COM objects must be accessed from the UI thread - removed Task.Run() wrapper to fix RPC_E_SERVERCALL_RETRYLATER
                    // Safely check for active presentation
                    PowerPoint.Presentation activePresentation = null;
                    try
                    {
                        activePresentation = Globals.ThisAddIn.Application.ActivePresentation;
                        if (activePresentation == null) return links;
                    }
                    catch (System.Runtime.InteropServices.COMException ex) when (ex.ErrorCode == unchecked((int)0x80048240))
                    {
                        // COM exception 0x80048240: "There is no active presentation"
                        return links; // Return empty list when no presentation is open
                    }
                    catch (System.Runtime.InteropServices.COMException ex)
                    {
                        ErrorHandlingService.LogException(ex, "GetAllLinksAsync: COM exception accessing active presentation");
                        return links; // Return empty list on COM errors
                    }

                    // Get custom document properties using reflection-based COM access
                    object customPropsObject = activePresentation.CustomDocumentProperties;
                    if (customPropsObject == null) return links;

                    Type customPropsType = customPropsObject.GetType();
                    int count = (int)customPropsType.InvokeMember("Count",
                        System.Reflection.BindingFlags.GetProperty, null, customPropsObject, null);

                    var staleLinks = new List<string>(); // Track stale links for cleanup

                    for (int i = 1; i <= count; i++)
                {
                    try
                    {
                        object property = customPropsType.InvokeMember("Item",
                            System.Reflection.BindingFlags.GetProperty, null, customPropsObject, new object[] { i });

                        if (property != null)
                        {
                            Type propertyType = property.GetType();
                            string name = (string)propertyType.InvokeMember("Name",
                                System.Reflection.BindingFlags.GetProperty, null, property, null);

                            if (name.StartsWith("QuantBoostLink_"))
                            {
                                string value = (string)propertyType.InvokeMember("Value",
                                    System.Reflection.BindingFlags.GetProperty, null, property, null);

                                var metadata = DeserializeFromSimpleJson(value);
                                if (metadata != null)
                                {
                                    var chartLink = ConvertToChartLink(metadata);

                                    // Validate that the shape still exists in the presentation
                                    bool shapeExists = await ValidateShapeExistsAsync(chartLink);
                                    if (shapeExists)
                                    {
                                        // Diagnostic logging for loaded timestamps
                                        // Debug logging removed for production
                                        // ErrorHandlingService.LogException(null,
                                        //     $"TIMESTAMP DEBUG - GetAllLinksAsync LOADED: LinkId={chartLink.LinkId}, LastRefreshedUtc={chartLink.LastRefreshedUtc?.ToString("yyyy-MM-dd HH:mm:ss") ?? "NULL"}");

                                        links.Add(chartLink);
                                    }
                                    else
                                    {
                                        // Shape no longer exists - mark for cleanup
                                        staleLinks.Add(chartLink.LinkId);
                                        ErrorHandlingService.LogException(null,
                                            $"GetAllLinksAsync: Found stale link {chartLink.LinkId} - shape no longer exists on slide {chartLink.SlideIndex}");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorHandlingService.LogException(ex, $"Error reading link property at index {i}");
                    }
                }

                    // Clean up any stale links found during validation
                    if (staleLinks.Count > 0)
                    {
                        ErrorHandlingService.LogException(null,
                            $"GetAllLinksAsync: Found {staleLinks.Count} stale links (shapes deleted): {string.Join(", ", staleLinks)}");

                        // Automatically clean up stale links (remove orphaned metadata)
                        await CleanupStaleLinksAsync(staleLinks);
                    }

                    return links;
                });
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error getting all links");
                return links;
            }
        }        // Convert ChartLink to ExcelLinkMetadata and call RefreshLinkAsync
        public async Task RefreshChartAsync(ChartLink link)
        {
            if (link == null) return;
            
            var metadata = ConvertToExcelLinkMetadata(link);
            await RefreshLinkAsync(metadata);
            
            // Update the link's last refreshed time and user
            var timestampBefore = link.LastRefreshedUtc;
            link.LastRefreshedUtc = DateTime.UtcNow;
            link.ModifiedBy = Environment.UserName;
            
            // Diagnostic logging for timestamp tracking
            // Debug logging removed for production
            // ErrorHandlingService.LogException(null,
            //     $"TIMESTAMP DEBUG - RefreshChartAsync: LinkId={link.LinkId}, Before={timestampBefore?.ToString("yyyy-MM-dd HH:mm:ss") ?? "NULL"}, After={link.LastRefreshedUtc?.ToString("yyyy-MM-dd HH:mm:ss") ?? "NULL"}");
            link.ErrorState = null; // Clear any previous error state on successful refresh
            await UpdateLinkMetadataAsync(link);
        }// Bulk refresh (call RefreshChartAsync for each link with error handling)
        public async Task RefreshAllChartsAsync(IProgress<ProgressState> progress)
        {
            var links = await GetAllLinksAsync();
            int count = links.Count;
            int i = 0;
            int successCount = 0;
            int errorCount = 0;
            var staleLinks = new List<string>(); // Track stale links for cleanup

            foreach (var link in links)
            {
                try
                {
                    await RefreshChartAsync(link);
                    successCount++;
                    ErrorHandlingService.LogException(null, 
                        $"RefreshAllChartsAsync: Successfully refreshed link {link.LinkId}");
                }
                catch (KeyNotFoundException ex) when (ex.Message.Contains("Shape with LinkId") && ex.Message.Contains("not found"))
                {
                    // Handle stale links (metadata exists but shape was deleted)
                    errorCount++;
                    staleLinks.Add(link.LinkId);
                    ErrorHandlingService.LogException(ex, 
                        $"RefreshAllChartsAsync: Skipping stale link {link.LinkId} - shape no longer exists");
                }
                catch (FileNotFoundException ex)
                {
                    // Handle missing source files
                    errorCount++;
                    ErrorHandlingService.LogException(ex, 
                        $"RefreshAllChartsAsync: Skipping link {link.LinkId} - source file not found: {link.SourceFilePath}");
                }
                catch (Exception ex)
                {
                    // Handle other unexpected errors
                    errorCount++;
                    ErrorHandlingService.LogException(ex, 
                        $"RefreshAllChartsAsync: Failed to refresh link {link.LinkId}");
                }

                i++;
                progress?.Report(
                    ProgressState.ForStep(
                        $"Refreshing {i} of {count} (✓{successCount} ✗{errorCount})",
                        (int)((i * 100.0) / count)
                    )
                );
            }            // Log summary
            ErrorHandlingService.LogException(null, 
                $"RefreshAllChartsAsync completed: {successCount} successful, {errorCount} errors");
            
            if (staleLinks.Count > 0)
            {
                ErrorHandlingService.LogException(null, 
                    $"Found {staleLinks.Count} stale links (shapes deleted): {string.Join(", ", staleLinks)}");
                
                // Automatically clean up stale links (remove orphaned metadata)
                await CleanupStaleLinksAsync(staleLinks);
            }
        }

        /// <summary>
        /// Refreshes only the active links (where IsActive = true)
        /// </summary>
        public async Task RefreshActiveChartsAsync(IProgress<ProgressState> progress)
        {
            var allLinks = await GetAllLinksAsync();
            var activeLinks = allLinks.Where(link => link.IsActive).ToList();
            
            int count = activeLinks.Count;
            int i = 0;
            int successCount = 0;
            int errorCount = 0;
            var staleLinks = new List<string>(); // Track stale links for cleanup

            foreach (var link in activeLinks)
            {
                try
                {
                    await RefreshChartAsync(link);
                    successCount++;
                    ErrorHandlingService.LogException(null,
                        $"RefreshActiveChartsAsync: Successfully refreshed active link {link.LinkId}");
                }
                catch (KeyNotFoundException ex) when (ex.Message.Contains("Shape with LinkId") && ex.Message.Contains("not found"))
                {
                    // Handle stale links (metadata exists but shape was deleted)
                    errorCount++;
                    staleLinks.Add(link.LinkId);
                    ErrorHandlingService.LogException(ex,
                        $"RefreshActiveChartsAsync: Skipping stale active link {link.LinkId} - shape no longer exists");
                }
                catch (FileNotFoundException ex)
                {
                    // Handle missing source files
                    errorCount++;
                    ErrorHandlingService.LogException(ex,
                        $"RefreshActiveChartsAsync: Skipping active link {link.LinkId} - source file not found: {link.SourceFilePath}");
                }
                catch (Exception ex)
                {
                    // Handle other unexpected errors
                    errorCount++;
                    ErrorHandlingService.LogException(ex,
                        $"RefreshActiveChartsAsync: Failed to refresh active link {link.LinkId}");
                }

                i++;
                progress?.Report(
                    ProgressState.ForStep(
                        $"Refreshing active links {i} of {count} (✓{successCount} ✗{errorCount})",
                        (int)((i * 100.0) / count)
                    )
                );
            }

            // Log summary
            ErrorHandlingService.LogException(null,
                $"RefreshActiveChartsAsync completed: {successCount} successful, {errorCount} errors from {count} active links");
            
            if (staleLinks.Count > 0)
            {
                ErrorHandlingService.LogException(null,
                    $"Found {staleLinks.Count} stale active links (shapes deleted): {string.Join(", ", staleLinks)}");
                
                // Automatically clean up stale links (remove orphaned metadata)
                await CleanupStaleLinksAsync(staleLinks);
            }
        }

        /// <summary>
        /// Validates that a shape referenced by a ChartLink still exists in the presentation
        /// </summary>
        /// <param name="chartLink">The ChartLink to validate</param>
        /// <returns>True if the shape exists, false otherwise</returns>
        private async Task<bool> ValidateShapeExistsAsync(ChartLink chartLink)
        {
            if (chartLink == null || string.IsNullOrEmpty(chartLink.LinkId) || chartLink.SlideIndex <= 0)
                return false;

            try
            {
                string altTextToFind = $"QuantBoostLink:{chartLink.LinkId}";

                using (var pptWrapper = new PowerPointComWrapper(Globals.ThisAddIn.Application))
                {
                    var slide = pptWrapper.GetSlideByIndex(chartLink.SlideIndex);
                    if (slide == null) return false;

                    var shape = pptWrapper.GetShapeByAltText(slide, altTextToFind);
                    return shape != null;
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    $"ValidateShapeExistsAsync: Error validating shape for link {chartLink.LinkId}");
                return false;
            }
        }

        /// <summary>
        /// Automatically removes stale link metadata when shapes have been manually deleted
        /// </summary>
        private async Task CleanupStaleLinksAsync(List<string> staleLinkIds)
        {
            if (staleLinkIds == null || staleLinkIds.Count == 0) return;

            try
            {
                var activePresentation = Globals.ThisAddIn.Application.ActivePresentation;
                if (activePresentation == null) return;

                int cleanedCount = 0;
                foreach (string linkId in staleLinkIds)
                {
                    try
                    {
                        await RemoveMetadataViaCOMAsync(linkId, activePresentation);
                        cleanedCount++;
                        ErrorHandlingService.LogException(null, 
                            $"CleanupStaleLinksAsync: Removed stale metadata for link {linkId}");
                    }
                    catch (Exception ex)
                    {
                        ErrorHandlingService.LogException(ex, 
                            $"CleanupStaleLinksAsync: Failed to remove metadata for link {linkId}");
                    }
                }

                ErrorHandlingService.LogException(null, 
                    $"CleanupStaleLinksAsync: Cleaned up {cleanedCount} of {staleLinkIds.Count} stale links");
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, 
                    "CleanupStaleLinksAsync: Failed to perform stale link cleanup");
            }
        }        // Breaks the link for the given ChartLink (removes metadata only, keeps the shape)
        public async Task<bool> BreakLinkAsync(ChartLink link)
        {
            if (link == null) return false;
            try
            {
                // DO NOT remove the shape from PowerPoint - just break the link metadata
                // The shape should remain as a static image/object in the presentation
                
                // Remove the custom XML part (metadata) from the slide
                var activePresentation = Globals.ThisAddIn.Application.ActivePresentation;
                string presentationPath = activePresentation.FullName;
                
                // Check if presentation is saved (has a file path)
                if (string.IsNullOrEmpty(presentationPath))
                {
                    throw new InvalidOperationException("Cannot remove Excel link metadata from an unsaved presentation. Please save the presentation first.");
                }
                
                // Remove metadata using PowerPoint COM interface to avoid file locking issues
                await RemoveMetadataViaCOMAsync(link.LinkId, activePresentation);
                return true;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Failed to break link");
                return false;
            }
        }/// <summary>
        /// Exports the given links to a CSV file (shows SaveFileDialog)
        /// </summary>
        public Task<bool> ExportLinksToCsvAsync(List<ChartLink> links)
        {
            if (links == null || links.Count == 0) return Task.FromResult(false);
            try
            {
                string filePath = null;
                using (var sfd = new SaveFileDialog { Filter = "CSV Files (*.csv)|*.csv", Title = "Export Links to CSV" })
                {
                    if (sfd.ShowDialog() == DialogResult.OK)
                        filePath = sfd.FileName;
                }
                if (string.IsNullOrEmpty(filePath)) return Task.FromResult(false);
                using (var writer = new StreamWriter(filePath))
                {
                    writer.WriteLine("LinkId,LinkType,SlideIndex,WorksheetName,ChartNameOrId,SourceFilePath,IsActive,LastRefreshedUtc,ModifiedBy,ErrorState");
                    foreach (var link in links)
                    {
                        writer.WriteLine($"{link.LinkId},{link.LinkType},{link.SlideIndex},'{link.WorksheetName}','{link.ChartNameOrId}',{link.SourceFilePath},{link.IsActive},{link.LastRefreshedUtc},{link.ModifiedBy},{link.ErrorState}");
                    }
                }
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Failed to export links to CSV");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Updates the metadata for a given ChartLink in the presentation
        public async Task<bool> UpdateLinkMetadataAsync(ChartLink link)
        {
            if (link == null) return false;
            try
            {
                // Use COM retry logic to handle transient RPC_E_SERVERCALL_RETRYLATER errors
                return await ExecuteWithComRetryAsync(async () =>
                {
                    var activePresentation = Globals.ThisAddIn.Application.ActivePresentation;
                    string presentationPath = activePresentation.FullName;
                    
                    // Check if presentation is saved (has a file path)
                    if (string.IsNullOrEmpty(presentationPath))
                    {
                        throw new InvalidOperationException("Cannot update Excel link metadata in an unsaved presentation. Please save the presentation first.");
                    }
                    
                    // Debug logging removed for production
                    // ErrorHandlingService.LogException(null,
                    //     $"TIMESTAMP DEBUG - UpdateLinkMetadataAsync BEFORE: LinkId={link.LinkId}, LastRefreshedUtc={link.LastRefreshedUtc?.ToString("yyyy-MM-dd HH:mm:ss") ?? "NULL"}");
                    
                    // Update metadata using PowerPoint COM interface to avoid file locking issues
                    await UpdateMetadataViaCOMAsync(link, activePresentation);
                    
                    // Debug logging removed for production
                    // ErrorHandlingService.LogException(null,
                    //     $"TIMESTAMP DEBUG - UpdateLinkMetadataAsync AFTER: LinkId={link.LinkId}, LastRefreshedUtc={link.LastRefreshedUtc?.ToString("yyyy-MM-dd HH:mm:ss") ?? "NULL"}");
                    
                    return true;
                });
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Failed to update link metadata");
                return false;
            }
        }        /// <summary>
        /// Stores metadata using PowerPoint's COM interface to avoid file locking issues
        /// </summary>
        private async Task StoreMetadataViaCOMAsync(ExcelLinkMetadata linkMetadata, PowerPoint.Presentation presentation)
        {
            await Task.Run(() =>
            {
                try
                {
                    // Serialize metadata to JSON using simple serialization
                    string jsonMetadata = SerializeToSimpleJson(linkMetadata);
                    
                    // Store in PowerPoint custom document properties
                    string propertyName = $"QuantBoostLink_{linkMetadata.LinkId}";
                    
                    // Get the custom document properties collection - no casting, work with raw COM object
                    object customPropsObject = presentation.CustomDocumentProperties;
                    
                    if (customPropsObject == null)
                    {
                        throw new InvalidOperationException("Unable to access CustomDocumentProperties");
                    }

                    // Use reflection to access COM object properties and methods
                    Type customPropsType = customPropsObject.GetType();
                    
                    // Remove existing property if it exists using reflection
                    try
                    {
                        // Get Count property
                        int count = (int)customPropsType.InvokeMember("Count", 
                            System.Reflection.BindingFlags.GetProperty, null, customPropsObject, null);
                        
                        // Iterate through properties (1-based indexing)
                        for (int i = count; i >= 1; i--)
                        {
                            // Get property by index
                            object property = customPropsType.InvokeMember("Item", 
                                System.Reflection.BindingFlags.GetProperty, null, customPropsObject, new object[] { i });
                            
                            if (property != null)
                            {
                                // Get property name
                                Type propertyType = property.GetType();
                                string currentName = (string)propertyType.InvokeMember("Name", 
                                    System.Reflection.BindingFlags.GetProperty, null, property, null);
                                
                                if (currentName == propertyName)
                                {
                                    // Delete the property
                                    propertyType.InvokeMember("Delete", 
                                        System.Reflection.BindingFlags.InvokeMethod, null, property, null);
                                    break;
                                }
                            }
                        }
                    }
                    catch { /* Property doesn't exist, which is fine */ }
                    
                    // Add new property with metadata using reflection
                    customPropsType.InvokeMember("Add", System.Reflection.BindingFlags.InvokeMethod, null, customPropsObject, 
                        new object[] { propertyName, false, 4 /* msoPropertyTypeString */, jsonMetadata });
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Failed to store metadata via COM: {ex.Message}", ex);
                }
            });
        }        /// <summary>
        /// Removes metadata using PowerPoint's COM interface to avoid file locking issues
        /// </summary>
        private async Task RemoveMetadataViaCOMAsync(string linkId, PowerPoint.Presentation presentation)
        {
            await Task.Run(() =>
            {
                try
                {
                    string propertyName = $"QuantBoostLink_{linkId}";
                    
                    // Get the custom document properties collection - no casting, work with raw COM object
                    object customPropsObject = presentation.CustomDocumentProperties;
                    
                    if (customPropsObject == null)
                    {
                        throw new InvalidOperationException("Unable to access CustomDocumentProperties");
                    }

                    // Use reflection to access COM object properties and methods
                    Type customPropsType = customPropsObject.GetType();
                    
                    // Get Count property
                    int count = (int)customPropsType.InvokeMember("Count", 
                        System.Reflection.BindingFlags.GetProperty, null, customPropsObject, null);
                    
                    // Find and remove the property
                    for (int i = count; i >= 1; i--)
                    {
                        // Get property by index
                        object property = customPropsType.InvokeMember("Item", 
                            System.Reflection.BindingFlags.GetProperty, null, customPropsObject, new object[] { i });
                        
                        if (property != null)
                        {
                            // Get property name
                            Type propertyType = property.GetType();
                            string currentName = (string)propertyType.InvokeMember("Name", 
                                System.Reflection.BindingFlags.GetProperty, null, property, null);
                            
                            if (currentName == propertyName)
                            {
                                // Delete the property
                                propertyType.InvokeMember("Delete", 
                                    System.Reflection.BindingFlags.InvokeMethod, null, property, null);
                                break;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Failed to remove metadata via COM: {ex.Message}", ex);
                }
            });
        }        /// <summary>
        /// Updates metadata using PowerPoint's COM interface to avoid file locking issues
        /// </summary>
        private async Task UpdateMetadataViaCOMAsync(ChartLink link, PowerPoint.Presentation presentation)
        {
            await Task.Run(() =>
            {
                try
                {
                    string jsonMetadata = SerializeToSimpleJson(link);
                    string propertyName = $"QuantBoostLink_{link.LinkId}";
                    
                    // Get the custom document properties collection - no casting, work with raw COM object
                    object customPropsObject = presentation.CustomDocumentProperties;
                    
                    if (customPropsObject == null)
                    {
                        throw new InvalidOperationException("Unable to access CustomDocumentProperties");
                    }

                    // Use reflection to access COM object properties and methods
                    Type customPropsType = customPropsObject.GetType();
                    
                    // Get Count property
                    int count = (int)customPropsType.InvokeMember("Count", 
                        System.Reflection.BindingFlags.GetProperty, null, customPropsObject, null);
                    
                    // Find and update the property
                    bool found = false;
                    for (int i = count; i >= 1; i--)
                    {
                        // Get property by index
                        object property = customPropsType.InvokeMember("Item", 
                            System.Reflection.BindingFlags.GetProperty, null, customPropsObject, new object[] { i });
                        
                        if (property != null)
                        {
                            // Get property name
                            Type propertyType = property.GetType();
                            string currentName = (string)propertyType.InvokeMember("Name", 
                                System.Reflection.BindingFlags.GetProperty, null, property, null);
                            
                            if (currentName == propertyName)
                            {
                                // Update the property value
                                propertyType.InvokeMember("Value", 
                                    System.Reflection.BindingFlags.SetProperty, null, property, new object[] { jsonMetadata });
                                found = true;
                                break;
                            }
                        }
                    }
                    
                    // If not found, add new property
                    if (!found)
                    {
                        customPropsType.InvokeMember("Add", System.Reflection.BindingFlags.InvokeMethod, null, customPropsObject, 
                            new object[] { propertyName, false, 4 /* msoPropertyTypeString */, jsonMetadata });
                    }
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Failed to update metadata via COM: {ex.Message}", ex);
                }
            });
        }

        /// <summary>
        /// Simple JSON serialization for ExcelLinkMetadata
        /// </summary>
        private string SerializeToSimpleJson(ExcelLinkMetadata metadata)
        {
            return $@"{{
  ""LinkId"": ""{metadata.LinkId}"",
  ""LinkType"": ""{metadata.LinkType}"",
  ""SourceFilePath"": ""{metadata.SourceFilePath?.Replace("\"", "\\\"")}"",
  ""SourceFileHash"": ""{metadata.SourceFileHash}"",
  ""SourceFileLastModifiedUtc"": ""{metadata.SourceFileLastModifiedUtc:yyyy-MM-ddTHH:mm:ss.fffZ}"",
  ""WorksheetName"": ""{metadata.WorksheetName?.Replace("\"", "\\\"")}"",
  ""SourceIdentifier"": ""{metadata.SourceIdentifier?.Replace("\"", "\\\"")}"",
  ""SlideIndex"": {metadata.SlideIndex},
  ""PowerPointShapeId"": ""{metadata.PowerPointShapeId?.Replace("\"", "\\\"")}"",
  ""ModifiedBy"": ""{metadata.ModifiedBy?.Replace("\"", "\\\"")}"",
  ""IsActive"": {metadata.IsActive.ToString().ToLower()}
}}";
        }        /// <summary>
        /// Simple JSON serialization for ChartLink
        /// </summary>
        private string SerializeToSimpleJson(ChartLink link)
        {
            return $@"{{
  ""LinkId"": ""{link.LinkId}"",
  ""LinkType"": ""{link.LinkType?.Replace("\"", "\\\"")}"",
  ""SourceFilePath"": ""{link.SourceFilePath?.Replace("\"", "\\\"")}"",  ""WorksheetName"": ""{link.WorksheetName?.Replace("\"", "\\\"")}"",
  ""ChartNameOrId"": ""{link.ChartNameOrId?.Replace("\"", "\\\"")}"",  ""SourceRange"": ""{link.SourceRange?.Replace("\"", "\\\"")}"",
  ""SourceIdentifier"": ""{(!string.IsNullOrEmpty(link.SourceRange) ? link.SourceRange : link.ChartNameOrId)?.Replace("\"", "\\\"")}"",
  ""SlideIndex"": {link.SlideIndex},
  ""PowerPointShapeId"": ""{link.PowerPointShapeId?.Replace("\"", "\\\"")}"",
  ""IsActive"": {link.IsActive.ToString().ToLower()},
  ""LastRefreshedUtc"": ""{link.LastRefreshedUtc?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") ?? ""}"",
  ""ModifiedBy"": ""{link.ModifiedBy?.Replace("\"", "\\\"") ?? ""}""
}}";
        }        /// <summary>
        /// Simple JSON deserialization for ExcelLinkMetadata
        /// </summary>
        private ExcelLinkMetadata DeserializeFromSimpleJson(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json)) return null;

                var metadata = new ExcelLinkMetadata();
                
                // Extract basic properties using simple string parsing
                metadata.LinkId = ExtractJsonValue(json, "LinkId");
                
                string linkTypeStr = ExtractJsonValue(json, "LinkType");
                if (!string.IsNullOrEmpty(linkTypeStr) && Enum.TryParse<ExcelLinkType>(linkTypeStr, out ExcelLinkType linkType))
                    metadata.LinkType = linkType;
                
                metadata.SourceFilePath = ExtractJsonValue(json, "SourceFilePath");
                metadata.SourceFileHash = ExtractJsonValue(json, "SourceFileHash");
                metadata.WorksheetName = ExtractJsonValue(json, "WorksheetName");
                
                // Handle both old and new JSON formats for SourceIdentifier
                metadata.SourceIdentifier = ExtractJsonValue(json, "SourceIdentifier");
                if (string.IsNullOrEmpty(metadata.SourceIdentifier))
                {
                    // Fallback to old format: use SourceRange first, then ChartNameOrId
                    string sourceRange = ExtractJsonValue(json, "SourceRange");
                    string chartNameOrId = ExtractJsonValue(json, "ChartNameOrId");
                    metadata.SourceIdentifier = !string.IsNullOrEmpty(sourceRange) ? sourceRange : chartNameOrId;
                    
                    // Log the conversion for debugging
                    ErrorHandlingService.LogException(null, 
                        $"DeserializeFromSimpleJson: Converted from old format - LinkId: {metadata.LinkId}, SourceRange: '{sourceRange}', ChartNameOrId: '{chartNameOrId}', Final SourceIdentifier: '{metadata.SourceIdentifier}'");
                }
                else
                {
                    // Log successful new format read
                    ErrorHandlingService.LogException(null, 
                        $"DeserializeFromSimpleJson: Read new format - LinkId: {metadata.LinkId}, SourceIdentifier: '{metadata.SourceIdentifier}'");
                }
                
                metadata.PowerPointShapeId = ExtractJsonValue(json, "PowerPointShapeId");
                metadata.ModifiedBy = ExtractJsonValue(json, "ModifiedBy");
                
                // Extract LastRefreshedUtc timestamp
                string lastRefreshedStr = ExtractJsonValue(json, "LastRefreshedUtc");
                if (!string.IsNullOrEmpty(lastRefreshedStr) && DateTime.TryParse(lastRefreshedStr, null, System.Globalization.DateTimeStyles.RoundtripKind, out DateTime lastRefreshed))
                    metadata.LastRefreshedUtc = lastRefreshed;
                
                string slideIndexStr = ExtractJsonValue(json, "SlideIndex");
                if (!string.IsNullOrEmpty(slideIndexStr) && int.TryParse(slideIndexStr, out int slideIndex))
                    metadata.SlideIndex = slideIndex;
                  string isActiveStr = ExtractJsonValue(json, "IsActive");
                if (!string.IsNullOrEmpty(isActiveStr) && bool.TryParse(isActiveStr, out bool isActive))
                    metadata.IsActive = isActive;

                // Infer LinkType if not set or Unknown, based on SourceIdentifier pattern
                if (metadata.LinkType == ExcelLinkType.Unknown && !string.IsNullOrEmpty(metadata.SourceIdentifier))
                {
                    metadata.LinkType = InferLinkTypeFromSourceIdentifier(metadata.SourceIdentifier);
                    
                    // Log the inference for debugging
                    ErrorHandlingService.LogException(null, 
                        $"DeserializeFromSimpleJson: Inferred LinkType '{metadata.LinkType}' from SourceIdentifier '{metadata.SourceIdentifier}'");
                }

                return metadata;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Failed to deserialize metadata JSON");
                return null;
            }
        }

        /// <summary>
        /// Helper method to extract a value from simple JSON format
        /// </summary>
        private string ExtractJsonValue(string json, string key)
        {
            try
            {
                string pattern = $"\"{key}\":\\s*\"([^\"]*?)\"";
                var match = System.Text.RegularExpressions.Regex.Match(json, pattern);
                if (match.Success)
                    return match.Groups[1].Value.Replace("\\\"", "\"");
                
                // Try numeric/boolean values without quotes
                pattern = $"\"{key}\":\\s*([^,\\}}]+)";
                match = System.Text.RegularExpressions.Regex.Match(json, pattern);
                if (match.Success)
                    return match.Groups[1].Value.Trim();
                
                return null;
            }
            catch
            {
                return null;
            }        }

        /// <summary>
        /// Infers the LinkType based on the pattern of the SourceIdentifier
        /// </summary>
        private ExcelLinkType InferLinkTypeFromSourceIdentifier(string sourceIdentifier)
        {
            if (string.IsNullOrEmpty(sourceIdentifier))
                return ExcelLinkType.Unknown;

            // Check if it looks like an Excel range (contains $ and : characters)
            // Examples: $J$58:$T$70, A1:B10, $A$1:$Z$100
            if (sourceIdentifier.Contains(":") && (sourceIdentifier.Contains("$") || System.Text.RegularExpressions.Regex.IsMatch(sourceIdentifier, @"^[A-Z]+\d+:[A-Z]+\d+$")))
            {
                return ExcelLinkType.Range;
            }

            // If it doesn't match range patterns, assume it's a chart name
            return ExcelLinkType.Chart;
        }

        /// <summary>
        /// Convert ExcelLinkMetadata to ChartLink
        /// </summary>
        private ChartLink ConvertToChartLink(ExcelLinkMetadata metadata)
        {
            if (metadata == null) return null;

            return new ChartLink
            {
                LinkId = metadata.LinkId,
                SourceFilePath = metadata.SourceFilePath,
                SourceFileHash = metadata.SourceFileHash,
                SourceFileLastModifiedUtc = metadata.SourceFileLastModifiedUtc,
                WorksheetName = metadata.WorksheetName,
                ChartNameOrId = metadata.SourceIdentifier,
                SourceRange = metadata.LinkType == ExcelLinkType.Range ? metadata.SourceIdentifier : null,
                PowerPointShapeId = metadata.PowerPointShapeId,
                SlideIndex = metadata.SlideIndex,
                IsActive = metadata.IsActive,
                LinkType = metadata.LinkType.ToString(),
                ModifiedBy = metadata.ModifiedBy,
                LastRefreshedUtc = metadata.LastRefreshedUtc,
                ErrorState = metadata.ErrorState,
                Notes = metadata.Notes,
                PreserveFormatting = metadata.PreserveFormatting
            };
        }

        /// <summary>
        /// Convert ChartLink to ExcelLinkMetadata
        /// </summary>
        private ExcelLinkMetadata ConvertToExcelLinkMetadata(ChartLink link)
        {
            if (link == null) return null;

            ExcelLinkType linkType = ExcelLinkType.Unknown;
            if (!string.IsNullOrEmpty(link.LinkType) && Enum.TryParse<ExcelLinkType>(link.LinkType, out ExcelLinkType parsedType))
                linkType = parsedType;

            return new ExcelLinkMetadata
            {
                LinkId = link.LinkId,
                LinkType = linkType,
                SourceFilePath = link.SourceFilePath,
                SourceFileHash = link.SourceFileHash,
                SourceFileLastModifiedUtc = link.SourceFileLastModifiedUtc,
                WorksheetName = link.WorksheetName,
                SourceIdentifier = !string.IsNullOrEmpty(link.SourceRange) ? link.SourceRange : link.ChartNameOrId,
                PowerPointShapeId = link.PowerPointShapeId,
                SlideIndex = link.SlideIndex,
                IsActive = link.IsActive,
                ModifiedBy = link.ModifiedBy,                LastRefreshedUtc = link.LastRefreshedUtc,
                ErrorState = link.ErrorState,
                Notes = link.Notes,
                PreserveFormatting = link.PreserveFormatting
            };
        }

        /// <summary>
        /// Attempts to paste from clipboard with retry logic and fallback formats to handle timing issues
        /// </summary>
        private async Task<PowerPoint.ShapeRange> PasteWithFallbackAsync(PowerPointComWrapper pptWrapper, PowerPoint.Slide targetSlide)
        {
            // Array of paste formats to try in order of preference
            PowerPoint.PpPasteDataType[] formatFallbacks = new[]
            {
                PowerPoint.PpPasteDataType.ppPasteEnhancedMetafile,
                PowerPoint.PpPasteDataType.ppPasteBitmap,
                PowerPoint.PpPasteDataType.ppPasteDefault
            };

            const int maxRetries = 3;
            const int delayBetweenRetries = 200; // milliseconds

            Exception lastException = null;

            for (int formatIndex = 0; formatIndex < formatFallbacks.Length; formatIndex++)
            {
                var format = formatFallbacks[formatIndex];
                
                for (int retry = 0; retry < maxRetries; retry++)
                {
                    try
                    {
                        // Add small delay before each attempt to ensure clipboard is ready
                        if (retry > 0 || formatIndex > 0)
                        {
                            await Task.Delay(delayBetweenRetries * (retry + 1));
                        }

                        ErrorHandlingService.LogException(null, 
                            $"Attempting PasteSpecial with format {format}, attempt {retry + 1}/{maxRetries}");

                        var result = pptWrapper.PasteSpecial(targetSlide, format);
                        if (result != null && result.Count > 0)
                        {
                            ErrorHandlingService.LogException(null, 
                                $"PasteSpecial successful with format {format} on attempt {retry + 1}");
                            return result;
                        }
                    }
                    catch (Exception ex)
                    {
                        lastException = ex;
                        ErrorHandlingService.LogException(ex, 
                            $"PasteSpecial failed with format {format}, attempt {retry + 1}/{maxRetries}: {ex.Message}");
                          // If this was the last retry for this format, continue to next format
                        if (retry == maxRetries - 1)
                        {
                            break;
                        }
                    }
                }
            }
            
            // If we get here, all formats and retries failed
            throw new Exception($"Failed to paste after trying all formats and retries. Last error: {lastException?.Message}", lastException);
        }

        /// <summary>
        /// Diagnostic method to show what's actually stored in the presentation
        /// </summary>
        public async Task DiagnoseLinkMetadataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // Safely check for active presentation
                    PowerPoint.Presentation activePresentation = null;
                    try
                    {
                        activePresentation = Globals.ThisAddIn.Application.ActivePresentation;
                        if (activePresentation == null) return;
                    }
                    catch (System.Runtime.InteropServices.COMException ex) when (ex.ErrorCode == unchecked((int)0x80048240))
                    {
                        // COM exception 0x80048240: "There is no active presentation"
                        ErrorHandlingService.LogException(null, "DiagnoseLinkMetadataAsync: No active presentation to diagnose");
                        return;
                    }
                    catch (System.Runtime.InteropServices.COMException ex)
                    {
                        ErrorHandlingService.LogException(ex, "DiagnoseLinkMetadataAsync: COM exception accessing active presentation");
                        return;
                    }

                    ErrorHandlingService.LogException(null, "=== LINK METADATA DIAGNOSIS ===");

                    object customPropsObject = activePresentation.CustomDocumentProperties;
                    if (customPropsObject == null) 
                    {
                        ErrorHandlingService.LogException(null, "No CustomDocumentProperties found");
                        return;
                    }

                    Type customPropsType = customPropsObject.GetType();
                    int count = (int)customPropsType.InvokeMember("Count", 
                        System.Reflection.BindingFlags.GetProperty, null, customPropsObject, null);

                    ErrorHandlingService.LogException(null, $"Total custom properties: {count}");

                    for (int i = 1; i <= count; i++)
                    {
                        try
                        {
                            object property = customPropsType.InvokeMember("Item", 
                                System.Reflection.BindingFlags.GetProperty, null, customPropsObject, new object[] { i });
                            
                            if (property != null)
                            {
                                Type propertyType = property.GetType();
                                string name = (string)propertyType.InvokeMember("Name", 
                                    System.Reflection.BindingFlags.GetProperty, null, property, null);
                                
                                if (name.StartsWith("QuantBoostLink_"))
                                {
                                    string value = (string)propertyType.InvokeMember("Value", 
                                        System.Reflection.BindingFlags.GetProperty, null, property, null);
                                    
                                    ErrorHandlingService.LogException(null, $"Property: {name}");
                                    ErrorHandlingService.LogException(null, $"Raw JSON: {value}");
                                    
                                    // Check if LinkType is present
                                    if (value.Contains("\"LinkType\""))
                                    {
                                        string linkType = ExtractJsonValue(value, "LinkType");
                                        ErrorHandlingService.LogException(null, $"LinkType found: '{linkType}'");
                                    }
                                    else
                                    {
                                        ErrorHandlingService.LogException(null, "LinkType NOT FOUND in JSON - needs repair!");
                                    }
                                    ErrorHandlingService.LogException(null, "---");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            ErrorHandlingService.LogException(ex, $"Error reading property at index {i}");
                        }
                    }                    ErrorHandlingService.LogException(null, "=== END DIAGNOSIS ===");
                }
                catch (Exception ex)
                {
                    ErrorHandlingService.LogException(ex, "Error during metadata diagnosis");
                }
            });
        }

        /// <summary>
        /// Repairs existing links by inferring and setting correct LinkType based on available metadata
        /// </summary>
        public async Task<int> RepairExistingLinksAsync()
        {
            return await Task.Run(() =>
            {
                int repairedCount = 0;
                try
                {
                    var activePresentation = Globals.ThisAddIn.Application.ActivePresentation;
                    if (activePresentation == null) return 0;

                    ErrorHandlingService.LogException(null, "=== STARTING LINK REPAIR ===");

                    object customPropsObject = activePresentation.CustomDocumentProperties;
                    if (customPropsObject == null) return 0;

                    Type customPropsType = customPropsObject.GetType();
                    int count = (int)customPropsType.InvokeMember("Count", 
                        System.Reflection.BindingFlags.GetProperty, null, customPropsObject, null);

                    for (int i = 1; i <= count; i++)
                    {
                        try
                        {
                            object property = customPropsType.InvokeMember("Item", 
                                System.Reflection.BindingFlags.GetProperty, null, customPropsObject, new object[] { i });
                            
                            if (property != null)
                            {
                                Type propertyType = property.GetType();
                                string name = (string)propertyType.InvokeMember("Name", 
                                    System.Reflection.BindingFlags.GetProperty, null, property, null);
                                
                                if (name.StartsWith("QuantBoostLink_"))
                                {
                                    string value = (string)propertyType.InvokeMember("Value", 
                                        System.Reflection.BindingFlags.GetProperty, null, property, null);
                                    
                                    // Check if this link needs repair (missing LinkType or LinkType is "Unknown")
                                    string currentLinkType = ExtractJsonValue(value, "LinkType");
                                    if (string.IsNullOrEmpty(currentLinkType) || currentLinkType == "Unknown")
                                    {
                                        ErrorHandlingService.LogException(null, $"Repairing link: {name}");
                                        
                                        // Infer LinkType from metadata
                                        string chartNameOrId = ExtractJsonValue(value, "ChartNameOrId");
                                        string sourceRange = ExtractJsonValue(value, "SourceRange");
                                        string sourceIdentifier = ExtractJsonValue(value, "SourceIdentifier");
                                        
                                        string inferredLinkType = "Unknown";
                                        
                                        // Logic to infer type based on available data
                                        if (!string.IsNullOrEmpty(chartNameOrId))
                                        {
                                            inferredLinkType = "Chart";
                                        }
                                        else if (!string.IsNullOrEmpty(sourceRange) || 
                                                (sourceIdentifier != null && sourceIdentifier.Contains(":")))
                                        {
                                            inferredLinkType = "Range";
                                        }
                                        else if (!string.IsNullOrEmpty(sourceIdentifier))
                                        {
                                            // Could be Shape if it's not a range format
                                            inferredLinkType = sourceIdentifier.Contains(":") ? "Range" : "Shape";
                                        }
                                        
                                        ErrorHandlingService.LogException(null, $"Inferred LinkType: {inferredLinkType}");
                                        
                                        // Update the JSON with the correct LinkType
                                        string updatedValue = value;
                                        if (string.IsNullOrEmpty(currentLinkType))
                                        {
                                            // Add LinkType field after LinkId
                                            updatedValue = value.Replace(
                                                "\"LinkId\":", 
                                                $"\"LinkId\":");
                                            updatedValue = updatedValue.Replace(
                                                "\"SourceFilePath\":",
                                                $"\"LinkType\": \"{inferredLinkType}\",\n  \"SourceFilePath\":");
                                        }
                                        else
                                        {
                                            // Replace existing LinkType
                                            updatedValue = value.Replace(
                                                $"\"LinkType\": \"{currentLinkType}\"",
                                                $"\"LinkType\": \"{inferredLinkType}\"");
                                        }
                                        
                                        // Update the property value
                                        propertyType.InvokeMember("Value", 
                                            System.Reflection.BindingFlags.SetProperty, null, property, new object[] { updatedValue });
                                        
                                        repairedCount++;
                                        ErrorHandlingService.LogException(null, $"Successfully repaired link {name} with LinkType: {inferredLinkType}");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            ErrorHandlingService.LogException(ex, $"Error repairing property at index {i}");
                        }
                    }
                    ErrorHandlingService.LogException(null, $"=== REPAIR COMPLETE: {repairedCount} links repaired ===");
                }
                catch (Exception ex)
                {
                    ErrorHandlingService.LogException(ex, "Error during link repair");
                }
                return repairedCount;
            });
        }

        #region Enhanced Clipboard Management Methods

        /// <summary>
        /// Configuration for clipboard operations with retry logic and timing.
        /// </summary>
        private static class ClipboardConfig
        {
            public const int MaxRetryAttempts = 3;
            public const int SharePointMaxRetries = 5;
            public const int BaseRetryDelayMs = 200;
            public const int SharePointDelayMs = 500;
            public const int MaxRetryDelayMs = 2000;
            public const int OperationTimeoutMs = 10000;
        }

        /// <summary>
        /// Executes clipboard operations with comprehensive retry logic and error recovery.
        /// Handles clipboard-related COM exceptions and provides progressive recovery strategies.
        /// </summary>
        /// <param name="operation">The clipboard operation to execute</param>
        /// <param name="operationName">Name of the operation for logging purposes</param>
        /// <param name="maxRetries">Maximum number of retry attempts</param>
        /// <returns>True if operation succeeded, false otherwise</returns>
        private async Task<bool> ExecuteClipboardOperationWithRetry(
            Func<Task<bool>> operation,
            string operationName,
            int maxRetries = ClipboardConfig.MaxRetryAttempts)
        {
            Exception lastException = null;

            for (int attempt = 0; attempt < maxRetries; attempt++)
            {
                try
                {
                    // Pre-operation diagnostics for first attempt and after failures
                    if (attempt == 0 || lastException != null)
                    {
                        var diagnostics = ClipboardManager.DiagnoseClipboardState();
                        ErrorHandlingService.LogException(null,
                            $"{operationName}: Attempt {attempt + 1}/{maxRetries}. Clipboard state: {diagnostics}");
                    }

                    // Execute the operation with timeout protection
                    var operationTask = operation();
                    var timeoutTask = Task.Delay(ClipboardConfig.OperationTimeoutMs);
                    var completedTask = await Task.WhenAny(operationTask, timeoutTask);

                    if (completedTask == timeoutTask)
                    {
                        throw new TimeoutException($"{operationName}: Operation timed out after {ClipboardConfig.OperationTimeoutMs}ms");
                    }

                    bool result = await operationTask;
                    if (result)
                    {
                        ErrorHandlingService.LogException(null,
                            $"{operationName}: Operation succeeded on attempt {attempt + 1}");
                        return true;
                    }

                    // Operation returned false - treat as failure
                    lastException = new InvalidOperationException($"{operationName}: Operation returned false");
                }
                catch (Exception ex)
                {
                    lastException = ex;

                    // Check if this is a clipboard-related error that we can recover from
                    if (IsClipboardRelatedError(ex))
                    {
                        ErrorHandlingService.LogException(ex,
                            $"{operationName}: Clipboard-related error on attempt {attempt + 1}/{maxRetries}");

                        // Attempt recovery before next retry
                        if (attempt < maxRetries - 1)
                        {
                            await RecoverFromClipboardError(ex, attempt);
                        }
                    }
                    else
                    {
                        // Non-clipboard error - don't retry
                        ErrorHandlingService.LogException(ex,
                            $"{operationName}: Non-clipboard error, aborting retries");
                        throw;
                    }
                }

                // Progressive delay before next attempt (except for last attempt)
                if (attempt < maxRetries - 1)
                {
                    int delay = Math.Min(
                        ClipboardConfig.BaseRetryDelayMs * (int)Math.Pow(2, attempt),
                        ClipboardConfig.MaxRetryDelayMs);

                    ErrorHandlingService.LogException(null,
                        $"{operationName}: Waiting {delay}ms before retry {attempt + 2}");
                    await Task.Delay(delay);
                }
            }

            // All retries exhausted
            ErrorHandlingService.LogException(lastException,
                $"{operationName}: All {maxRetries} attempts failed");
            return false;
        }

        /// <summary>
        /// Determines if an exception is related to clipboard operations and can be recovered from.
        /// </summary>
        /// <param name="ex">Exception to analyze</param>
        /// <returns>True if this is a recoverable clipboard error</returns>
        private bool IsClipboardRelatedError(Exception ex)
        {
            if (ex is COMException comEx)
            {
                // Known clipboard-related COM error codes
                switch ((uint)comEx.ErrorCode)
                {
                    case 0x800A03EC: // CopyPicture method of Range class failed
                    case 0x80048240: // The specified data type is unavailable
                    case 0x80004005: // Unspecified error (often clipboard-related)
                    case 0x800401D0: // CLIPBRD_E_CANT_OPEN
                    case 0x800401D1: // CLIPBRD_E_CANT_EMPTY
                    case 0x800401D2: // CLIPBRD_E_CANT_SET
                    case 0x800401D3: // CLIPBRD_E_BAD_DATA
                    case 0x800401D4: // CLIPBRD_E_CANT_CLOSE
                        return true;
                }
            }

            // Check exception message for clipboard-related keywords
            string message = ex.Message?.ToLowerInvariant() ?? string.Empty;
            return message.Contains("clipboard") ||
                   message.Contains("copypicture") ||
                   message.Contains("pastespecial") ||
                   message.Contains("data type is unavailable");
        }

        /// <summary>
        /// Implements progressive recovery strategies for clipboard-related errors.
        /// </summary>
        /// <param name="ex">The clipboard error that occurred</param>
        /// <param name="attemptNumber">Current attempt number (0-based)</param>
        private async Task RecoverFromClipboardError(Exception ex, int attemptNumber)
        {
            try
            {
                ErrorHandlingService.LogException(null,
                    $"RecoverFromClipboardError: Starting recovery for attempt {attemptNumber + 1}");

                // Strategy 1: Clear clipboard state
                bool clearSuccess = await ClipboardManager.SafeClearClipboardAsync();
                ErrorHandlingService.LogException(null,
                    $"RecoverFromClipboardError: Clipboard clear {(clearSuccess ? "succeeded" : "failed")}");

                // Strategy 2: Wait for clipboard to become available
                int maxWaitAttempts = 5;
                for (int waitAttempt = 0; waitAttempt < maxWaitAttempts; waitAttempt++)
                {
                    if (ClipboardManager.CanAccessClipboard())
                    {
                        ErrorHandlingService.LogException(null,
                            $"RecoverFromClipboardError: Clipboard access restored after {waitAttempt + 1} attempts");
                        break;
                    }

                    await Task.Delay(100 * (waitAttempt + 1)); // Progressive wait
                }

                // Strategy 3: Force garbage collection to release any COM objects
                if (attemptNumber >= 1)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();

                    ErrorHandlingService.LogException(null,
                        "RecoverFromClipboardError: Forced garbage collection completed");
                }

                // Strategy 4: Additional delay for SharePoint-related errors
                if (ex.Message?.Contains("SharePoint") == true || attemptNumber >= 2)
                {
                    await Task.Delay(ClipboardConfig.SharePointDelayMs);
                    ErrorHandlingService.LogException(null,
                        $"RecoverFromClipboardError: Applied SharePoint delay ({ClipboardConfig.SharePointDelayMs}ms)");
                }

                // Final diagnostics
                var postRecoveryDiagnostics = ClipboardManager.DiagnoseClipboardState();
                ErrorHandlingService.LogException(null,
                    $"RecoverFromClipboardError: Post-recovery state: {postRecoveryDiagnostics}");
            }
            catch (Exception recoveryEx)
            {
                ErrorHandlingService.LogException(recoveryEx,
                    "RecoverFromClipboardError: Error during clipboard recovery");
            }
        }

        /// <summary>
        /// Enhanced range copy with multi-strategy clipboard management.
        /// Replaces the original single CopyPicture() call with intelligent fallback.
        /// </summary>
        /// <param name="range">Excel range to copy</param>
        /// <param name="copyFormat">Preferred copy format</param>
        /// <param name="filePath">Path to the Excel file for SharePoint detection</param>
        /// <returns>True if copy succeeded, false otherwise</returns>
        private async Task<bool> CopyRangeWithEnhancedStrategy(Excel.Range range, string copyFormat = "xlPicture", string filePath = null)
        {
            return await ExecuteClipboardOperationWithRetry(async () =>
            {
                // Pre-operation diagnostics and logging
                var preDiagnostics = ClipboardManager.DiagnoseClipboardState();
                ErrorHandlingService.LogException(null,
                    $"Starting enhanced range copy operation. Pre-operation state: {preDiagnostics}");

                // Strategy 1: CopyPicture with xlPicture format
                if (await TryCopyPictureStrategy(range, Excel.XlCopyPictureFormat.xlPicture, filePath))
                {
                    return await ValidateClipboardContent("xlPicture");
                }

                // Strategy 2: CopyPicture with xlBitmap format
                if (await TryCopyPictureStrategy(range, Excel.XlCopyPictureFormat.xlBitmap, filePath))
                {
                    return await ValidateClipboardContent("xlBitmap");
                }

                // Strategy 3: Standard Copy method as final fallback
                if (await TryStandardCopyStrategy(range))
                {
                    return await ValidateClipboardContent("Standard");
                }

                throw new InvalidOperationException("All copy strategies failed - clipboard may be locked by another process");

            }, "CopyRange", ClipboardConfig.MaxRetryAttempts);
        }

        /// <summary>
        /// Enhanced shape copy with multi-strategy clipboard management.
        /// Replaces the original single CopyPicture() call for shapes.
        /// </summary>
        /// <param name="shape">Excel shape to copy</param>
        /// <param name="filePath">Path to the Excel file for SharePoint detection</param>
        /// <returns>True if copy succeeded, false otherwise</returns>
        private async Task<bool> CopyShapeWithEnhancedStrategy(Excel.Shape shape, string filePath = null)
        {
            return await ExecuteClipboardOperationWithRetry(async () =>
            {
                // Pre-operation diagnostics and logging
                var preDiagnostics = ClipboardManager.DiagnoseClipboardState();
                ErrorHandlingService.LogException(null,
                    $"Starting enhanced shape copy operation. Pre-operation state: {preDiagnostics}");

                // Strategy 1: Shape.CopyPicture with xlPicture format
                if (await TryShapeCopyPictureStrategy(shape, Excel.XlCopyPictureFormat.xlPicture, filePath))
                {
                    return await ValidateClipboardContent("Shape-xlPicture");
                }

                // Strategy 2: Shape.CopyPicture with xlBitmap format
                if (await TryShapeCopyPictureStrategy(shape, Excel.XlCopyPictureFormat.xlBitmap, filePath))
                {
                    return await ValidateClipboardContent("Shape-xlBitmap");
                }

                // Strategy 3: Shape.Copy method as fallback
                if (await TryShapeStandardCopyStrategy(shape))
                {
                    return await ValidateClipboardContent("Shape-Standard");
                }

                throw new InvalidOperationException("All shape copy strategies failed");

            }, "CopyShape", ClipboardConfig.MaxRetryAttempts);
        }

        /// <summary>
        /// Attempts to copy using CopyPicture method with specified format.
        /// Enhanced with timing optimizations for SharePoint files.
        /// </summary>
        /// <param name="range">Excel range to copy</param>
        /// <param name="pictureFormat">Picture format to use</param>
        /// <param name="filePath">Path to the Excel file for SharePoint detection</param>
        private async Task<bool> TryCopyPictureStrategy(Excel.Range range, Excel.XlCopyPictureFormat pictureFormat, string filePath = null)
        {
            try
            {
                // Clear clipboard before operation to ensure clean state
                await ClipboardManager.SafeClearClipboardAsync().ConfigureAwait(false);

                // Enhanced timing strategy based on research findings
                if (!await PrepareRangeForCopyPicture(range, filePath))
                {
                    ErrorHandlingService.LogException(null,
                        $"TryCopyPictureStrategy: Range validation failed for format {pictureFormat}");
                    return false;
                }

                // Execute CopyPicture with specified format
                range.CopyPicture(Excel.XlPictureAppearance.xlScreen, pictureFormat);

                // Allow time for clipboard update and verify operation succeeded
                await Task.Delay(100).ConfigureAwait(false);
                return ClipboardManager.HasClipboardContent();
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    $"TryCopyPictureStrategy: Failed with format {pictureFormat}");
                return false;
            }
        }

        /// <summary>
        /// Attempts to copy using standard Range.Copy method.
        /// </summary>
        private async Task<bool> TryStandardCopyStrategy(Excel.Range range)
        {
            try
            {
                // Clear clipboard before operation
                await ClipboardManager.SafeClearClipboardAsync().ConfigureAwait(false);

                // Small delay to ensure clipboard is ready
                await Task.Delay(50).ConfigureAwait(false);

                // Execute standard copy
                range.Copy();

                // Allow time for clipboard update and verify operation succeeded
                await Task.Delay(100).ConfigureAwait(false);
                return ClipboardManager.HasClipboardContent();
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "TryStandardCopyStrategy: Standard copy failed");
                return false;
            }
        }

        /// <summary>
        /// Attempts to copy shape using CopyPicture method with specified format.
        /// Enhanced with timing optimizations for SharePoint files.
        /// </summary>
        /// <param name="shape">Excel shape to copy</param>
        /// <param name="pictureFormat">Picture format to use</param>
        /// <param name="filePath">Path to the Excel file for SharePoint detection</param>
        private async Task<bool> TryShapeCopyPictureStrategy(Excel.Shape shape, Excel.XlCopyPictureFormat pictureFormat, string filePath = null)
        {
            try
            {
                // Clear clipboard before operation
                await ClipboardManager.SafeClearClipboardAsync().ConfigureAwait(false);

                // Enhanced timing strategy for shapes
                if (!await PrepareShapeForCopyPicture(shape, filePath))
                {
                    ErrorHandlingService.LogException(null,
                        $"TryShapeCopyPictureStrategy: Shape validation failed for format {pictureFormat}");
                    return false;
                }

                // Execute shape CopyPicture with specified format
                shape.CopyPicture(Excel.XlPictureAppearance.xlScreen, pictureFormat);

                // Allow time for clipboard update and verify operation succeeded
                await Task.Delay(100).ConfigureAwait(false);
                return ClipboardManager.HasClipboardContent();
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    $"TryShapeCopyPictureStrategy: Failed with format {pictureFormat}");
                return false;
            }
        }

        /// <summary>
        /// Attempts to copy shape using standard Shape.Copy method.
        /// </summary>
        private async Task<bool> TryShapeStandardCopyStrategy(Excel.Shape shape)
        {
            try
            {
                // Clear clipboard before operation
                await ClipboardManager.SafeClearClipboardAsync().ConfigureAwait(false);

                // Small delay to ensure clipboard is ready
                await Task.Delay(50).ConfigureAwait(false);

                // Execute standard shape copy
                shape.Copy();

                // Allow time for clipboard update and verify operation succeeded
                await Task.Delay(100).ConfigureAwait(false);
                return ClipboardManager.HasClipboardContent();
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "TryShapeStandardCopyStrategy: Standard shape copy failed");
                return false;
            }
        }

        /// <summary>
        /// Validates that clipboard contains expected content after copy operation.
        /// </summary>
        private async Task<bool> ValidateClipboardContent(string strategy)
        {
            try
            {
                // Allow additional time for clipboard to stabilize
                await Task.Delay(50).ConfigureAwait(false);

                bool hasContent = ClipboardManager.HasClipboardContent();

                ErrorHandlingService.LogException(null,
                    $"ValidateClipboardContent: Strategy '{strategy}' - Content available: {hasContent}");

                if (hasContent)
                {
                    // Additional validation - get diagnostics for logging
                    var diagnostics = ClipboardManager.DiagnoseClipboardState();
                    ErrorHandlingService.LogException(null,
                        $"ValidateClipboardContent: Post-copy diagnostics: {diagnostics}");
                }

                return hasContent;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    $"ValidateClipboardContent: Error validating clipboard for strategy '{strategy}'");
                return false;
            }
        }

        #region Timing Enhancement Methods

        /// <summary>
        /// Prepares range for CopyPicture operation with timing optimizations.
        /// Based on research findings for reducing CopyPicture failures.
        /// </summary>
        /// <param name="range">Excel range to prepare</param>
        /// <param name="filePath">Path to the Excel file for accurate SharePoint detection</param>
        private async Task<bool> PrepareRangeForCopyPicture(Excel.Range range, string filePath = null)
        {
            try
            {
                // Validate range readiness
                if (!ValidateRangeReadiness(range))
                {
                    ErrorHandlingService.LogException(null,
                        "PrepareRangeForCopyPicture: Range validation failed");
                    return false;
                }

                // Determine if this is a SharePoint file using actual file path if available
                bool isSharePointFile = !string.IsNullOrEmpty(filePath)
                    ? IsCurrentOperationSharePointFile(filePath)
                    : IsCurrentOperationSharePointFile();

                // Apply DoEvents to allow Excel to process pending operations
                System.Windows.Forms.Application.DoEvents();

                // Apply timing delay based on file type
                int delay = isSharePointFile ? 100 : 50; // SharePoint files need longer delay
                await Task.Delay(delay).ConfigureAwait(false);

                ErrorHandlingService.LogException(null,
                    $"PrepareRangeForCopyPicture: Applied {delay}ms delay for {(isSharePointFile ? "SharePoint" : "local")} file");

                return true;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "PrepareRangeForCopyPicture: Error during range preparation");
                return false;
            }
        }

        /// <summary>
        /// Validates that the range is ready for CopyPicture operation.
        /// </summary>
        private bool ValidateRangeReadiness(Excel.Range range)
        {
            try
            {
                // Ensure range is accessible and ready
                var address = range.Address;
                var worksheet = range.Worksheet;
                var workbook = worksheet.Parent as Excel.Workbook;

                if (workbook == null)
                {
                    ErrorHandlingService.LogException(null,
                        "ValidateRangeReadiness: Could not access workbook");
                    return false;
                }

                // Check if workbook is in a ready state by accessing a property
                // This forces Excel to ensure the workbook is fully loaded
                var name = workbook.Name;

                ErrorHandlingService.LogException(null,
                    $"ValidateRangeReadiness: Range {address} in workbook '{name}' is ready");

                return true;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "ValidateRangeReadiness: Range validation failed");
                return false;
            }
        }

        /// <summary>
        /// Determines if the current operation involves a SharePoint file.
        /// Uses the current Excel wrapper context to determine file type.
        /// </summary>
        private bool IsCurrentOperationSharePointFile()
        {
            try
            {
                // In PowerPoint add-in context, we need to check differently
                // We can't access Excel's ActiveWorkbook from PowerPoint Application
                // Instead, we'll use a conservative approach and default to SharePoint timing

                // SharePoint timing is more conservative (longer delays) and works for both
                // SharePoint and local files, so it's the safer default

                ErrorHandlingService.LogException(null,
                    "IsCurrentOperationSharePointFile: Using conservative SharePoint timing as default");

                return true; // Use SharePoint timing as safe default
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "IsCurrentOperationSharePointFile: Error checking file type");
                return true; // Default to SharePoint timing (more conservative)
            }
        }

        /// <summary>
        /// Determines if a specific file path involves a SharePoint file.
        /// This overload can be used when the file path is known.
        /// </summary>
        private bool IsCurrentOperationSharePointFile(string filePath)
        {
            try
            {
                if (!string.IsNullOrEmpty(filePath))
                {
                    bool isSharePoint = IsSharePointPath(filePath);
                    ErrorHandlingService.LogException(null,
                        $"IsCurrentOperationSharePointFile: File '{filePath}' is {(isSharePoint ? "SharePoint" : "local")}");
                    return isSharePoint;
                }

                // Fallback to default behavior
                return IsCurrentOperationSharePointFile();
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "IsCurrentOperationSharePointFile: Error checking specific file path");
                return true; // Default to SharePoint timing (more conservative)
            }
        }

        /// <summary>
        /// Checks if a file path indicates a SharePoint location.
        /// </summary>
        private bool IsSharePointPath(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            return filePath.Contains("sharepoint") ||
                   filePath.Contains("office365") ||
                   filePath.StartsWith("https://", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Prepares shape for CopyPicture operation with timing optimizations.
        /// </summary>
        /// <param name="shape">Excel shape to prepare</param>
        /// <param name="filePath">Path to the Excel file for accurate SharePoint detection</param>
        private async Task<bool> PrepareShapeForCopyPicture(Excel.Shape shape, string filePath = null)
        {
            try
            {
                // Validate shape readiness
                if (!ValidateShapeReadiness(shape))
                {
                    ErrorHandlingService.LogException(null,
                        "PrepareShapeForCopyPicture: Shape validation failed");
                    return false;
                }

                // Determine if this is a SharePoint file using actual file path if available
                bool isSharePointFile = !string.IsNullOrEmpty(filePath)
                    ? IsCurrentOperationSharePointFile(filePath)
                    : IsCurrentOperationSharePointFile();

                // Apply DoEvents to allow Excel to process pending operations
                System.Windows.Forms.Application.DoEvents();

                // Apply timing delay based on file type (shapes may need slightly longer)
                int delay = isSharePointFile ? 120 : 60; // Slightly longer for shapes
                await Task.Delay(delay).ConfigureAwait(false);

                ErrorHandlingService.LogException(null,
                    $"PrepareShapeForCopyPicture: Applied {delay}ms delay for {(isSharePointFile ? "SharePoint" : "local")} file");

                return true;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "PrepareShapeForCopyPicture: Error during shape preparation");
                return false;
            }
        }

        /// <summary>
        /// Validates that the shape is ready for CopyPicture operation.
        /// </summary>
        private bool ValidateShapeReadiness(Excel.Shape shape)
        {
            try
            {
                // Ensure shape is accessible and ready
                var name = shape.Name;
                var worksheet = shape.Parent as Excel.Worksheet;

                if (worksheet == null)
                {
                    ErrorHandlingService.LogException(null,
                        "ValidateShapeReadiness: Could not access worksheet");
                    return false;
                }

                var workbook = worksheet.Parent as Excel.Workbook;
                if (workbook == null)
                {
                    ErrorHandlingService.LogException(null,
                        "ValidateShapeReadiness: Could not access workbook");
                    return false;
                }

                // Check if workbook is in a ready state
                var workbookName = workbook.Name;

                ErrorHandlingService.LogException(null,
                    $"ValidateShapeReadiness: Shape '{name}' in workbook '{workbookName}' is ready");

                return true;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "ValidateShapeReadiness: Shape validation failed");
                return false;
            }
        }

        /// <summary>
        /// Progressive timing strategy for stubborn CopyPicture operations.
        /// Tries multiple delays with increasing duration for maximum compatibility.
        /// </summary>
        private async Task<bool> TryCopyPictureWithProgressiveTiming(Excel.Range range, Excel.XlCopyPictureFormat pictureFormat)
        {
            // Define progressive delays based on file type
            int[] delays = IsCurrentOperationSharePointFile()
                ? new[] { 50, 100, 200, 500, 800 }  // SharePoint: more aggressive progression
                : new[] { 25, 50, 100, 200 };       // Local: lighter progression

            ErrorHandlingService.LogException(null,
                $"TryCopyPictureWithProgressiveTiming: Attempting {delays.Length} timing strategies for {pictureFormat}");

            foreach (int delay in delays)
            {
                try
                {
                    // Clear clipboard before each attempt
                    await ClipboardManager.SafeClearClipboardAsync().ConfigureAwait(false);

                    // Apply DoEvents and progressive delay
                    System.Windows.Forms.Application.DoEvents();
                    await Task.Delay(delay).ConfigureAwait(false);

                    ErrorHandlingService.LogException(null,
                        $"TryCopyPictureWithProgressiveTiming: Trying {delay}ms delay");

                    // Attempt CopyPicture
                    range.CopyPicture(Excel.XlPictureAppearance.xlScreen, pictureFormat);
                    await Task.Delay(100).ConfigureAwait(false);

                    // Check if successful
                    if (ClipboardManager.HasClipboardContent())
                    {
                        ErrorHandlingService.LogException(null,
                            $"TryCopyPictureWithProgressiveTiming: SUCCESS with {delay}ms delay");
                        return true;
                    }
                }
                catch (COMException ex) when (ex.ErrorCode == unchecked((int)0x800A03EC))
                {
                    ErrorHandlingService.LogException(null,
                        $"TryCopyPictureWithProgressiveTiming: {delay}ms delay failed, trying next");
                    continue; // Try next delay
                }
                catch (Exception ex)
                {
                    ErrorHandlingService.LogException(ex,
                        $"TryCopyPictureWithProgressiveTiming: Unexpected error with {delay}ms delay");
                    continue; // Try next delay
                }
            }

            ErrorHandlingService.LogException(null,
                "TryCopyPictureWithProgressiveTiming: All timing strategies failed");
            return false;
        }

        #endregion

        /// <summary>
        /// Enhanced paste operation with comprehensive clipboard validation and SharePoint awareness.
        /// Integrates with existing PasteWithFallbackAsync while adding clipboard state management.
        /// </summary>
        /// <param name="targetSlide">PowerPoint slide to paste into</param>
        /// <param name="isSharePointSource">Whether the source is a SharePoint file</param>
        /// <param name="pasteFormat">Preferred paste format</param>
        /// <returns>PowerPoint ShapeRange if successful, null otherwise</returns>
        private async Task<PowerPoint.ShapeRange> PasteWithClipboardValidation(
            PowerPoint.Slide targetSlide,
            bool isSharePointSource = false,
            PowerPoint.PpPasteDataType pasteFormat = PowerPoint.PpPasteDataType.ppPasteEnhancedMetafile)
        {
            // Use higher retry count for SharePoint sources
            int maxRetries = isSharePointSource ? ClipboardConfig.SharePointMaxRetries : ClipboardConfig.MaxRetryAttempts;

            PowerPoint.ShapeRange result = null;

            bool success = await ExecuteClipboardOperationWithRetry(async () =>
            {
                // Pre-paste clipboard validation
                if (!await ValidateClipboardForPaste())
                {
                    await RecoverClipboardForPaste();

                    // Re-validate after recovery
                    if (!await ValidateClipboardForPaste())
                    {
                        throw new InvalidOperationException("Clipboard recovery failed - cannot proceed with paste operation");
                    }
                }

                // Execute paste with format fallback strategy
                result = await ExecutePasteWithFormatFallback(targetSlide, pasteFormat, isSharePointSource);
                return result != null; // Convert to bool for ExecuteClipboardOperationWithRetry

            }, $"PasteWithValidation-SharePoint:{isSharePointSource}", maxRetries);

            return success ? result : null;
        }

        /// <summary>
        /// Validates clipboard state before paste operations.
        /// </summary>
        private async Task<bool> ValidateClipboardForPaste()
        {
            try
            {
                // Check basic clipboard accessibility
                if (!ClipboardManager.CanAccessClipboard())
                {
                    ErrorHandlingService.LogException(null,
                        "ValidateClipboardForPaste: Cannot access clipboard");
                    return false;
                }

                // Check if clipboard has content
                if (!ClipboardManager.HasClipboardContent())
                {
                    ErrorHandlingService.LogException(null,
                        "ValidateClipboardForPaste: Clipboard has no content");
                    return false;
                }

                // Get detailed diagnostics
                var diagnostics = ClipboardManager.DiagnoseClipboardState();
                ErrorHandlingService.LogException(null,
                    $"ValidateClipboardForPaste: Validation passed. State: {diagnostics}");

                return true;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "ValidateClipboardForPaste: Error during clipboard validation");
                return false;
            }
        }

        /// <summary>
        /// Recovers clipboard state for paste operations.
        /// </summary>
        private async Task RecoverClipboardForPaste()
        {
            try
            {
                ErrorHandlingService.LogException(null,
                    "RecoverClipboardForPaste: Starting clipboard recovery for paste operation");

                // Wait for clipboard to become available
                int maxWaitAttempts = 10;
                for (int attempt = 0; attempt < maxWaitAttempts; attempt++)
                {
                    if (ClipboardManager.CanAccessClipboard())
                    {
                        ErrorHandlingService.LogException(null,
                            $"RecoverClipboardForPaste: Clipboard access restored after {attempt + 1} attempts");
                        break;
                    }

                    await Task.Delay(100 * (attempt + 1)); // Progressive wait
                }

                // Additional recovery delay
                await Task.Delay(200);

                ErrorHandlingService.LogException(null,
                    "RecoverClipboardForPaste: Recovery completed");
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "RecoverClipboardForPaste: Error during clipboard recovery");
            }
        }

        /// <summary>
        /// Executes paste operation with progressive format fallback strategy.
        /// Enhanced version of the original PasteWithFallbackAsync with clipboard awareness.
        /// </summary>
        private async Task<PowerPoint.ShapeRange> ExecutePasteWithFormatFallback(
            PowerPoint.Slide targetSlide,
            PowerPoint.PpPasteDataType preferredFormat,
            bool isSharePointSource)
        {
            // Define fallback formats based on preferred format
            var fallbackFormats = GetPasteFallbackFormats(preferredFormat);

            // Use longer delays for SharePoint sources
            int formatRetryDelay = isSharePointSource ? ClipboardConfig.SharePointDelayMs : ClipboardConfig.BaseRetryDelayMs;

            Exception lastException = null;

            foreach (var format in fallbackFormats)
            {
                try
                {
                    // Validate clipboard before each format attempt
                    if (!ClipboardManager.HasClipboardContent())
                    {
                        ErrorHandlingService.LogException(null,
                            $"ExecutePasteWithFormatFallback: Clipboard content lost before attempting format {format}");
                        return null;
                    }

                    // Attempt paste with current format using PowerPointComWrapper
                    ErrorHandlingService.LogException(null,
                        $"ExecutePasteWithFormatFallback: Attempting paste with format {format}");

                    var result = _pptWrapper.PasteSpecial(targetSlide, format);
                    if (result != null && result.Count > 0)
                    {
                        ErrorHandlingService.LogException(null,
                            $"ExecutePasteWithFormatFallback: Paste operation succeeded with format: {format}");
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    ErrorHandlingService.LogException(ex,
                        $"ExecutePasteWithFormatFallback: Format {format} failed: {ex.Message}");

                    // Clear clipboard state between failed attempts
                    await ClipboardManager.SafeClearClipboardAsync().ConfigureAwait(false);
                }

                // Progressive delay between format attempts (except for last format)
                if (format != fallbackFormats.Last())
                {
                    await Task.Delay(formatRetryDelay).ConfigureAwait(false);
                }
            }

            // All formats failed
            throw new Exception($"Failed to paste after trying all formats. Last error: {lastException?.Message}", lastException);
        }

        /// <summary>
        /// Gets the fallback format sequence based on preferred format.
        /// </summary>
        private PowerPoint.PpPasteDataType[] GetPasteFallbackFormats(PowerPoint.PpPasteDataType preferredFormat)
        {
            // Return format sequence based on preferred format
            switch (preferredFormat)
            {
                case PowerPoint.PpPasteDataType.ppPasteEnhancedMetafile:
                    return new[]
                    {
                        PowerPoint.PpPasteDataType.ppPasteEnhancedMetafile,
                        PowerPoint.PpPasteDataType.ppPasteBitmap,
                        PowerPoint.PpPasteDataType.ppPasteDefault
                    };

                case PowerPoint.PpPasteDataType.ppPasteBitmap:
                    return new[]
                    {
                        PowerPoint.PpPasteDataType.ppPasteBitmap,
                        PowerPoint.PpPasteDataType.ppPasteEnhancedMetafile,
                        PowerPoint.PpPasteDataType.ppPasteDefault
                    };

                default:
                    return new[]
                    {
                        preferredFormat,
                        PowerPoint.PpPasteDataType.ppPasteEnhancedMetafile,
                        PowerPoint.PpPasteDataType.ppPasteBitmap,
                        PowerPoint.PpPasteDataType.ppPasteDefault
                    };
            }
        }

        #endregion
    }

    /// <summary>
    /// Manages PowerPoint window focus restoration after Excel operations.
    /// Captures complete PowerPoint window state (size, position, state) before Excel operations
    /// and restores the window exactly as it was afterward.
    /// </summary>
    public class PowerPointFocusManager : IDisposable
    {
        #region Fields and Structures

        private IntPtr _powerPointWindowHandle = IntPtr.Zero;
        private bool _powerPointWasActive = false;
        private WINDOWPLACEMENT _originalWindowPlacement;
        private bool _windowPlacementCaptured = false;
        private bool _disposed = false;

        // Win32 structures for complete window state management
        [System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
        private struct WINDOWPLACEMENT
        {
            public int length;
            public int flags;
            public int showCmd;
            public POINT ptMinPosition;
            public POINT ptMaxPosition;
            public RECT rcNormalPosition;
        }

        [System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
        private struct POINT
        {
            public int X;
            public int Y;
        }

        [System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        #endregion

        #region Win32 API Declarations

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool IsWindow(IntPtr hWnd);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint processId);

        [System.Runtime.InteropServices.DllImport("user32.dll", CharSet = System.Runtime.InteropServices.CharSet.Auto)]
        private static extern int GetWindowText(IntPtr hWnd, System.Text.StringBuilder lpString, int nMaxCount);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool GetWindowPlacement(IntPtr hWnd, ref WINDOWPLACEMENT lpwndpl);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool SetWindowPlacement(IntPtr hWnd, ref WINDOWPLACEMENT lpwndpl);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        // Window show state constants
        private const int SW_HIDE = 0;
        private const int SW_SHOWNORMAL = 1;
        private const int SW_SHOWMINIMIZED = 2;
        private const int SW_SHOWMAXIMIZED = 3;
        private const int SW_SHOWNOACTIVATE = 4;
        private const int SW_SHOW = 5;
        private const int SW_MINIMIZE = 6;
        private const int SW_SHOWMINNOACTIVE = 7;
        private const int SW_SHOWNA = 8;
        private const int SW_RESTORE = 9;
        private const int SW_SHOWDEFAULT = 10;

        #endregion

        #region Constructor and Initialization

        /// <summary>
        /// Initializes the focus manager and captures current PowerPoint window state.
        /// </summary>
        public PowerPointFocusManager()
        {
            try
            {
                CapturePowerPointWindowState();
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Failed to capture PowerPoint window state");
            }
        }

        /// <summary>
        /// Captures the complete PowerPoint window state including size, position, and window state.
        /// </summary>
        private void CapturePowerPointWindowState()
        {
            try
            {
                // Get PowerPoint application handle
                var pptApp = Globals.ThisAddIn?.Application;
                if (pptApp != null)
                {
                    _powerPointWindowHandle = (IntPtr)pptApp.HWND;

                    // Check if PowerPoint is currently the active window
                    var foregroundWindow = GetForegroundWindow();
                    _powerPointWasActive = (foregroundWindow == _powerPointWindowHandle);

                    // Capture complete window placement (size, position, state)
                    _originalWindowPlacement = new WINDOWPLACEMENT();
                    _originalWindowPlacement.length = System.Runtime.InteropServices.Marshal.SizeOf(_originalWindowPlacement);

                    _windowPlacementCaptured = GetWindowPlacement(_powerPointWindowHandle, ref _originalWindowPlacement);

                    if (_windowPlacementCaptured)
                    {
                        string windowState = GetWindowStateDescription(_originalWindowPlacement.showCmd);
                        var rect = _originalWindowPlacement.rcNormalPosition;

                        ErrorHandlingService.LogException(null,
                            $"PowerPointFocusManager: Captured PowerPoint window state - " +
                            $"Handle: {_powerPointWindowHandle}, WasActive: {_powerPointWasActive}, " +
                            $"State: {windowState}, Position: ({rect.Left},{rect.Top}), " +
                            $"Size: {rect.Right - rect.Left}x{rect.Bottom - rect.Top}");
                    }
                    else
                    {
                        ErrorHandlingService.LogException(null,
                            $"PowerPointFocusManager: Failed to capture window placement, will use basic restoration");
                    }
                }
                else
                {
                    ErrorHandlingService.LogException(null,
                        "PowerPointFocusManager: Could not access PowerPoint application");
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "PowerPointFocusManager: Error capturing PowerPoint window state");
                _windowPlacementCaptured = false;
            }
        }

        /// <summary>
        /// Gets a human-readable description of the window state.
        /// </summary>
        private string GetWindowStateDescription(int showCmd)
        {
            switch (showCmd)
            {
                case SW_SHOWNORMAL: return "Normal";
                case SW_SHOWMAXIMIZED: return "Maximized";
                case SW_SHOWMINIMIZED: return "Minimized";
                case SW_HIDE: return "Hidden";
                default: return $"Unknown({showCmd})";
            }
        }

        /// <summary>
        /// Logs the current window state for diagnostic purposes.
        /// </summary>
        private void LogCurrentWindowState(string context)
        {
            try
            {
                if (_powerPointWindowHandle == IntPtr.Zero)
                {
                    ErrorHandlingService.LogException(null,
                        $"PowerPointFocusManager [{context}]: No window handle available");
                    return;
                }

                WINDOWPLACEMENT currentPlacement = new WINDOWPLACEMENT();
                currentPlacement.length = System.Runtime.InteropServices.Marshal.SizeOf(currentPlacement);

                if (GetWindowPlacement(_powerPointWindowHandle, ref currentPlacement))
                {
                    string windowState = GetWindowStateDescription(currentPlacement.showCmd);
                    var rect = currentPlacement.rcNormalPosition;

                    ErrorHandlingService.LogException(null,
                        $"PowerPointFocusManager [{context}]: Current window state - " +
                        $"State: {windowState}, Position: ({rect.Left},{rect.Top}), " +
                        $"Size: {rect.Right - rect.Left}x{rect.Bottom - rect.Top}");
                }
                else
                {
                    ErrorHandlingService.LogException(null,
                        $"PowerPointFocusManager [{context}]: Failed to get current window placement");
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    $"PowerPointFocusManager [{context}]: Error logging current window state");
            }
        }

        #endregion

        #region Focus Restoration

        /// <summary>
        /// Restores the PowerPoint window to its original state after Excel operations.
        /// Restores size, position, window state, and focus with comprehensive fallback logic.
        /// </summary>
        public async Task RestorePowerPointFocusAsync()
        {
            if (_disposed)
            {
                ErrorHandlingService.LogException(null,
                    "PowerPointFocusManager: Cannot restore focus - manager is disposed");
                return;
            }

            try
            {
                // Small delay to ensure Excel operations are complete
                await Task.Delay(100);

                if (_powerPointWindowHandle != IntPtr.Zero && IsValidPowerPointWindow())
                {
                    ErrorHandlingService.LogException(null,
                        "PowerPointFocusManager: Attempting to restore PowerPoint window state");

                    // Log current state before restoration
                    LogCurrentWindowState("Before Restoration");

                    bool restorationSucceeded = false;

                    // Strategy 1: Restore complete window placement (preferred)
                    if (_windowPlacementCaptured)
                    {
                        restorationSucceeded = await RestoreCompleteWindowState();
                    }

                    // Strategy 2: Fallback to basic restoration
                    if (!restorationSucceeded)
                    {
                        restorationSucceeded = await RestoreBasicWindowState();
                    }

                    // Strategy 3: Emergency fallback - maximize window
                    if (!restorationSucceeded)
                    {
                        await EmergencyMaximizeWindow();
                    }

                    // Log final state after restoration
                    LogCurrentWindowState("After Restoration");

                    // Final step: Ensure window has focus
                    await Task.Delay(50);
                    bool focusRestored = SetForegroundWindow(_powerPointWindowHandle);

                    ErrorHandlingService.LogException(null,
                        $"PowerPointFocusManager: Window restoration completed, focus {(focusRestored ? "succeeded" : "failed")}");
                }
                else
                {
                    ErrorHandlingService.LogException(null,
                        "PowerPointFocusManager: Cannot restore focus - PowerPoint window not available");
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "PowerPointFocusManager: Error during window restoration");
            }
        }

        /// <summary>
        /// Strategy 1: Restores complete window state using SetWindowPlacement.
        /// </summary>
        private async Task<bool> RestoreCompleteWindowState()
        {
            try
            {
                ErrorHandlingService.LogException(null,
                    "PowerPointFocusManager: Attempting complete window state restoration");

                // Restore the complete window placement
                bool placementRestored = SetWindowPlacement(_powerPointWindowHandle, ref _originalWindowPlacement);

                if (placementRestored)
                {
                    await Task.Delay(50); // Allow window to settle

                    string windowState = GetWindowStateDescription(_originalWindowPlacement.showCmd);
                    var rect = _originalWindowPlacement.rcNormalPosition;

                    ErrorHandlingService.LogException(null,
                        $"PowerPointFocusManager: Successfully restored window to {windowState} state, " +
                        $"Position: ({rect.Left},{rect.Top}), Size: {rect.Right - rect.Left}x{rect.Bottom - rect.Top}");

                    return true;
                }
                else
                {
                    ErrorHandlingService.LogException(null,
                        "PowerPointFocusManager: SetWindowPlacement failed");
                    return false;
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "PowerPointFocusManager: Error in complete window state restoration");
                return false;
            }
        }

        /// <summary>
        /// Strategy 2: Basic window restoration using ShowWindow.
        /// </summary>
        private async Task<bool> RestoreBasicWindowState()
        {
            try
            {
                ErrorHandlingService.LogException(null,
                    "PowerPointFocusManager: Attempting basic window restoration");

                // Determine appropriate show command based on captured state
                int showCommand = SW_RESTORE;
                if (_windowPlacementCaptured)
                {
                    switch (_originalWindowPlacement.showCmd)
                    {
                        case SW_SHOWMAXIMIZED:
                            showCommand = SW_SHOWMAXIMIZED;
                            break;
                        case SW_SHOWNORMAL:
                            showCommand = SW_SHOWNORMAL;
                            break;
                        default:
                            showCommand = SW_RESTORE;
                            break;
                    }
                }

                bool windowShown = ShowWindow(_powerPointWindowHandle, showCommand);

                if (windowShown)
                {
                    await Task.Delay(50);
                    ErrorHandlingService.LogException(null,
                        $"PowerPointFocusManager: Basic restoration succeeded with command {showCommand}");
                    return true;
                }
                else
                {
                    ErrorHandlingService.LogException(null,
                        "PowerPointFocusManager: ShowWindow failed in basic restoration");
                    return false;
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "PowerPointFocusManager: Error in basic window restoration");
                return false;
            }
        }

        /// <summary>
        /// Strategy 3: Emergency fallback - maximize the window to ensure it's usable.
        /// </summary>
        private async Task EmergencyMaximizeWindow()
        {
            try
            {
                ErrorHandlingService.LogException(null,
                    "PowerPointFocusManager: Using emergency maximize fallback");

                ShowWindow(_powerPointWindowHandle, SW_SHOWMAXIMIZED);
                await Task.Delay(50);

                ErrorHandlingService.LogException(null,
                    "PowerPointFocusManager: Emergency maximize completed");
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "PowerPointFocusManager: Error in emergency maximize");
            }
        }

        /// <summary>
        /// Validates that the captured PowerPoint window is still valid and accessible.
        /// </summary>
        private bool IsValidPowerPointWindow()
        {
            try
            {
                if (_powerPointWindowHandle == IntPtr.Zero)
                    return false;

                // Check if window handle is still valid
                if (!IsWindow(_powerPointWindowHandle))
                {
                    ErrorHandlingService.LogException(null,
                        "PowerPointFocusManager: PowerPoint window handle is no longer valid");
                    return false;
                }

                // Check if window is visible (note: minimized windows are still "visible" in Win32 terms)
                if (!IsWindowVisible(_powerPointWindowHandle))
                {
                    ErrorHandlingService.LogException(null,
                        "PowerPointFocusManager: PowerPoint window is not visible");
                    return false;
                }

                // Verify it's still a PowerPoint window by checking process
                uint processId;
                GetWindowThreadProcessId(_powerPointWindowHandle, out processId);

                try
                {
                    using (var process = System.Diagnostics.Process.GetProcessById((int)processId))
                    {
                        bool isPowerPoint = process.ProcessName.ToLower().Contains("powerpnt");
                        if (!isPowerPoint)
                        {
                            ErrorHandlingService.LogException(null,
                                $"PowerPointFocusManager: Window belongs to {process.ProcessName}, not PowerPoint");
                        }
                        return isPowerPoint;
                    }
                }
                catch
                {
                    ErrorHandlingService.LogException(null,
                        "PowerPointFocusManager: Could not verify PowerPoint process");
                    return false;
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex,
                    "PowerPointFocusManager: Error validating PowerPoint window");
                return false;
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes the focus manager and cleans up resources.
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _powerPointWindowHandle = IntPtr.Zero;
                _powerPointWasActive = false;
                _windowPlacementCaptured = false;

                // Clear the window placement structure
                _originalWindowPlacement = new WINDOWPLACEMENT();

                _disposed = true;

                ErrorHandlingService.LogException(null,
                    "PowerPointFocusManager: Disposed successfully");
            }
        }

        #endregion
    }
}