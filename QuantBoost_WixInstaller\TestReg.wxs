<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
	<!-- Registry components for VSTO add-ins. These tell Office where to find the .vsto manifests.
			 Note: These are per-user (HKCU). If you need per-machine install, consider Office add-in registration
			 under HKLM with proper policy keys, or run per-user installs for each profile. -->
	<Fragment>
		<DirectoryRef Id="INSTALLDIR">
			<Component Id="Registry.Excel" Guid="7E1B5B56-7A61-4D96-9E7D-ABF9A91B7B20" KeyPath="yes" Win64="yes">
				<RegistryKey Root="HKCU" Key="Software\Microsoft\Office\Excel\Addins\QuantBoost.Excel">
					<RegistryValue Name="Description" Value="QuantBoost Excel Add-in" Type="string" />
					<RegistryValue Name="FriendlyName" Value="QuantBoost" Type="string" />
					<RegistryValue Name="LoadBehavior" Value="3" Type="integer" />
					<!-- Append |vstolocal so VSTO loads local assemblies -->
					<RegistryValue Name="Manifest" Value="[EXCELDIR]QuantBoost.Excel.vsto|vstolocal" Type="string" />
				</RegistryKey>
			</Component>

			<Component Id="Registry.PowerPoint" Guid="2C6F1B0E-0B3E-4CAB-8C24-71F5C7C5D3C9" KeyPath="yes" Win64="yes">
				<RegistryKey Root="HKCU" Key="Software\Microsoft\Office\PowerPoint\Addins\QuantBoost.PowerPoint">
					<RegistryValue Name="Description" Value="QuantBoost PowerPoint Add-in" Type="string" />
					<RegistryValue Name="FriendlyName" Value="QuantBoost" Type="string" />
					<RegistryValue Name="LoadBehavior" Value="3" Type="integer" />
					<RegistryValue Name="Manifest" Value="[POWERPOINTDIR]QuantBoost.PowerPoint.vsto|vstolocal" Type="string" />
				</RegistryKey>
			</Component>
		</DirectoryRef>
	</Fragment>
</Wix>

