const express = require('express');
const router = express.Router();
const { supabase } = require('../supabaseClient');
const { sendSuccess, sendError } = require('../utils/responseHelpers');
const { authenticateJWT } = require('../middleware/authMiddleware');

// Apply JWT authentication to all routes in this file
router.use(authenticateJWT);

// GET /subscriptions/:subscriptionId/team-licenses - Get team licenses for a specific subscription
router.get('/subscriptions/:subscriptionId/team-licenses', async (req, res) => {
    const userId = req.user.id;
    const userEmail = req.user.email;
    const { subscriptionId } = req.params;

    if (!userId || !subscriptionId) {
        return sendError(res, 400, 'User ID and subscription ID are required.');
    }

    console.log(`🔍 [TEAM-LICENSES] Fetching team licenses for subscription: ${subscriptionId}, user: ${userId} (${userEmail})`);

    try {
        // Extract the JWT token from the Authorization header
        const authHeader = req.headers.authorization;
        const token = authHeader ? authHeader.split(' ')[1] : null;
        
        console.log(`🔍 [TEAM-LICENSES] Using auth token: ${token ? 'PRESENT' : 'MISSING'}`);
        
        // Create a Supabase client with the user's auth token to properly apply RLS
        const { createClient } = require('@supabase/supabase-js');
        const SUPABASE_URL = process.env.SUPABASE_URL;
        const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;
        
        const userSupabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
            global: {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            }
        });

        // First verify the user owns this subscription
        const { data: subscription, error: subError } = await userSupabase
            .from('subscriptions')
            .select('id, user_id')
            .eq('id', subscriptionId)
            .eq('user_id', userId)
            .single();

        if (subError || !subscription) {
            console.log(`🔍 [TEAM-LICENSES] Subscription not found or access denied: ${subscriptionId}`);
            return sendError(res, 404, 'Subscription not found or access denied.');
        }

        // Get licenses for this subscription
        const { data: licenses, error: licensesError } = await userSupabase
            .from('licenses')
            .select('*')
            .eq('subscription_id', subscriptionId);

        console.log(`🔍 [TEAM-LICENSES] Query result - error:`, licensesError);
        console.log(`🔍 [TEAM-LICENSES] Query result - licenses count:`, licenses?.length || 0);
        console.log(`🔍 [TEAM-LICENSES] Query result - licenses data:`, JSON.stringify(licenses, null, 2));

        if (licensesError) {
            console.error('Error fetching team licenses:', licensesError);
            return sendError(res, 500, 'Failed to fetch team licenses.');
        }

        console.log(`🔍 [TEAM-LICENSES] Returning ${licenses?.length || 0} team licenses for subscription ${subscriptionId}`);
        sendSuccess(res, licenses || []);

    } catch (err) {
        console.error('Unexpected error fetching team licenses:', err);
        sendError(res, 500, 'An unexpected error occurred while fetching team licenses.');
    }
});

module.exports = router;
