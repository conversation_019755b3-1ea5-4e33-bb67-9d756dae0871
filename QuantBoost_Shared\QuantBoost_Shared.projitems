﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <MSBuildAllProjects Condition="'$(MSBuildVersion)' == '' Or '$(MSBuildVersion)' &lt; '16.0'">$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
    <HasSharedItems>true</HasSharedItems>
    <SharedGUID>2919934e-8104-43a1-bc64-84a06cb1f8f1</SharedGUID>
  </PropertyGroup>
  <PropertyGroup Label="Configuration">
    <Import_RootNamespace>QuantBoost_Shared</Import_RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <Folder Include="$(MSBuildThisFileDirectory)Models\" />
    <Folder Include="$(MSBuildThisFileDirectory)Configuration\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="$(MSBuildThisFileDirectory)Security\TokenStorage.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)UI\LicenseDetailsClient.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)UI\LicenseDialog.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)UI\ViewModelBase.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)UI\RelayCommand.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Utilities\AsyncHelper.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Utilities\ErrorHandlingService.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Utilities\MagicLinkAuthReceiver.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Utilities\ProgressState.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Utilities\ToastNotifier.cs" />
  </ItemGroup>
</Project>