﻿
﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using QuantBoost_Excel.Features.ExcelTrace.Model;

namespace QuantBoost_Excel.Features.ExcelTrace.UI
{
    /// <summary>
    /// CRITICAL FIX: Converter to create visual indentation indicators based on tree depth.
    /// </summary>
    public class DepthToIndentationConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int depth)
            {
                if (depth == 0) return "";

                // Create visual tree structure indicators
                var indent = new System.Text.StringBuilder();
                for (int i = 1; i < depth; i++)
                {
                    indent.Append("│ ");
                }
                if (depth > 0)
                {
                    indent.Append("├─");
                }
                return indent.ToString();
            }
            return "";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// REFACTOR: Converter to determine if a TreeViewItem should show an expander arrow.
    /// BUGFIX: Enhanced to properly detect leaf nodes and prevent showing expanders for non-expandable nodes.
    /// ROBUSTNESS: Made more resilient to null values and missing parameters.
    /// </summary>
    public class NodeExpandabilityConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length >= 5)
            {
                // Extract values with null safety
                bool hasItems = values[0] is bool hi && hi;
                bool childrenLoaded = values[1] is bool cl && cl;
                string formula = values[2] as string; // Can be null
                string displayValue = values[3] as string; // Can be null
                object rawValue = values[4]; // Can be null

                // Show expander if:
                // 1. Node already has children (hasItems = true), OR
                // 2. Node is lazy-loaded AND has potential for children:
                //    - Has a formula (can trace precedents), OR
                //    - Is an array node (can expand array elements)

                bool hasFormula = !string.IsNullOrEmpty(formula) && formula.StartsWith("=");
                bool isArrayNode = displayValue == "<array>" && rawValue != null && rawValue.GetType().IsArray;

                bool isLazyLoadable = !childrenLoaded && (hasFormula || isArrayNode);
                bool shouldShowExpander = hasItems || isLazyLoadable;

                System.Diagnostics.Debug.WriteLine($"NodeExpandabilityConverter: hasItems={hasItems}, childrenLoaded={childrenLoaded}, formula='{formula?.Substring(0, Math.Min(20, formula?.Length ?? 0))}...', displayValue='{displayValue}', isArray={rawValue?.GetType().IsArray ?? false}, hasFormula={hasFormula}, isArrayNode={isArrayNode}, shouldShowExpander={shouldShowExpander}");

                return shouldShowExpander ? Visibility.Visible : Visibility.Hidden;
            }

            // Fallback: if we don't have enough parameters, check the first two (original logic)
            if (values.Length >= 2 && values[0] is bool hasItemsFallback && values[1] is bool childrenLoadedFallback)
            {
                bool shouldShowExpanderFallback = hasItemsFallback || !childrenLoadedFallback;
                System.Diagnostics.Debug.WriteLine($"NodeExpandabilityConverter: Fallback mode - hasItems={hasItemsFallback}, childrenLoaded={childrenLoadedFallback}, shouldShowExpander={shouldShowExpanderFallback}");
                return shouldShowExpanderFallback ? Visibility.Visible : Visibility.Hidden;
            }

            // Last resort: hide expander
            System.Diagnostics.Debug.WriteLine($"NodeExpandabilityConverter: Insufficient parameters ({values.Length}), hiding expander");
            return Visibility.Hidden;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public partial class TraceView : UserControl
    {
        #region Routed Events
        public static readonly RoutedEvent CloseEvent = EventManager.RegisterRoutedEvent("Close", RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(TraceView));
        public static readonly RoutedEvent NavigateToNodeEvent = EventManager.RegisterRoutedEvent("NavigateToNode", RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(TraceView));
        public static readonly RoutedEvent SettingsEvent = EventManager.RegisterRoutedEvent("Settings", RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(TraceView));

        public event RoutedEventHandler Close { add { AddHandler(CloseEvent, value); } remove { RemoveHandler(CloseEvent, value); } }
        public event RoutedEventHandler NavigateToNode { add { AddHandler(NavigateToNodeEvent, value); } remove { RemoveHandler(NavigateToNodeEvent, value); } }
        public event RoutedEventHandler Settings { add { AddHandler(SettingsEvent, value); } remove { RemoveHandler(SettingsEvent, value); } }
        #endregion

        private TraceNode _rootNode;

        public TraceView()
        {
            InitializeComponent();
            // Subscribe to the key event that drives the new behavior.
            PrecedentTreeView.SelectedItemChanged += PrecedentTreeView_SelectedItemChanged;

            // CRITICAL FIX: Remove problematic PreviewMouseLeftButtonDown handler and use proper approach
            PrecedentTreeView.PreviewMouseLeftButtonUp += PrecedentTreeView_PreviewMouseLeftButtonUp;

            // CRITICAL FIX: Add event handler to synchronize TreeView visual state with data model
            PrecedentTreeView.ItemContainerGenerator.StatusChanged += ItemContainerGenerator_StatusChanged;

            // DEBUG: Verify event handlers are properly wired
            System.Diagnostics.Debug.WriteLine($"CONSTRUCTOR: TraceView initialized");
            System.Diagnostics.Debug.WriteLine($"CONSTRUCTOR: PrecedentTreeView.Name = {PrecedentTreeView?.Name ?? "null"}");
            System.Diagnostics.Debug.WriteLine($"CONSTRUCTOR: Event handlers wired up including Expanded/Collapsed");
        }

        public void SetTraceData(TraceNode rootNode)
        {
            _rootNode = rootNode ?? throw new ArgumentNullException(nameof(rootNode));
            _rootNode.IsExpanded = true;

            // TASK 2.3: Remove auto-expansion of first level children to reduce initial expansion depth
            // Auto-expand first level children --- REMOVED THIS BLOCK
            // foreach (var child in _rootNode.Children)
            // {
            //     child.IsExpanded = true;
            // }

            this.DataContext = _rootNode;
            PrecedentTreeView.ItemsSource = new[] { _rootNode };

            // ENHANCED: Use enhanced formula display for improved behavior
            // Initialize with root node formula
            UpdateFormulaDisplayEnhanced(_rootNode);
            UpdateStatusText();

            // Force TreeView to update its visual state after setting IsExpanded
            Dispatcher.BeginInvoke(new Action(() =>
            {
                try
                {
                    PrecedentTreeView.UpdateLayout();

                    // Ensure root node container is properly expanded
                    var rootContainer = PrecedentTreeView.ItemContainerGenerator.ContainerFromItem(_rootNode) as TreeViewItem;
                    if (rootContainer != null)
                    {
                        rootContainer.IsExpanded = true;
                        rootContainer.UpdateLayout();

                        // TASK 2.3: Remove auto-expansion of first-level children
                        // Force generation of child containers and expand them --- REMOVED THIS BLOCK
                        // foreach (var child in _rootNode.Children)
                        // {
                        //     var childContainer = rootContainer.ItemContainerGenerator.ContainerFromItem(child) as TreeViewItem;
                        //     if (childContainer != null)
                        //     {
                        //         childContainer.IsExpanded = true;
                        //     }
                        // }

                        System.Diagnostics.Debug.WriteLine($"Root expanded, first-level children remain collapsed for reduced initial expansion");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("Warning: Could not find root TreeViewItem container for auto-expansion");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error during auto-expansion: {ex.Message}");
                }
            }), System.Windows.Threading.DispatcherPriority.Loaded);

            // CRITICAL FIX: Simplified selection logic from the plan with proper focus management
            Dispatcher.BeginInvoke(new Action(() =>
            {
                if (PrecedentTreeView.ItemContainerGenerator.ContainerFromItem(_rootNode) is TreeViewItem item)
                {
                    item.IsSelected = true;
                    item.Focus();

                    // CRITICAL FIX: Ensure TreeView has keyboard focus for navigation
                    PrecedentTreeView.Focus();
                    System.Diagnostics.Debug.WriteLine("TreeView focused for keyboard navigation");

                    // CRITICAL FIX: Hook TreeViewItem events for state synchronization
                    HookTreeViewItemEvents(PrecedentTreeView);
                    System.Diagnostics.Debug.WriteLine("TreeViewItem events hooked for state synchronization");

                    // DEBUG: Check focus state
                    System.Diagnostics.Debug.WriteLine($"FOCUS DEBUG: TreeView.IsFocused = {PrecedentTreeView.IsFocused}");
                    System.Diagnostics.Debug.WriteLine($"FOCUS DEBUG: TreeView.IsKeyboardFocused = {PrecedentTreeView.IsKeyboardFocused}");
                    System.Diagnostics.Debug.WriteLine($"FOCUS DEBUG: TreeViewItem.IsFocused = {item.IsFocused}");
                    System.Diagnostics.Debug.WriteLine($"FOCUS DEBUG: TreeViewItem.IsKeyboardFocused = {item.IsKeyboardFocused}");
                }
            }), System.Windows.Threading.DispatcherPriority.Loaded);
        }

        #region UI Event Handlers

        private void CloseButton_Click(object sender, RoutedEventArgs e) => RaiseEvent(new RoutedEventArgs(CloseEvent, this));
        private void SettingsButton_Click(object sender, RoutedEventArgs e) => RaiseEvent(new RoutedEventArgs(SettingsEvent, sender));

        // Wrap Formula Button handlers removed - wrapping is now always enabled

        /// <summary>
        /// CRITICAL FIX: Handles ItemContainerGenerator status changes to hook into TreeViewItem events.
        /// This allows us to synchronize TreeView visual state with our data model.
        /// </summary>
        private void ItemContainerGenerator_StatusChanged(object sender, EventArgs e)
        {
            if (PrecedentTreeView.ItemContainerGenerator.Status == GeneratorStatus.ContainersGenerated)
            {
                HookTreeViewItemEvents(PrecedentTreeView);
            }
        }

        /// <summary>
        /// CRITICAL FIX: Recursively hooks into TreeViewItem Expanded/Collapsed events.
        /// This ensures our TraceNode.IsExpanded property stays in sync with TreeView visual state.
        /// </summary>
        private void HookTreeViewItemEvents(ItemsControl itemsControl)
        {
            try
            {
                for (int i = 0; i < itemsControl.Items.Count; i++)
                {
                    var container = itemsControl.ItemContainerGenerator.ContainerFromIndex(i) as TreeViewItem;
                    if (container != null)
                    {
                        // Remove existing handlers to prevent duplicates
                        container.Expanded -= TreeViewItem_Expanded;
                        container.Collapsed -= TreeViewItem_Collapsed;

                        // Add handlers
                        container.Expanded += TreeViewItem_Expanded;
                        container.Collapsed += TreeViewItem_Collapsed;

                        // Recursively hook child items
                        HookTreeViewItemEvents(container);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error hooking TreeViewItem events: {ex.Message}");
            }
        }

        /// <summary>
        /// CRITICAL FIX: Handles TreeViewItem expansion to sync with data model and trigger lazy loading.
        /// REFACTOR: Enhanced to handle lazy loading when nodes are expanded via ToggleButton clicks.
        /// </summary>
        private void TreeViewItem_Expanded(object sender, RoutedEventArgs e)
        {
            if (sender is TreeViewItem treeViewItem && treeViewItem.DataContext is TraceNode node)
            {
                System.Diagnostics.Debug.WriteLine($"SYNC: TreeViewItem expanded - updating data model for: {node.DisplayText}");
                node.IsExpanded = true;
                System.Diagnostics.Debug.WriteLine($"SYNC: Data model IsExpanded set to true for: {node.DisplayText}");

                // REFACTOR: Check if this node needs lazy loading
                if (!node.ChildrenLoaded)
                {
                    System.Diagnostics.Debug.WriteLine($"SYNC: Node needs lazy loading: {node.DisplayText}");

                    if (node.DisplayValue == "<array>" && node.RawValue != null && node.RawValue.GetType().IsArray)
                    {
                        System.Diagnostics.Debug.WriteLine($"SYNC: Expanding array node: {node.DisplayText}");
                        ExpandArrayNode(node);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"SYNC: Loading precedents for lazy node: {node.DisplayText}");
                        ExpandLazyLoadedNode(node);
                    }
                }

                // Hook events for any newly generated child containers
                Dispatcher.BeginInvoke(new Action(() => HookTreeViewItemEvents(treeViewItem)),
                    System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        /// <summary>
        /// CRITICAL FIX: Handles TreeViewItem collapse to sync with data model.
        /// </summary>
        private void TreeViewItem_Collapsed(object sender, RoutedEventArgs e)
        {
            if (sender is TreeViewItem treeViewItem && treeViewItem.DataContext is TraceNode node)
            {
                System.Diagnostics.Debug.WriteLine($"SYNC: TreeViewItem collapsed - updating data model for: {node.DisplayText}");
                node.IsExpanded = false;
                System.Diagnostics.Debug.WriteLine($"SYNC: Data model IsExpanded set to false for: {node.DisplayText}");
            }
        }

        /// <summary>
        /// ENHANCED: Updated handler to implement improved formula bar behavior.
        /// When selecting leaf nodes or collapsed branches, keeps parent formula visible with highlighting.
        /// </summary>
        private void PrecedentTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            // The newly selected item is in e.NewValue
            if (e.NewValue is TraceNode selectedNode)
            {
                // ENHANCED: Update formula display with improved logic
                UpdateFormulaDisplayEnhanced(selectedNode);

                // Update context menu visibility based on selected node
                UpdateContextMenuVisibility(selectedNode);

                // Raise the navigation event
                RaiseEvent(new RoutedEventArgs(NavigateToNodeEvent, selectedNode));
            }
        }

        /// <summary>
        /// ENHANCED: Updates the visibility of context menu items based on the selected node.
        /// </summary>
        private void UpdateContextMenuVisibility(TraceNode selectedNode)
        {
            if (PrecedentTreeView.ContextMenu != null)
            {
                var expandArrayMenuItem = PrecedentTreeView.ContextMenu.Items
                    .OfType<MenuItem>()
                    .FirstOrDefault(m => m.Name == "ExpandArrayMenuItem");

                if (expandArrayMenuItem != null)
                {
                    // ENHANCED: Show "Expand Array" for any lazy-loaded node (arrays or regular precedents)
                    // Update the header text based on node type
                    if (selectedNode?.DisplayValue == "<array>" && !selectedNode.ChildrenLoaded)
                    {
                        expandArrayMenuItem.Header = "Expand Array";
                        expandArrayMenuItem.Visibility = Visibility.Visible;
                    }
                    else if (selectedNode != null && !selectedNode.ChildrenLoaded)
                    {
                        expandArrayMenuItem.Header = "Load Precedents";
                        expandArrayMenuItem.Visibility = Visibility.Visible;
                    }
                    else
                    {
                        expandArrayMenuItem.Visibility = Visibility.Collapsed;
                    }
                }
            }
        }

        /// <summary>
        /// DEPRECATED: This method is no longer used since Task 3 implementation.
        /// Replaced by UpdateFormulaDisplay() which provides dynamic formula content based on selection.
        /// Kept for backward compatibility but should not be called.
        /// </summary>
        private void InitializeStaticFormulaDisplay()
        {
            try
            {
                if (_rootNode == null || FormulaRichTextBox == null)
                {
                    return;
                }

                var document = new System.Windows.Documents.FlowDocument
                {
                    FontFamily = new System.Windows.Media.FontFamily("Segoe UI"),
                    FontSize = 13
                };
                var paragraph = new System.Windows.Documents.Paragraph { Margin = new System.Windows.Thickness(0) };

                string rootFormula = _rootNode.Formula;

                if (string.IsNullOrEmpty(rootFormula))
                {
                    // No formula - show value in gray
                    var run = new System.Windows.Documents.Run(_rootNode.DisplayValue ?? "No formula")
                    {
                        Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray)
                    };
                    paragraph.Inlines.Add(run);
                }
                else
                {
                    // CRITICAL FIX: Simplified formula display to prevent corruption
                    // Just show the original formula without complex syntax highlighting
                    // The highlighting will be applied dynamically when nodes are selected
                    var run = new System.Windows.Documents.Run(rootFormula)
                    {
                        Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Black)
                    };
                    paragraph.Inlines.Add(run);
                }

                document.Blocks.Add(paragraph);
                FormulaRichTextBox.Document = document;

                System.Diagnostics.Debug.WriteLine("Static formula display initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing static formula display: {ex.Message}");
                // Fallback to simple text
                try
                {
                    var document = new System.Windows.Documents.FlowDocument();
                    var paragraph = new System.Windows.Documents.Paragraph { Margin = new System.Windows.Thickness(0) };
                    paragraph.Inlines.Add(new System.Windows.Documents.Run(_rootNode?.Formula ?? "Error"));
                    document.Blocks.Add(paragraph);
                    FormulaRichTextBox.Document = document;
                }
                catch
                {
                    // Ignore secondary errors
                }
            }
        }



        /// <summary>
        /// TASK 3.3: Enhanced highlighting logic that works with dynamic formula content.
        /// This method highlights precedents within whatever formula is currently displayed in the formula bar.
        /// </summary>
        /// <param name="selectedNode">The selected TraceNode to highlight in the formula.</param>
        private void HighlightSelectedPrecedent(TraceNode selectedNode)
        {
            try
            {
                if (selectedNode == null || FormulaRichTextBox == null)
                {
                    return;
                }

                var docStart = FormulaRichTextBox.Document.ContentStart;
                var docEnd = FormulaRichTextBox.Document.ContentEnd;

                // Clear all previous background highlights
                var fullRange = new System.Windows.Documents.TextRange(docStart, docEnd);
                fullRange.ApplyPropertyValue(System.Windows.Documents.TextElement.BackgroundProperty, System.Windows.Media.Brushes.Transparent);

                // TASK 3.3: Only apply highlighting if the selected node has its own formula
                // (i.e., the formula bar is showing this node's formula, not descriptive text)
                if (!string.IsNullOrEmpty(selectedNode.Formula))
                {
                    // For nodes with formulas, we can highlight precedents within that formula
                    // If this node has formula position information, use it for precise highlighting
                    if (selectedNode.FormulaStartIndex >= 0 && selectedNode.FormulaLength > 0)
                    {
                        HighlightByPosition(selectedNode.FormulaStartIndex, selectedNode.FormulaLength);
                    }
                    else
                    {
                        // Fallback: search for the node's DisplayText within the currently displayed formula
                        HighlightByTextSearch(selectedNode.DisplayText);
                    }

                    System.Diagnostics.Debug.WriteLine($"TASK 3.3: Highlighted precedent in sub-formula: {selectedNode.DisplayText}");
                }
                else
                {
                    // For nodes without formulas (values, array elements), no highlighting is needed
                    // The formula bar shows descriptive text, not a formula to highlight within
                    System.Diagnostics.Debug.WriteLine($"TASK 3.3: No highlighting for non-formula node: {selectedNode.DisplayText}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error highlighting selected precedent: {ex.Message}");
            }
        }

        /// <summary>
        /// CRITICAL FIX: Highlights text at a specific position within the formula using robust FlowDocument navigation.
        /// This is the improved implementation from the plan that properly handles FlowDocument structure.
        /// </summary>
        private void HighlightByPosition(int startIndex, int length)
        {
            try
            {
                var document = FormulaRichTextBox.Document;

                // Clear previous highlights
                var fullRange = new System.Windows.Documents.TextRange(document.ContentStart, document.ContentEnd);
                fullRange.ApplyPropertyValue(System.Windows.Documents.TextElement.BackgroundProperty, System.Windows.Media.Brushes.Transparent);

                // Get all inline elements
                var inlines = document.Blocks.OfType<System.Windows.Documents.Paragraph>()
                                      .SelectMany(p => p.Inlines).ToList();

                int currentPosition = 0;
                foreach (var inline in inlines)
                {
                    if (inline is System.Windows.Documents.Run run)
                    {
                        string text = run.Text;
                        int runStart = currentPosition;
                        int runEnd = currentPosition + text.Length;

                        // Check if this run contains our highlight range
                        if (runStart < startIndex + length && runEnd > startIndex)
                        {
                            // Calculate overlap
                            int highlightStart = Math.Max(0, startIndex - runStart);
                            int highlightEnd = Math.Min(text.Length, startIndex + length - runStart);

                            if (highlightStart < highlightEnd)
                            {
                                // Apply highlight to the specific portion
                                var startPointer = run.ContentStart.GetPositionAtOffset(highlightStart);
                                var endPointer = run.ContentStart.GetPositionAtOffset(highlightEnd);

                                if (startPointer != null && endPointer != null)
                                {
                                    var highlightRange = new System.Windows.Documents.TextRange(startPointer, endPointer);
                                    highlightRange.ApplyPropertyValue(
                                        System.Windows.Documents.TextElement.BackgroundProperty,
                                        new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromArgb(128, 179, 217, 255))
                                    );
                                }
                            }
                        }
                        currentPosition += text.Length;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in highlighting: {ex.Message}");
            }
        }

        /// <summary>
        /// CRITICAL FIX: Highlights text by searching for it within the formula content using improved algorithm.
        /// Enhanced to handle complex sheet names and edge cases better.
        /// </summary>
        private void HighlightByTextSearch(string textToFind)
        {
            try
            {
                if (string.IsNullOrEmpty(textToFind))
                    return;

                var docStart = FormulaRichTextBox.Document.ContentStart;
                var docEnd = FormulaRichTextBox.Document.ContentEnd;

                // CRITICAL FIX: Get the full text content first for accurate searching
                var fullTextRange = new System.Windows.Documents.TextRange(docStart, docEnd);
                string fullText = fullTextRange.Text;

                System.Diagnostics.Debug.WriteLine($"Searching for '{textToFind}' in formula: '{fullText}'");

                // Try multiple search strategies
                int textIndex = -1;
                string actualSearchText = textToFind;

                // Strategy 1: Exact match
                textIndex = fullText.IndexOf(textToFind, StringComparison.OrdinalIgnoreCase);

                // Strategy 2: Remove sheet qualifiers if not found
                if (textIndex == -1 && textToFind.Contains("!"))
                {
                    actualSearchText = textToFind.Substring(textToFind.LastIndexOf('!') + 1);
                    textIndex = fullText.IndexOf(actualSearchText, StringComparison.OrdinalIgnoreCase);
                    System.Diagnostics.Debug.WriteLine($"Trying simplified search: '{actualSearchText}'");
                }

                // Strategy 3: Try to find just the cell address part
                if (textIndex == -1)
                {
                    // Extract just the cell address (e.g., "AN2549" from "'Platform monthly GP Hours'!AN2549")
                    var cellAddressMatch = System.Text.RegularExpressions.Regex.Match(textToFind, @"[A-Z]+\d+$");
                    if (cellAddressMatch.Success)
                    {
                        actualSearchText = cellAddressMatch.Value;
                        textIndex = fullText.IndexOf(actualSearchText, StringComparison.OrdinalIgnoreCase);
                        System.Diagnostics.Debug.WriteLine($"Trying cell address only: '{actualSearchText}'");
                    }
                }

                if (textIndex == -1)
                {
                    System.Diagnostics.Debug.WriteLine($"CRITICAL FIX: Text not found in formula: {textToFind}");
                    return;
                }

                // CRITICAL FIX: Use the robust position-based highlighting with the found index
                HighlightByPosition(textIndex, actualSearchText.Length);

                System.Diagnostics.Debug.WriteLine($"CRITICAL FIX: Text-based highlighting successful: '{actualSearchText}' at position {textIndex}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in text-based highlighting: {ex.Message}");
            }
        }

        /// <summary>
        /// ENHANCED: Improved formula display that maintains parent formula context for leaf nodes.
        /// Implements the desired behavior where leaf nodes keep showing parent formula with highlighting.
        /// </summary>
        /// <param name="selectedNode">The selected TraceNode to display formula for.</param>
        private void UpdateFormulaDisplayEnhanced(TraceNode selectedNode)
        {
            try
            {
                if (selectedNode == null || FormulaRichTextBox == null)
                {
                    return;
                }

                // Determine which formula to display and which node to highlight
                TraceNode formulaNode = null;
                TraceNode highlightNode = null;

                // CORRECTED LOGIC: Check if this is a leaf node or collapsed branch
                // A node should show parent formula if:
                // 1. It's a leaf node (no children), OR
                // 2. It's a collapsed branch (has children but not expanded), OR
                // 3. It's an array node
                bool isLeafNode = !selectedNode.HasChildren;
                bool isCollapsedBranch = selectedNode.HasChildren && !selectedNode.IsExpanded;
                bool isArrayNode = selectedNode.NodeType == NodeType.ArrayElement || selectedNode.DisplayValue == "<array>";

                if (isLeafNode || isCollapsedBranch || isArrayNode)
                {
                    // Show parent's formula and highlight the selected node within it
                    var parentNode = FindParentNode(_rootNode, selectedNode);
                    if (parentNode != null && !string.IsNullOrEmpty(parentNode.Formula))
                    {
                        formulaNode = parentNode;
                        highlightNode = selectedNode;
                        System.Diagnostics.Debug.WriteLine($"ENHANCED: Showing parent formula for {(isLeafNode ? "leaf" : isCollapsedBranch ? "collapsed branch" : "array")} node: {selectedNode.DisplayText}");
                    }
                    else
                    {
                        // No parent with formula found - show node's own content
                        formulaNode = selectedNode;
                        highlightNode = null;
                        System.Diagnostics.Debug.WriteLine($"ENHANCED: No parent formula found, showing node content: {selectedNode.DisplayText}");
                    }
                }
                else
                {
                    // This is an expanded branch node - show its own formula
                    // CORRECTED: Don't highlight when showing node's own formula
                    formulaNode = selectedNode;
                    highlightNode = null; // No highlighting for own formula
                    System.Diagnostics.Debug.WriteLine($"ENHANCED: Showing own formula for expanded branch (no highlighting): {selectedNode.DisplayText}");
                }

                // Update the formula display
                DisplayFormulaForNode(formulaNode, highlightNode);

                System.Diagnostics.Debug.WriteLine($"ENHANCED: Updated formula display - Formula from: {formulaNode?.DisplayText}, Highlight: {highlightNode?.DisplayText}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateFormulaDisplayEnhanced: {ex.Message}");
                // Fallback to original method
                UpdateFormulaDisplay(selectedNode);
            }
        }

        /// <summary>
        /// TASK 3.1: Helper method to update the formula display based on the selected node.
        /// This replaces the static formula display with dynamic content based on node selection.
        /// </summary>
        /// <param name="node">The selected TraceNode to display formula for.</param>
        private void UpdateFormulaDisplay(TraceNode node)
        {
            try
            {
                if (node == null || FormulaRichTextBox == null)
                {
                    return;
                }

                var document = new System.Windows.Documents.FlowDocument
                {
                    FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                    FontSize = 13
                };
                var paragraph = new System.Windows.Documents.Paragraph { Margin = new System.Windows.Thickness(0) };

                // TASK 3.1: Logic to populate FormulaRichTextBox based on the provided node
                if (!string.IsNullOrEmpty(node.Formula))
                {
                    // 1. If node.Formula is not null or empty, display the formula
                    var run = new System.Windows.Documents.Run(node.Formula)
                    {
                        Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Black)
                    };
                    paragraph.Inlines.Add(run);
                }
                else if (node.NodeType == NodeType.Range || node.NodeType == NodeType.ArrayElement)
                {
                    // 2. If no formula but it's a Range or ArrayElement, display parent's formula for context
                    var parentNode = FindParentNode(_rootNode, node);
                    if (parentNode != null && !string.IsNullOrEmpty(parentNode.Formula))
                    {
                        var run = new System.Windows.Documents.Run($"Parent: {parentNode.Formula}")
                        {
                            Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.DarkBlue),
                            FontStyle = System.Windows.FontStyles.Italic
                        };
                        paragraph.Inlines.Add(run);
                    }
                    else
                    {
                        // Descriptive text for array elements
                        var run = new System.Windows.Documents.Run("<Array Element>")
                        {
                            Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray),
                            FontStyle = System.Windows.FontStyles.Italic
                        };
                        paragraph.Inlines.Add(run);
                    }
                }
                else
                {
                    // 3. Otherwise (simple value), display the DisplayValue in muted style
                    var run = new System.Windows.Documents.Run(node.DisplayValue ?? "No value")
                    {
                        Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray),
                        FontStyle = System.Windows.FontStyles.Italic
                    };
                    paragraph.Inlines.Add(run);
                }

                document.Blocks.Add(paragraph);
                FormulaRichTextBox.Document = document;

                // 4. If the node is the root, call HighlightSelectedPrecedent to clear highlights
                if (node.Depth == 0)
                {
                    HighlightSelectedPrecedent(node);
                }

                System.Diagnostics.Debug.WriteLine($"TASK 3.1: Updated formula display for node: {node.DisplayText}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateFormulaDisplay: {ex.Message}");
                // Fallback to simple text
                try
                {
                    var document = new System.Windows.Documents.FlowDocument();
                    var paragraph = new System.Windows.Documents.Paragraph { Margin = new System.Windows.Thickness(0) };
                    paragraph.Inlines.Add(new System.Windows.Documents.Run(node?.DisplayValue ?? "Error"));
                    document.Blocks.Add(paragraph);
                    FormulaRichTextBox.Document = document;
                }
                catch
                {
                    // Ignore secondary errors
                }
            }
        }

        /// <summary>
        /// ENHANCED: Helper method to display formula and apply highlighting.
        /// </summary>
        /// <param name="formulaNode">The node whose formula to display</param>
        /// <param name="highlightNode">The node to highlight within the formula (null for no highlighting)</param>
        private void DisplayFormulaForNode(TraceNode formulaNode, TraceNode highlightNode)
        {
            try
            {
                var document = new System.Windows.Documents.FlowDocument
                {
                    FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                    FontSize = 13
                };
                var paragraph = new System.Windows.Documents.Paragraph { Margin = new System.Windows.Thickness(0) };

                if (!string.IsNullOrEmpty(formulaNode.Formula))
                {
                    // Display the formula
                    var run = new System.Windows.Documents.Run(formulaNode.Formula)
                    {
                        Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Black)
                    };
                    paragraph.Inlines.Add(run);
                }
                else
                {
                    // Display descriptive text for nodes without formulas
                    string displayText = formulaNode.DisplayValue ?? "No formula";
                    if (formulaNode.NodeType == NodeType.ArrayElement)
                    {
                        displayText = "<Array Element>";
                    }

                    var run = new System.Windows.Documents.Run(displayText)
                    {
                        Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray),
                        FontStyle = System.Windows.FontStyles.Italic
                    };
                    paragraph.Inlines.Add(run);
                }

                document.Blocks.Add(paragraph);
                FormulaRichTextBox.Document = document;

                // Apply highlighting if needed
                // CORRECTED: Only highlight when showing parent's formula for a child node
                // Don't highlight when showing a node's own formula
                if (highlightNode != null && formulaNode != highlightNode && !string.IsNullOrEmpty(formulaNode.Formula))
                {
                    ApplyFormulaHighlighting(highlightNode);
                    System.Diagnostics.Debug.WriteLine($"ENHANCED: Applied highlighting for child node {highlightNode.DisplayText} in parent formula");
                }
                else if (highlightNode != null && formulaNode == highlightNode)
                {
                    System.Diagnostics.Debug.WriteLine($"ENHANCED: No highlighting applied - showing node's own formula: {highlightNode.DisplayText}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"ENHANCED: No highlighting applied - no highlight node or no formula");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in DisplayFormulaForNode: {ex.Message}");
            }
        }

        /// <summary>
        /// EPIC 3 TASK 3.1.3: Helper method to get all nodes in the tree flattened.
        /// </summary>
        private List<TraceNode> GetAllNodesFlattened(TraceNode root)
        {
            var result = new List<TraceNode>();
            if (root == null) return result;

            result.Add(root);
            foreach (var child in root.Children)
            {
                result.AddRange(GetAllNodesFlattened(child));
            }
            return result;
        }

        /// <summary>
        /// ENHANCED: Applies highlighting to the formula for the specified node.
        /// Uses the node's formula position information if available, otherwise falls back to text search.
        /// </summary>
        /// <param name="nodeToHighlight">The node to highlight within the currently displayed formula</param>
        private void ApplyFormulaHighlighting(TraceNode nodeToHighlight)
        {
            try
            {
                // Use existing highlighting logic with position information if available
                if (nodeToHighlight.FormulaStartIndex >= 0 && nodeToHighlight.FormulaLength > 0)
                {
                    HighlightByPosition(nodeToHighlight.FormulaStartIndex, nodeToHighlight.FormulaLength);
                }
                else
                {
                    // Fallback to text search highlighting
                    HighlightByTextSearch(nodeToHighlight.DisplayText);
                }

                System.Diagnostics.Debug.WriteLine($"ENHANCED: Applied highlighting for node: {nodeToHighlight.DisplayText}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ApplyFormulaHighlighting: {ex.Message}");
            }
        }

        /// <summary>
        /// DEPRECATED: This method is no longer used since Task 3 implementation.
        /// Highlighting is now handled by HighlightSelectedPrecedent() which works with dynamic formula content.
        /// Kept for potential future enhancements.
        /// </summary>
        private void ApplyNodeHighlighting(TraceNode node, System.Windows.Media.Brush brush)
        {
            try
            {
                // This method is now handled by the enhanced HighlightSelectedPrecedent method
                // which works with dynamic formula content from UpdateFormulaDisplay
                System.Diagnostics.Debug.WriteLine($"DEPRECATED: ApplyNodeHighlighting called for: {node.DisplayText}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in deprecated ApplyNodeHighlighting: {ex.Message}");
            }
        }

        /// <summary>
        /// CRITICAL FIX: Improved keyboard navigation that properly handles TreeView expansion/collapse.
        /// ENHANCED: Added comprehensive debug logging to identify keyboard navigation issues.
        /// </summary>
        private void PrecedentTreeView_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"=== KEYBOARD DEBUG: PreviewKeyDown triggered ===");
            System.Diagnostics.Debug.WriteLine($"Key pressed: {e.Key}");
            System.Diagnostics.Debug.WriteLine($"TreeView.SelectedItem: {PrecedentTreeView.SelectedItem?.GetType()?.Name ?? "null"}");

            if (PrecedentTreeView.SelectedItem is TraceNode selectedNode)
            {
                System.Diagnostics.Debug.WriteLine($"Selected Node: {selectedNode.DisplayText}");
                System.Diagnostics.Debug.WriteLine($"DisplayValue: '{selectedNode.DisplayValue}'");
                System.Diagnostics.Debug.WriteLine($"ChildrenLoaded: {selectedNode.ChildrenLoaded}");
                System.Diagnostics.Debug.WriteLine($"HasChildren: {selectedNode.HasChildren}");
                System.Diagnostics.Debug.WriteLine($"Children.Count: {selectedNode.Children.Count}");
                System.Diagnostics.Debug.WriteLine($"IsExpanded: {selectedNode.IsExpanded}");
                System.Diagnostics.Debug.WriteLine($"RawValue Type: {selectedNode.RawValue?.GetType()?.Name ?? "null"}");
                System.Diagnostics.Debug.WriteLine($"RawValue IsArray: {selectedNode.RawValue?.GetType()?.IsArray ?? false}");

                switch (e.Key)
                {
                    case Key.Right:
                        System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Processing right arrow key");

                        // TASK 1.1: Check for lazy-loaded nodes BEFORE existing logic
                        if (!selectedNode.ChildrenLoaded)
                        {
                            System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Node is NOT loaded, triggering lazy expansion");

                            // TASK 1.1: Trigger the correct expansion method for lazy-loaded nodes
                            if (selectedNode.DisplayValue == "<array>" && selectedNode.RawValue != null && selectedNode.RawValue.GetType().IsArray)
                            {
                                System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Expanding array node {selectedNode.DisplayText}");
                                ExpandArrayNode(selectedNode);
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Loading precedents for lazy node {selectedNode.DisplayText}");
                                ExpandLazyLoadedNode(selectedNode);
                            }
                            e.Handled = true; // TASK 1.1: Prevent default TreeView behavior
                            System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Set e.Handled = true for lazy loading");
                        }
                        // ENHANCED: Handle right arrow properly for TreeView navigation with lazy loading support
                        else if (selectedNode.HasChildren)
                        {
                            System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Node has children, checking expansion state");

                            if (!selectedNode.IsExpanded)
                            {
                                System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Node is collapsed, letting TreeView handle expansion natively");

                                // BREAKTHROUGH SOLUTION: Don't handle the event at all for nodes with children
                                // This is exactly what double-click does - it doesn't set e.Handled = true
                                // Let the TreeView's native right arrow behavior handle the expansion

                                System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: NOT setting e.Handled = true, letting TreeView expand natively");

                                // Schedule child selection after TreeView has handled the expansion
                                Dispatcher.BeginInvoke(new Action(() =>
                                {
                                    System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Post-native-expansion - checking if we can move to first child");

                                    // Check if TreeView successfully expanded the node
                                    if (selectedNode.IsExpanded && selectedNode.Children.Count > 0)
                                    {
                                        var firstChild = selectedNode.Children[0];
                                        System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: TreeView expanded successfully, moving to first child: {firstChild.DisplayText}");

                                        // Try to select the first child
                                        Dispatcher.BeginInvoke(new Action(() =>
                                        {
                                            try
                                            {
                                                // Use the simplest possible selection method
                                                var childContainer = PrecedentTreeView.ItemContainerGenerator.ContainerFromItem(firstChild) as TreeViewItem;
                                                if (childContainer != null)
                                                {
                                                    childContainer.IsSelected = true;
                                                    childContainer.Focus();
                                                    System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Successfully selected first child via native expansion");
                                                }
                                                else
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Child container still null after native expansion");
                                                    // Try the enhanced selection as fallback
                                                    SelectTreeViewItem(firstChild);
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Error selecting child after native expansion: {ex.Message}");
                                            }
                                        }), System.Windows.Threading.DispatcherPriority.ApplicationIdle);
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: TreeView native expansion failed or no children");
                                        System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: IsExpanded={selectedNode.IsExpanded}, Children.Count={selectedNode.Children.Count}");
                                    }
                                }), System.Windows.Threading.DispatcherPriority.Background);

                                // CRITICAL: Don't set e.Handled = true - let TreeView handle it natively
                                System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Allowing TreeView native expansion for: {selectedNode.DisplayText}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Node is already expanded, moving to first child immediately");
                                // Already expanded, move to first child immediately
                                if (selectedNode.Children.Count > 0)
                                {
                                    // Find the TreeViewItem for the first child and select it
                                    var firstChild = selectedNode.Children[0];
                                    System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: First child is: {firstChild.DisplayText}");
                                    SelectTreeViewItem(firstChild);
                                    e.Handled = true;
                                    System.Diagnostics.Debug.WriteLine($"Moved to first child: {firstChild.DisplayText}");
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: No children found despite HasChildren = true");
                                }
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"RIGHT ARROW: Node has no children and is already loaded, preventing default navigation");
                            // No children and already loaded, prevent default navigation
                            e.Handled = true;
                        }
                        break;

                    case Key.Left:
                        System.Diagnostics.Debug.WriteLine($"LEFT ARROW: Processing left arrow key");

                        // ENHANCED DEBUG: Check both data model and visual TreeViewItem state
                        var treeViewItem = PrecedentTreeView.ItemContainerGenerator.ContainerFromItem(selectedNode) as TreeViewItem;
                        System.Diagnostics.Debug.WriteLine($"LEFT ARROW DEBUG: Data model IsExpanded = {selectedNode.IsExpanded}");
                        System.Diagnostics.Debug.WriteLine($"LEFT ARROW DEBUG: TreeViewItem found = {treeViewItem != null}");
                        if (treeViewItem != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"LEFT ARROW DEBUG: TreeViewItem.IsExpanded = {treeViewItem.IsExpanded}");
                            System.Diagnostics.Debug.WriteLine($"LEFT ARROW DEBUG: TreeViewItem.HasItems = {treeViewItem.HasItems}");
                        }

                        // ENHANCED DEBUG: Try alternative methods to find TreeViewItem
                        TreeViewItem alternativeTreeViewItem = null;
                        if (treeViewItem == null)
                        {
                            System.Diagnostics.Debug.WriteLine($"LEFT ARROW DEBUG: Trying visual tree search for TreeViewItem...");
                            alternativeTreeViewItem = FindTreeViewItemInVisualTree(PrecedentTreeView, selectedNode);
                            System.Diagnostics.Debug.WriteLine($"LEFT ARROW DEBUG: Visual tree search found = {alternativeTreeViewItem != null}");
                            if (alternativeTreeViewItem != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"LEFT ARROW DEBUG: Alternative TreeViewItem.IsExpanded = {alternativeTreeViewItem.IsExpanded}");
                                System.Diagnostics.Debug.WriteLine($"LEFT ARROW DEBUG: Alternative TreeViewItem.HasItems = {alternativeTreeViewItem.HasItems}");
                            }
                        }

                        // Use whichever TreeViewItem we found
                        var actualTreeViewItem = treeViewItem ?? alternativeTreeViewItem;

                        // ENHANCED: Check BOTH data model AND visual state for more reliable detection
                        bool isVisuallyExpanded = actualTreeViewItem?.IsExpanded ?? false;
                        bool hasChildrenInModel = selectedNode.HasChildren;

                        System.Diagnostics.Debug.WriteLine($"LEFT ARROW DEBUG: isVisuallyExpanded = {isVisuallyExpanded}");
                        System.Diagnostics.Debug.WriteLine($"LEFT ARROW DEBUG: hasChildrenInModel = {hasChildrenInModel}");
                        System.Diagnostics.Debug.WriteLine($"LEFT ARROW DEBUG: Final TreeViewItem found = {actualTreeViewItem != null}");

                        // CLEAN DESIGN: Left arrow only handles collapse, up/down arrows handle navigation
                        if (hasChildrenInModel && (selectedNode.IsExpanded || isVisuallyExpanded))
                        {
                            System.Diagnostics.Debug.WriteLine($"LEFT ARROW: Node has children and is expanded (data={selectedNode.IsExpanded}, visual={isVisuallyExpanded}), collapsing manually");

                            // CRITICAL FIX: Manually collapse the node to prevent TreeView's default left arrow behavior (navigate to parent)
                            // We'll handle the collapse ourselves and prevent the default navigation

                            // First, try to collapse via TreeViewItem if we can find it
                            if (actualTreeViewItem != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"LEFT ARROW: Collapsing via actualTreeViewItem.IsExpanded = false");
                                actualTreeViewItem.IsExpanded = false;
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"LEFT ARROW: Collapsing via data model selectedNode.IsExpanded = false");
                                selectedNode.IsExpanded = false;
                            }

                            // Prevent TreeView's default left arrow behavior (navigate to parent)
                            e.Handled = true;

                            System.Diagnostics.Debug.WriteLine($"LEFT ARROW: Manually collapsed node: {selectedNode.DisplayText}");
                        }
                        else if (hasChildrenInModel && !selectedNode.IsExpanded && !isVisuallyExpanded)
                        {
                            // ENHANCED DEBUG: Special case - node has children but appears collapsed in both data and visual
                            // This might indicate a state synchronization issue
                            System.Diagnostics.Debug.WriteLine($"LEFT ARROW: POTENTIAL SYNC ISSUE - Node has children but appears collapsed in both data and visual state");
                            System.Diagnostics.Debug.WriteLine($"LEFT ARROW: This might be a visual/data model sync issue. Checking if node is actually visually expanded...");

                            // Force a more thorough check - maybe the node is visually expanded but our detection failed
                            if (actualTreeViewItem != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"LEFT ARROW: Found TreeViewItem, forcing collapse anyway");
                                actualTreeViewItem.IsExpanded = false;
                                e.Handled = true;
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"LEFT ARROW: No TreeViewItem found, cannot collapse");
                                e.Handled = true; // Still prevent default navigation
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"LEFT ARROW: Node is collapsed or has no children, no action needed");
                            System.Diagnostics.Debug.WriteLine($"LEFT ARROW: Use Up/Down arrows for navigation between nodes");

                            // CLEAN DESIGN: Don't handle parent navigation here - that's for up/down arrows
                            // Just prevent any default TreeView behavior for non-expandable nodes
                            e.Handled = true;
                        }
                        break;

                    case Key.Enter:
                    case Key.Space:
                        System.Diagnostics.Debug.WriteLine($"ENTER/SPACE: Navigating to selected node: {selectedNode.DisplayText}");
                        // Navigate to selected node
                        RaiseEvent(new RoutedEventArgs(NavigateToNodeEvent, selectedNode));
                        e.Handled = true;
                        break;

                    case Key.Up:
                        System.Diagnostics.Debug.WriteLine($"UP ARROW: Letting TreeView handle native up navigation");
                        // Let TreeView handle up navigation natively - don't set e.Handled = true
                        break;

                    case Key.Down:
                        System.Diagnostics.Debug.WriteLine($"DOWN ARROW: Letting TreeView handle native down navigation");
                        // Let TreeView handle down navigation natively - don't set e.Handled = true
                        break;

                    case Key.F5:
                        System.Diagnostics.Debug.WriteLine($"F5: Refreshing trace data");
                        // Refresh/reload trace data
                        if (_rootNode != null)
                        {
                            SetTraceData(_rootNode);
                        }
                        e.Handled = true;
                        break;

                    default:
                        System.Diagnostics.Debug.WriteLine($"OTHER KEY: {e.Key} pressed but not handled");
                        break;
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"KEYBOARD DEBUG: No selected node found");
            }

            System.Diagnostics.Debug.WriteLine($"=== END KEYBOARD DEBUG ===");
        }

        /// <summary>
        /// CRITICAL FIX: Simplified mouse handling that doesn't interfere with ToggleButton.
        /// REFACTOR: Let the ToggleButton handle its own clicks naturally.
        /// </summary>
        private void PrecedentTreeView_PreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"MOUSE DEBUG: PreviewMouseLeftButtonUp triggered");

            var hitTest = e.OriginalSource as DependencyObject;

            // Check if we clicked on the expander - if so, let it handle the click naturally
            while (hitTest != null)
            {
                if (hitTest is ToggleButton toggleButton && toggleButton.Name == "Expander")
                {
                    System.Diagnostics.Debug.WriteLine($"MOUSE DEBUG: Clicked on expander, letting it handle naturally");
                    return; // Don't interfere with ToggleButton's natural behavior
                }

                hitTest = VisualTreeHelper.GetParent(hitTest);
            }

            // Otherwise, handle selection
            if (FindAncestor<TreeViewItem>(e.OriginalSource as DependencyObject) is TreeViewItem item)
            {
                System.Diagnostics.Debug.WriteLine($"MOUSE DEBUG: Clicked on TreeViewItem, setting selection and focus");
                item.IsSelected = true;
                item.Focus();

                // Also ensure TreeView has focus for keyboard navigation
                PrecedentTreeView.Focus();

                System.Diagnostics.Debug.WriteLine($"MOUSE DEBUG: After click - TreeView.IsFocused = {PrecedentTreeView.IsFocused}");
                System.Diagnostics.Debug.WriteLine($"MOUSE DEBUG: After click - TreeView.IsKeyboardFocused = {PrecedentTreeView.IsKeyboardFocused}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"MOUSE DEBUG: Did not click on TreeViewItem");
            }
        }

        /// <summary>
        /// ENHANCED: Handles double-click events on TreeView items for lazy loading, array expansion and navigation.
        /// ENHANCED: Added comprehensive debug logging to compare with keyboard behavior.
        /// </summary>
        private void PrecedentTreeView_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (PrecedentTreeView.SelectedItem is TraceNode selectedNode)
            {
                System.Diagnostics.Debug.WriteLine($"=== DOUBLE-CLICK DEBUG ===");
                System.Diagnostics.Debug.WriteLine($"Selected Node: {selectedNode.DisplayText}");
                System.Diagnostics.Debug.WriteLine($"DisplayValue: '{selectedNode.DisplayValue}'");
                System.Diagnostics.Debug.WriteLine($"ChildrenLoaded: {selectedNode.ChildrenLoaded}");
                System.Diagnostics.Debug.WriteLine($"HasChildren: {selectedNode.HasChildren}");
                System.Diagnostics.Debug.WriteLine($"Children.Count: {selectedNode.Children.Count}");
                System.Diagnostics.Debug.WriteLine($"Data Model IsExpanded: {selectedNode.IsExpanded}");
                System.Diagnostics.Debug.WriteLine($"RawValue Type: {selectedNode.RawValue?.GetType()?.Name ?? "null"}");
                System.Diagnostics.Debug.WriteLine($"RawValue IsArray: {selectedNode.RawValue?.GetType()?.IsArray ?? false}");

                // DEBUG: Check TreeViewItem state
                var treeViewItem = PrecedentTreeView.ItemContainerGenerator.ContainerFromItem(selectedNode) as TreeViewItem;
                if (treeViewItem != null)
                {
                    System.Diagnostics.Debug.WriteLine($"DOUBLE-CLICK: TreeViewItem.IsExpanded = {treeViewItem.IsExpanded}");
                    System.Diagnostics.Debug.WriteLine($"DOUBLE-CLICK: TreeViewItem.HasItems = {treeViewItem.HasItems}");
                    System.Diagnostics.Debug.WriteLine($"DOUBLE-CLICK: TreeViewItem.Items.Count = {treeViewItem.Items.Count}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"DOUBLE-CLICK: TreeViewItem is null");
                }

                // ENHANCED: Handle lazy-loaded nodes (arrays or regular precedents)
                if (!selectedNode.ChildrenLoaded)
                {
                    // Check if this is an array node that needs special array expansion
                    if (selectedNode.DisplayValue == "<array>")
                    {
                        System.Diagnostics.Debug.WriteLine($"DOUBLE-CLICK: Expanding array node {selectedNode.DisplayText}");
                        ExpandArrayNode(selectedNode);
                    }
                    else
                    {
                        // ENHANCED: Handle regular lazy-loaded nodes (e.g., precedents not yet loaded)
                        System.Diagnostics.Debug.WriteLine($"DOUBLE-CLICK: Lazy-loading precedents for node {selectedNode.DisplayText}");
                        ExpandLazyLoadedNode(selectedNode);
                    }
                    e.Handled = true;
                }
                else if (!selectedNode.HasChildren)
                {
                    System.Diagnostics.Debug.WriteLine($"DOUBLE-CLICK: Navigating to leaf node {selectedNode.DisplayText}");
                    // For leaf nodes, navigate to the cell
                    RaiseEvent(new RoutedEventArgs(NavigateToNodeEvent, selectedNode));
                    e.Handled = true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"DOUBLE-CLICK: Node has children but no special action needed");
                    System.Diagnostics.Debug.WriteLine($"DOUBLE-CLICK: Checking if TreeView default expansion will handle this...");

                    // DEBUG: Let's see what happens if we don't handle the event
                    // The TreeView might have its own double-click expansion logic
                    System.Diagnostics.Debug.WriteLine($"DOUBLE-CLICK: NOT setting e.Handled = true, letting TreeView handle it");
                    // e.Handled = true; // COMMENTED OUT to let TreeView handle it
                }
                System.Diagnostics.Debug.WriteLine($"=== END DOUBLE-CLICK DEBUG ===");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"DOUBLE-CLICK: No selected node");
            }
        }

        /// <summary>
        /// Expands an array node by populating its children with array elements.
        /// CRITICAL FIX: Use proper TreeView update without breaking bindings.
        /// </summary>
        /// <param name="arrayNode">The array node to expand.</param>
        private void ExpandArrayNode(TraceNode arrayNode)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== EXPAND ARRAY NODE DEBUG ===");
                System.Diagnostics.Debug.WriteLine($"Node: {arrayNode.DisplayText}");
                System.Diagnostics.Debug.WriteLine($"DisplayValue: '{arrayNode.DisplayValue}'");
                System.Diagnostics.Debug.WriteLine($"RawValue Type: {arrayNode.RawValue?.GetType()?.Name ?? "null"}");
                System.Diagnostics.Debug.WriteLine($"RawValue IsArray: {arrayNode.RawValue?.GetType()?.IsArray ?? false}");
                System.Diagnostics.Debug.WriteLine($"ChildrenLoaded: {arrayNode.ChildrenLoaded}");
                System.Diagnostics.Debug.WriteLine($"Current Children Count: {arrayNode.Children.Count}");

                // Use the TracerEngine to populate array children
                var tracerEngine = new QuantBoost_Excel.Features.ExcelTrace.Logic.TracerEngine();
                System.Diagnostics.Debug.WriteLine($"Created TracerEngine, calling PopulateNodeChildren...");

                bool success = tracerEngine.PopulateNodeChildren(arrayNode);
                System.Diagnostics.Debug.WriteLine($"PopulateNodeChildren returned: {success}");
                System.Diagnostics.Debug.WriteLine($"Children Count after population: {arrayNode.Children.Count}");
                System.Diagnostics.Debug.WriteLine($"ChildrenLoaded after population: {arrayNode.ChildrenLoaded}");

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"SUCCESS: TracerEngine populated {arrayNode.Children.Count} children for array node: {arrayNode.DisplayText}");

                    // Log details of each child
                    for (int i = 0; i < Math.Min(arrayNode.Children.Count, 5); i++)
                    {
                        var child = arrayNode.Children[i];
                        System.Diagnostics.Debug.WriteLine($"  Child {i}: {child.DisplayText} = '{child.DisplayValue}'");
                    }
                    if (arrayNode.Children.Count > 5)
                    {
                        System.Diagnostics.Debug.WriteLine($"  ... and {arrayNode.Children.Count - 5} more children");
                    }

                    // CRITICAL FIX: Use proper TreeView update without breaking bindings
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"UI UPDATE: Starting UI update for {arrayNode.DisplayText}");

                            // Set IsExpanded on the data model (triggers INotifyPropertyChanged)
                            arrayNode.IsExpanded = true;
                            System.Diagnostics.Debug.WriteLine($"UI UPDATE: Set IsExpanded = true");

                            // Force TreeView to update layout
                            PrecedentTreeView.UpdateLayout();
                            System.Diagnostics.Debug.WriteLine($"UI UPDATE: Called UpdateLayout()");

                            // Find and expand the specific TreeViewItem
                            var treeViewItem = FindTreeViewItem(PrecedentTreeView, arrayNode);
                            if (treeViewItem != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"UI UPDATE: Found TreeViewItem for {arrayNode.DisplayText}");
                                treeViewItem.IsExpanded = true;
                                treeViewItem.UpdateLayout();

                                // Force container generation for child items
                                var generator = treeViewItem.ItemContainerGenerator;
                                System.Diagnostics.Debug.WriteLine($"UI UPDATE: Generator status: {generator.Status}");
                                if (generator.Status != GeneratorStatus.ContainersGenerated)
                                {
                                    System.Diagnostics.Debug.WriteLine($"UI UPDATE: Forcing container generation for {arrayNode.Children.Count} children");
                                    // Force generation by accessing containers
                                    for (int i = 0; i < arrayNode.Children.Count; i++)
                                    {
                                        var container = generator.ContainerFromIndex(i);
                                        System.Diagnostics.Debug.WriteLine($"UI UPDATE: Container {i}: {container?.GetType()?.Name ?? "null"}");
                                    }
                                }

                                System.Diagnostics.Debug.WriteLine($"UI UPDATE: Successfully expanded TreeViewItem for: {arrayNode.DisplayText}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"UI UPDATE: ERROR - Could not find TreeViewItem for: {arrayNode.DisplayText}");
                            }

                            System.Diagnostics.Debug.WriteLine($"UI UPDATE: Completed UI update for array node: {arrayNode.DisplayText} with {arrayNode.Children.Count} elements");
                        }
                        catch (Exception uiEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"UI UPDATE ERROR: {uiEx.Message}");
                            System.Diagnostics.Debug.WriteLine($"UI UPDATE ERROR: {uiEx.StackTrace}");
                        }
                    }), System.Windows.Threading.DispatcherPriority.Loaded);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"FAILURE: TracerEngine failed to expand array node: {arrayNode.DisplayText}");
                }

                System.Diagnostics.Debug.WriteLine($"=== END EXPAND ARRAY NODE DEBUG ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"EXCEPTION in ExpandArrayNode: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"EXCEPTION StackTrace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// ENHANCED: Expands a lazy-loaded node by populating its children with precedents.
        /// This handles regular nodes that haven't had their precedents loaded yet.
        /// </summary>
        /// <param name="lazyNode">The lazy-loaded node to expand.</param>
        private void ExpandLazyLoadedNode(TraceNode lazyNode)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== EXPAND LAZY-LOADED NODE DEBUG ===");
                System.Diagnostics.Debug.WriteLine($"Node: {lazyNode.DisplayText}");
                System.Diagnostics.Debug.WriteLine($"DisplayValue: '{lazyNode.DisplayValue}'");
                System.Diagnostics.Debug.WriteLine($"ChildrenLoaded: {lazyNode.ChildrenLoaded}");
                System.Diagnostics.Debug.WriteLine($"Current Children Count: {lazyNode.Children.Count}");

                // Use the TracerEngine to populate precedent children
                var tracerEngine = new QuantBoost_Excel.Features.ExcelTrace.Logic.TracerEngine();
                System.Diagnostics.Debug.WriteLine($"Created TracerEngine, calling PopulateNodeChildren...");

                bool success = tracerEngine.PopulateNodeChildren(lazyNode);
                System.Diagnostics.Debug.WriteLine($"PopulateNodeChildren returned: {success}");
                System.Diagnostics.Debug.WriteLine($"Children Count after population: {lazyNode.Children.Count}");
                System.Diagnostics.Debug.WriteLine($"ChildrenLoaded after population: {lazyNode.ChildrenLoaded}");

                if (success && lazyNode.Children.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"SUCCESS: TracerEngine populated {lazyNode.Children.Count} children for lazy node: {lazyNode.DisplayText}");

                    // Log details of each child (limit to first 5 for readability)
                    for (int i = 0; i < Math.Min(lazyNode.Children.Count, 5); i++)
                    {
                        var child = lazyNode.Children[i];
                        System.Diagnostics.Debug.WriteLine($"  Child {i}: {child.DisplayText} = '{child.DisplayValue}'");
                    }
                    if (lazyNode.Children.Count > 5)
                    {
                        System.Diagnostics.Debug.WriteLine($"  ... and {lazyNode.Children.Count - 5} more children");
                    }

                    // ENHANCED: Force UI updates after expansion to ensure visual consistency
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"UI UPDATE: Starting UI update for {lazyNode.DisplayText}");

                            // Set IsExpanded on the data model (triggers INotifyPropertyChanged)
                            lazyNode.IsExpanded = true;
                            System.Diagnostics.Debug.WriteLine($"UI UPDATE: Set IsExpanded = true");

                            // Force TreeView to update layout
                            PrecedentTreeView.UpdateLayout();
                            System.Diagnostics.Debug.WriteLine($"UI UPDATE: Called UpdateLayout()");

                            // Find and expand the specific TreeViewItem
                            var treeViewItem = FindTreeViewItem(PrecedentTreeView, lazyNode);
                            if (treeViewItem != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"UI UPDATE: Found TreeViewItem for {lazyNode.DisplayText}");
                                treeViewItem.IsExpanded = true;
                                treeViewItem.UpdateLayout();

                                // Force container generation for child items
                                var generator = treeViewItem.ItemContainerGenerator;
                                System.Diagnostics.Debug.WriteLine($"UI UPDATE: Generator status: {generator.Status}");
                                if (generator.Status != GeneratorStatus.ContainersGenerated)
                                {
                                    System.Diagnostics.Debug.WriteLine($"UI UPDATE: Forcing container generation for {lazyNode.Children.Count} children");
                                    // Force generation by accessing containers
                                    for (int i = 0; i < Math.Min(lazyNode.Children.Count, 10); i++) // Limit to first 10 for performance
                                    {
                                        var container = generator.ContainerFromIndex(i);
                                        System.Diagnostics.Debug.WriteLine($"UI UPDATE: Container {i}: {container?.GetType()?.Name ?? "null"}");
                                    }
                                }

                                System.Diagnostics.Debug.WriteLine($"UI UPDATE: Successfully expanded TreeViewItem for: {lazyNode.DisplayText}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"UI UPDATE: ERROR - Could not find TreeViewItem for: {lazyNode.DisplayText}");
                            }

                            System.Diagnostics.Debug.WriteLine($"UI UPDATE: Completed UI update for lazy node: {lazyNode.DisplayText} with {lazyNode.Children.Count} children");
                        }
                        catch (Exception uiEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"UI UPDATE ERROR: {uiEx.Message}");
                            System.Diagnostics.Debug.WriteLine($"UI UPDATE ERROR: {uiEx.StackTrace}");
                        }
                    }), System.Windows.Threading.DispatcherPriority.Loaded);
                }
                else if (success && lazyNode.Children.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"SUCCESS: TracerEngine completed lazy loading but found no children for: {lazyNode.DisplayText}");
                    // Node is now properly loaded but has no children - this is valid
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"FAILURE: TracerEngine failed to expand lazy node: {lazyNode.DisplayText}");
                }

                System.Diagnostics.Debug.WriteLine($"=== END EXPAND LAZY-LOADED NODE DEBUG ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"EXCEPTION in ExpandLazyLoadedNode: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"EXCEPTION StackTrace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// CRITICAL FIX: Helper method to find TreeViewItem for a given data object.
        /// </summary>
        private TreeViewItem FindTreeViewItem(ItemsControl container, object item)
        {
            if (container == null || item == null)
                return null;

            // Check if this container represents the item we're looking for
            if (container is TreeViewItem treeViewItem)
            {
                if (treeViewItem.Header == item || treeViewItem.DataContext == item)
                    return treeViewItem;
            }

            // Search through all items in the container
            for (int i = 0; i < container.Items.Count; i++)
            {
                // Check if this item is the one we're looking for
                if (container.Items[i] == item)
                {
                    var itemContainer = container.ItemContainerGenerator.ContainerFromIndex(i) as TreeViewItem;
                    if (itemContainer != null)
                        return itemContainer;
                }

                // Recursively search in child containers
                var child = container.ItemContainerGenerator.ContainerFromIndex(i) as TreeViewItem;
                if (child != null)
                {
                    var result = FindTreeViewItem(child, item);
                    if (result != null)
                        return result;
                }
            }
            return null;
        }
        
        // Helper method to find a parent of a specific type in the visual tree.
        private static T FindAncestor<T>(DependencyObject current) where T : DependencyObject
        {
            do
            {
                if (current is T)
                {
                    return (T)current;
                }
                current = VisualTreeHelper.GetParent(current);
            }
            while (current != null);
            return null;
        }

        /// <summary>
        /// ENHANCED: Alternative method to find TreeViewItem by searching the visual tree.
        /// This is a fallback when ItemContainerGenerator.ContainerFromItem returns null.
        /// </summary>
        private TreeViewItem FindTreeViewItemInVisualTree(DependencyObject parent, object dataItem)
        {
            if (parent == null) return null;

            // Check if this is a TreeViewItem with the matching data
            if (parent is TreeViewItem treeViewItem)
            {
                if (treeViewItem.DataContext == dataItem || treeViewItem.Header == dataItem)
                {
                    return treeViewItem;
                }
            }

            // Search children
            int childCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childCount; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                var result = FindTreeViewItemInVisualTree(child, dataItem);
                if (result != null)
                {
                    return result;
                }
            }

            return null;
        }

        /// <summary>
        /// CRITICAL FIX: Helper method to programmatically select a TreeView item.
        /// ENHANCED: Added comprehensive debug logging to identify selection issues.
        /// </summary>
        private void SelectTreeViewItem(TraceNode node)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== SELECT TREE VIEW ITEM DEBUG ===");
                System.Diagnostics.Debug.WriteLine($"Attempting to select node: {node?.DisplayText ?? "null"}");

                if (node == null)
                {
                    System.Diagnostics.Debug.WriteLine($"SELECT: Node is null, cannot select");
                    return;
                }

                // Force the TreeView to update its containers
                System.Diagnostics.Debug.WriteLine($"SELECT: Calling UpdateLayout()");
                PrecedentTreeView.UpdateLayout();

                // Get the TreeViewItem for the node
                System.Diagnostics.Debug.WriteLine($"SELECT: Getting container from ItemContainerGenerator");
                System.Diagnostics.Debug.WriteLine($"SELECT: ItemContainerGenerator.Status = {PrecedentTreeView.ItemContainerGenerator.Status}");

                var treeViewItem = PrecedentTreeView.ItemContainerGenerator.ContainerFromItem(node) as TreeViewItem;

                // ENHANCED: Try alternative methods if ContainerFromItem fails
                if (treeViewItem == null)
                {
                    System.Diagnostics.Debug.WriteLine($"SELECT: ContainerFromItem returned null, trying alternative approaches");

                    // Try to find the container by searching through the visual tree
                    treeViewItem = FindTreeViewItemInVisualTree(PrecedentTreeView, node);
                    if (treeViewItem != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"SELECT: Found TreeViewItem via visual tree search");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"SELECT: Visual tree search also failed");

                        // Try forcing container generation
                        System.Diagnostics.Debug.WriteLine($"SELECT: Attempting to force container generation");
                        for (int i = 0; i < PrecedentTreeView.Items.Count; i++)
                        {
                            var container = PrecedentTreeView.ItemContainerGenerator.ContainerFromIndex(i);
                            if (container != null && PrecedentTreeView.Items[i] == node)
                            {
                                treeViewItem = container as TreeViewItem;
                                System.Diagnostics.Debug.WriteLine($"SELECT: Found container via index-based search at index {i}");
                                break;
                            }
                        }
                    }
                }

                if (treeViewItem != null)
                {
                    System.Diagnostics.Debug.WriteLine($"SELECT: Found TreeViewItem, setting IsSelected = true");
                    treeViewItem.IsSelected = true;
                    System.Diagnostics.Debug.WriteLine($"SELECT: Calling Focus()");
                    treeViewItem.Focus();
                    System.Diagnostics.Debug.WriteLine($"SELECT: Calling BringIntoView()");
                    treeViewItem.BringIntoView();
                    System.Diagnostics.Debug.WriteLine($"SELECT: Successfully selected TreeViewItem for: {node.DisplayText}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"SELECT: TreeViewItem not found, trying to expand parents");
                    // If container not found, try to expand parents first
                    var parentNode = FindParentNode(_rootNode, node);
                    if (parentNode != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"SELECT: Found parent node: {parentNode.DisplayText}, IsExpanded: {parentNode.IsExpanded}");
                        if (!parentNode.IsExpanded)
                        {
                            System.Diagnostics.Debug.WriteLine($"SELECT: Expanding parent node: {parentNode.DisplayText}");
                            parentNode.IsExpanded = true;
                            // Try again after a brief delay
                            System.Diagnostics.Debug.WriteLine($"SELECT: Scheduling retry with Dispatcher");
                            Dispatcher.BeginInvoke(new Action(() => SelectTreeViewItem(node)),
                                System.Windows.Threading.DispatcherPriority.Background);
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"SELECT: Parent is already expanded but container still not found");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"SELECT: No parent node found for: {node.DisplayText}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"=== END SELECT TREE VIEW ITEM DEBUG ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SELECT ERROR: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"SELECT ERROR STACK: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// ENHANCED: Specialized method to select a TreeViewItem with forced container generation.
        /// This is specifically for selecting child nodes immediately after parent expansion.
        /// </summary>
        private void SelectTreeViewItemWithForceGeneration(TraceNode childNode, TraceNode parentNode)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== ENHANCED SELECT WITH FORCE GENERATION ===");
                System.Diagnostics.Debug.WriteLine($"Child: {childNode?.DisplayText ?? "null"}");
                System.Diagnostics.Debug.WriteLine($"Parent: {parentNode?.DisplayText ?? "null"}");

                if (childNode == null || parentNode == null)
                {
                    System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Null parameters, cannot proceed");
                    return;
                }

                // First, ensure the parent TreeViewItem is properly expanded
                var parentTreeViewItem = PrecedentTreeView.ItemContainerGenerator.ContainerFromItem(parentNode) as TreeViewItem;
                if (parentTreeViewItem != null)
                {
                    System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Found parent TreeViewItem");
                    System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Parent IsExpanded = {parentTreeViewItem.IsExpanded}");
                    System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Parent HasItems = {parentTreeViewItem.HasItems}");

                    // Ensure parent is expanded
                    if (!parentTreeViewItem.IsExpanded)
                    {
                        parentTreeViewItem.IsExpanded = true;
                        System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Set parent IsExpanded = true");
                    }

                    // Force container generation for children
                    parentTreeViewItem.UpdateLayout();

                    // Try to generate child containers
                    var childGenerator = parentTreeViewItem.ItemContainerGenerator;
                    System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Child generator status = {childGenerator.Status}");

                    if (childGenerator.Status != GeneratorStatus.ContainersGenerated)
                    {
                        System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Forcing child container generation");
                        // Force generation by accessing all child containers
                        for (int i = 0; i < parentTreeViewItem.Items.Count; i++)
                        {
                            var container = childGenerator.ContainerFromIndex(i);
                            System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Child container {i}: {container?.GetType()?.Name ?? "null"}");
                        }
                    }

                    // Now try to find the specific child container
                    var childTreeViewItem = childGenerator.ContainerFromItem(childNode) as TreeViewItem;
                    if (childTreeViewItem != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Found child TreeViewItem, selecting it");
                        childTreeViewItem.IsSelected = true;
                        childTreeViewItem.Focus();
                        childTreeViewItem.BringIntoView();
                        System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Successfully selected child: {childNode.DisplayText}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Child TreeViewItem still not found, trying direct search");
                        // Fallback to the original SelectTreeViewItem method
                        SelectTreeViewItem(childNode);
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT: Parent TreeViewItem not found, using fallback");
                    SelectTreeViewItem(childNode);
                }

                System.Diagnostics.Debug.WriteLine($"=== END ENHANCED SELECT ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT ERROR: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"ENHANCED SELECT ERROR STACK: {ex.StackTrace}");
                // Fallback to original method
                SelectTreeViewItem(childNode);
            }
        }

        /// <summary>
        /// CRITICAL FIX: Helper method to find the parent node of a given node in the tree.
        /// </summary>
        private TraceNode FindParentNode(TraceNode root, TraceNode target)
        {
            if (root == null || target == null) return null;

            // Check direct children
            foreach (var child in root.Children)
            {
                if (child == target)
                {
                    return root;
                }

                // Recursively search in children
                var found = FindParentNode(child, target);
                if (found != null)
                {
                    return found;
                }
            }

            return null;
        }

        #endregion

        #region Context Menu Handlers

        /// <summary>
        /// Handles the Navigate to Cell context menu item.
        /// </summary>
        private void NavigateToCell_Click(object sender, RoutedEventArgs e)
        {
            if (PrecedentTreeView.SelectedItem is TraceNode selectedNode)
            {
                RaiseEvent(new RoutedEventArgs(NavigateToNodeEvent, selectedNode));
            }
        }

        /// <summary>
        /// ENHANCED: Handles the Expand Array / Load Precedents context menu item.
        /// </summary>
        private void ExpandArray_Click(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"=== CONTEXT MENU EXPAND/LOAD DEBUG ===");
            if (PrecedentTreeView.SelectedItem is TraceNode selectedNode)
            {
                System.Diagnostics.Debug.WriteLine($"Selected Node: {selectedNode.DisplayText}");
                System.Diagnostics.Debug.WriteLine($"DisplayValue: '{selectedNode.DisplayValue}'");
                System.Diagnostics.Debug.WriteLine($"ChildrenLoaded: {selectedNode.ChildrenLoaded}");

                // ENHANCED: Handle any lazy-loaded node (arrays or regular precedents)
                if (!selectedNode.ChildrenLoaded)
                {
                    // Check if this is an array node that needs special array expansion
                    if (selectedNode.DisplayValue == "<array>")
                    {
                        System.Diagnostics.Debug.WriteLine($"CONTEXT MENU: Expanding array node {selectedNode.DisplayText}");
                        ExpandArrayNode(selectedNode);
                    }
                    else
                    {
                        // ENHANCED: Handle regular lazy-loaded nodes (e.g., precedents not yet loaded)
                        System.Diagnostics.Debug.WriteLine($"CONTEXT MENU: Lazy-loading precedents for node {selectedNode.DisplayText}");
                        ExpandLazyLoadedNode(selectedNode);
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"CONTEXT MENU: Cannot expand - ChildrenLoaded={selectedNode.ChildrenLoaded}");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"CONTEXT MENU: No selected node");
            }
            System.Diagnostics.Debug.WriteLine($"=== END CONTEXT MENU EXPAND/LOAD DEBUG ===");
        }

        /// <summary>
        /// Handles the Copy Cell Address context menu item.
        /// </summary>
        private void CopyCellAddress_Click(object sender, RoutedEventArgs e)
        {
            if (PrecedentTreeView.SelectedItem is TraceNode selectedNode)
            {
                try
                {
                    string address = selectedNode.FullAddress ?? selectedNode.DisplayText;
                    System.Windows.Clipboard.SetText(address);
                    System.Diagnostics.Debug.WriteLine($"Copied address to clipboard: {address}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error copying address: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Handles the Copy Cell Value context menu item.
        /// </summary>
        private void CopyCellValue_Click(object sender, RoutedEventArgs e)
        {
            if (PrecedentTreeView.SelectedItem is TraceNode selectedNode)
            {
                try
                {
                    string value = selectedNode.DisplayValue ?? "";
                    System.Windows.Clipboard.SetText(value);
                    System.Diagnostics.Debug.WriteLine($"Copied value to clipboard: {value}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error copying value: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Handles the Expand All context menu item.
        /// </summary>
        private void ExpandAll_Click(object sender, RoutedEventArgs e)
        {
            if (_rootNode != null)
            {
                ExpandAllNodes(_rootNode);
            }
        }

        /// <summary>
        /// Handles the Collapse All context menu item.
        /// </summary>
        private void CollapseAll_Click(object sender, RoutedEventArgs e)
        {
            if (_rootNode != null)
            {
                CollapseAllNodes(_rootNode);
                _rootNode.IsExpanded = true; // Keep root expanded
            }
        }

        /// <summary>
        /// Recursively expands all nodes in the tree.
        /// </summary>
        private void ExpandAllNodes(TraceNode node)
        {
            if (node == null) return;

            node.IsExpanded = true;
            foreach (var child in node.Children)
            {
                ExpandAllNodes(child);
            }
        }

        /// <summary>
        /// Recursively collapses all nodes in the tree.
        /// </summary>
        private void CollapseAllNodes(TraceNode node)
        {
            if (node == null) return;

            foreach (var child in node.Children)
            {
                CollapseAllNodes(child);
            }
            node.IsExpanded = false;
        }

        /// <summary>
        /// Updates the status text with simple ready message.
        /// </summary>
        private void UpdateStatusText()
        {
            if (_rootNode == null || StatusTextBlock == null) return;

            try
            {
                // Show simple status message instead of confusing node counts
                int errorNodes = CountErrorNodes(_rootNode);

                string status = "Ready";
                if (errorNodes > 0)
                {
                    status = $"Ready - {errorNodes} error(s) found";
                }

                StatusTextBlock.Text = status;
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "Ready";
                System.Diagnostics.Debug.WriteLine($"Error updating status: {ex.Message}");
            }
        }

        /// <summary>
        /// Counts all nodes in the tree.
        /// </summary>
        private int CountAllNodes(TraceNode node)
        {
            if (node == null) return 0;

            int count = 1;
            foreach (var child in node.Children)
            {
                count += CountAllNodes(child);
            }
            return count;
        }

        /// <summary>
        /// Counts leaf nodes (nodes without children) in the tree.
        /// </summary>
        private int CountLeafNodes(TraceNode node)
        {
            if (node == null) return 0;

            if (!node.HasChildren) return 1;

            int count = 0;
            foreach (var child in node.Children)
            {
                count += CountLeafNodes(child);
            }
            return count;
        }

        /// <summary>
        /// Counts error nodes in the tree.
        /// </summary>
        private int CountErrorNodes(TraceNode node)
        {
            if (node == null) return 0;

            int count = node.HasError ? 1 : 0;
            foreach (var child in node.Children)
            {
                count += CountErrorNodes(child);
            }
            return count;
        }

        #endregion
    }
}
