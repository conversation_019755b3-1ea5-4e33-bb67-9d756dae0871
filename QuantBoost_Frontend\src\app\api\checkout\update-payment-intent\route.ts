import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { ENV, validateStripeConfig, getEnvironmentDebugInfo } from '@/lib/env';

export async function POST(request: NextRequest) {
  // Validate Stripe configuration first
  const stripeValidation = validateStripeConfig();
  if (!stripeValidation.isValid) {
    console.error('❌ Stripe configuration validation failed:', stripeValidation.missing);
    return NextResponse.json({
      error: 'Payment system configuration error',
      details: stripeValidation.missing,
      debug: getEnvironmentDebugInfo()
    }, { status: 500 });
  }

  // Create Stripe client using our env module
  const stripe = new Stripe(ENV.STRIPE_SECRET_KEY!, {
    apiVersion: '2025-08-27.basil' as any,
  });

  try {
    const { paymentIntentId, email, customerInfo } = await request.json();

    if (!paymentIntentId) {
      return NextResponse.json({ error: 'Payment Intent ID is required' }, { status: 400 });
    }

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      console.error(`❌ Invalid email format: ${email}`);
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    if (email === '<EMAIL>') {
      console.error(`❌ Rejected guest email in payment intent update`);
      return NextResponse.json({
        error: 'Guest checkout not supported. Please provide a valid email address.'
      }, { status: 400 });
    }

    console.log(`🔄 Updating Payment Intent ${paymentIntentId} with email: ${email}`);

    // Retrieve the current payment intent
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (!paymentIntent) {
      return NextResponse.json({ error: 'Payment Intent not found' }, { status: 404 });
    }

    // Get the customer ID from the payment intent
    const customerId = paymentIntent.customer as string;

    if (customerId) {
      // Update the customer with the email and additional info
      const customerUpdateData: any = {
        email: email,
      };

      // Add customer info if provided
      if (customerInfo) {
        if (customerInfo.firstName || customerInfo.lastName) {
          customerUpdateData.name = `${customerInfo.firstName || ''} ${customerInfo.lastName || ''}`.trim();
        }
        if (customerInfo.phone) {
          customerUpdateData.phone = customerInfo.phone;
        }
        if (customerInfo.addressLine1 && customerInfo.city && customerInfo.country) {
          customerUpdateData.address = {
            line1: customerInfo.addressLine1,
            line2: customerInfo.addressLine2 || undefined,
            city: customerInfo.city,
            state: customerInfo.state || undefined,
            postal_code: customerInfo.postalCode || undefined,
            country: customerInfo.country,
          };
        }
      }

      console.log(`🔄 Updating customer ${customerId} with email and info`);
      await stripe.customers.update(customerId, customerUpdateData);
      console.log(`✅ Updated customer ${customerId} with email: ${email}`);
    }

    // Update the payment intent metadata with the email
    const updatedPaymentIntent = await stripe.paymentIntents.update(paymentIntentId, {
      receipt_email: email,
      metadata: {
        ...paymentIntent.metadata,
        email: email,
        customerEmail: email,
        emailCaptured: 'true',
        emailCapturedAt: new Date().toISOString(),
      },
    });

    console.log(`✅ Updated Payment Intent ${paymentIntentId} with email: ${email}`);

    return NextResponse.json({
      success: true,
      paymentIntentId: updatedPaymentIntent.id,
      customerId: customerId,
      email: email,
    });

  } catch (error) {
    console.error('Error updating payment intent:', error);

    // Log environment variable status for debugging
    console.error('Environment check:', {
      hasStripeSecret: !!process.env.STRIPE_SECRET_KEY,
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasSupabaseKey: !!process.env.SUPABASE_SERVICE_KEY
    });

    return NextResponse.json({
      error: 'Failed to update payment intent',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
