# Deploy API routes to Azure Static Web App (Staging)
# This script deploys just the API routes without the full frontend

Write-Host "🚀 Deploying API routes to Azure Static Web App (Staging)..." -ForegroundColor Green

# Check if Azure Static Web Apps CLI is installed
if (!(Get-Command "swa" -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Azure Static Web Apps CLI not found. Installing..." -ForegroundColor Red
    npm install -g @azure/static-web-apps-cli
}

# API Token from Terraform output
$API_TOKEN = "84ae3faf7f212973b0772417f22b2c70039add10f2574439b4950afac53c43a001-381c8c86-026a-4d9e-a69c-c162cbdd440400f09120ab50190f"

# Deploy using SWA CLI
Write-Host "📦 Deploying API routes..." -ForegroundColor Yellow

swa deploy ./api --deployment-token $API_TOKEN --api-location ./api

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ API deployment successful!" -ForegroundColor Green
    Write-Host "🌐 Webhook endpoint: https://purple-glacier-0ab50190f.1.azurestaticapps.net/api/webhooks/stripe" -ForegroundColor Cyan
} else {
    Write-Host "❌ API deployment failed!" -ForegroundColor Red
    exit 1
}

Write-Host "🎯 Next steps:" -ForegroundColor Yellow
Write-Host "1. Update Stripe webhook endpoint to: https://purple-glacier-0ab50190f.1.azurestaticapps.net/api/webhooks/stripe"
Write-Host "2. Test the webhook endpoint"
Write-Host "3. Test the complete payment flow"
